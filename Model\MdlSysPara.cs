﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysPara.cs
*
* 功 能： N/A
* 类 名： MdlSysPara
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-16 11:46:54   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 系统参数
	/// </summary>
	[Serializable]
	public partial class MdlSysPara
	{
		public MdlSysPara()
		{}
		#region Model
		private string _paracode;
		private string _paratype;
		private string _paraname;
		private string _paravalue;
		/// <summary>
		/// 参数编码
		/// </summary>
		public string ParaCode
		{
			set{ _paracode=value;}
			get{return _paracode;}
		}
		/// <summary>
		/// 参数类别
		/// </summary>
		public string ParaType
		{
			set{ _paratype=value;}
			get{return _paratype;}
		}
		/// <summary>
		/// 参数名称
		/// </summary>
		public string ParaName
		{
			set{ _paraname=value;}
			get{return _paraname;}
		}
		/// <summary>
		/// 参数值
		/// </summary>
		public string ParaValue
		{
			set{ _paravalue=value;}
			get{return _paravalue;}
		}
		#endregion Model

	}
}

