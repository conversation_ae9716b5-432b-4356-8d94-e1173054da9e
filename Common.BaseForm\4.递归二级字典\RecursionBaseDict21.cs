﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Common.Delegate;

namespace Common.BaseForm
{
    public partial class RecursionBaseDict21 : BaseDict1
    {
        #region 基础变量

        private Common.Delegate.TransmitTreeNode _transmitTreeNode = new TransmitTreeNode();
        private TreeView _baseTreeView = new TreeView();
        private bool _isMainTb = true;
        protected Common.Delegate.TransmitTreeNode MyTransmitTreeNode
        {
            get { return _transmitTreeNode; }
            set
            {
                _transmitTreeNode = value;
            }
        }

        protected TreeView BaseTreeView
        {
            get { return _baseTreeView; }
            set
            {
                _baseTreeView = value;
                TreeInit();
            }

        }

        #endregion
        public RecursionBaseDict21()
        {
            InitializeComponent();
        }
        private void BaseDict21_Load(object sender, EventArgs e)
        {
            if (!base.IsDesignMode())
            {

                MyTransmitTreeNode.SetRecursionTreeNode += TreeEdit;
            }

        }

        #region 自定义函数

        /// <summary>
        /// TreeView初始化
        /// </summary>
        private void TreeInit()
        {
            _baseTreeView.Dock = DockStyle.Fill;
            _baseTreeView.Nodes.Clear();
            _baseTreeView.FullRowSelect = true;
            _baseTreeView.HideSelection = false;
            _baseTreeView.HotTracking = true;
            _baseTreeView.Scrollable = true;
            _baseTreeView.ShowRootLines = true;
            _baseTreeView.Sorted = false;
            _baseTreeView.Font = new Font("宋体", (float)10.5, FontStyle.Regular);
            _baseTreeView.ImageList = imageList2;
            _baseTreeView.ShowPlusMinus = true;
            _baseTreeView.ShowLines = true;
        }

        protected virtual void TreeEdit(TreeNode fatherNode, TreeNode node)
        {

        }

        protected delegate bool DeleNodeDelete(string code);
        protected void NodeDelete(TreeNode node, DeleNodeDelete deleNodeDelete)
        {
            DeleteDB(node, deleNodeDelete);
            deleNodeDelete(node.Name);
            node.Remove();
        }

        private void DeleteDB(TreeNode node, DeleNodeDelete deleNodeDelete)
        {
            foreach (TreeNode childNode in node.Nodes)
            {
                if (childNode.GetNodeCount(false) > 0)
                {
                    DeleteDB(childNode, deleNodeDelete);
                }

                deleNodeDelete(childNode.Name);
            }
        }
        #endregion


    }
}
