# QR-100 特定蛋白分析仪通信协议

本文档根据汇松科技（HEALES TECHNOLOGY）提供的《QR-100特定蛋白分析仪通信协议表》整理而成，描述了设备通过串口发送数据的帧结构和字段定义。

## 1. 通信参数

在与设备进行通信前，请按以下参数配置串口：

- 波特率 (Baud Rate) 115200
- 停止位 (Stop Bits) 1
- 数据位 (Data Bits) 8
- 校验位 (Parity) 不校验 (None)

注意： 具体的通信端口（如 COM1, COM2 等）需要根据实际的物理连接进行选择。

## 2. 数据帧结构 (共28字节)

设备发送的每一帧数据固定为28个字节。下表详细说明了每个字节的含义。

 字节  数据含义  数据类型  数据值范围 (Hex)  说明 
---  ---  ---  ---  ---
 1  随机数  16进制  `00` - `FF`  一个不确定的随机数。 
 2-6  帧开始字  ASCII  `73 74 61 72 74`  固定的ASCII字符串 start。 
 7-8  标本编号  16进制  `0000` - `FFFF`  16位无符号整数，高字节在前。计算公式：`值 = (字节7  256) + 字节8`。 注： 文档示例为 `E8` 和 `03`，说明为 `1000`，这可能存在印刷错误。`1000` 的16进制应为 `0x03E8`，即字节7为`03`，字节8为`E8`。 
 9  年字节  16进制  `00` - `FF`  年份的后两位。例如，`0x0C` 代表12年 (2012年)。 
 10  月字节  16进制  `00` - `0C`  月份。例如，`0x07` 代表7月。 
 11  日字节  16进制  `00` - `1F`  日期。例如，`0x13` 代表19日。 
 12  时字节  16进制  `00` - `17`  小时 (24小时制)。例如，`0x0A` 代表10点。 
 13  分字节  16进制  `00` - `3B`  分钟。例如，`0x29` 代表41分。 
 14  秒字节  16进制  `00` - `3B`  秒。例如，`0x0E` 代表14秒。 
 15  标本类型字节  16进制  `01` - `05`  `01` 全血, `02` 血浆, `03` 血清, `04` 尿液, `05` 其它。 
 16  标本参数类型字节  16进制  `21` - `25`  定义标本的相关参数类型。`0x21` 全血HCT, `0x22` 血浆稀释倍数, `0x23` 血清稀释倍数, `0x24` 尿液稀释倍数, `0x25` 其它稀释倍数。 
 17  标本参数值-整数  16进制  `00` - `FF`  标本参数值的整数部分。 
 18  标本参数值-小数  16进制  `00` - `FF`  标本参数值的小数部分。 
 19  标本参数值-小数点标志  16进制  `31`  `32`  `0x31` 有小数点, `0x32` 无小数点。参数最终值 = `(字节17) + (字节18  100)`。 
 20  项目字节  16进制  `11` - `15`  定义检测项目。 `0x11` CRP, `0x12` hs_CRP, `0x13` mALB, `0x14` Cys-C, `0x15` PCT。 
 21  结果-整数高8位  16进制  `00` - `FF`  测量结果的整数部分高8位。 
 22  结果-整数低8位  16进制  `00` - `FF`  测量结果的整数部分低8位。 
 23  结果-小数部分  16进制  `00` - `FF`  测量结果的小数部分。最终结果值 = `(字节21  256 + 字节22) + (字节23  100)`。 
 24  结果-标志字节  16进制  `00` - `FF`  包含结果范围和格式信息。该字节分为高4位和低4位：br - 低4位 小数点有无。`00` 无小数点, `01` 有小数点。br - 高4位 测量范围。`0x1` 结果超上限 ()，`0x4` 结果低于下限 ()，`0x3` 结果在范围内。 
 25-27 帧结束字  ASCII  `65 6E 64`  固定的ASCII字符串 end。 

---