using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisControl;
using ZTLisEnum;

namespace ZTLisBaseDict
{
    public partial class LabInstrSpecimenParamMap2 : Common.BaseForm.BaseDict2
    {
        BLL.BllLabInstrSpecimenParamMap _bllLabInstrSpecimenParamMap = new BllLabInstrSpecimenParamMap();
        int _instrumentId = 0;
        public LabInstrSpecimenParamMap2()
        {
            InitializeComponent();
        }

        public LabInstrSpecimenParamMap2(int instrumentId, bool insert, DataRow myRow, DataTable myTable)
        {
            InitializeComponent();
            base.Insert = insert;
            base.MyRow = myRow;
            base.MyTable = myTable;
            _instrumentId = instrumentId;
        }

        private void LabInstrSpecimenParamMap2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);

            TxtInstrParamCode.GotFocus += new System.EventHandler(base.InputEn);
            TxtInstrParamName.GotFocus += new System.EventHandler(base.InputEn);
            NumConversionFactor.GotFocus += new System.EventHandler(base.InputEn);
            NumConversionOffset.GotFocus += new System.EventHandler(base.InputEn);
        }

        #region 自定义函数

        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            NumConversionFactor.CustomFormat = "0.0000";
            NumConversionOffset.CustomFormat = "0.0000";

            // 初始化标本参数下拉框
            ComboSpecimenParam.Init();
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtInstrParamCode.Text = "";
            TxtInstrParamName.Text = "";
            ComboSpecimenParam.SelectedIndex = -1;
            NumConversionFactor.Value = 1.0000m;
            NumConversionOffset.Value = 0.0000m;

            TxtInstrParamCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;

            TxtInstrParamCode.Text = row["InstrParamCode"] + "";
            TxtInstrParamName.Text = row["InstrParamName"] + "";

            if (row["ParamId"] != DBNull.Value)
            {
                ComboSpecimenParam.SelectedValue = row["ParamId"];
            }

            if (row["ConversionFactor"] != DBNull.Value)
            {
                NumConversionFactor.Value = decimal.Parse(row["ConversionFactor"].ToString());
            }

            if (row["ConversionOffset"] != DBNull.Value)
            {
                NumConversionOffset.Value = decimal.Parse(row["ConversionOffset"].ToString());
            }

            TxtInstrParamCode.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtInstrParamCode.Text))
            {
                MessageBox.Show("请填写仪器参数代码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtInstrParamCode.Select();
                return false;
            }

            if (string.IsNullOrEmpty(TxtInstrParamName.Text))
            {
                MessageBox.Show("请填写仪器参数名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtInstrParamName.Select();
                return false;
            }

            if (ComboSpecimenParam.SelectedValue == null)
            {
                MessageBox.Show("请选择LIS标本参数！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                ComboSpecimenParam.Select();
                return false;
            }

            // 验证转换系数
            if (CustomControl.Func.NotAllowEmpty(NumConversionFactor)) return false;

            decimal factor;
            if (!decimal.TryParse(NumConversionFactor.Value.ToString(), out factor) || factor <= 0)
            {
                MessageBox.Show("转换系数必须大于0！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumConversionFactor.Select();
                return false;
            }

            // 验证转换偏移量
            if (CustomControl.Func.NotAllowEmpty(NumConversionOffset)) return false;

            decimal offset;
            if (!decimal.TryParse(NumConversionOffset.Value.ToString(), out offset))
            {
                MessageBox.Show("转换偏移量格式不正确！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumConversionOffset.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtInstrParamCode.Text))
                {
                    MessageBox.Show("参数代码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrParamCode.Select();
                    return false;
                }
            }
            else
            {
                if (TxtInstrParamCode.Text.Trim() != base.MyRow["InstrParamCode"].ToString().Trim() && CheckCodeExists(TxtInstrParamCode.Text))
                {
                    MessageBox.Show("参数代码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrParamCode.Select();
                    return false;
                }
            }

            return true;
        }

        private bool CheckCodeExists(string code)
        {
            string strWhere = $"InstrumentId = {_instrumentId} AND InstrParamCode = '{code.Trim()}'";
            if (!base.Insert)
            {
                strWhere += $" AND MapId <> {base.MyRow["MapId"]}";
            }

            DataSet ds = _bllLabInstrSpecimenParamMap.GetList(strWhere);
            return ds.Tables[0].Rows.Count > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            Model.MdlLabInstrSpecimenParamMap model = new MdlLabInstrSpecimenParamMap();
            model.InstrumentId = _instrumentId;
            model.InstrParamCode = TxtInstrParamCode.Text.Trim();
            model.InstrParamName = TxtInstrParamName.Text.Trim();
            model.ParamId = int.Parse(ComboSpecimenParam.SelectedValue.ToString());
            model.ConversionFactor = Common.ConvertObject.ObjToDecimal(NumConversionFactor.Value);
            model.ConversionOffset = Common.ConvertObject.ObjToDecimal(NumConversionOffset.Value);
            model.Status = true;
            model.CreatedAt = DateTime.Now;
            model.UpdatedAt = DateTime.Now;

            //数据保存
            try
            {
                base.MyRow["MapId"] = _bllLabInstrSpecimenParamMap.Add(model);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                ComboSpecimenParam.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            Model.MdlLabInstrSpecimenParamMap model = _bllLabInstrSpecimenParamMap.GetModel(long.Parse(base.MyRow["MapId"].ToString()));
            model.InstrParamCode = TxtInstrParamCode.Text.Trim();
            model.InstrParamName = TxtInstrParamName.Text.Trim();
            model.ParamId = int.Parse(ComboSpecimenParam.SelectedValue.ToString());
            model.ConversionFactor = Common.ConvertObject.ObjToDecimal(NumConversionFactor.Value);
            model.ConversionOffset = Common.ConvertObject.ObjToDecimal(NumConversionOffset.Value);
            model.UpdatedAt = DateTime.Now;

            //数据保存
            try
            {
                _bllLabInstrSpecimenParamMap.Update(model);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 控件事件

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                this.DataAdd();
            }
            else
            {
                this.DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}