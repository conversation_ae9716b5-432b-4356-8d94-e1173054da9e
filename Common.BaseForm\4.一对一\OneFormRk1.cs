﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Common;
using Common.Delegate;
using CustomControl;


namespace Common.BaseForm
{
    public partial class OneFormRk1 : Common.BaseForm.BaseFather
    {
        #region 基础变量
        Common.Delegate.TransmitModel _transmitModel = new Common.Delegate.TransmitModel();

        protected bool Insert { get; set; }
        public Common.Delegate.TransmitModel MyTransmitModel
        {
            get { return _transmitModel; }
            set
            {
                _transmitModel = value;
            }
        }

        #region 按钮    
        protected MyButton BaseBtnDelete { get; set; }
        protected MyButton BaseBtnSave { get; set; }
        protected MyButton BaseBtnNew { get; set; }
        protected MyButton BaseBtnComplete { get; set; }
        protected MyButton BaseBtnPrint { get; set; }
        protected MyButton BaseBtnSearch { get; set; }
        protected MyButton BaseBtnReturn { get; set; }
        protected MyButton BaseBtnClose { get; set; }
        #endregion

        #endregion
        public OneFormRk1()
        {
            InitializeComponent();
        }


        #region 控件动作

        private void BaseForm1_Load(object sender, EventArgs e)
        {
            if (!base.IsDesignMode())
            {
                this.MyTransmitModel.SetModel += ChangeModel;
            }
        }

        private void BaseForm1_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.MyTransmitModel.SetModel -= ChangeModel;
        }


        #region 快捷键

        private void OneFormRk1_KeyDown(object sender, KeyEventArgs e)
        {
            if (BaseBtnSave != null && e.Control == true & e.KeyCode == Keys.S & BaseBtnSave.Enabled == true)
            {
                DataSave(true);
            }
            if (BaseBtnNew != null && e.KeyData == Keys.F2 & BaseBtnNew.Enabled == true)
            {
                DataNew();
            }

            if (BaseBtnSave != null && e.KeyData == Keys.F3 & BaseBtnSave.Enabled == true)
            {
                DataSave(true);
            }

            if (BaseBtnComplete != null && e.KeyData == Keys.F4 & BaseBtnComplete.Enabled == true)
            {
                DataComplete();
            }

            if (BaseBtnPrint != null && e.KeyData == Keys.F5 & BaseBtnPrint.Enabled == true)
            {
                DataPrint();
            }
            if (BaseBtnSearch != null && e.KeyData == Keys.F6 & BaseBtnSearch.Enabled == true)
            {
                DataSearch();
            }
            if (BaseBtnReturn != null && e.KeyData == Keys.F7 & BaseBtnReturn.Enabled == true)
            {
                DataReturn();
            }
            if (BaseBtnClose != null && e.KeyData == Keys.F9 & BaseBtnClose.Enabled == true)
            {
                this.Close();
            }
        }

        #endregion

        #endregion

        #region 公共函数

        protected virtual void BtnState()
        {
        }

        #region  显示函数
        protected virtual void DataClear()
        {

        }
        #endregion

        protected virtual void ChangeModel(object model)
        {
        }
        protected virtual void DataDelete()
        {

        }

        protected void DataDelete(string PrimaryKey, Func<string, bool> Delete)
        {
            if (MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) == DialogResult.OK)
            {
                if (Delete(PrimaryKey) == true) MessageBox.Show("单据删除成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DataNew();
            }
        }

        protected virtual void DataNew()
        {
            DataClear();
            BtnState();
        }

        protected virtual bool DataSave(bool showMsg)
        {
            return default(bool);
        }
        protected virtual void DataComplete()
        {
        }
        protected virtual void DataPrint()
        {
        }
        protected virtual void DataSearch()
        {
        }

        protected virtual void DataReturn()
        {
        }


        #endregion


    }
}
