﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZTLisEnum;

namespace ZTLisConnectors
{
    public class Func
    {      /// <summary>
           /// 获取连接类型的描述
           /// </summary>
           /// <param name="connType">连接类型</param>
           /// <returns>连接类型描述</returns>
        public static string GetConnectionTypeDescription(ConnType connType)
        {
            switch (connType)
            {
                case ConnType.串口: return "串口连接 (RS232)";
                case ConnType.TCP: return "TCP/IP连接";
                case ConnType.文件共享: return "文件共享";
                case ConnType.中间库: return "中间库";
                case ConnType.REST接口: return "REST API";
                case ConnType.HL7_MLLP: return "HL7 MLLP";
                default: return "未知连接类型";
            }
        }

        /// <summary>
        /// 检查连接类型是否已实现
        /// </summary>
        /// <param name="connType">连接类型</param>
        /// <returns>是否已实现</returns>
        public static bool IsConnectionTypeImplemented(ConnType connType)
        {
            switch (connType)
            {
                case ConnType.串口: // 串口连接
                case ConnType.TCP: // TCP连接
                    return true;
                case ConnType.文件共享: // 文件共享
                case ConnType.中间库: // 中间库
                case ConnType.REST接口: // REST API
                case ConnType.HL7_MLLP: // HL7 MLLP
                    return false;
                default:
                    return false;
            }
        }
    }
}
