namespace ZTLisBaseDict
{
    partial class DictUnit1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DictUnit1));
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.C1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.C1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.CmdAdd = new C1.Win.C1Command.C1Command();
            this.C1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.CmdDelete = new C1.Win.C1Command.C1Command();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.CmdImport = new C1.Win.C1Command.C1Command();
            this.c1CommandLink5 = new C1.Win.C1Command.C1CommandLink();
            this.CmdExport = new C1.Win.C1Command.C1Command();
            this.c1CommandLink6 = new C1.Win.C1Command.C1CommandLink();
            this.CmdEnable = new C1.Win.C1Command.C1Command();
            this.c1CommandLink7 = new C1.Win.C1Command.C1CommandLink();
            this.CmdDisable = new C1.Win.C1Command.C1Command();
            this.C1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.CmdRefresh = new C1.Win.C1Command.C1Command();
            this.LblTotal = new System.Windows.Forms.Label();
            this.TxtFilter = new CustomControl.MyTextBox();
            this.myGrid1 = new CustomControl.MyGrid();
            this.CmdMoveUp = new C1.Win.C1Command.C1Command();
            this.CmdMoveDown = new C1.Win.C1Command.C1Command();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.SuspendLayout();
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.Images.SetKeyName(0, "增加.png");
            this.imageList1.Images.SetKeyName(1, "删除.png");
            this.imageList1.Images.SetKeyName(2, "打印.png");
            this.imageList1.Images.SetKeyName(3, "导入.png");
            this.imageList1.Images.SetKeyName(4, "导出.png");
            this.imageList1.Images.SetKeyName(5, "上移.png");
            this.imageList1.Images.SetKeyName(6, "下移.png");
            this.imageList1.Images.SetKeyName(7, "启用.png");
            this.imageList1.Images.SetKeyName(8, "停用.png");
            this.imageList1.Images.SetKeyName(9, "刷新.png");
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.Controls.Add(this.C1ToolBar1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.LblTotal, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.TxtFilter, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.myGrid1, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(778, 538);
            this.tableLayoutPanel1.TabIndex = 6;
            // 
            // C1ToolBar1
            // 
            this.C1ToolBar1.AccessibleName = "Tool Bar";
            this.C1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.C1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.C1ToolBar1.CommandHolder = null;
            this.C1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.C1CommandLink1,
            this.C1CommandLink2,
            this.c1CommandLink4,
            this.c1CommandLink5,
            this.c1CommandLink6,
            this.c1CommandLink7,
            this.C1CommandLink3});
            this.C1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.C1ToolBar1.Name = "C1ToolBar1";
            this.C1ToolBar1.Size = new System.Drawing.Size(273, 44);
            this.C1ToolBar1.Text = "C1ToolBar1";
            this.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // C1CommandLink1
            // 
            this.C1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink1.Command = this.CmdAdd;
            // 
            // CmdAdd
            // 
            this.CmdAdd.ImageIndex = 0;
            this.CmdAdd.Name = "CmdAdd";
            this.CmdAdd.ShortcutText = "";
            this.CmdAdd.Text = "增加";
            this.CmdAdd.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdAdd_Click);
            // 
            // C1CommandLink2
            // 
            this.C1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink2.Command = this.CmdDelete;
            this.C1CommandLink2.SortOrder = 1;
            // 
            // CmdDelete
            // 
            this.CmdDelete.ImageIndex = 1;
            this.CmdDelete.Name = "CmdDelete";
            this.CmdDelete.ShortcutText = "";
            this.CmdDelete.Text = "删除";
            this.CmdDelete.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDelete_Click);
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink4.Command = this.CmdImport;
            this.c1CommandLink4.SortOrder = 2;
            // 
            // CmdImport
            // 
            this.CmdImport.ImageIndex = 3;
            this.CmdImport.Name = "CmdImport";
            this.CmdImport.ShortcutText = "";
            this.CmdImport.Text = "导入";
            this.CmdImport.ToolTipText = "使用导出模版导入";
            this.CmdImport.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdImport_Click);
            // 
            // c1CommandLink5
            // 
            this.c1CommandLink5.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink5.Command = this.CmdExport;
            this.c1CommandLink5.SortOrder = 3;
            // 
            // CmdExport
            // 
            this.CmdExport.ImageIndex = 4;
            this.CmdExport.Name = "CmdExport";
            this.CmdExport.ShortcutText = "";
            this.CmdExport.Text = "导出";
            this.CmdExport.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdExport_Click);
            // 
            // c1CommandLink6
            // 
            this.c1CommandLink6.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink6.Command = this.CmdEnable;
            this.c1CommandLink6.SortOrder = 4;
            // 
            // CmdEnable
            // 
            this.CmdEnable.ImageIndex = 7;
            this.CmdEnable.Name = "CmdEnable";
            this.CmdEnable.ShortcutText = "";
            this.CmdEnable.Text = "启用";
            this.CmdEnable.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdEnable_Click);
            // 
            // c1CommandLink7
            // 
            this.c1CommandLink7.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink7.Command = this.CmdDisable;
            this.c1CommandLink7.SortOrder = 5;
            // 
            // CmdDisable
            // 
            this.CmdDisable.ImageIndex = 8;
            this.CmdDisable.Name = "CmdDisable";
            this.CmdDisable.ShortcutText = "";
            this.CmdDisable.Text = "停用";
            this.CmdDisable.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDisable_Click);
            // 
            // C1CommandLink3
            // 
            this.C1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink3.Command = this.CmdRefresh;
            this.C1CommandLink3.SortOrder = 6;
            // 
            // CmdRefresh
            // 
            this.CmdRefresh.ImageIndex = 9;
            this.CmdRefresh.Name = "CmdRefresh";
            this.CmdRefresh.ShortcutText = "";
            this.CmdRefresh.Text = "刷新";
            this.CmdRefresh.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdRefresh_Click);
            // 
            // LblTotal
            // 
            this.LblTotal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.LblTotal.AutoSize = true;
            this.LblTotal.Location = new System.Drawing.Point(596, 18);
            this.LblTotal.Name = "LblTotal";
            this.LblTotal.Size = new System.Drawing.Size(179, 14);
            this.LblTotal.TabIndex = 0;
            this.LblTotal.Text = "label1";
            // 
            // TxtFilter
            // 
            this.TxtFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtFilter.Captain = "过滤框";
            this.TxtFilter.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFilter.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFilter.CaptainWidth = 55F;
            this.TxtFilter.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFilter.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFilter.EditMask = null;
            this.TxtFilter.Location = new System.Drawing.Point(282, 15);
            this.TxtFilter.Multiline = false;
            this.TxtFilter.Name = "TxtFilter";
            this.TxtFilter.PasswordChar = '\0';
            this.TxtFilter.ReadOnly = false;
            this.TxtFilter.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFilter.SelectionStart = 0;
            this.TxtFilter.SelectStart = 0;
            this.TxtFilter.Size = new System.Drawing.Size(308, 20);
            this.TxtFilter.TabIndex = 1;
            this.TxtFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFilter.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFilter.Watermark = null;
            this.TxtFilter.TextChanged += new System.EventHandler(this.TxtFilter_TextChanged);
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.tableLayoutPanel1.SetColumnSpan(this.myGrid1, 3);
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 50);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(540, 288);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 2;
            this.myGrid1.Xmlpath = null;
            this.myGrid1.DoubleClick += new System.EventHandler(this.myGrid1_DoubleClick);
            // 
            // CmdMoveUp
            // 
            this.CmdMoveUp.ImageIndex = 5;
            this.CmdMoveUp.Name = "CmdMoveUp";
            this.CmdMoveUp.ShortcutText = "";
            this.CmdMoveUp.Text = "上移";
            // 
            // CmdMoveDown
            // 
            this.CmdMoveDown.ImageIndex = 6;
            this.CmdMoveDown.Name = "CmdMoveDown";
            this.CmdMoveDown.ShortcutText = "";
            this.CmdMoveDown.Text = "下移";
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmdAdd);
            this.c1CommandHolder1.Commands.Add(this.CmdDelete);
            this.c1CommandHolder1.Commands.Add(this.CmdImport);
            this.c1CommandHolder1.Commands.Add(this.CmdExport);
            this.c1CommandHolder1.Commands.Add(this.CmdEnable);
            this.c1CommandHolder1.Commands.Add(this.CmdDisable);
            this.c1CommandHolder1.Commands.Add(this.CmdRefresh);
            this.c1CommandHolder1.Commands.Add(this.CmdMoveUp);
            this.c1CommandHolder1.Commands.Add(this.CmdMoveDown);
            this.c1CommandHolder1.ImageList = this.imageList1;
            this.c1CommandHolder1.Owner = this;
            // 
            // DictUnit1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(778, 538);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "DictUnit1";
            this.Text = "单位字典";
            this.Load += new System.EventHandler(this.DictUnit1_Load);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        internal C1.Win.C1Command.C1ToolBar C1ToolBar1;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink1;
        private C1.Win.C1Command.C1Command CmdAdd;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink2;
        private C1.Win.C1Command.C1Command CmdDelete;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink3;
        private C1.Win.C1Command.C1Command CmdRefresh;
        private System.Windows.Forms.Label LblTotal;
        internal CustomControl.MyTextBox TxtFilter;
        private CustomControl.MyGrid myGrid1;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1Command CmdImport;
        private C1.Win.C1Command.C1Command CmdMoveDown;
        private C1.Win.C1Command.C1Command CmdExport;
        private C1.Win.C1Command.C1Command CmdEnable;
        private C1.Win.C1Command.C1Command CmdMoveUp;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1CommandLink c1CommandLink5;
        private C1.Win.C1Command.C1Command CmdDisable;
        private C1.Win.C1Command.C1CommandLink c1CommandLink6;
        private C1.Win.C1Command.C1CommandLink c1CommandLink7;
    }
}