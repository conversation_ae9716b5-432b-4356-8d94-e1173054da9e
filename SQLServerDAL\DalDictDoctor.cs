﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictDoctor.cs
*
* 功 能： N/A
* 类 名： DalDictDoctor
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:12   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictDoctor
	/// </summary>
	public partial class DalDictDoctor : IDalDictDoctor
	{
		public DalDictDoctor()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("DoctorId", "DictDoctor");
		}
		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(DoctorCode) FROM DictDoctor where DoctorCode NOT LIKE '%[^0-9]%' And LEN(DoctorCode)=" + length, length));
			return max;
		}
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int DoctorId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictDoctor");
			strSql.Append(" where DoctorId=@DoctorId");
			SqlParameter[] parameters = {
					new SqlParameter("@DoctorId", SqlDbType.Int,4)
			};
			parameters[0].Value = DoctorId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictDoctor model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictDoctor(");
			strSql.Append("DoctorCode,DoctorName,HisDoctorCode,PinYin,DeptId,Title,LicenseNo,Mobile,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@DoctorCode,@DoctorName,@HisDoctorCode,@PinYin,@DeptId,@Title,@LicenseNo,@Mobile,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@DoctorCode", SqlDbType.NVarChar,20),
					new SqlParameter("@DoctorName", SqlDbType.NVarChar,100),
					new SqlParameter("@HisDoctorCode", SqlDbType.NVarChar,200),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@DeptId", SqlDbType.Int,4),
					new SqlParameter("@Title", SqlDbType.NVarChar,50),
					new SqlParameter("@LicenseNo", SqlDbType.NVarChar,50),
					new SqlParameter("@Mobile", SqlDbType.NVarChar,20),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.DoctorCode;
			parameters[1].Value = model.DoctorName;
			parameters[2].Value = model.HisDoctorCode;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = model.DeptId;
			parameters[5].Value = model.Title;
			parameters[6].Value = model.LicenseNo;
			parameters[7].Value = model.Mobile;
			parameters[8].Value = model.Status;
			parameters[9].Value = model.CreatedAt;
			parameters[10].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictDoctor model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictDoctor set ");
			strSql.Append("DoctorCode=@DoctorCode,");
			strSql.Append("DoctorName=@DoctorName,");
			strSql.Append("HisDoctorCode=@HisDoctorCode,");
			strSql.Append("PinYin=@PinYin,");
			strSql.Append("DeptId=@DeptId,");
			strSql.Append("Title=@Title,");
			strSql.Append("LicenseNo=@LicenseNo,");
			strSql.Append("Mobile=@Mobile,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where DoctorId=@DoctorId");
			SqlParameter[] parameters = {
					new SqlParameter("@DoctorCode", SqlDbType.NVarChar,20),
					new SqlParameter("@DoctorName", SqlDbType.NVarChar,100),
					new SqlParameter("@HisDoctorCode", SqlDbType.NVarChar,200),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@DeptId", SqlDbType.Int,4),
					new SqlParameter("@Title", SqlDbType.NVarChar,50),
					new SqlParameter("@LicenseNo", SqlDbType.NVarChar,50),
					new SqlParameter("@Mobile", SqlDbType.NVarChar,20),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@DoctorId", SqlDbType.Int,4)};
			parameters[0].Value = model.DoctorCode;
			parameters[1].Value = model.DoctorName;
			parameters[2].Value = model.HisDoctorCode;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = model.DeptId;
			parameters[5].Value = model.Title;
			parameters[6].Value = model.LicenseNo;
			parameters[7].Value = model.Mobile;
			parameters[8].Value = model.Status;
			parameters[9].Value = model.CreatedAt;
			parameters[10].Value = model.UpdatedAt;
			parameters[11].Value = model.DoctorId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int DoctorId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictDoctor ");
			strSql.Append(" where DoctorId=@DoctorId");
			SqlParameter[] parameters = {
					new SqlParameter("@DoctorId", SqlDbType.Int,4)
			};
			parameters[0].Value = DoctorId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string DoctorIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictDoctor ");
			strSql.Append(" where DoctorId in (" + DoctorIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictDoctor GetModel(int DoctorId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 DoctorId,DoctorCode,DoctorName,HisDoctorCode,PinYin,DeptId,Title,LicenseNo,Mobile,Status,CreatedAt,UpdatedAt from DictDoctor ");
			strSql.Append(" where DoctorId=@DoctorId");
			SqlParameter[] parameters = {
					new SqlParameter("@DoctorId", SqlDbType.Int,4)
			};
			parameters[0].Value = DoctorId;

			Model.MdlDictDoctor model = new Model.MdlDictDoctor();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictDoctor DataRowToModel(DataRow row)
		{
			Model.MdlDictDoctor model = new Model.MdlDictDoctor();
			if (row != null)
			{
				if (row["DoctorId"] != null && row["DoctorId"].ToString() != "")
				{
					model.DoctorId = int.Parse(row["DoctorId"].ToString());
				}
				if (row["DoctorCode"] != null)
				{
					model.DoctorCode = row["DoctorCode"].ToString();
				}
				if (row["DoctorName"] != null)
				{
					model.DoctorName = row["DoctorName"].ToString();
				}
				if (row["HisDoctorCode"] != null)
				{
					model.HisDoctorCode = row["HisDoctorCode"].ToString();
				}
				if (row["PinYin"] != null)
				{
					model.PinYin = row["PinYin"].ToString();
				}
				if (row["DeptId"] != null && row["DeptId"].ToString() != "")
				{
					model.DeptId = int.Parse(row["DeptId"].ToString());
				}
				if (row["Title"] != null)
				{
					model.Title = row["Title"].ToString();
				}
				if (row["LicenseNo"] != null)
				{
					model.LicenseNo = row["LicenseNo"].ToString();
				}
				if (row["Mobile"] != null)
				{
					model.Mobile = row["Mobile"].ToString();
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DictDoctor.* ");
			strSql.Append(",DictDepartment.DeptName ");
			strSql.Append(" FROM DictDoctor ");
			strSql.Append(" inner join DictDepartment on DictDoctor.DeptId = DictDepartment.DeptId");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" DoctorId,DoctorCode,DoctorName,HisDoctorCode,PinYin,DeptId,Title,LicenseNo,Mobile,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictDoctor ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictDoctor ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.DoctorId desc");
			}
			strSql.Append(")AS Row, T.*  from DictDoctor T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictDoctor";
			parameters[1].Value = "DoctorId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

