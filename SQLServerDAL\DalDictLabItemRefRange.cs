﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictLabItemRefRange.cs
*
* 功 能： N/A
* 类 名： DalDictLabItemRefRange
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:13   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictLabItemRefRange
	/// </summary>
	public partial class DalDictLabItemRefRange : IDalDictLabItemRefRange
	{
		public DalDictLabItemRefRange()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long RefRangeId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictLabItemRefRange");
			strSql.Append(" where RefRangeId=@RefRangeId");
			SqlParameter[] parameters = {
					new SqlParameter("@RefRangeId", SqlDbType.BigInt)
			};
			parameters[0].Value = RefRangeId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlDictLabItemRefRange model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictLabItemRefRange(");
			strSql.Append("LabItemId,MethodId,Sex,AgeLower,AgeUpper,AgeUnit,LowerLimit,UpperLimit,TextualRefValue,AlertLow,AlertHigh,CriticalLow,CriticalHigh,Unit,Status,EffectiveFrom,EffectiveTo,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@LabItemId,@MethodId,@Sex,@AgeLower,@AgeUpper,@AgeUnit,@LowerLimit,@UpperLimit,@TextualRefValue,@AlertLow,@AlertHigh,@CriticalLow,@CriticalHigh,@Unit,@Status,@EffectiveFrom,@EffectiveTo,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@MethodId", SqlDbType.Int,4),
					new SqlParameter("@Sex", SqlDbType.Char,1),
					new SqlParameter("@AgeLower", SqlDbType.Decimal,9),
					new SqlParameter("@AgeUpper", SqlDbType.Decimal,9),
					new SqlParameter("@AgeUnit", SqlDbType.NVarChar,2),
					new SqlParameter("@LowerLimit", SqlDbType.NVarChar,20),
					new SqlParameter("@UpperLimit", SqlDbType.NVarChar,20),
					new SqlParameter("@TextualRefValue", SqlDbType.NVarChar,100),
					new SqlParameter("@AlertLow", SqlDbType.NVarChar,20),
					new SqlParameter("@AlertHigh", SqlDbType.NVarChar,20),
					new SqlParameter("@CriticalLow", SqlDbType.NVarChar,20),
					new SqlParameter("@CriticalHigh", SqlDbType.NVarChar,20),
					new SqlParameter("@Unit", SqlDbType.NVarChar,20),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@EffectiveFrom", SqlDbType.DateTime),
					new SqlParameter("@EffectiveTo", SqlDbType.DateTime),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.LabItemId;
			parameters[1].Value = model.MethodId;
			parameters[2].Value = model.Sex;
			parameters[3].Value = model.AgeLower;
			parameters[4].Value = model.AgeUpper;
			parameters[5].Value = model.AgeUnit;
			parameters[6].Value = model.LowerLimit;
			parameters[7].Value = model.UpperLimit;
			parameters[8].Value = model.TextualRefValue;
			parameters[9].Value = model.AlertLow;
			parameters[10].Value = model.AlertHigh;
			parameters[11].Value = model.CriticalLow;
			parameters[12].Value = model.CriticalHigh;
			parameters[13].Value = model.Unit;
			parameters[14].Value = model.Status;
			parameters[15].Value = model.EffectiveFrom;
			parameters[16].Value = model.EffectiveTo;
			parameters[17].Value = model.CreatedAt;
			parameters[18].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictLabItemRefRange model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictLabItemRefRange set ");
			strSql.Append("LabItemId=@LabItemId,");
			strSql.Append("MethodId=@MethodId,");
			strSql.Append("Sex=@Sex,");
			strSql.Append("AgeLower=@AgeLower,");
			strSql.Append("AgeUpper=@AgeUpper,");
			strSql.Append("AgeUnit=@AgeUnit,");
			strSql.Append("LowerLimit=@LowerLimit,");
			strSql.Append("UpperLimit=@UpperLimit,");
			strSql.Append("TextualRefValue=@TextualRefValue,");
			strSql.Append("AlertLow=@AlertLow,");
			strSql.Append("AlertHigh=@AlertHigh,");
			strSql.Append("CriticalLow=@CriticalLow,");
			strSql.Append("CriticalHigh=@CriticalHigh,");
			strSql.Append("Unit=@Unit,");
			strSql.Append("Status=@Status,");
			strSql.Append("EffectiveFrom=@EffectiveFrom,");
			strSql.Append("EffectiveTo=@EffectiveTo,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where RefRangeId=@RefRangeId");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@MethodId", SqlDbType.Int,4),
					new SqlParameter("@Sex", SqlDbType.Char,1),
					new SqlParameter("@AgeLower", SqlDbType.Decimal,9),
					new SqlParameter("@AgeUpper", SqlDbType.Decimal,9),
					new SqlParameter("@AgeUnit", SqlDbType.NVarChar,2),
					new SqlParameter("@LowerLimit", SqlDbType.NVarChar,20),
					new SqlParameter("@UpperLimit", SqlDbType.NVarChar,20),
					new SqlParameter("@TextualRefValue", SqlDbType.NVarChar,100),
					new SqlParameter("@AlertLow", SqlDbType.NVarChar,20),
					new SqlParameter("@AlertHigh", SqlDbType.NVarChar,20),
					new SqlParameter("@CriticalLow", SqlDbType.NVarChar,20),
					new SqlParameter("@CriticalHigh", SqlDbType.NVarChar,20),
					new SqlParameter("@Unit", SqlDbType.NVarChar,20),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@EffectiveFrom", SqlDbType.DateTime),
					new SqlParameter("@EffectiveTo", SqlDbType.DateTime),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@RefRangeId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.LabItemId;
			parameters[1].Value = model.MethodId;
			parameters[2].Value = model.Sex;
			parameters[3].Value = model.AgeLower;
			parameters[4].Value = model.AgeUpper;
			parameters[5].Value = model.AgeUnit;
			parameters[6].Value = model.LowerLimit;
			parameters[7].Value = model.UpperLimit;
			parameters[8].Value = model.TextualRefValue;
			parameters[9].Value = model.AlertLow;
			parameters[10].Value = model.AlertHigh;
			parameters[11].Value = model.CriticalLow;
			parameters[12].Value = model.CriticalHigh;
			parameters[13].Value = model.Unit;
			parameters[14].Value = model.Status;
			parameters[15].Value = model.EffectiveFrom;
			parameters[16].Value = model.EffectiveTo;
			parameters[17].Value = model.CreatedAt;
			parameters[18].Value = model.UpdatedAt;
			parameters[19].Value = model.RefRangeId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long RefRangeId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictLabItemRefRange ");
			strSql.Append(" where RefRangeId=@RefRangeId");
			SqlParameter[] parameters = {
					new SqlParameter("@RefRangeId", SqlDbType.BigInt)
			};
			parameters[0].Value = RefRangeId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string RefRangeIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictLabItemRefRange ");
			strSql.Append(" where RefRangeId in (" + RefRangeIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabItemRefRange GetModel(long RefRangeId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 * from DictLabItemRefRange ");
			strSql.Append(" where RefRangeId=@RefRangeId");
			SqlParameter[] parameters = {
					new SqlParameter("@RefRangeId", SqlDbType.BigInt)
			};
			parameters[0].Value = RefRangeId;

			Model.MdlDictLabItemRefRange model = new Model.MdlDictLabItemRefRange();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabItemRefRange DataRowToModel(DataRow row)
		{
			Model.MdlDictLabItemRefRange model = new Model.MdlDictLabItemRefRange();
			if (row != null)
			{
				if (row["RefRangeId"] != null && row["RefRangeId"].ToString() != "")
				{
					model.RefRangeId = long.Parse(row["RefRangeId"].ToString());
				}
				if (row["LabItemId"] != null && row["LabItemId"].ToString() != "")
				{
					model.LabItemId = int.Parse(row["LabItemId"].ToString());
				}
				if (row["MethodId"] != null && row["MethodId"].ToString() != "")
				{
					model.MethodId = int.Parse(row["MethodId"].ToString());
				}
				if (row["Sex"] != null)
				{
					model.Sex = row["Sex"].ToString();
				}
				if (row["AgeLower"] != null && row["AgeLower"].ToString() != "")
				{
					model.AgeLower = decimal.Parse(row["AgeLower"].ToString());
				}
				if (row["AgeUpper"] != null && row["AgeUpper"].ToString() != "")
				{
					model.AgeUpper = decimal.Parse(row["AgeUpper"].ToString());
				}
				if (row["AgeUnit"] != null)
				{
					model.AgeUnit = row["AgeUnit"].ToString();
				}
				if (row["LowerLimit"] != null)
				{
					model.LowerLimit = row["LowerLimit"].ToString();
				}
				if (row["UpperLimit"] != null)
				{
					model.UpperLimit = row["UpperLimit"].ToString();
				}
				if (row["TextualRefValue"] != null)
				{
					model.TextualRefValue = row["TextualRefValue"].ToString();
				}
				if (row["AlertLow"] != null)
				{
					model.AlertLow = row["AlertLow"].ToString();
				}
				if (row["AlertHigh"] != null)
				{
					model.AlertHigh = row["AlertHigh"].ToString();
				}
				if (row["CriticalLow"] != null)
				{
					model.CriticalLow = row["CriticalLow"].ToString();
				}
				if (row["CriticalHigh"] != null)
				{
					model.CriticalHigh = row["CriticalHigh"].ToString();
				}
				if (row["Unit"] != null)
				{
					model.Unit = row["Unit"].ToString();
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["EffectiveFrom"] != null && row["EffectiveFrom"].ToString() != "")
				{
					model.EffectiveFrom = DateTime.Parse(row["EffectiveFrom"].ToString());
				}
				if (row["EffectiveTo"] != null && row["EffectiveTo"].ToString() != "")
				{
					model.EffectiveTo = DateTime.Parse(row["EffectiveTo"].ToString());
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DictLabItemRefRange.* ");
			strSql.Append(" ,DictLabItem.LabItemName,DictMethod.MethodName ");
			strSql.Append(" FROM DictLabItemRefRange ");
			strSql.Append(" inner join DictLabItem on DictLabItemRefRange.LabItemId = DictLabItem.LabItemId ");
			strSql.Append(" left join DictMethod on DictLabItem.DefaultMethodId = DictMethod.MethodId ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" RefRangeId,LabItemId,MethodId,Sex,AgeLower,AgeUpper,LowerLimit,UpperLimit,Unit,Status,EffectiveFrom,EffectiveTo,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictLabItemRefRange ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictLabItemRefRange ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.RefRangeId desc");
			}
			strSql.Append(")AS Row, T.*  from DictLabItemRefRange T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictLabItemRefRange";
			parameters[1].Value = "RefRangeId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

