﻿using System;
using System.Reflection;
using System.Configuration;
using Common;
using IDAL;
namespace DALFactory
{
    /// <summary>
    /// 抽象工厂模式创建DAL。
    /// web.config 需要加入配置：(利用工厂模式+反射机制+缓存机制,实现动态创建不同的数据层对象接口) 
    /// DataCache类在导出代码的文件夹里
    /// <appSettings> 
    /// <add key="DAL" value="SQLServerDAL" /> (这里的命名空间根据实际情况更改为自己项目的命名空间)
    /// </appSettings> 
    /// </summary>
    public sealed class DataAccess//<t>
    {
        private static readonly string AssemblyPath = Common.WinFormVar.Var.DALPath;
        /// <summary>
        /// 创建对象或从缓存获取
        /// </summary>
        public static object CreateObject(string AssemblyPath, string ClassNamespace)
        {
            object objType = DataCache.GetCache(ClassNamespace);//从缓存读取
            if (objType == null)
            {
                try
                {
                    objType = Assembly.Load(AssemblyPath).CreateInstance(ClassNamespace);//反射创建
                    DataCache.SetCache(ClassNamespace, objType);// 写入缓存
                }
                catch
                { }
            }
            return objType;
        }


        /// <summary>
		/// 创建DalDictDepartment数据层接口。科室字典表
		/// </summary>
		public static IDAL.IDalDictDepartment CreateDalDictDepartment()
        {

            string ClassNamespace = AssemblyPath + ".DalDictDepartment";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictDepartment)objType;
        }


        /// <summary>
        /// 创建DalDictDoctor数据层接口。医生/检验人员字典表
        /// </summary>
        public static IDAL.IDalDictDoctor CreateDalDictDoctor()
        {

            string ClassNamespace = AssemblyPath + ".DalDictDoctor";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictDoctor)objType;
        }


        /// <summary>
        /// 创建DalDictLabItem数据层接口。检验项目字典表：统一
        /// </summary>
        public static IDAL.IDalDictLabItem CreateDalDictLabItem()
        {

            string ClassNamespace = AssemblyPath + ".DalDictLabItem";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictLabItem)objType;
        }


        /// <summary>
        /// 创建DalDictLabItemCategory数据层接口。检验项目类别字典表
        /// </summary>
        public static IDAL.IDalDictLabItemCategory CreateDalDictLabItemCategory()
        {

            string ClassNamespace = AssemblyPath + ".DalDictLabItemCategory";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictLabItemCategory)objType;
        }


        /// <summary>
        /// 创建DalDictLabItemRefRange数据层接口。检验项目参考区间表：
        /// </summary>
        public static IDAL.IDalDictLabItemRefRange CreateDalDictLabItemRefRange()
        {

            string ClassNamespace = AssemblyPath + ".DalDictLabItemRefRange";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictLabItemRefRange)objType;
        }


        /// <summary>
        /// 创建DalDictMethod数据层接口。检验方法学字典表
        /// </summary>
        public static IDAL.IDalDictMethod CreateDalDictMethod()
        {

            string ClassNamespace = AssemblyPath + ".DalDictMethod";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictMethod)objType;
        }


        /// <summary>
        /// 创建DalDictMethodCategory数据层接口。方法学类别字典表
        /// </summary>
        public static IDAL.IDalDictMethodCategory CreateDalDictMethodCategory()
        {

            string ClassNamespace = AssemblyPath + ".DalDictMethodCategory";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictMethodCategory)objType;
        }


        /// <summary>
        /// 创建DalDictSpecimenCondition数据层接口。标本状态字典：正常、
        /// </summary>
        public static IDAL.IDalDictSpecimenCondition CreateDalDictSpecimenCondition()
        {

            string ClassNamespace = AssemblyPath + ".DalDictSpecimenCondition";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictSpecimenCondition)objType;
        }

        /// <summary>
        /// 创建DalDictVisitType数据层接口。就诊类型字典表
        /// </summary>
        public static IDAL.IDalDictVisitType CreateDalDictVisitType()
        {

            string ClassNamespace = AssemblyPath + ".DalDictVisitType";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictVisitType)objType;
        }


        /// <summary>
        /// 创建DalLabInstrItemMap数据层接口。仪器项目映射表：连接
        /// </summary>
        public static IDAL.IDalLabInstrItemMap CreateDalLabInstrItemMap()
        {

            string ClassNamespace = AssemblyPath + ".DalLabInstrItemMap";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabInstrItemMap)objType;
        }

        /// <summary>
        /// 创建DalLabInstrRawMsg数据层接口。仪器原始报文缓冲表
        /// </summary>
        public static IDAL.IDalLabInstrRawMsg CreateDalLabInstrRawMsg()
        {

            string ClassNamespace = AssemblyPath + ".DalLabInstrRawMsg";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabInstrRawMsg)objType;
        }


        /// <summary>
        /// 创建DalLabInstrResultQ数据层接口。解析结果中间队列表：
        /// </summary>
        public static IDAL.IDalLabInstrResultQ CreateDalLabInstrResultQ()
        {

            string ClassNamespace = AssemblyPath + ".DalLabInstrResultQ";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabInstrResultQ)objType;
        }


        /// <summary>
        /// 创建DalLabInstrument数据层接口。仪器主档/连接参数表
        /// </summary>
        public static IDAL.IDalLabInstrument CreateDalLabInstrument()
        {

            string ClassNamespace = AssemblyPath + ".DalLabInstrument";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabInstrument)objType;
        }


        /// <summary>
        /// 创建DalLabResult数据层接口。检验结果主表：保存审
        /// </summary>
        public static IDAL.IDalLabResult CreateDalLabResult()
        {

            string ClassNamespace = AssemblyPath + ".DalLabResult";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabResult)objType;
        }


        /// <summary>
        /// 创建DalSysMenu1数据层接口。一级菜单
        /// </summary>
        public static IDAL.IDalSysMenu1 CreateDalSysMenu1()
        {

            string ClassNamespace = AssemblyPath + ".DalSysMenu1";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysMenu1)objType;
        }


        /// <summary>
        /// 创建DalSysMenu2数据层接口。二级菜单
        /// </summary>
        public static IDAL.IDalSysMenu2 CreateDalSysMenu2()
        {

            string ClassNamespace = AssemblyPath + ".DalSysMenu2";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysMenu2)objType;
        }


        /// <summary>
        /// 创建DalSysModule数据层接口。系统模块
        /// </summary>
        public static IDAL.IDalSysModule CreateDalSysModule()
        {

            string ClassNamespace = AssemblyPath + ".DalSysModule";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysModule)objType;
        }


        /// <summary>
        /// 创建DalSysModuleAuth数据层接口。系统模块权限
        /// </summary>
        public static IDAL.IDalSysModuleAuth CreateDalSysModuleAuth()
        {

            string ClassNamespace = AssemblyPath + ".DalSysModuleAuth";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysModuleAuth)objType;
        }


        /// <summary>
        /// 创建DalSysPara数据层接口。系统参数
        /// </summary>
        public static IDAL.IDalSysPara CreateDalSysPara()
        {

            string ClassNamespace = AssemblyPath + ".DalSysPara";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysPara)objType;
        }


        /// <summary>
        /// 创建DalSysRole数据层接口。系统角色
        /// </summary>
        public static IDAL.IDalSysRole CreateDalSysRole()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRole";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRole)objType;
        }


        /// <summary>
        /// 创建DalSysRoleAuth数据层接口。角色权限
        /// </summary>
        public static IDAL.IDalSysRoleAuth CreateDalSysRoleAuth()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRoleAuth";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRoleAuth)objType;
        }


        /// <summary>
        /// 创建DalSysRoleModule数据层接口。角色模块
        /// </summary>
        public static IDAL.IDalSysRoleModule CreateDalSysRoleModule()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRoleModule";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRoleModule)objType;
        }


        /// <summary>
        /// 创建DalSysRpt数据层接口。系统报表
        /// </summary>
        public static IDAL.IDalSysRpt CreateDalSysRpt()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRpt";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRpt)objType;
        }


        /// <summary>
        /// 创建DalSysRpt_Class数据层接口。报表类别
        /// </summary>
        public static IDAL.IDalSysRpt_Class CreateDalSysRpt_Class()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRpt_Class";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRpt_Class)objType;
        }


        /// <summary>
        /// 创建DalSysRpt_Class_Level数据层接口。报表类别规则
        /// </summary>
        public static IDAL.IDalSysRpt_Class_Level CreateDalSysRpt_Class_Level()
        {

            string ClassNamespace = AssemblyPath + ".DalSysRpt_Class_Level";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysRpt_Class_Level)objType;
        }
        /// <summary>
        /// 创建DalSysRpt_Class_Level数据层接口。报表类别规则
        /// </summary>
        public static IDAL.IDalDictSampleType CreateDalDictSampleType()
        {

            string ClassNamespace = AssemblyPath + ".DalDictSampleType";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictSampleType)objType;
        }

        /// <summary>
        /// 创建DalDictSpecimenParam数据层接口。标本参数字典表：标准化管理标本参数（如HCT、HIL指数等）
        /// </summary>
        public static IDAL.IDalDictSpecimenParam CreateDalDictSpecimenParam()
        {

            string ClassNamespace = AssemblyPath + ".DalDictSpecimenParam";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictSpecimenParam)objType;
        }


        /// <summary>
        /// 创建DalLabInstrSpecimenParamMap数据层接口。仪器标本参数映射表：
        /// </summary>
        public static IDAL.IDalLabInstrSpecimenParamMap CreateDalLabInstrSpecimenParamMap()
        {

            string ClassNamespace = AssemblyPath + ".DalLabInstrSpecimenParamMap";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabInstrSpecimenParamMap)objType;
        }
        /// <summary>
        /// 创建DalDictUnit数据层接口。检验结果单位字典表
        /// </summary>
        public static IDAL.IDalDictUnit CreateDalDictUnit()
        {

            string ClassNamespace = AssemblyPath + ".DalDictUnit";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictUnit)objType;
        }
        /// <summary>
        /// 创建DalDictLabProfile数据层接口。检验组合字典表
        /// </summary>
        public static IDAL.IDalDictLabProfile CreateDalDictLabProfile()
        {

            string ClassNamespace = AssemblyPath + ".DalDictLabProfile";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictLabProfile)objType;
        }


        /// <summary>
        /// 创建DalDictLabProfileItemMap数据层接口。检验组合-项目映射表
        /// </summary>
        public static IDAL.IDalDictLabProfileItemMap CreateDalDictLabProfileItemMap()
        {

            string ClassNamespace = AssemblyPath + ".DalDictLabProfileItemMap";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictLabProfileItemMap)objType;
        }


        /// <summary>
        /// 创建DalLabOrder数据层接口。检验申请主表
        /// </summary>
        public static IDAL.IDalLabOrder CreateDalLabOrder()
        {

            string ClassNamespace = AssemblyPath + ".DalLabOrder";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabOrder)objType;
        }


        /// <summary>
        /// 创建DalLabOrderItem数据层接口。检验医嘱明细表
        /// </summary>
        public static IDAL.IDalLabOrderItem CreateDalLabOrderItem()
        {

            string ClassNamespace = AssemblyPath + ".DalLabOrderItem";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabOrderItem)objType;
        }
        /// <summary>
        /// 创建DalSysUser数据层接口。系统用户表
        /// </summary>
        public static IDAL.IDalSysUser CreateDalSysUser()
        {

            string ClassNamespace = AssemblyPath + ".DalSysUser";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalSysUser)objType;
        }


        /// <summary>
        /// 创建DalLabResultReview数据层接口。检验结果审核记录表
        /// </summary>
        public static IDAL.IDalLabResultReview CreateDalLabResultReview()
        {

            string ClassNamespace = AssemblyPath + ".DalLabResultReview";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabResultReview)objType;
        }


        /// <summary>
        /// 创建DalLabSpecimen数据层接口。检验标本信息表
        /// </summary>
        public static IDAL.IDalLabSpecimen CreateDalLabSpecimen()
        {

            string ClassNamespace = AssemblyPath + ".DalLabSpecimen";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalLabSpecimen)objType;
        }


        /// <summary>
        /// 创建DalDictLabItemCalculation数据层接口。检验项目计算公式表
        /// </summary>
        public static IDAL.IDalDictLabItemCalculation CreateDalDictLabItemCalculation()
        {

            string ClassNamespace = AssemblyPath + ".DalDictLabItemCalculation";
            object objType = CreateObject(AssemblyPath, ClassNamespace);
            return (IDAL.IDalDictLabItemCalculation)objType;
        }
    }
}
