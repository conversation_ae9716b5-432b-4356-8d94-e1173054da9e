using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZTLisEnum;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// 解析器核心接口，定义解析器的标准功能
    /// </summary>
    public interface IParser
    {
        /// <summary>
        /// 解析器名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 支持的协议类型
        /// </summary>
        ProtocolType SupportedProtocol { get; }

        /// <summary>
        /// 解析原始报文
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <param name="instrumentId">仪器ID</param>
        /// <returns>解析结果</returns>
        Task<ParseResult> ParseAsync(string rawMessage, int instrumentId);

        /// <summary>
        /// 验证报文格式
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <returns>是否有效</returns>
        bool ValidateMessage(string rawMessage);
    }
}