﻿namespace CustomControl
{
    partial class MyGrid
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        
        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MyGrid));
            this.c1TrueDBGrid1 = new C1.Win.C1TrueDBGrid.C1TrueDBGrid();
            this.c1ContextMenu1 = new C1.Win.C1Command.C1ContextMenu();
            this.c1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.unfoldComm = new C1.Win.C1Command.C1Command();
            this.c1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.shrinkComm = new C1.Win.C1Command.C1Command();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.ResetComm = new C1.Win.C1Command.C1Command();
            this.c1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.customComm = new C1.Win.C1Command.C1Command();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            ((System.ComponentModel.ISupportInitialize)(this.c1TrueDBGrid1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.SuspendLayout();
            // 
            // c1TrueDBGrid1
            // 
            this.c1TrueDBGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.c1TrueDBGrid1.GroupByCaption = "将列头拖拽到这里以便按照该列进行分组";
            this.c1TrueDBGrid1.Images.Add(((System.Drawing.Image)(resources.GetObject("c1TrueDBGrid1.Images"))));
            this.c1TrueDBGrid1.Location = new System.Drawing.Point(0, 0);
            this.c1TrueDBGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.c1TrueDBGrid1.Name = "c1TrueDBGrid1";
            this.c1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口";
            this.c1TrueDBGrid1.PreviewInfo.Location = new System.Drawing.Point(0, 0);
            this.c1TrueDBGrid1.PreviewInfo.Size = new System.Drawing.Size(0, 0);
            this.c1TrueDBGrid1.PreviewInfo.ZoomFactor = 75D;
            this.c1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen;
            this.c1TrueDBGrid1.PrintInfo.MeasurementPrinterName = null;
            this.c1TrueDBGrid1.Size = new System.Drawing.Size(150, 150);
            this.c1TrueDBGrid1.TabIndex = 0;
            this.c1TrueDBGrid1.Text = "c1TrueDBGrid1";
            this.c1TrueDBGrid1.UseCompatibleTextRendering = false;
            this.c1TrueDBGrid1.PropBag = resources.GetString("c1TrueDBGrid1.PropBag");
            // 
            // c1ContextMenu1
            // 
            this.c1ContextMenu1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink1,
            this.c1CommandLink2,
            this.c1CommandLink4,
            this.c1CommandLink3});
            this.c1ContextMenu1.Name = "c1ContextMenu1";
            this.c1ContextMenu1.ShortcutText = "";
            this.c1ContextMenu1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue;
            this.c1ContextMenu1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            // 
            // c1CommandLink1
            // 
            this.c1CommandLink1.Command = this.unfoldComm;
            // 
            // unfoldComm
            // 
            this.unfoldComm.Name = "unfoldComm";
            this.unfoldComm.ShortcutText = "";
            this.unfoldComm.Text = "展开所有节点";
            this.unfoldComm.Visible = false;
            this.unfoldComm.Click += new C1.Win.C1Command.ClickEventHandler(this.unfoldComm_Click);
            // 
            // c1CommandLink2
            // 
            this.c1CommandLink2.Command = this.shrinkComm;
            this.c1CommandLink2.SortOrder = 1;
            // 
            // shrinkComm
            // 
            this.shrinkComm.Name = "shrinkComm";
            this.shrinkComm.ShortcutText = "";
            this.shrinkComm.Text = "收缩所有节点";
            this.shrinkComm.Visible = false;
            this.shrinkComm.Click += new C1.Win.C1Command.ClickEventHandler(this.shrinkComm_Click);
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.Command = this.ResetComm;
            this.c1CommandLink4.Delimiter = true;
            this.c1CommandLink4.SortOrder = 2;
            // 
            // ResetComm
            // 
            this.ResetComm.Image = global::CustomControl.Properties.Resources.重置;
            this.ResetComm.Name = "ResetComm";
            this.ResetComm.ShortcutText = "";
            this.ResetComm.Text = "重置样式";
            this.ResetComm.Visible = false;
            this.ResetComm.Click += new C1.Win.C1Command.ClickEventHandler(this.ResetComm_Click);
            // 
            // c1CommandLink3
            // 
            this.c1CommandLink3.Command = this.customComm;
            this.c1CommandLink3.SortOrder = 3;
            this.c1CommandLink3.Text = "选择显示的列";
            // 
            // customComm
            // 
            this.customComm.Enabled = false;
            this.customComm.Name = "customComm";
            this.customComm.ShortcutText = "";
            this.customComm.Text = "自定义显示列";
            this.customComm.Visible = false;
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.c1ContextMenu1);
            this.c1CommandHolder1.Commands.Add(this.unfoldComm);
            this.c1CommandHolder1.Commands.Add(this.shrinkComm);
            this.c1CommandHolder1.Commands.Add(this.customComm);
            this.c1CommandHolder1.Commands.Add(this.ResetComm);
            this.c1CommandHolder1.Owner = this;
            // 
            // MyGrid
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.c1TrueDBGrid1);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "MyGrid";
            ((System.ComponentModel.ISupportInitialize)(this.c1TrueDBGrid1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private C1.Win.C1TrueDBGrid.C1TrueDBGrid c1TrueDBGrid1;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1ContextMenu c1ContextMenu1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink1;
        private C1.Win.C1Command.C1Command unfoldComm;
        private C1.Win.C1Command.C1CommandLink c1CommandLink2;
        private C1.Win.C1Command.C1Command shrinkComm;
        private C1.Win.C1Command.C1CommandLink c1CommandLink3;
        private C1.Win.C1Command.C1Command customComm;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1Command ResetComm;
    }
}
