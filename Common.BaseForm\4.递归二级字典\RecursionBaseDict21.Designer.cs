﻿namespace Common.BaseForm
{
    partial class RecursionBaseDict21
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(RecursionBaseDict21));
            this.imageList2 = new System.Windows.Forms.ImageList(this.components);
            this.SuspendLayout();
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.Images.SetKeyName(0, "增加.png");
            this.imageList1.Images.SetKeyName(1, "删除.png");
            this.imageList1.Images.SetKeyName(2, "打印.png");
            this.imageList1.Images.SetKeyName(3, "导入.png");
            this.imageList1.Images.SetKeyName(4, "导出.png");
            this.imageList1.Images.SetKeyName(5, "上移.png");
            this.imageList1.Images.SetKeyName(6, "下移.png");
            this.imageList1.Images.SetKeyName(7, "启用.png");
            this.imageList1.Images.SetKeyName(8, "停用.png");
            this.imageList1.Images.SetKeyName(9, "刷新.png");
            // 
            // imageList2
            // 
            this.imageList2.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList2.ImageStream")));
            this.imageList2.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList2.Images.SetKeyName(0, "Cabinet_24px_1109498_easyicon.net.png");
            this.imageList2.Images.SetKeyName(1, "Folder_Closed_24px_1079181_easyicon.net.png");
            this.imageList2.Images.SetKeyName(2, "Folder_Open_24px_1079182_easyicon.net.png");
            // 
            // RecursionBaseDict21
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.ClientSize = new System.Drawing.Size(300, 300);
            this.Name = "RecursionBaseDict21";
            this.Text = "BaseDict21";
            this.Load += new System.EventHandler(this.BaseDict21_Load);
            this.ResumeLayout(false);

        }

        #endregion

        protected System.Windows.Forms.ImageList imageList2;
    }
}
