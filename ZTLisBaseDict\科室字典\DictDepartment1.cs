using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictDepartment1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictDepartment _bllDictDepartment = new BllDictDepartment();

        public DictDepartment1()
        {
            InitializeComponent();
        }

        private void DictDepartment1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "DeptCode", 120, "中", "", false);
            myGrid1.Init_Column("名称", "DeptName", 160, "左", "", false);
            myGrid1.Init_Column("简称", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("HIS编码", "HisDeptCode", 150, "左", "", false);
            myGrid1.Init_Column("科室类型", "DeptType", 120, "左", "", false);
            myGrid1.Init_Column("备注", "Description", 200, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllDictDepartment.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["DeptId"] };
            base.MyTable.Columns["DeptId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictDepartment2 vform = new DictDepartment2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除科室：" + this.myGrid1.Columns["DeptName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictDepartment.Delete(int.Parse(base.MyRow["DeptId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("DeptCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("DeptName", new Common.MdlExcel(15, "", "名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "简称", true));
            colconfig.Add("HisDeptCode", new Common.MdlExcel(15, "", "HIS编码", true));
            colconfig.Add("DeptType", new Common.MdlExcel(12, "", "科室类型", true));
            colconfig.Add("Description", new Common.MdlExcel(20, "", "备注", true));
            Common.ExcelHelper.Export(base.MyTable, "科室字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictDepartment mdlDictDepartment = new MdlDictDepartment();
                    if (row["名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictDepartment.DeptCode = row["编码"] + "";
                    mdlDictDepartment.DeptName = row["名称"] + "";
                    mdlDictDepartment.PinYin = base.MyChs2Spell.GetPy(row["名称"].ToString());
                    mdlDictDepartment.HisDeptCode = row["HIS编码"] + "";
                    mdlDictDepartment.DeptType = row["科室类型"] + "";
                    mdlDictDepartment.Description = row["备注"] + "";
                    mdlDictDepartment.ParentDeptId = null;
                    mdlDictDepartment.Status = true;
                    mdlDictDepartment.CreatedAt = DateTime.Now;
                    mdlDictDepartment.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictDepartment).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictDepartment, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["DeptId"] = _bllDictDepartment.Add(mdlDictDepartment);
                    base.MyTable.Rows.Add(newRow);
                    base.MyTable.AcceptChanges();
                    UpdateData[i - 1] = "成功";
                    i = i + 1;
                }
                base.MyTransmitTxt.OnSetText("最后");
                MessageBox.Show("操作结果：\n成功" + successCnt + "条;失败" + failCnt + "条;", "操作结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
            }
        }

        private void DataEnable(bool Status)
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;

            // 获取当前记录的Model并更新状态
            var mdlDictDepartment = _bllDictDepartment.GetModel(int.Parse(base.MyRow["DeptId"].ToString()));
            mdlDictDepartment.Status = Status;
            mdlDictDepartment.UpdatedAt = DateTime.Now;
            _bllDictDepartment.Update(mdlDictDepartment);

            base.MyRow["Status"] = Status;
            base.MyRow["UpdatedAt"] = DateTime.Now;
            base.MyRow.AcceptChanges();
        }

        #endregion

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }
        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("DeptCode+DeptName+PinYin", TxtFilter.Text.Trim());
        }
    }
}