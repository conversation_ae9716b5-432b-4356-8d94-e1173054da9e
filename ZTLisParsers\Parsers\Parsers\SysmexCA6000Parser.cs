using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Model;
using ZTLisEnum;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// Sysmex CA-6000 血凝仪解释器
    /// 解析已去除STX/ETX的原始字符串到LabInstrResultQ
    /// 示例数据: D1210101U2310271430000101  20231027001M           0410128 0510305 0620285
    /// </summary>
    public class SysmexCA6000Parser : BaseParser
    {
        /// <summary>
        /// 解析器名称
        /// </summary>
        public override string Name => "Sysmex CA-6000 血凝仪解释器";

        /// <summary>
        /// 支持的协议类型
        /// </summary>
        public override ProtocolType SupportedProtocol => ProtocolType.自定义;

        /// <summary>
        /// 验证报文格式
        /// </summary>
        public override bool ValidateMessage(string rawMessage)
        {
            return !string.IsNullOrEmpty(rawMessage) && rawMessage.Length > 50 && rawMessage[0] == 'D';
        }

        /// <summary>
        /// 解析消息
        /// </summary>
        protected override async Task<List<MdlLabInstrResultQ>> ParseMessageAsync(string rawMessage, int instrumentId)
        {
            var results = new List<MdlLabInstrResultQ>();

            try
            {
                WriteLog($"解析消息: {rawMessage}");

                string barcode = ExtractField(rawMessage, 25, 13).Trim();
                WriteLog($"样本条码: {barcode}");

                // 解析检验项目数据块（从位置50开始，每9个字符一个项目）
                int dataStartPosition = 50;
                int remainingLength = rawMessage.Length - dataStartPosition;

                // 每个项目数据块: 3位项目代码 + 5位结果值 + 1位范围= 9字符
                for (int i = 0; i < remainingLength; i += 9)
                {
                    if (i + 9 <= remainingLength)
                    {
                        string dataBlock = rawMessage.Substring(dataStartPosition + i, 9).Trim();
                        if (!string.IsNullOrEmpty(dataBlock) && dataBlock.Length >= 9)
                        {
                            var result = ParseDataBlock(dataBlock, barcode, instrumentId);
                            if (result != null)
                            {
                                results.Add(result);
                            }
                        }
                    }
                }

                WriteLog($"成功解析 {results.Count} 个检验结果");
            }
            catch (Exception ex)
            {
                WriteLog($"解析异常: {ex.Message}");
                throw;
            }

            return results;
        }

        /// <summary>
        /// 解析单个数据块
        /// 示例: 0410128 -> 项目041, 值101.28
        /// </summary>
        private MdlLabInstrResultQ ParseDataBlock(string dataBlock, string barcode, int instrumentId)
        {
            try
            {
                // 解析参数代码（前3位）
                string parameterCode = dataBlock.Substring(0, 3);

                // 解析数据值（后5位）
                string dataValue = dataBlock.Substring(3, 5);

                string flag = dataBlock.Substring(8, 1);

                WriteLog($"项目: {parameterCode}, 值: {dataValue}");

                return CreateResult(instrumentId, barcode, parameterCode, dataValue, flag, 0);
            }
            catch (Exception ex)
            {
                WriteLog($"解析数据块异常: {ex.Message}, 数据块: {dataBlock}");
                return null;
            }
        }


        /// <summary>
        /// 提取字段
        /// </summary>
        private string ExtractField(string message, int startIndex, int length)
        {
            if (message == null || startIndex < 0 || startIndex + length > message.Length)
            {
                return string.Empty;
            }

            return message.Substring(startIndex, length);
        }
    }
}