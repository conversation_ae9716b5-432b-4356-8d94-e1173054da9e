﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Reflection;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public partial class MySingleComobo : UserControl
    {
        public MySingleComobo()
        {
            InitializeComponent();
            Init_TDBCombo();
        }

        #region 私有函数

        private void Init_TDBCombo()
        {
            c1Combo1.ClearItems();
            c1Combo1.AllowSort = true;
            c1Combo1.AutoDropDown = true;
            c1Combo1.AutoCompletion = true;
            c1Combo1.AutoSelect = true;
            c1Combo1.AutoSize = false;
            c1Combo1.ColumnHeaders = false;
            c1Combo1.SuperBack = true;
            c1Combo1.Width = 80;
            c1Combo1.DropDownWidth = 100;
            c1Combo1.ItemHeight = 24;
            c1Combo1.MaxDropDownItems = 10;
            c1Combo1.DataMode = C1.Win.C1List.DataModeEnum.AddItem;
            c1Combo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            c1Combo1.Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom;
            c1Combo1.VisualStyle = C1.Win.C1List.VisualStyle.Office2010Blue;
            c1Combo1.AddItemCols = 2;
            c1Combo1.AddItemSeparator = ',';
            c1Combo1.Columns[0].Caption = "value";
            c1Combo1.Columns[1].Caption = "key";

            c1Combo1.Splits[0].ExtendRightColumn = true;
            c1Combo1.Splits[0].ColumnCaptionHeight = 10;
            c1Combo1.Splits[0].DisplayColumns[0].Width = 30;
            c1Combo1.Splits[0].Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom;
            c1Combo1.Splits[0].SelectedStyle.BackColor = System.Drawing.Color.Teal;
            c1Combo1.Splits[0].SelectedStyle.ForeColor = System.Drawing.Color.Black;

            c1Combo1.Splits[0].Style.ForeColor = Color.Black;
            c1Combo1.Splits[0].Style.BackColor = Color.White;

            c1Combo1.Font = new Font("宋体", (float)10.5, FontStyle.Regular);

            c1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.Single;
            c1Combo1.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown;
            c1Combo1.ValueMember = "key";
            c1Combo1.DisplayMember = "value";
            c1Combo1.MatchCol = C1.Win.C1List.MatchColEnum.DisplayMember;
            c1Combo1.MatchEntry = C1.Win.C1List.MatchEntryEnum.Standard;

        }

        private void c1Combo1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (EnterToTab == true)
            {
                if (e.KeyChar != (char)(Keys.Return)) return;
                e.Handled = true;
                System.Windows.Forms.SendKeys.Send("{Tab}");
            }
        }

        private void MySingleComobo_Resize(object sender, EventArgs e)
        {
            this.DroupDownWidth = this.Width - (int)this.CaptainWidth;
        }


        /// <summary>
        /// 枚举类型转化为DataTable
        /// </summary>
        /// <param name="enumType">枚举类型</param>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <returns></returns>
        private DataTable EnumToDataTable(Type enumType, string key = "key", string val = "value")
        {
            string[] Names = System.Enum.GetNames(enumType);

            Array Values = System.Enum.GetValues(enumType);

            DataTable table = new DataTable();
            table.Columns.Add(key, System.Type.GetType("System.Int32"));
            table.Columns.Add(val, System.Type.GetType("System.String"));
            table.Columns[key].Unique = true;
            for (int i = 0; i < Values.Length; i++)
            {
                DataRow DR = table.NewRow();
                DR[key] = (int)Values.GetValue(i);
                DR[val] = GetDescription(enumType, Names[i].ToString());
                table.Rows.Add(DR);
            }
            return table;
        }

        private string GetDescription(Type type, string en)
        {
            MemberInfo[] memberInfos = type.GetMember(en);   //获取成员  
            if (memberInfos != null && memberInfos.Length > 0)
            {
                DescriptionAttribute[] attrs = memberInfos[0].GetCustomAttributes(typeof(DescriptionAttribute), false) as DescriptionAttribute[];   //获取描述特性  
                if (attrs != null && attrs.Length > 0)
                {
                    return attrs[0].Description;    //返回当前描述
                }
            }
            return en.ToString();
        }

        #endregion

        #region 公开属性
        public C1.Win.C1List.ComboStyleEnum ComboStyle
        {
            get { return c1Combo1.ComboStyle; }
            set
            {
                c1Combo1.ComboStyle = ComboStyle;
                Invalidate();
            }
        }
        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }


        float _CaptainWidth = 70;
        [Description("标题宽度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public float CaptainWidth
        {
            get
            {
                _CaptainWidth = label1.Width + 6;
                return _CaptainWidth;
            }
            set
            {
                //_CaptainWidth = value;
                //tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0 || label1.Text == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }


        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1List.VisualStyle VisualStyle
        {
            get { return c1Combo1.VisualStyle; }
            set
            {
                c1Combo1.VisualStyle = value;
                Invalidate();
            }
        }

        [Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object SelectedValue
        {
            get { return this.c1Combo1.SelectedValue; }
            set
            {
                this.c1Combo1.SelectedValue = value;
                Invalidate();
            }
        }

        public C1.Win.C1List.C1DataColumnCollection Columns
        {
            get { return this.c1Combo1.Columns; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int SelectedIndex
        {
            get { return this.c1Combo1.SelectedIndex; }
            set
            {
                this.c1Combo1.SelectedIndex = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public override string Text
        {
            get { return this.c1Combo1.Text; }
            set
            {
                this.c1Combo1.Text = value;
                Invalidate();
            }
        }



        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string SelectedText
        {
            get { return this.c1Combo1.SelectedText; }
            set
            {
                c1Combo1.SelectedText = value;
                Invalidate();
            }
        }


        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int DroupDownWidth
        {
            set { c1Combo1.DropDownWidth = value; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string Additem
        {
            set { c1Combo1.AddItem(value); }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1List.C1DisplayColumnCollection DisplayColumns
        {
            get { return this.c1Combo1.Splits[0].DisplayColumns; }
        }

        [Description("字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                c1Combo1.Font = value;
                c1Combo1.EditorFont = value;
                Invalidate();
            }
        }

        [Description("标题字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }

        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }

        [Description("下拉文本字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font ItemTextFont
        {
            get { return c1Combo1.Font; }
            set
            {
                c1Combo1.Font = value;
                Invalidate();
            }
        }

        [Description("文本框文本字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font TextFont
        {
            get { return c1Combo1.EditorFont; }
            set
            {
                c1Combo1.EditorFont = value;
                Invalidate();
            }
        }

        [Description("控件是否启用"), Browsable(true)]
        public new bool Enabled
        {
            get { return c1Combo1.Enabled; }
            set
            {
                c1Combo1.Enabled = value;
                if (value == true)
                {
                    c1Combo1.TabStop = true;
                    this.TabStop = true;
                    c1Combo1.VisualStyle = C1.Win.C1List.VisualStyle.Office2010Blue;
                    c1Combo1.EditorBackColor = SystemColors.Window;
                }
                else
                {
                    c1Combo1.TabStop = false;
                    this.TabStop = false;
                    c1Combo1.EditorBackColor = SystemColors.Info;
                    c1Combo1.ForeColor = Color.Black;


                    label1.ForeColor = Color.Black;
                }

                Invalidate();
            }
        }

        private bool _entertotab = true;
        [Description("回车键走Tab键"), Browsable(true), DefaultValue(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public bool EnterToTab
        {
            get { return _entertotab; }
            set
            {
                _entertotab = value;
            }
        }

        [Description("下拉高度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public int ItemHeight
        {
            get { return c1Combo1.ItemHeight; }
            set
            {
                c1Combo1.ItemHeight = value;
                Invalidate();
            }
        }

        public bool ReadOnly
        {
            get { return c1Combo1.ReadOnly; }
            set
            {
                if (value == true)
                {
                    c1Combo1.BackColor = Color.MistyRose;
                    c1Combo1.ForeColor = Color.Black;
                }
                else
                {
                    c1Combo1.VisualStyle = C1.Win.C1List.VisualStyle.Office2010Blue;

                }
                c1Combo1.ReadOnly = value;
                Invalidate();
            }
        }

        [Description("数据源")]
        public Type DataSource
        {
            set
            {
                DataTable dt = EnumToDataTable(value);
                foreach (DataRow row in dt.Rows)
                {
                    this.Additem = row[1] + "," + row[0];

                }
            }
        }
        [Description("数据源")]
        public DataTable DataTable
        {
            set
            {
                foreach (DataRow row in value.Rows)
                {
                    this.Additem = row[1] + "," + row[0];

                }
            }
        }
        #endregion

        #region 公开方法
        public new void Select()
        {
            c1Combo1.Select();
        }

        public void ClearItems()
        {
            this.c1Combo1.ClearItems();

        }

        public void InsertItem(string newItem, int index)
        {
            this.c1Combo1.InsertItem(newItem, index);

        }

        #endregion

        #region 公开事件

        [Description("当SelectedValue属性发生变化时触发"), Browsable(true)]
        public event EventHandler SelectedValueChanged
        {
            add
            {
                c1Combo1.SelectedValueChanged += value;
            }
            remove
            {
                c1Combo1.SelectedValueChanged -= value;
            }
        }

        [Description("当用户改变列表中的一行时触发")]
        public event EventHandler RowChange
        {
            add
            {
                c1Combo1.RowChange += value;
            }
            remove
            {
                c1Combo1.RowChange -= value;
            }
        }

        public new event KeyPressEventHandler KeyPress
        {
            add
            {
                c1Combo1.KeyPress += value;
            }
            remove
            {
                c1Combo1.KeyPress -= value;
            }
        }

        public event C1.Win.C1List.CloseEventHandler Close
        {
            add
            {
                c1Combo1.Close += value;
            }
            remove
            {
                c1Combo1.Close -= value;
            }
        }

        #endregion



    }
}
