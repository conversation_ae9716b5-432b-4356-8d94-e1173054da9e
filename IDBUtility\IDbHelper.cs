﻿using System.Collections;
using System.Collections.Generic;
using System.Data;

namespace IDBUtility
{
    public interface IDbHelper
    {
        string ConnectionString { set; get; }
        int TimeOut { set; }
        #region 公用方法

        bool TestConn();

        int GetMaxID(string FieldName, string TableName);

        string  F_MaxCode(string V_数据条件, int V_编码长度);


        bool Exists(string strSql);


        bool Exists(string strSql, params IDataParameter[] cmdParms);

        #endregion

        #region 执行简单语句

        /// <summary>
        ///     执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数(Update,Delete,Insert返回影响行数，Select返回-1)</returns>
        /// <remarks></remarks>
        int ExecuteSql(string SQLString);


        /// <summary>
        ///     执行带一个存储过程参数的的SQL语句。
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <param name="content">参数内容,比如一个字段是格式复杂的文章，有特殊符号，可以通过这个方式添加</param>
        /// <returns>影响的记录数</returns>
        int ExecuteSql(string SQLString, string content);


        /// <summary>
        ///     Execute a SqlCommand (that returns no resultset) using an existing SQL Transaction
        ///     using the provided parameters.
        /// </summary>
        /// <remarks>
        ///     e.g.:
        ///     int result = ExecuteNonQuery(connString, CommandType.StoredProcedure, "PublishOrders", new
        ///     IDataParameter("@prodid", 24));
        /// </remarks>
        /// <param name="trans">an existing sql transaction</param>
        /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
        /// <param name="commandText">the stored procedure name or T-SQL command</param>
        /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
        /// <returns>an int representing the number of rows affected by the command</returns>
        int ExecuteSql(List<string> cmdText, List<IDataParameter[]> cmdParms);

        int ExecuteSql(string cmdText, List<IDataParameter[]> cmdParms);
        /// <summary>
        ///     执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>
        int ExecuteSqlTran(ArrayList SQLStringList);


        /// <summary>
        ///     执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        object GetSingle(string SQLString);


        /// <summary>
        ///     执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        object GetSingle(string SQLString, params IDataParameter[] cmdParms);


        /// <summary>
        ///     执行查询语句，返回IDataReader
        /// </summary>
        /// <param name="strSQL">查询语句</param>
        /// <returns>IDataReader</returns>
        IDataReader ExecuteReader(string strSQL);


        /// <summary>
        ///     执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        DataSet Query(string SQLString);


        /// <summary>
        ///     执行查询语句，返回DataSet,去掉约束6
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        DataSet Query(string SQLString, bool Constraints);


        /// <summary>
        ///     执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        DataSet Query(string SQLString, params IDataParameter[] cmdParms);

        /// <summary>
        ///     给Dataset填充datatable
        /// </summary>
        /// <param name="SQLds">Dataset</param>
        /// <param name="SQLString">查询语句</param>
        /// <param name="SQLDtName">表名</param>
        /// <param name="dtClear">是否清空原有表</param>
        /// <remarks></remarks>
        void QueryDt(DataSet SQLds, string SQLString, string SQLDtName, bool dtClear);

        /// <summary>
        ///     给Dataset填充datatable
        /// </summary>
        /// <param name="SQLds">Dataset</param>
        /// <param name="SQLString">查询语句</param>
        /// <param name="SQLDtName">表名</param>
        /// <param name="dtClear">是否清空原有表</param>
        /// <param name="Constraints">是否强制约束</param>
        /// <remarks></remarks>
        void QueryDt(DataSet SQLds, string SQLString, string SQLDtName, bool dtClear, bool Constraints);

        /// <summary>
        ///     执行sql语句,返回sql的Print的内容
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>返回Print 内容</returns>
        /// <remarks></remarks>
        string ExecuteSqlPrint(string SQLString);

        #endregion

        #region 执行带参数的SQL语句
        /// <summary>
        ///     执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数</returns>
        int ExecuteSql(string SQLString, params IDataParameter[] cmdParms);

        #endregion

        #region 存储过程操作

        /// <summary>
        ///     执行存储过程，返回DataReader
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>IDataReader</returns>
        IDataReader RunProcedureDr(string storedProcName, IDataParameter[] parameters);


        /// <summary>
        ///     执行存储过程，带参数，无返回值
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        void RunProcedure(string storedProcName, IDataParameter[] parameters);


        /// <summary>
        ///     执行存储过程，返回DataSet
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="tableName">DataSet结果中的表名</param>
        /// <returns>DataSet</returns>
        DataSet RunProcedure(string storedProcName, IDataParameter[] parameters, string tableName);


        /// <summary>
        ///     执行存储过程，无参数
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        void RunProcedure(string storedProcName);


        /// <summary>
        ///     执行存储过程，无参数，返回DataSet
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="tableName">数据表名</param>
        DataSet RunProcedure(string storedProcName, string tableName);

        /// <summary>
        ///     执行存储过程，返回影响的行数
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="rowsAffected">影响的行数</param>
        /// <returns></returns>
        int RunProcedure(string storedProcName, IDataParameter[] parameters, ref int rowsAffected);

        #endregion
    }
}