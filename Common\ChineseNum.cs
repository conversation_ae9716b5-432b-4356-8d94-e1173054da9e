﻿/*********************************************************************************** 
*        文 件 名 :ChineseNum 
*        功能描述 :金额大小写转换类  
*        生成日期 :2017/3/1 14:41:06 
*        作   者 :董铮
*        机 器 名 :2013-20160817SN
*        All rights reserved 
************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common
{
    public class ChineseNum
    {
        //输入字串
        private string _InputString;
        //输出字串,如果无效则输出错误信息
        private string _OutString;
        //判断输出字串是否有效

        private bool _Valiad;

        public string InputString
        {
            set
            {
                _InputString = value;
                ConvertToChineseNum();
            }
        }

        public bool Valiad
        {
            get { return _Valiad; }
        }

        public string OutString
        {
            get { return _OutString; }
        }

        private void ConvertToChineseNum()
        {
            string numList = "零壹贰叁肆伍陆柒捌玖";
            string rmbList = "分角元拾佰仟万拾佰仟亿拾佰仟万";
            double number = 0;
            string tempOutString = "";
            try
            {
                number = double.Parse(this._InputString);
            }
            catch 
            {
                this._OutString = "传入参数非数字！";
                this._Valiad = false;
                return;
            }

            if (number > 9999999999999.99)
            {
                this._Valiad = false;
                this._OutString = "超出范围的人民币值";
                return;
            }

            string tempNumberString = Convert.ToInt64(number * 100).ToString();
            int tempNmberLength = tempNumberString.Length;
            int i = 0;

            while (i < tempNmberLength)
            {
                int oneNumber = Int32.Parse(tempNumberString.Substring(i, 1));
                string oneNumberChar = numList.Substring(oneNumber, 1);
                string oneNumberUnit = rmbList.Substring(tempNmberLength - i - 1, 1);
                if (!(oneNumberChar == "零"))
                {
                    tempOutString += oneNumberChar + oneNumberUnit;

                }
                else
                {
                    if (oneNumberUnit == "亿" || oneNumberUnit == "万" || oneNumberUnit == "元" || oneNumberUnit == "零")
                    {
                        while (tempOutString.EndsWith("零"))
                        {
                            tempOutString = tempOutString.Substring(0, tempOutString.Length - 1);
                        }
                    }
                    if (oneNumberUnit == "亿" || (oneNumberUnit == "万" && !tempOutString.EndsWith("亿")) || oneNumberUnit == "元")
                    {
                        tempOutString += oneNumberUnit;
                    }
                    else
                    {
                        bool tempEnd = tempOutString.EndsWith("亿");
                        bool zeroEnd = tempOutString.EndsWith("零");
                        if (tempOutString.Length > 1)
                        {
                            bool zeroStart = tempOutString.Substring(tempOutString.Length - 2, 2).StartsWith("零");
                            if (!zeroEnd && (zeroStart || !tempEnd))
                            {
                                tempOutString += oneNumberChar;
                            }
                        }
                        else
                        {
                            if (!zeroEnd && !tempEnd)
                            {
                                tempOutString += oneNumberChar;
                            }
                        }
                    }
                }
                i += 1;
            }
            while (tempOutString.EndsWith("零"))
            {
                tempOutString = tempOutString.Substring(0, tempOutString.Length - 1);
            }
            while (tempOutString.EndsWith("元"))
            {
                tempOutString = tempOutString + "整";
            }
            this._OutString = tempOutString;
            this._Valiad = true;
        }
    }
}
