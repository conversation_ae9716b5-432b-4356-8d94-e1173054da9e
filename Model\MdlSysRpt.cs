﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysRpt.cs
*
* 功 能： N/A
* 类 名： MdlSysRpt
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-20 11:31:02   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 系统报表
	/// </summary>
	[Serializable]
	public partial class MdlSysRpt
	{
		public MdlSysRpt()
		{}
		#region Model
		private string _rpt_class_code;
		private string _rptcode;
		private string _rptname;
		private string _rptpath;
		private byte[] _rptcontent;
		private DateTime? _editdtime;
		private string _editor;
		/// <summary>
		/// 类别编码
		/// </summary>
		public string Rpt_Class_Code
		{
			set{ _rpt_class_code=value;}
			get{return _rpt_class_code;}
		}
		/// <summary>
		/// 编码
		/// </summary>
		public string RptCode
		{
			set{ _rptcode=value;}
			get{return _rptcode;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string RptName
		{
			set{ _rptname=value;}
			get{return _rptname;}
		}
		/// <summary>
		/// 路径
		/// </summary>
		public string RptPath
		{
			set{ _rptpath=value;}
			get{return _rptpath;}
		}
		/// <summary>
		/// 内容
		/// </summary>
		public byte[] RptContent
		{
			set{ _rptcontent=value;}
			get{return _rptcontent;}
		}
		/// <summary>
		/// 编辑时间
		/// </summary>
		public DateTime? EditDTime
		{
			set{ _editdtime=value;}
			get{return _editdtime;}
		}
		/// <summary>
		/// 编辑人
		/// </summary>
		public string Editor
		{
			set{ _editor=value;}
			get{return _editor;}
		}
		#endregion Model

	}
}

