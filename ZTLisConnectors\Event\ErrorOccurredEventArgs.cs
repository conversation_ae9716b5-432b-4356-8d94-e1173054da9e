﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器发生错误的事件参数
    /// </summary>
    public class ErrorOccurredEventArgs : EventArgs
    {
        /// <summary>
        /// 仪器ID
        /// </summary>
        public int InstrumentId { get; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; }

        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime OccurredTime { get; }

        /// <summary>
        /// 相关异常对象（如果有）
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ErrorOccurredEventArgs(int instrumentId, int errorCode, string errorMessage, Exception exception = null)
        {
            InstrumentId = instrumentId;
            ErrorCode = errorCode;
            ErrorMessage = errorMessage;
            OccurredTime = DateTime.Now;
            Exception = exception;
        }
    }

}
