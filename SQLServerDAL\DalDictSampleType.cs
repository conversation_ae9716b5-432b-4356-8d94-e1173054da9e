﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictSampleType.cs
*
* 功 能： N/A
* 类 名： DalDictSampleType
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/5/23 15:35:45   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictSampleType
	/// </summary>
	public partial class DalDictSampleType : IDalDictSampleType
	{
		public DalDictSampleType()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("SampleTypeId", "DictSampleType");
		}
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(SampleTypeCode) FROM DictSampleType where SampleTypeCode NOT LIKE '%[^0-9]%' And LEN(SampleTypeCode)=" + length, length));
			return max;
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int SampleTypeId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictSampleType");
			strSql.Append(" where SampleTypeId=@SampleTypeId");
			SqlParameter[] parameters = {
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4)
			};
			parameters[0].Value = SampleTypeId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictSampleType model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictSampleType(");
			strSql.Append("SampleTypeCode,SampleTypeName,PinYin,EnglishName,ColorHex,ContainerType,DefaultVolumeMl,PreTreatment,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@SampleTypeCode,@SampleTypeName,@PinYin,@EnglishName,@ColorHex,@ContainerType,@DefaultVolumeMl,@PreTreatment,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@SampleTypeCode", SqlDbType.NVarChar,20),
					new SqlParameter("@SampleTypeName", SqlDbType.NVarChar,100),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@EnglishName", SqlDbType.NVarChar,100),
					new SqlParameter("@ColorHex", SqlDbType.Char,6),
					new SqlParameter("@ContainerType", SqlDbType.NVarChar,50),
					new SqlParameter("@DefaultVolumeMl", SqlDbType.Decimal,5),
					new SqlParameter("@PreTreatment", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.SampleTypeCode;
			parameters[1].Value = model.SampleTypeName;
			parameters[2].Value = model.PinYin;
			parameters[3].Value = Common.Tools.IsValueNull(model.EnglishName);
			parameters[4].Value = Common.Tools.IsValueNull(model.ColorHex);
			parameters[5].Value = Common.Tools.IsValueNull(model.ContainerType);
			parameters[6].Value = Common.Tools.IsValueNull(model.DefaultVolumeMl);
			parameters[7].Value = Common.Tools.IsValueNull(model.PreTreatment);
			parameters[8].Value = model.Status;
			parameters[9].Value = model.CreatedAt;
			parameters[10].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictSampleType model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictSampleType set ");
			strSql.Append("SampleTypeCode=@SampleTypeCode,");
			strSql.Append("SampleTypeName=@SampleTypeName,");
			strSql.Append("PinYin=@PinYin,");
			strSql.Append("EnglishName=@EnglishName,");
			strSql.Append("ColorHex=@ColorHex,");
			strSql.Append("ContainerType=@ContainerType,");
			strSql.Append("DefaultVolumeMl=@DefaultVolumeMl,");
			strSql.Append("PreTreatment=@PreTreatment,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where SampleTypeId=@SampleTypeId");
			SqlParameter[] parameters = {
					new SqlParameter("@SampleTypeCode", SqlDbType.NVarChar,20),
					new SqlParameter("@SampleTypeName", SqlDbType.NVarChar,100),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@EnglishName", SqlDbType.NVarChar,100),
					new SqlParameter("@ColorHex", SqlDbType.Char,6),
					new SqlParameter("@ContainerType", SqlDbType.NVarChar,50),
					new SqlParameter("@DefaultVolumeMl", SqlDbType.Decimal,5),
					new SqlParameter("@PreTreatment", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4)};
			parameters[0].Value = model.SampleTypeCode;
			parameters[1].Value = model.SampleTypeName;
			parameters[2].Value = model.PinYin;
			parameters[3].Value = Common.Tools.IsValueNull(model.EnglishName);
			parameters[4].Value = Common.Tools.IsValueNull(model.ColorHex);
			parameters[5].Value = Common.Tools.IsValueNull(model.ContainerType);
			parameters[6].Value = Common.Tools.IsValueNull(model.DefaultVolumeMl);
			parameters[7].Value = Common.Tools.IsValueNull(model.PreTreatment);
			parameters[8].Value = model.Status;
			parameters[9].Value = model.CreatedAt;
			parameters[10].Value = model.UpdatedAt;
			parameters[11].Value = model.SampleTypeId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int SampleTypeId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictSampleType ");
			strSql.Append(" where SampleTypeId=@SampleTypeId");
			SqlParameter[] parameters = {
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4)
			};
			parameters[0].Value = SampleTypeId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string SampleTypeIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictSampleType ");
			strSql.Append(" where SampleTypeId in (" + SampleTypeIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictSampleType GetModel(int SampleTypeId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 SampleTypeId,SampleTypeCode,SampleTypeName,PinYin,EnglishName,ColorHex,ContainerType,DefaultVolumeMl,PreTreatment,Status,CreatedAt,UpdatedAt from DictSampleType ");
			strSql.Append(" where SampleTypeId=@SampleTypeId");
			SqlParameter[] parameters = {
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4)
			};
			parameters[0].Value = SampleTypeId;

			Model.MdlDictSampleType model = new Model.MdlDictSampleType();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictSampleType DataRowToModel(DataRow row)
		{
			Model.MdlDictSampleType model = new Model.MdlDictSampleType();
			if (row != null)
			{
				if (row["SampleTypeId"] != null && row["SampleTypeId"].ToString() != "")
				{
					model.SampleTypeId = int.Parse(row["SampleTypeId"].ToString());
				}
				if (row["SampleTypeCode"] != null)
				{
					model.SampleTypeCode = row["SampleTypeCode"].ToString();
				}
				if (row["SampleTypeName"] != null)
				{
					model.SampleTypeName = row["SampleTypeName"].ToString();
				}
				if (row["PinYin"] != null)
				{
					model.PinYin = row["PinYin"].ToString();
				}
				if (row["EnglishName"] != null)
				{
					model.EnglishName = row["EnglishName"].ToString();
				}
				if (row["ColorHex"] != null)
				{
					model.ColorHex = row["ColorHex"].ToString();
				}
				if (row["ContainerType"] != null)
				{
					model.ContainerType = row["ContainerType"].ToString();
				}
				if (row["DefaultVolumeMl"] != null && row["DefaultVolumeMl"].ToString() != "")
				{
					model.DefaultVolumeMl = decimal.Parse(row["DefaultVolumeMl"].ToString());
				}
				if (row["PreTreatment"] != null)
				{
					model.PreTreatment = row["PreTreatment"].ToString();
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select SampleTypeId,SampleTypeCode,SampleTypeName,PinYin,EnglishName,ColorHex,ContainerType,DefaultVolumeMl,PreTreatment,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictSampleType ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" SampleTypeId,SampleTypeCode,SampleTypeName,PinYin,EnglishName,ColorHex,ContainerType,DefaultVolumeMl,PreTreatment,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictSampleType ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictSampleType ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.SampleTypeId desc");
			}
			strSql.Append(")AS Row, T.*  from DictSampleType T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictSampleType";
			parameters[1].Value = "SampleTypeId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

