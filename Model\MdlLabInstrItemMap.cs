﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabInstrItemMap.cs
*
* 功 能： N/A
* 类 名： MdlLabInstrItemMap
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 仪器项目映射表：连接仪器项目与 LIS 统一项目字典
	/// </summary>
	[Serializable]
	public partial class MdlLabInstrItemMap
	{
		public MdlLabInstrItemMap()
		{ }
		#region Model
		private long _mapid;
		private int _instrumentid;
		private string _instritemcode;
		private string _instritemname;
		private int _labitemid;
		private string _lisitemname;
		private string _resultformat;
		private int? _decimals;
		private DateTime _createdat;
		private DateTime _updatedat;
		/// <summary>
		/// 主键 ID（IDENTITY）
		/// </summary>
		public long MapId
		{
			set { _mapid = value; }
			get { return _mapid; }
		}
		/// <summary>
		/// 仪器 ID（LabInstrument.InstrumentId）
		/// </summary>
		public int InstrumentId
		{
			set { _instrumentid = value; }
			get { return _instrumentid; }
		}
		/// <summary>
		/// 仪器项目代码（Raw 报文中的项目标识）
		/// </summary>
		public string InstrItemCode
		{
			set { _instritemcode = value; }
			get { return _instritemcode; }
		}
		/// <summary>
		/// 仪器项目名称
		/// </summary>
		public string InstrItemName
		{
			set { _instritemname = value; }
			get { return _instritemname; }
		}
		/// <summary>
		/// LIS 统一项目ID（内部字典）
		/// </summary>
		public int LabItemId
		{
			set { _labitemid = value; }
			get { return _labitemid; }
		}
		/// <summary>
		/// LIS项目名称（冗余存储，优化UI查询）
		/// </summary>
		public string LisItemName
		{
			set { _lisitemname = value; }
			get { return _lisitemname; }
		}
		/// <summary>
		/// 结果格式：NUM=数值 TXT=文本 NEG=阴阳性
		/// </summary>
		public string ResultFormat
		{
			set { _resultformat = value; }
			get { return _resultformat; }
		}
		/// <summary>
		/// 数值结果保留的小数位数（仅 NUM 时有效）
		/// </summary>
		public int? Decimals
		{
			set { _decimals = value; }
			get { return _decimals; }
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

