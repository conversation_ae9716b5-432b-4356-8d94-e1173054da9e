using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    /// <summary>
    /// 检验仪器控件
    /// </summary>
    public partial class ComboLabInstrument : MyDtComobo
    {
        public ComboLabInstrument()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            strWhere += " and Status=1";
            BLL.BllLabInstrument _bllLabInstrument = new BllLabInstrument();
            this.DataView = _bllLabInstrument.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("InstrName", "仪器名称", 160, "左");
            this.Init_Colum("InstrCode", "仪器编码", 80, "左");
            this.Init_Colum("Vendor", "厂商", 0, "左");
            this.Init_Colum("Model", "型号", 0, "左");
            this.DisplayMember = "InstrName";
            this.ValueMember = "InstrumentId";
            int width = 260;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "InstrCode+InstrName+isnull(Vendor,'')+isnull(Model,'')";
        }
    }
}