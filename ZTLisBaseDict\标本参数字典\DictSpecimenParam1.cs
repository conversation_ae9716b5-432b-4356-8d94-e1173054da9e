using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictSpecimenParam1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictSpecimenParam _bllDictSpecimenParam = new BllDictSpecimenParam();

        public DictSpecimenParam1()
        {
            InitializeComponent();
        }

        private void DictSpecimenParam1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("参数编码", "ParamCode", 120, "中", "", false);
            myGrid1.Init_Column("参数名称", "ParamName", 160, "左", "", false);
            myGrid1.Init_Column("简称", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("英文名称", "EnglishName", 150, "左", "", false);
            myGrid1.Init_Column("参数类型", "ParamTypeName", 80, "中", "", false);
            myGrid1.Init_Column("单位", "Unit", 80, "中", "", false);
            myGrid1.Init_Column("描述", "Description", 300, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllDictSpecimenParam.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["ParamId"] };
            base.MyTable.Columns["ParamId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictSpecimenParam2 vform = new DictSpecimenParam2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除标本参数：" + this.myGrid1.Columns["ParamName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictSpecimenParam.Delete(int.Parse(base.MyRow["ParamId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("ParamCode", new Common.MdlExcel(10, "", "参数编码", true));
            colconfig.Add("ParamName", new Common.MdlExcel(15, "", "参数名称", true));
            colconfig.Add("EnglishName", new Common.MdlExcel(15, "", "英文名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "简称", true));
            colconfig.Add("ParamType", new Common.MdlExcel(8, "", "参数类型", true));
            colconfig.Add("Unit", new Common.MdlExcel(8, "", "单位", true));
            colconfig.Add("Description", new Common.MdlExcel(20, "", "参数描述", true));
            Common.ExcelHelper.Export(base.MyTable, "标本参数字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictSpecimenParam mdlDictSpecimenParam = new MdlDictSpecimenParam();
                    if (row["参数名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "参数名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["参数编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "参数编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictSpecimenParam.ParamCode = row["参数编码"] + "";
                    mdlDictSpecimenParam.ParamName = row["参数名称"] + "";
                    mdlDictSpecimenParam.PinYin = base.MyChs2Spell.GetPy(row["参数名称"].ToString());
                    mdlDictSpecimenParam.EnglishName = row["英文名称"] + "";
                    if (row["参数类型"] != null && row["参数类型"].ToString().Trim() != "")
                    {
                        int paramType;
                        if (int.TryParse(row["参数类型"].ToString(), out paramType))
                        {
                            mdlDictSpecimenParam.ParamType = paramType;
                        }
                    }
                    mdlDictSpecimenParam.Unit = row["单位"] + "";
                    mdlDictSpecimenParam.Description = row["参数描述"] + "";
                    mdlDictSpecimenParam.Status = true;
                    mdlDictSpecimenParam.CreatedAt = DateTime.Now;
                    mdlDictSpecimenParam.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictSpecimenParam).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictSpecimenParam, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["ParamId"] = _bllDictSpecimenParam.Add(mdlDictSpecimenParam);
                    base.MyTable.Rows.Add(newRow);
                    base.MyTable.AcceptChanges();

                    UpdateData[i - 1] = "";
                    i++;
                }
                Common.ExcelHelper.UpdateExcel(fileName, 0, UpdateData, tableColCnt, 2);
                MessageBox.Show("成功导入【" + successCnt + "】条记录!失败【" + failCnt + "】条\r\n如存在未导入数据，请查看导入Excel文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
            }
        }

        private void DataEnable(bool Status)
        {
            if (myGrid1.RowCount == 0) return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            Model.MdlDictSpecimenParam model1 = new Model.MdlDictSpecimenParam();
            model1 = _bllDictSpecimenParam.GetModel(int.Parse(base.MyRow["ParamId"].ToString()));
            model1.Status = Status;
            base.MyRow["Status"] = Status;
            base.MyRow.AcceptChanges();
            _bllDictSpecimenParam.Update(model1);
        }
        #endregion

        #region 控件动作
        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("ParamCode+ParamName+PinYin+EnglishName", TxtFilter.Text.Trim());
        }
        #endregion
    }
}