using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ZTLisControl
{
    public partial class SingleBaudRate : CustomControl.MySingleComobo
    {
        public SingleBaudRate()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        public void Init()
        {
            Additem = "1200,1200";
            Additem = "2400,2400";
            Additem = "4800,4800";
            Additem = "9600,9600";
            Additem = "14400,14400";
            Additem = "19200,19200";
            Additem = "38400,38400";
            Additem = "57600,57600";
            Additem = "115200,115200";
            DisplayColumns[1].Visible = false;
            DroupDownWidth = Width - (int)CaptainWidth;
            SelectedIndex = 3;
            ItemHeight = 20;
        }
    }
}