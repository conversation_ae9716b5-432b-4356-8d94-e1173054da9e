﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysMenu1.cs
*
* 功 能： N/A
* 类 名： DalSysMenu1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:33   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysMenu1
	/// </summary>
	public partial class DalSysMenu1:IDalSysMenu1
	{
		public DalSysMenu1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string FrsCode)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysMenu1");
			strSql.Append(" where FrsCode=@FrsCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@FrsCode", SqlDbType.Char,2)			};
			parameters[0].Value = FrsCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysMenu1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysMenu1(");
			strSql.Append("FrsCode,FrsName,SortNo)");
			strSql.Append(" values (");
			strSql.Append("@FrsCode,@FrsName,@SortNo)");
			SqlParameter[] parameters = {
					new SqlParameter("@FrsCode", SqlDbType.Char,2),
					new SqlParameter("@FrsName", SqlDbType.VarChar,50),
					new SqlParameter("@SortNo", SqlDbType.Int,4)};
			parameters[0].Value = model.FrsCode;
			parameters[1].Value = model.FrsName;
			parameters[2].Value = model.SortNo;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysMenu1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysMenu1 set ");
			strSql.Append("FrsName=@FrsName,");
			strSql.Append("SortNo=@SortNo");
			strSql.Append(" where FrsCode=@FrsCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@FrsName", SqlDbType.VarChar,50),
					new SqlParameter("@SortNo", SqlDbType.Int,4),
					new SqlParameter("@FrsCode", SqlDbType.Char,2)};
			parameters[0].Value = model.FrsName;
			parameters[1].Value = model.SortNo;
			parameters[2].Value = model.FrsCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string FrsCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysMenu1 ");
			strSql.Append(" where FrsCode=@FrsCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@FrsCode", SqlDbType.Char,2)			};
			parameters[0].Value = FrsCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string FrsCodelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysMenu1 ");
			strSql.Append(" where FrsCode in ("+FrsCodelist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysMenu1 GetModel(string FrsCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 FrsCode,FrsName,SortNo from SysMenu1 ");
			strSql.Append(" where FrsCode=@FrsCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@FrsCode", SqlDbType.Char,2)			};
			parameters[0].Value = FrsCode;

			Model.MdlSysMenu1 model=new Model.MdlSysMenu1();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysMenu1 DataRowToModel(DataRow row)
		{
			Model.MdlSysMenu1 model=new Model.MdlSysMenu1();
			if (row != null)
			{
				if(row["FrsCode"]!=null)
				{
					model.FrsCode=row["FrsCode"].ToString();
				}
				if(row["FrsName"]!=null)
				{
					model.FrsName=row["FrsName"].ToString();
				}
				if(row["SortNo"]!=null && row["SortNo"].ToString()!="")
				{
					model.SortNo=int.Parse(row["SortNo"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select FrsCode,FrsName,SortNo ");
			strSql.Append(" FROM SysMenu1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" FrsCode,FrsName,SortNo ");
			strSql.Append(" FROM SysMenu1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysMenu1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.FrsCode desc");
			}
			strSql.Append(")AS Row, T.*  from SysMenu1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysMenu1";
			parameters[1].Value = "FrsCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod
	    /// <summary>
	    /// 根据角色编码获取一级菜单
	    /// </summary>
	    /// <param name="roleCode"></param>
	    /// <returns></returns>
	    public DataSet GetMenu1(string roleCode)
	    {
	        StringBuilder strSql = new StringBuilder();
	        strSql.Append("select DISTINCT SysMenu1.* ");
	        strSql.Append("FROM   SysMenu1 , ");
	        strSql.Append("       SysMenu2 , ");
	        strSql.Append("       SysModule , ");
	        strSql.Append("       SysRoleModule ");
	        strSql.Append("WHERE  SysMenu1.FrsCode = SysMenu2.FrsCode ");
	        strSql.Append("       AND SysMenu2.ScndCode = SysModule.ScndCode ");
	        strSql.Append("       AND SysModule.ModuleCode = SysRoleModule.ModuleCode ");
	        strSql.Append("       AND RoleCode='" + roleCode + "' order by SysMenu1.SortNo");
	        return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
	    }
        #endregion  ExtensionMethod
    }
}

