﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FD356F66-2186-4B9D-B45B-E8E7B6040A8A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ZTLisResources</RootNamespace>
    <AssemblyName>ZTLisResources</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="C_Resources.cs" />
    <Compile Include="GridColImg.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GridColImg.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resource1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource1.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="GridColImg.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>GridColImg.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resource1.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\字典管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\标本类型.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\基础字典.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退出系统.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\检验方法.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\检验项目.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\项目字典.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\方法类别.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\项目类别.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\停用16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\启用16.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\标本状态.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\就诊类型.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\仪器字典.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\仪器信息.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\科室信息.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\医生信息.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\标本参数.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\项目单位.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\仪器项目映射.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\仪器标本参数映射.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\检验组合.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\检验申请.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\检验管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\用户管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\角色管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\用户设置.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\系统管理.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\锁定16.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>