using System;
using System.Diagnostics;
using System.Threading;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using ZTLisEnum;
using Parity = System.IO.Ports.Parity;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器基类，提供所有连接器的公共功能和属性
    /// </summary>
    public abstract class BaseConnector : IConnector
    {
        #region 受保护字段

        protected LabInstrument _config;
        protected Thread _heartbeatThread;
        protected volatile bool _isRunning;
        protected volatile bool _disposed;
        protected readonly object _lockObject = new object();
        protected ConnectorStatistics _statistics;
        protected ConnectorStatus _status;

        // 消息格式处理相关字段
        protected byte[] _msgHeaderBytes;
        protected byte[] _msgTailBytes;
        protected byte[] _ackSuccessBytes;
        protected byte[] _ackFailureBytes;
        protected bool _isBinaryFormat;

        // 随机数字节相关字段
        protected bool _hasRandomBytes;
        protected int _randomBytesCount;
        protected byte[] _fixedHeaderBytes;

        #endregion

        #region 公共属性

        /// <summary>
        /// 仪器ID
        /// </summary>
        public int InstrumentId { get; protected set; }

        /// <summary>
        /// 仪器名称
        /// </summary>
        public string InstrumentName { get; protected set; }

        /// <summary>
        /// 连接器当前状态
        /// </summary>
        public ConnectorStatus Status
        {
            get { return _status; }
            protected set
            {
                if (_status != value)
                {
                    var oldStatus = _status;
                    _status = value;
                    OnStatusChanged(new StatusChangedEventArgs(InstrumentId, oldStatus, value));
                }
            }
        }

        #endregion

        #region 事件

        /// <summary>
        /// 接收到数据时触发的事件
        /// </summary>
        public event EventHandler<DataReceivedEventArgs> DataReceived;

        /// <summary>
        /// 连接器状态变化时触发的事件
        /// </summary>
        public event EventHandler<StatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 发生错误时触发的事件
        /// </summary>
        public event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        #endregion

        #region 构造函数

        /// <summary>
        /// 基类构造函数
        /// </summary>
        protected BaseConnector()
        {
            _statistics = new ConnectorStatistics();
            Status = ConnectorStatus.Stopped;
        }

        #endregion

        #region IConnector接口实现

        /// <summary>
        /// 加载仪器配置参数
        /// </summary>
        /// <param name="instrumentConfig">仪器配置对象</param>
        public virtual void Configure(LabInstrument instrumentConfig)
        {
            if (instrumentConfig == null)
                throw new ArgumentNullException(nameof(instrumentConfig));

            // 验证连接类型
            ValidateConnectionType(instrumentConfig);

            _config = instrumentConfig;
            InstrumentId = instrumentConfig.InstrumentId;
            InstrumentName = GetInstrumentName(instrumentConfig);

            // 初始化消息格式配置
            InitializeMessageHeaderFormat();

            // 子类特定的配置初始化
            InitializeSpecificConfiguration();

            WriteLog($"{GetConnectorTypeName()}连接器已配置: {GetConnectionInfo()}");
        }

        /// <summary>
        /// 启动连接器
        /// </summary>
        public virtual void Start()
        {
            if (_config == null)
                throw new InvalidOperationException("连接器尚未配置，请先调用Configure方法");

            if (_isRunning)
            {
                WriteLog("连接器已在运行中");
                return;
            }

            try
            {
                Status = ConnectorStatus.Starting;
                WriteLog($"正在启动{GetConnectorTypeName()}连接器...");

                _isRunning = true;
                InitializeStatistics();

                // 子类特定的启动逻辑
                if (!StartSpecificConnection())
                {
                    throw new InvalidOperationException($"无法建立{GetConnectorTypeName()}连接");
                }

                Status = ConnectorStatus.Running;

                // 启动心跳线程
                StartHeartbeatIfConfigured();

                WriteLog($"{GetConnectorTypeName()}连接器启动成功");
            }
            catch (Exception ex)
            {
                Status = ConnectorStatus.Error;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetStartErrorCode(),
                    $"启动{GetConnectorTypeName()}连接器失败: {ex.Message}", ex));
                throw;
            }
        }

        /// <summary>
        /// 停止连接器
        /// </summary>
        public virtual void Stop()
        {
            if (!_isRunning)
            {
                WriteLog("连接器已停止");
                return;
            }

            try
            {
                Status = ConnectorStatus.Stopping;
                WriteLog($"正在停止{GetConnectorTypeName()}连接器...");

                _isRunning = false;

                // 停止子类特定的连接
                StopSpecificConnection();

                // 停止心跳线程
                StopHeartbeatThread();

                Status = ConnectorStatus.Stopped;
                WriteLog($"{GetConnectorTypeName()}连接器已停止");
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetStopErrorCode(),
                    $"停止{GetConnectorTypeName()}连接器失败: {ex.Message}", ex));
            }
        }

        /// <summary>
        /// 发送命令到仪器
        /// </summary>
        /// <param name="command">要发送的命令</param>
        /// <returns>操作是否成功</returns>
        public abstract bool SendCommand(string command);

        /// <summary>
        /// 获取连接器统计信息
        /// </summary>
        /// <returns>统计信息对象</returns>
        public virtual ConnectorStatistics GetStatistics()
        {
            return new ConnectorStatistics
            {
                StartTime = _statistics.StartTime,
                ReceivedMessagesCount = _statistics.ReceivedMessagesCount,
                SentCommandsCount = _statistics.SentCommandsCount,
                ErrorsCount = _statistics.ErrorsCount,
                LastActivityTime = _statistics.LastActivityTime
            };
        }

        #endregion

        #region 抽象方法 - 子类必须实现

        /// <summary>
        /// 验证连接类型是否支持
        /// </summary>
        /// <param name="instrumentConfig">仪器配置</param>
        protected abstract void ValidateConnectionType(LabInstrument instrumentConfig);

        /// <summary>
        /// 初始化特定连接的配置
        /// </summary>
        protected abstract void InitializeSpecificConfiguration();

        /// <summary>
        /// 启动特定类型的连接
        /// </summary>
        /// <returns>是否成功</returns>
        protected abstract bool StartSpecificConnection();

        /// <summary>
        /// 停止特定类型的连接
        /// </summary>
        protected abstract void StopSpecificConnection();

        /// <summary>
        /// 获取连接器类型名称
        /// </summary>
        /// <returns>连接器类型名称</returns>
        protected abstract string GetConnectorTypeName();

        /// <summary>
        /// 获取连接信息字符串
        /// </summary>
        /// <returns>连接信息</returns>
        protected abstract string GetConnectionInfo();

        /// <summary>
        /// 获取启动错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetStartErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.启动错误;
        }

        /// <summary>
        /// 获取停止错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetStopErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.停止错误;
        }

        #endregion

        #region 受保护的公共方法

        /// <summary>
        /// 初始化消息格式配置
        /// </summary>
        protected virtual void InitializeMessageHeaderFormat()
        {
            // 判断消息格式类型
            _isBinaryFormat = _config.MsgHeaderFormatType == MsgFormatType.字节流;

            // 初始化消息头
            if (!string.IsNullOrEmpty(_config.MsgHeader))
            {
                if (_isBinaryFormat)
                {
                    // 解析随机数字节格式
                    ParseRandomBytesHeader(_config.MsgHeader);
                }
                else
                {
                    _hasRandomBytes = false;
                    _msgHeaderBytes = Encoding.UTF8.GetBytes(_config.MsgHeader);
                }
            }

            // 初始化消息尾
            if (!string.IsNullOrEmpty(_config.MsgTail))
            {
                _msgTailBytes = _isBinaryFormat ?
                    ConvertHexStringToBytes(_config.MsgTail) :
                    Encoding.UTF8.GetBytes(_config.MsgTail);
            }

            // 初始化成功应答
            if (!string.IsNullOrEmpty(_config.AckSuccess))
            {
                _ackSuccessBytes = _isBinaryFormat ?
                    ConvertHexStringToBytes(_config.AckSuccess) :
                    Encoding.UTF8.GetBytes(_config.AckSuccess);
            }

            // 初始化失败应答
            if (!string.IsNullOrEmpty(_config.AckFailure))
            {
                _ackFailureBytes = _isBinaryFormat ?
                    ConvertHexStringToBytes(_config.AckFailure) :
                    Encoding.UTF8.GetBytes(_config.AckFailure);
            }

            WriteLog($"消息格式初始化完成 - 格式类型: {(_isBinaryFormat ? "字节流" : "字符串")}, 随机数字节: {(_hasRandomBytes ? $"{_randomBytesCount}个" : "无")}");
        }

        /// <summary>
        /// 解析包含随机数字节的消息头配置
        /// </summary>
        /// <param name="msgHeader">消息头配置字符串</param>
        protected virtual void ParseRandomBytesHeader(string msgHeader)
        {
            _hasRandomBytes = false;
            _randomBytesCount = 0;

            if (string.IsNullOrEmpty(msgHeader))
            {
                _msgHeaderBytes = new byte[0];
                return;
            }

            // 计算开头连续空格的数量
            int spaceCount = 0;
            for (int i = 0; i < msgHeader.Length; i++)
            {
                if (msgHeader[i] == ' ')
                    spaceCount++;
                else
                    break;
            }

            // 如果开头有空格，说明有随机数字节
            if (spaceCount > 0)
            {
                _hasRandomBytes = true;
                _randomBytesCount = spaceCount / 2; // 每2个空格表示1个随机数字节

                // 获取固定部分（去掉开头的空格）
                string fixedPart = msgHeader.Substring(spaceCount).Trim();
                _fixedHeaderBytes = string.IsNullOrEmpty(fixedPart) ?
                    new byte[0] : ConvertHexStringToBytes(fixedPart);

                // 创建完整消息头（随机数字节 + 固定部分）
                _msgHeaderBytes = new byte[_randomBytesCount + _fixedHeaderBytes.Length];
                Array.Copy(_fixedHeaderBytes, 0, _msgHeaderBytes, _randomBytesCount, _fixedHeaderBytes.Length);

                WriteLog($"检测到随机数字节格式: {_randomBytesCount}个随机字节 + 固定部分({ConvertBytesToHexString(_fixedHeaderBytes)})");
            }
            else
            {
                // 普通格式，没有随机数字节
                _hasRandomBytes = false;
                _msgHeaderBytes = ConvertHexStringToBytes(msgHeader);
            }
        }

        /// <summary>
        /// 将十六进制字符串转换为字节数组
        /// </summary>
        /// <param name="hexString">十六进制字符串</param>
        /// <returns>字节数组</returns>
        protected virtual byte[] ConvertHexStringToBytes(string hexString)
        {
            if (string.IsNullOrEmpty(hexString))
                return new byte[0];

            hexString = hexString.Replace(" ", "").Replace("-", "").Replace(":", "");

            if (hexString.Length % 2 != 0)
                throw new ArgumentException("十六进制字符串长度必须为偶数");

            byte[] bytes = new byte[hexString.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
            }
            return bytes;
        }

        /// <summary>
        /// 将字节数组转换为十六进制字符串
        /// </summary>
        /// <param name="bytes">字节数组</param>
        /// <returns>十六进制字符串</returns>
        protected virtual string ConvertBytesToHexString(byte[] bytes)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            return string.Join(" ", bytes.Select(b => b.ToString("X2")));
        }

        /// <summary>
        /// 处理接收到的原始数据，进行消息分割
        /// </summary>
        /// <param name="rawData">原始数据</param>
        protected virtual void ProcessRawData(byte[] rawData)
        {
            if (rawData == null || rawData.Length == 0)
                return;

            try
            {
                // 如果没有配置消息头和消息尾，直接处理
                if (_msgHeaderBytes == null && _msgTailBytes == null)
                {
                    ProcessCompleteMessage(rawData);
                    return;
                }

                // 进行消息分割处理
                var messages = SplitMessages(rawData);
                foreach (var message in messages)
                {
                    ProcessCompleteMessage(message);
                }
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetDataProcessErrorCode(),
                    $"处理原始数据失败: {ex.Message}", ex));
            }
        }

        /// <summary>
        /// 分割消息
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <returns>分割后的消息列表（不包含消息头和消息尾）</returns>
        protected virtual List<byte[]> SplitMessages(byte[] data)
        {
            var messages = new List<byte[]>();

            if (_msgHeaderBytes == null || _msgTailBytes == null)
            {
                messages.Add(data);
                return messages;
            }

            int startIndex = 0;
            while (startIndex < data.Length)
            {
                int headerIndex;
                int headerLength;

                if (_hasRandomBytes)
                {
                    // 查找带随机数字节的消息头
                    headerIndex = FindPatternWithRandomBytes(data, _fixedHeaderBytes, _randomBytesCount, startIndex);
                    headerLength = _randomBytesCount + _fixedHeaderBytes.Length;
                }
                else
                {
                    // 查找普通消息头
                    headerIndex = FindPattern(data, _msgHeaderBytes, startIndex);
                    headerLength = _msgHeaderBytes.Length;
                }

                if (headerIndex == -1)
                    break;

                // 查找消息尾
                int tailIndex = FindPattern(data, _msgTailBytes, headerIndex + headerLength);
                if (tailIndex == -1)
                    break;

                // 提取消息内容（去掉消息头和消息尾）
                int contentStartIndex = headerIndex + headerLength;
                int contentLength = tailIndex - contentStartIndex;

                if (contentLength > 0)
                {
                    byte[] message = new byte[contentLength];
                    Array.Copy(data, contentStartIndex, message, 0, contentLength);
                    messages.Add(message);
                }

                startIndex = tailIndex + _msgTailBytes.Length;
            }

            return messages;
        }

        /// <summary>
        /// 在数据中查找指定模式
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="pattern">模式</param>
        /// <param name="startIndex">开始索引</param>
        /// <returns>找到的位置，-1表示未找到</returns>
        protected virtual int FindPattern(byte[] data, byte[] pattern, int startIndex)
        {
            if (data == null || pattern == null || pattern.Length == 0)
                return -1;

            for (int i = startIndex; i <= data.Length - pattern.Length; i++)
            {
                bool found = true;
                for (int j = 0; j < pattern.Length; j++)
                {
                    if (data[i + j] != pattern[j])
                    {
                        found = false;
                        break;
                    }
                }
                if (found)
                    return i;
            }
            return -1;
        }

        /// <summary>
        /// 在数据中查找带随机数字节的消息头模式
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="fixedPattern">固定部分的模式</param>
        /// <param name="randomBytesCount">随机数字节数量</param>
        /// <param name="startIndex">开始索引</param>
        /// <returns>找到的位置，-1表示未找到</returns>
        protected virtual int FindPatternWithRandomBytes(byte[] data, byte[] fixedPattern, int randomBytesCount, int startIndex)
        {
            if (data == null || fixedPattern == null || fixedPattern.Length == 0 || randomBytesCount <= 0)
                return -1;

            // 需要至少有 随机字节数 + 固定部分的长度
            int totalPatternLength = randomBytesCount + fixedPattern.Length;

            for (int i = startIndex; i <= data.Length - totalPatternLength; i++)
            {
                // 跳过随机字节部分，检查固定部分
                bool found = true;
                for (int j = 0; j < fixedPattern.Length; j++)
                {
                    if (data[i + randomBytesCount + j] != fixedPattern[j])
                    {
                        found = false;
                        break;
                    }
                }
                if (found)
                    return i; // 返回消息头开始位置（包含随机字节）
            }
            return -1;
        }

        /// <summary>
        /// 处理完整的消息
        /// </summary>
        /// <param name="messageData">完整的消息数据</param>
        protected virtual void ProcessCompleteMessage(byte[] messageData)
        {
            try
            {
                _statistics.ReceivedMessagesCount++;
                _statistics.LastActivityTime = DateTime.Now;
                string messageText = "";
                if (_config.MsgFormatType == MsgFormatType.字符串)
                {
                    messageText = Encoding.UTF8.GetString(messageData);
                }
                else
                {
                    messageText = ConvertBytesToHexString(messageData);
                }
                WriteLog($"接收到完整消息: {messageText}");

                // 触发数据接收事件
                OnDataReceived(new DataReceivedEventArgs(InstrumentId, messageText, DateTime.Now));

                // 发送自动应答
                SendAutoAcknowledgment(true);
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                SendAutoAcknowledgment(false);
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetDataProcessErrorCode(),
                    $"处理完整消息失败: {ex.Message}", ex));
            }
        }

        /// <summary>
        /// 发送自动应答
        /// </summary>
        /// <param name="success">是否成功</param>
        protected virtual void SendAutoAcknowledgment(bool success)
        {
            try
            {
                byte[] ackBytes = success ? _ackSuccessBytes : _ackFailureBytes;
                if (ackBytes != null && ackBytes.Length > 0)
                {
                    SendRawData(ackBytes);
                    WriteLog($"发送自动应答: {(success ? "成功" : "失败")} - {ConvertBytesToHexString(ackBytes)}");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"发送自动应答失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送原始数据（子类需要实现）
        /// </summary>
        /// <param name="data">要发送的原始数据</param>
        /// <returns>是否成功</returns>
        protected abstract bool SendRawData(byte[] data);

        /// <summary>
        /// 初始化统计信息
        /// </summary>
        protected virtual void InitializeStatistics()
        {
            _statistics.StartTime = DateTime.Now;
            _statistics.ReceivedMessagesCount = 0;
            _statistics.SentCommandsCount = 0;
            _statistics.ErrorsCount = 0;
        }

        /// <summary>
        /// 获取仪器名称
        /// </summary>
        /// <param name="instrumentConfig">仪器配置</param>
        /// <returns>仪器名称</returns>
        protected virtual string GetInstrumentName(LabInstrument instrumentConfig)
        {
            return instrumentConfig.InstrName ?? $"{GetConnectorTypeName()}_{instrumentConfig.InstrCode}";
        }

        /// <summary>
        /// 启动心跳线程（如果配置了心跳）
        /// </summary>
        protected virtual void StartHeartbeatIfConfigured()
        {
            if (_config.KeepAliveSec.HasValue && _config.KeepAliveSec.Value > 0)
            {
                _heartbeatThread = new Thread(HeartbeatThreadProc)
                {
                    Name = $"{GetConnectorTypeName()}_Heartbeat_{InstrumentId}",
                    IsBackground = true
                };
                _heartbeatThread.Start();
            }
        }

        /// <summary>
        /// 停止心跳线程
        /// </summary>
        protected virtual void StopHeartbeatThread()
        {
            if (_heartbeatThread != null && _heartbeatThread.IsAlive)
            {
                if (!_heartbeatThread.Join(2000))
                {
                    _heartbeatThread.Abort();
                }
                _heartbeatThread = null;
            }
        }

        /// <summary>
        /// 心跳线程处理过程
        /// </summary>
        protected virtual void HeartbeatThreadProc()
        {
            WriteLog($"{GetConnectorTypeName()}心跳线程已启动，间隔: {_config.KeepAliveSec}秒");

            while (_isRunning)
            {
                try
                {
                    Thread.Sleep(_config.KeepAliveSec.Value * 1000);

                    if (_isRunning && IsConnectionAlive())
                    {
                        SendHeartbeat();
                    }
                }
                catch (Exception ex)
                {
                    WriteLog($"心跳异常: {ex.Message}");
                }
            }

            WriteLog($"{GetConnectorTypeName()}心跳线程已退出");
        }

        /// <summary>
        /// 检查连接是否活跃
        /// </summary>
        /// <returns>连接是否活跃</returns>
        protected abstract bool IsConnectionAlive();

        /// <summary>
        /// 发送心跳包
        /// </summary>
        protected abstract void SendHeartbeat();

        /// <summary>
        /// 获取数据处理错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetDataProcessErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.数据处理错误;
        }

        /// <summary>
        /// 获取发送失败错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetSendFailureErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.发送失败;
        }

        /// <summary>
        /// 获取初始化失败错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetInitializationFailureErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.初始化失败;
        }

        /// <summary>
        /// 获取接收错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetReceiveErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.接收错误;
        }

        /// <summary>
        /// 获取硬件错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetHardwareErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.硬件错误;
        }

        /// <summary>
        /// 获取发送文本失败错误代码
        /// </summary>
        /// <returns>错误代码</returns>
        protected virtual int GetSendTextFailureErrorCode()
        {
            return (int)ZTLisEnum.ConnectorErrorCode.发送文本失败;
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="message">日志消息</param>
        protected virtual void WriteLog(string message)
        {
            string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{GetConnectorTypeName()}-{InstrumentId}] {message}";
            Common.Log.LogHelper.Info(logMessage);
        }

        /// <summary>
        /// 根据字符获取校验位类型
        /// </summary>
        /// <param name="parityChar">校验位字符</param>
        /// <returns>校验位类型</returns>
        protected virtual Parity GetParityFromChar(char? parityChar)
        {
            if (!parityChar.HasValue)
                return Parity.None;

            switch (parityChar.Value)
            {
                case 'N':
                case 'n':
                    return Parity.None;
                case 'E':
                case 'e':
                    return Parity.Even;
                case 'O':
                case 'o':
                    return Parity.Odd;
                case 'M':
                case 'm':
                    return Parity.Mark;
                case 'S':
                case 's':
                    return Parity.Space;
                default:
                    return Parity.None;
            }
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发数据接收事件
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnDataReceived(DataReceivedEventArgs e)
        {
            DataReceived?.Invoke(this, e);
        }

        /// <summary>
        /// 触发状态变化事件
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnStatusChanged(StatusChangedEventArgs e)
        {
            WriteLog($"状态变化: {e.OldStatus} -> {e.NewStatus}");
            StatusChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnErrorOccurred(ErrorOccurredEventArgs e)
        {
            WriteLog($"发生错误 [{e.ErrorCode}]: {e.ErrorMessage}");
            ErrorOccurred?.Invoke(this, e);
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Stop();
                    DisposeSpecificResources();
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// 释放子类特定的资源
        /// </summary>
        protected abstract void DisposeSpecificResources();

        /// <summary>
        /// 析构函数
        /// </summary>
        ~BaseConnector()
        {
            Dispose(false);
        }

        #endregion
    }
}