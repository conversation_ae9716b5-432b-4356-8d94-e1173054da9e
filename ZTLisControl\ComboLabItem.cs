using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    /// <summary>
    /// 检验项目控件
    /// </summary>
    public partial class ComboLabItem : MyDtComobo
    {
        public ComboLabItem()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            strWhere += " and DictLabItem.Status=1";
            BLL.BllDictLabItem _bllDictLabItem = new BllDictLabItem();
            this.DataView = _bllDictLabItem.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("LabItemName", "项目名称", 160, "左");
            this.Init_Colum("LabItemCode", "项目编码", 100, "左");
            this.Init_Colum("PinYin", "拼音", 0, "左");
            this.Init_Colum("EnglishName", "英文名", 0, "左");
            this.DisplayMember = "LabItemName";
            this.ValueMember = "LabItemId";
            int width = 350;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "LabItemCode+isnull(PinYin,'')+isnull(EnglishName,'')+LabItemName";
        }
    }
}