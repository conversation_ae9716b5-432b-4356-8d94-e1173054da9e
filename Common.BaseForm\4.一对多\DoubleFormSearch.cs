﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Common.BaseForm
{
    public partial class DoubleFormSearch : Common.BaseForm.BaseChild
    {
        #region BasicVariables
        private DataTable _table = new DataTable();
        protected bool FrmLoad { get; set; }
        protected DataRow MyRow { get; set; }
        protected DataTable MyTable
        {
            get { return _table; }
            set { _table = value; }
        }
        protected CurrencyManager MyCm { get; set; }

        public Common.Delegate.TransmitModel MyTransmitModel { get; set; }

        private CustomControl.MyGrid _baseMyGrid;
        protected CustomControl.MyGrid BaseMyGrid
        {
            get { return _baseMyGrid; }
            set
            {
                _baseMyGrid = value;
                this.BaseMyGrid.MouseDoubleClick += myGrid1_MouseDoubleClick;
            }
        }

        #endregion
        public DoubleFormSearch()
        {
            InitializeComponent();
        }
        #region 控件动作

        private void DoubleFormSearch_Load(object sender, EventArgs e)
        {
            FrmLoad = false;
        }

        #region myGridEvents
        private void myGrid1_MouseDoubleClick(System.Object sender, System.Windows.Forms.MouseEventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }



        #endregion

        #endregion


    }
}
