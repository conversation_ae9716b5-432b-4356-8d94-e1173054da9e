﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Common
{
    public class Sha1
    {
        public Sha1()
        {
        }
        public string SHA1_Encrypt(string Source_String)
        {
            byte[] StrRes = Encoding.UTF8.GetBytes(Source_String);
            HashAlgorithm iSHA = new SHA1CryptoServiceProvider();
            StrRes = iSHA.ComputeHash(StrRes);
            StringBuilder EnText = new StringBuilder();
            foreach (byte iByte in StrRes)
            {
                EnText.AppendFormat("{0:x2}", iByte);
            }
            return EnText.ToString().ToUpper();
        }

        public string SHA256_Encrypt(string Source_String)
        {
            byte[] StrRes = Encoding.UTF8.GetBytes(Source_String);
            HashAlgorithm iSHA = new SHA256CryptoServiceProvider();
            StrRes = iSHA.ComputeHash(StrRes);
            StringBuilder EnText = new StringBuilder();
            foreach (byte iByte in StrRes)
            {
                EnText.AppendFormat("{0:x2}", iByte);
            }
            return EnText.ToString().ToUpper();
        }

        public string HmacSHA256(string message, string secret)
        {
            secret = secret ?? "";
            var encoding = new System.Text.UTF8Encoding();
            byte[] keyByte = encoding.GetBytes(secret);
            byte[] messageBytes = encoding.GetBytes(message);
            StringBuilder EnText = new StringBuilder();
            using (var hmacsha256 = new HMACSHA256(keyByte))
            {
      
                byte[] hashmessage = hmacsha256.ComputeHash(messageBytes);
                foreach (byte iByte in hashmessage)
                {
                    EnText.AppendFormat("{0:x2}", iByte);
                }
                return EnText.ToString().ToUpper();
            }
        }
        public string HmacSHA1(string message, string secret)
        {
            secret = secret ?? "";
            var encoding = new System.Text.UTF8Encoding();
            byte[] keyByte = encoding.GetBytes(secret);
            byte[] messageBytes = encoding.GetBytes(message);
            StringBuilder EnText = new StringBuilder();
            using (var hmacsha1 = new HMACSHA1(keyByte))
            {

                byte[] hashmessage = hmacsha1.ComputeHash(messageBytes);
                foreach (byte iByte in hashmessage)
                {
                    EnText.AppendFormat("{0:x2}", iByte);
                }
                return EnText.ToString().ToUpper();
            }
        }
        public string HmacSHA1Base64(string message, string secret)
        {
            secret = secret ?? "";
            var encoding = new System.Text.UTF8Encoding();
            byte[] keyByte = encoding.GetBytes(secret);
            byte[] messageBytes = encoding.GetBytes(message);
            StringBuilder EnText = new StringBuilder();
            using (var hmacsha1 = new HMACSHA1(keyByte))
            {

                byte[] hashmessage = hmacsha1.ComputeHash(messageBytes);
                return Convert.ToBase64String(hashmessage);
            }
        }
    }
}
