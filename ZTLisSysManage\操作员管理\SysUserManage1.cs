using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;
using Common;

namespace ZTLisSysManage
{
    public partial class SysUserManage1 : Common.BaseForm.BaseDict1
    {
        BLL.BllSysUser _bllSysUser = new BllSysUser();

        public SysUserManage1()
        {
            InitializeComponent();
        }

        private void SysUserManage1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("登录账号", "LoginName", 120, "左", "", false);
            myGrid1.Init_Column("用户姓名", "UserName", 120, "左", "", false);
            myGrid1.Init_Column("关联医生", "DoctorName", 100, "中", "", false);
            myGrid1.Init_Column("所属角色", "RoleName", 100, "中", "", false);
            myGrid1.Init_Column("是否锁定", "IsLockedOut", 80, "中", "", false);
            myGrid1.Init_Column("最后登录", "LastLoginAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.Splits[0].DisplayColumns["IsLockedOut"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.FetchCellStyle += GridFunction.IsLockedOut_FetchCellStyle;
            myGrid1.AllowSort = true;
        }

        private void DataInit()
        {
            base.MyTable = _bllSysUser.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["UserId"] };
            base.MyTable.Columns["UserId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            SysUserManage2 vform = new SysUserManage2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除操作员：" + this.myGrid1.Columns["UserName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllSysUser.Delete(int.Parse(base.MyRow["UserId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void DataRefresh()
        {
            this.DataInit();
        }



        #endregion

        #region 控件事件
        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            Model.MdlSysUser mdlSysUser = _bllSysUser.GetModel(int.Parse(base.MyRow["UserId"].ToString()));
            mdlSysUser.Status = true;
            mdlSysUser.UpdatedAt = DateTime.Now;
            _bllSysUser.Update(mdlSysUser);
            base.MyRow["Status"] = true;
            base.MyRow["UpdatedAt"] = DateTime.Now;
            base.MyTable.AcceptChanges();
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            Model.MdlSysUser mdlSysUser = _bllSysUser.GetModel(int.Parse(base.MyRow["UserId"].ToString()));
            mdlSysUser.Status = false;
            mdlSysUser.UpdatedAt = DateTime.Now;
            _bllSysUser.Update(mdlSysUser);
            base.MyRow["Status"] = false;
            base.MyRow["UpdatedAt"] = DateTime.Now;
            base.MyTable.AcceptChanges();
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            // 导出功能已移除
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            // 导入功能已移除
        }

        private void myGrid1_DoubleClick(object sender, EventArgs e)
        {
            this.DataEdit(false);
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("LoginName+isnull(UserName,'')", TxtFilter.Text.Trim());
        }

        #endregion

    }
}