﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictLabItemCalculation.cs
*
* 功 能： N/A
* 类 名： MdlDictLabItemCalculation
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验项目计算公式表
	/// </summary>
	[Serializable]
	public partial class MdlDictLabItemCalculation
	{
		public MdlDictLabItemCalculation()
		{}
		#region Model
		private int _calculationid;
		private int _labitemid;
		private string _formulaexpression;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 计算规则ID
		/// </summary>
		public int CalculationId
		{
			set{ _calculationid=value;}
			get{return _calculationid;}
		}
		/// <summary>
		/// 关联的计算项目ID (一个项目只有一个公式)
		/// </summary>
		public int LabItemId
		{
			set{ _labitemid=value;}
			get{return _labitemid;}
		}
		/// <summary>
		/// 公式表达式字符串，如: ([BIO_TP]-[BIO_ALB])/[BIO_ALB]
		/// </summary>
		public string FormulaExpression
		{
			set{ _formulaexpression=value;}
			get{return _formulaexpression;}
		}
		/// <summary>
		/// 公式说明/备注
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 状态：1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

