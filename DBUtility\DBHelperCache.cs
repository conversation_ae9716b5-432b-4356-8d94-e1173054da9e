﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using IDBUtility;
using InterSystems.Data.CacheClient;

namespace DBUtility
{
    public class DBHelperCache : IDbHelper
    {
        private int _timeOut = 60;
        protected string __connectionString;

        public string ConnectionString
        {
            set { __connectionString = value; }
            get { return __connectionString; }
        }

        public int TimeOut
        {
            set { _timeOut = value; }
        }

        #region 公用方法

        /// <summary>
        /// 测试数据库是否连通
        /// </summary>
        public bool TestConn()
        {
            bool result = false;
            CacheConnection connection = new CacheConnection(__connectionString);
            try
            {
                connection.Open();
                if (connection.State == ConnectionState.Open)
                {
                    result = true;
                }
                else
                {
                    result = false;
                }
            }
            catch
            {
                result = false;
            }
            finally
            {
                connection.Close();
            }
            return result;
        }

        public int GetMaxID(string FieldName, string TableName)
        {
            var strsql = "select max(" + FieldName + ")+1 from " + TableName;
            var obj = GetSingle(strsql);
            if (obj == null)
            {
                return 1;
            }
            return int.Parse(obj.ToString());
        }

        public String F_MaxCode(String V_数据条件, int V_编码长度)
        {
            var str = GetSingle(V_数据条件);
            string V_Code = "";
            if (str == null)
                V_Code = "1";
            else
                V_Code = (int.Parse(str.ToString()) + 1).ToString();



            String V_0 = "0000000000000000000000000000000000000000000000000000000000000000000000000000000000000";
            String V_1 = V_0.Substring(0, V_编码长度);
            String V_2 = "";
            if (V_Code.Length < V_编码长度)
                V_2 = V_1.Substring(0, V_编码长度 - V_Code.Length);

            return (V_2 + V_Code);
        }

        public bool Exists(string strSql)
        {
            var obj = GetSingle(strSql);
            int cmdresult;
            if (Equals(obj, null) || Equals(obj, DBNull.Value))
            {
                cmdresult = 0;
            }
            else
            {
                //也可能=0
                cmdresult = int.Parse(obj.ToString());
            }
            if (cmdresult == 0)
            {
                return false;
            }
            return true;
        }

        public bool Exists(string strSql, params IDataParameter[] cmdParms)
        {
            var obj = GetSingle(strSql, cmdParms);
            int cmdresult;
            if (Equals(obj, null) || Equals(obj, DBNull.Value))
            {
                cmdresult = 0;
            }
            else
            {
                cmdresult = int.Parse(obj.ToString());
            }
            if (cmdresult == 0)
            {
                return false;
            }
            return true;
        }

        #endregion

        #region 执行简单语句

        /// <summary>
        ///     执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数(Update,Delete,Insert返回影响行数，Select返回-1)</returns>
        /// <remarks></remarks>
        public int ExecuteSql(string SQLString)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand(SQLString, connection))
                {
                    try
                    {
                        cmd.CommandTimeout = 300;
                        connection.Open();
                        var rows = cmd.ExecuteNonQuery();
                        return rows;
                    }
                    catch (Exception E)
                    {
                        cmd.Dispose();
                        connection.Close();
                        throw E;
                    }
                }
            }
        }

        /// <summary>
        ///     执行带一个存储过程参数的的SQL语句。
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <param name="content">参数内容,比如一个字段是格式复杂的文章，有特殊符号，可以通过这个方式添加</param>
        /// <returns>影响的记录数</returns>
        public int ExecuteSql(string SQLString, string content)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                var cmd = new CacheCommand(SQLString, connection);
                var myParameter = new CacheParameter("@content", SqlDbType.NText);
                myParameter.Value = content;
                cmd.Parameters.Add(myParameter);
                try
                {
                    connection.Open();
                    var rows = cmd.ExecuteNonQuery();
                    return rows;
                }
                catch (Exception E)
                {
                    throw E;
                }
                finally
                {
                    cmd.Dispose();
                    connection.Close();
                }
            }
        }

        /// <summary>
        ///     Execute a CacheCommand (that returns no resultset) using an existing SQL Transaction
        ///     using the provided parameters.
        /// </summary>
        /// <remarks>
        ///     e.g.:
        ///     int result = ExecuteNonQuery(connString, CommandType.StoredProcedure, "PublishOrders", new CacheParameter("@prodid",
        ///     24));
        /// </remarks>
        /// <param name="trans">an existing sql transaction</param>
        /// <param name="commandType">the CommandType (stored procedure, text, etc.)</param>
        /// <param name="commandText">the stored procedure name or T-SQL command</param>
        /// <param name="commandParameters">an array of SqlParamters used to execute the command</param>
        /// <returns>an int representing the number of rows affected by the command</returns>
        public int ExecuteSql(List<string> cmdText, List<IDataParameter[]> cmdParms)
        {
            using (var conn = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand())
                {
                    conn.Open();
                    CacheTransaction trans = conn.BeginTransaction();
                    cmd.Transaction = trans;
                    CacheParameter[] parameters;
                    var rows = 0;
                    try
                    {
                        for (var n = 0; n <= cmdText.Count - 1; n++)
                        {
                            var strsql = cmdText[n];
                            parameters = (CacheParameter[])cmdParms[n];
                            if (strsql.Trim().Length > 1)
                            {
                                PrepareCommand(cmd, (CacheConnection)trans.Connection, trans, strsql, parameters);
                                rows = rows + Math.Abs(cmd.ExecuteNonQuery());
                                cmd.Parameters.Clear();
                            }
                        }
                        trans.Commit();
                        //rows = 1
                    }
                    catch (Exception E)
                    {
                        rows = 0;
                        trans.Rollback();
                        throw E;
                    }
                    return rows;
                }
            }
        }

        public int ExecuteSql(string cmdText, List<IDataParameter[]> cmdParms)
        {
            using (var conn = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand())
                {
                    conn.Open();
                    CacheTransaction trans = conn.BeginTransaction();
                    cmd.Transaction = trans;
                    CacheParameter[] parameters;
                    var rows = 0;
                    try
                    {
                        if (cmdText.Trim().Length > 1)
                        {
                            for (var n = 0; n <= cmdParms.Count - 1; n++)
                            {
                                parameters = (CacheParameter[])cmdParms[n];
                                PrepareCommand(cmd, (CacheConnection)trans.Connection, trans, cmdText, parameters);
                                rows = rows + Math.Abs(cmd.ExecuteNonQuery());
                                cmd.Parameters.Clear();
                            }
                        }
                        trans.Commit();
                        //rows = 1
                    }
                    catch (Exception E)
                    {
                        rows = 0;
                        trans.Rollback();
                        throw E;
                    }
                    return rows;
                }
            }
        }

        /// <summary>
        ///     执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>
        public int ExecuteSqlTran(ArrayList SQLStringList)
        {
            using (var conn = new CacheConnection(__connectionString))
            {
                conn.Open();
                var cmd = new CacheCommand();
                cmd.CommandTimeout = 300;
                cmd.Connection = conn;
                var trans = conn.BeginTransaction();
                cmd.Transaction = trans;
                int rows;
                try
                {
                    for (var n = 0; n <= SQLStringList.Count - 1; n++)
                    {
                        var strsql = SQLStringList[n].ToString();
                        if (strsql.Trim().Length > 1)
                        {
                            cmd.CommandText = strsql;
                            cmd.ExecuteNonQuery();
                        }
                    }
                    trans.Commit();
                    rows = 1;
                }
                catch (Exception E)
                {
                    rows = 0;
                    trans.Rollback();
                    throw E;
                }
                return rows;
            }
        }

        /// <summary>
        ///     执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        public object GetSingle(string SQLString)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand(SQLString, connection))
                {
                    cmd.CommandTimeout = 300;
                    try
                    {
                        connection.Open();
                        var obj = cmd.ExecuteScalar();
                        if (Equals(obj, null) || Equals(obj, DBNull.Value))
                        {
                            return null;
                        }
                        return obj;
                    }
                    catch (Exception e)
                    {
                        connection.Close();
                        throw e;
                    }
                }
            }
        }

        /// <summary>
        ///     执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        public object GetSingle(string SQLString, params IDataParameter[] cmdParms)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand())
                {
                    try
                    {
                        PrepareCommand(cmd, connection, null, SQLString, (CacheParameter[])cmdParms);
                        var obj = cmd.ExecuteScalar();
                        cmd.Parameters.Clear();
                        if (Equals(obj, null) || Equals(obj, DBNull.Value))
                        {
                            return null;
                        }
                        return obj;
                    }
                    catch (Exception e)
                    {
                        throw e;
                    }
                }
            }
        }

        /// <summary>
        ///     执行查询语句，返回CacheDataReader
        /// </summary>
        /// <param name="strSQL">查询语句</param>
        /// <returns>CacheDataReader</returns>
        public IDataReader ExecuteReader(string strSQL)
        {
            var connection = new CacheConnection(__connectionString);
            var cmd = new CacheCommand(strSQL, connection);
            try
            {
                connection.Open();
                var myReader = cmd.ExecuteReader(CommandBehavior.CloseConnection);
                //CommandBehavior.CloseConnection的作用
                //它能够保证当CacheDataReader对象被关闭时，其依赖的连接也会被自动关闭
                return myReader;
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                //connection.Close()
            }
        }

        /// <summary>
        ///     执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        public DataSet Query(string SQLString)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                var ds = new DataSet();
                try
                {
                    connection.Open();
                    var command = new CacheDataAdapter(SQLString, connection);
                    command.SelectCommand.CommandTimeout = _timeOut;
                    command.AcceptChangesDuringFill = true;
                    command.MissingSchemaAction = MissingSchemaAction.AddWithKey;
                    command.Fill(ds, "ds");
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                return ds;
            }
        }

        /// <summary>
        ///     执行查询语句，返回DataSet,去掉约束6
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        public DataSet Query(string SQLString, bool Constraints)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                var ds = new DataSet();
                ds.EnforceConstraints = Constraints;
                try
                {
                    connection.Open();
                    var command = new CacheDataAdapter(SQLString, connection);
                    command.SelectCommand.CommandTimeout = _timeOut;
                    command.AcceptChangesDuringFill = true;
                    command.MissingSchemaAction = MissingSchemaAction.AddWithKey;
                    command.Fill(ds, "ds");
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                return ds;
            }
        }

        /// <summary>
        ///     执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        public DataSet Query(string SQLString, params IDataParameter[] cmdParms)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                var cmd = new CacheCommand();
                PrepareCommand(cmd, connection, null, SQLString, (CacheParameter[])cmdParms);
                using (var da = new CacheDataAdapter(cmd))
                {
                    var ds = new DataSet();
                    try
                    {
                        da.AcceptChangesDuringFill = true;
                        da.MissingSchemaAction = MissingSchemaAction.AddWithKey;
                        da.Fill(ds, "ds");
                        cmd.Parameters.Clear();
                    }
                    catch (Exception ex)
                    {
                        throw new Exception(ex.Message);
                    }
                    return ds;
                }
            }
        }

        /// <summary>
        ///     给Dataset填充datatable
        /// </summary>
        /// <param name="SQLds">Dataset</param>
        /// <param name="SQLString">查询语句</param>
        /// <param name="SQLDtName">表名</param>
        /// <param name="dtClear">是否清空原有表</param>
        /// <remarks></remarks>
        public void QueryDt(DataSet SQLds, string SQLString, string SQLDtName, bool dtClear)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                try
                {
                    if (dtClear)
                    {
                        if (SQLds.Tables[SQLDtName] != null)
                            SQLds.Tables[SQLDtName].Clear();
                    }
                    connection.Open();
                    var command = new CacheDataAdapter(SQLString, connection);
                    {
                        command.AcceptChangesDuringFill = true;
                        command.MissingSchemaAction = MissingSchemaAction.AddWithKey;
                        command.Fill(SQLds, SQLDtName);
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        /// <summary>
        ///     给Dataset填充datatable
        /// </summary>
        /// <param name="SQLds">Dataset</param>
        /// <param name="SQLString">查询语句</param>
        /// <param name="SQLDtName">表名</param>
        /// <param name="dtClear">是否清空原有表</param>
        /// <param name="Constraints">是否强制约束</param>
        /// <remarks></remarks>
        public void QueryDt(DataSet SQLds, string SQLString, string SQLDtName, bool dtClear, bool Constraints)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                try
                {
                    if (dtClear)
                    {
                        if (SQLds.Tables[SQLDtName] != null)
                            SQLds.Tables[SQLDtName].Clear();
                    }
                    SQLds.EnforceConstraints = Constraints;
                    connection.Open();
                    var command = new CacheDataAdapter(SQLString, connection);
                    // ERROR: Not supported in C#: WithStatement
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }

        private string m_Result;

        /// <summary>
        ///     执行sql语句,返回sql的Print的内容
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>返回Print 内容</returns>
        /// <remarks></remarks>
        public string ExecuteSqlPrint(string SQLString)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand(SQLString, connection))
                {
                    try
                    {
                        //connection.InfoMessage += SetText;
                        connection.Open();
                        cmd.ExecuteNonQuery();
                        return m_Result;
                    }
                    catch (Exception E)
                    {
                        connection.Close();
                        throw E;
                    }
                }
            }
        }

        private void SetText(object sender1, SqlInfoMessageEventArgs e1)
        {
            m_Result = e1.Message;
        }

        #endregion

        #region 执行带参数的SQL语句

        /// <summary>
        ///     执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数</returns>
        public int ExecuteSql(string SQLString, params IDataParameter[] cmdParms)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                using (var cmd = new CacheCommand())
                {
                    try
                    {
                        PrepareCommand(cmd, connection, null, SQLString, (CacheParameter[])cmdParms);
                        var rows = cmd.ExecuteNonQuery();
                        cmd.Parameters.Clear();
                        return rows;
                    }
                    catch (Exception E)
                    {
                        throw E;
                    }
                }
            }
        }

        private void PrepareCommand(CacheCommand cmd, CacheConnection conn, CacheTransaction trans, string cmdText, IDataParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
            {
                conn.Open();
            }
            cmd.Connection = conn;
            cmd.CommandText = cmdText;
            if (trans != null)
            {
                cmd.Transaction = trans;
            }
            cmd.CommandType = CommandType.Text;
            //cmdType;
            if (cmdParms != null)
            {
                foreach (var parm in cmdParms)
                {
                    cmd.Parameters.Add(parm);
                }
            }
        }

        #endregion

        #region 存储过程操作

        /// <summary>
        ///     执行存储过程，返回DataReader
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>CacheDataReader</returns>
        public IDataReader RunProcedureDr(string storedProcName, IDataParameter[] parameters)
        {
            var connection = new CacheConnection(__connectionString);
            CacheDataReader returnReader;
            connection.Open();
            var command = BuildQueryCommand(connection, storedProcName, parameters);
            command.CommandType = CommandType.StoredProcedure;
            returnReader = command.ExecuteReader(CommandBehavior.CloseConnection);
            return returnReader;
        }

        /// <summary>
        ///     执行存储过程，带参数，无返回值
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        public void RunProcedure(string storedProcName, IDataParameter[] parameters)
        {
            var connection = new CacheConnection(__connectionString);
            connection.Open();
            var command = BuildQueryCommand(connection, storedProcName, parameters);
            command.ExecuteNonQuery();
            //End Using
        }

        /// <summary>
        ///     执行存储过程，返回DataSet
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="tableName">DataSet结果中的表名</param>
        /// <returns>DataSet</returns>
        public DataSet RunProcedure(string storedProcName, IDataParameter[] parameters, string tableName)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                var dataSet = new DataSet();
                connection.Open();
                var sqlDA = new CacheDataAdapter();
                sqlDA.SelectCommand = BuildQueryCommand(connection, storedProcName, parameters);
                sqlDA.Fill(dataSet, tableName);
                connection.Close();
                return dataSet;
            }
        }

        /// <summary>
        ///     执行存储过程，无参数
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        public void RunProcedure(string storedProcName)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                connection.Open();
                var cmd = new CacheCommand(storedProcName, connection);
                cmd.CommandTimeout = 300;
                cmd.CommandType = CommandType.StoredProcedure;
                try
                {
                    cmd.ExecuteNonQuery();
                }
                catch (Exception E)
                {
                    throw E;
                }
                finally
                {
                    cmd.Dispose();
                    connection.Close();
                }
            }
        }

        /// <summary>
        ///     执行存储过程，无参数，返回DataSet
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="tableName">数据表名</param>
        public DataSet RunProcedure(string storedProcName, string tableName)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                var dataSet = new DataSet();
                connection.Open();
                var sqlDA = new CacheDataAdapter();
                var cmd = new CacheCommand(storedProcName, connection);
                cmd.CommandTimeout = 300;
                cmd.CommandType = CommandType.StoredProcedure;
                try
                {
                    sqlDA.SelectCommand = cmd;
                    sqlDA.Fill(dataSet, tableName);
                }
                catch (Exception E)
                {
                    throw E;
                }
                finally
                {
                    cmd.Dispose();
                    connection.Close();
                }
                return dataSet;
            }
        }

        /// <summary>
        ///     构建 CacheCommand 对象(用来返回一个结果集，而不是一个整数值)
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>CacheCommand</returns>
        private CacheCommand BuildQueryCommand(CacheConnection connection, string storedProcName,
            IDataParameter[] parameters)
        {
            var command = new CacheCommand(storedProcName, connection);
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.Clear();
            foreach (CacheParameter parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }
            return command;
        }

        /// <summary>
        ///     执行存储过程，返回影响的行数
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="rowsAffected">影响的行数</param>
        /// <returns></returns>
        public int RunProcedure(string storedProcName, IDataParameter[] parameters, ref int rowsAffected)
        {
            using (var connection = new CacheConnection(__connectionString))
            {
                int result;
                connection.Open();
                var command = BuildIntCommand(connection, storedProcName, parameters);
                rowsAffected = command.ExecuteNonQuery();
                result = (int)command.Parameters["ReturnValue"].Value;
                //Connection.Close();
                return result;
            }
        }

        /// <summary>
        ///     创建 CacheCommand 对象实例(用来返回一个整数值)
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>CacheCommand 对象实例</returns>
        private CacheCommand BuildIntCommand(CacheConnection connection, string storedProcName, IDataParameter[] parameters)
        {
            var command = BuildQueryCommand(connection, storedProcName, parameters);
            command.Parameters.Add(new CacheParameter("ReturnValue", CacheDbType.Int, 4, ParameterDirection.ReturnValue,
                false, 0, 0, string.Empty, DataRowVersion.Default, null));
            return command;
        }

        #endregion
    }
}