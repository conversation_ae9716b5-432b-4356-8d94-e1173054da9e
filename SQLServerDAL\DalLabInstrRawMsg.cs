﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabInstrRawMsg.cs
*
* 功 能： N/A
* 类 名： DalLabInstrRawMsg
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabInstrRawMsg
	/// </summary>
	public partial class DalLabInstrRawMsg : IDalLabInstrRawMsg
	{
		public DalLabInstrRawMsg()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long RawId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabInstrRawMsg");
			strSql.Append(" where RawId=@RawId");
			SqlParameter[] parameters = {
					new SqlParameter("@RawId", SqlDbType.BigInt)
			};
			parameters[0].Value = RawId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabInstrRawMsg model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabInstrRawMsg(");
			strSql.Append("InstrumentId,RawText,AcquiredAt,ParsedFlag,ParseError)");
			strSql.Append(" values (");
			strSql.Append("@InstrumentId,@RawText,@AcquiredAt,@ParsedFlag,@ParseError)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@RawText", SqlDbType.NVarChar,-1),
					new SqlParameter("@AcquiredAt", SqlDbType.DateTime),
					new SqlParameter("@ParsedFlag", SqlDbType.Bit,1),
					new SqlParameter("@ParseError", SqlDbType.NVarChar,400)};
			parameters[0].Value = model.InstrumentId;
			parameters[1].Value = model.RawText;
			parameters[2].Value = model.AcquiredAt;
			parameters[3].Value = model.ParsedFlag;
			parameters[4].Value = Common.Tools.IsValueNull(model.ParseError);

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabInstrRawMsg model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabInstrRawMsg set ");
			strSql.Append("InstrumentId=@InstrumentId,");
			strSql.Append("RawText=@RawText,");
			strSql.Append("AcquiredAt=@AcquiredAt,");
			strSql.Append("ParsedFlag=@ParsedFlag,");
			strSql.Append("ParseError=@ParseError");
			strSql.Append(" where RawId=@RawId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@RawText", SqlDbType.NVarChar,-1),
					new SqlParameter("@AcquiredAt", SqlDbType.DateTime),
					new SqlParameter("@ParsedFlag", SqlDbType.Bit,1),
					new SqlParameter("@ParseError", SqlDbType.NVarChar,400),
					new SqlParameter("@RawId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.InstrumentId;
			parameters[1].Value = model.RawText;
			parameters[2].Value = model.AcquiredAt;
			parameters[3].Value = model.ParsedFlag;
			parameters[4].Value = Common.Tools.IsValueNull(model.ParseError) ;
			parameters[5].Value = model.RawId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long RawId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrRawMsg ");
			strSql.Append(" where RawId=@RawId");
			SqlParameter[] parameters = {
					new SqlParameter("@RawId", SqlDbType.BigInt)
			};
			parameters[0].Value = RawId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string RawIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrRawMsg ");
			strSql.Append(" where RawId in (" + RawIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrRawMsg GetModel(long RawId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 RawId,InstrumentId,RawText,AcquiredAt,ParsedFlag,ParseError from LabInstrRawMsg ");
			strSql.Append(" where RawId=@RawId");
			SqlParameter[] parameters = {
					new SqlParameter("@RawId", SqlDbType.BigInt)
			};
			parameters[0].Value = RawId;

			Model.MdlLabInstrRawMsg model = new Model.MdlLabInstrRawMsg();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrRawMsg DataRowToModel(DataRow row)
		{
			Model.MdlLabInstrRawMsg model = new Model.MdlLabInstrRawMsg();
			if (row != null)
			{
				if (row["RawId"] != null && row["RawId"].ToString() != "")
				{
					model.RawId = long.Parse(row["RawId"].ToString());
				}
				if (row["InstrumentId"] != null && row["InstrumentId"].ToString() != "")
				{
					model.InstrumentId = int.Parse(row["InstrumentId"].ToString());
				}
				if (row["RawText"] != null)
				{
					model.RawText = row["RawText"].ToString();
				}
				if (row["AcquiredAt"] != null && row["AcquiredAt"].ToString() != "")
				{
					model.AcquiredAt = DateTime.Parse(row["AcquiredAt"].ToString());
				}
				if (row["ParsedFlag"] != null && row["ParsedFlag"].ToString() != "")
				{
					if ((row["ParsedFlag"].ToString() == "1") || (row["ParsedFlag"].ToString().ToLower() == "true"))
					{
						model.ParsedFlag = true;
					}
					else
					{
						model.ParsedFlag = false;
					}
				}
				if (row["ParseError"] != null)
				{
					model.ParseError = row["ParseError"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select RawId,InstrumentId,RawText,AcquiredAt,ParsedFlag,ParseError ");
			strSql.Append(" FROM LabInstrRawMsg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" RawId,InstrumentId,RawText,AcquiredAt,ParsedFlag,ParseError ");
			strSql.Append(" FROM LabInstrRawMsg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabInstrRawMsg ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.RawId desc");
			}
			strSql.Append(")AS Row, T.*  from LabInstrRawMsg T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabInstrRawMsg";
			parameters[1].Value = "RawId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

