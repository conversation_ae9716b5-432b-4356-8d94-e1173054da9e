﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// 接收到数据的事件参数
    /// </summary>
    public class DataReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 仪器ID
        /// </summary>
        public int InstrumentId { get; }

        /// <summary>
        /// 原始数据文本
        /// </summary>
        public string RawData { get; }

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime ReceivedTime { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DataReceivedEventArgs(int instrumentId, string rawData, DateTime receivedTime)
        {
            InstrumentId = instrumentId;
            RawData = rawData;
            ReceivedTime = receivedTime;
        }
    }

}
