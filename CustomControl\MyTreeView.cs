﻿/*********************************************************************************** 
*        Filename :Class1 
*        Description :  
*        Date:2017/06/12 15:20:01 
 *       Machinename:DESKTOP-PJMK973 
*        Created by: <T> 董铮
*        All rights reserved 
* 
*       ChangeLog: 
*       2017/06/12 15:20:01: 
 *          Created! 
************************************************************************************/
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using C1.Win.TreeView;

namespace CustomControl
{
    public class MyTreeView : C1TreeView
    {
        private DataTable _dataTable;

        #region 方法

        public void Init()
        {
            this.ShowColumnHeaders = false;
            this.CheckBoxes = true;
            this.Styles.Font = new Font("宋体", (float)10.5, FontStyle.Regular);
            this.Styles.CheckBoxStyle = CheckBoxStyle.Office2010Blue;
            this.Styles.ExpandButtonStyle = ExpandButtonStyle.VS2015;
            this.Styles.Default.BackColor = Color.White;
            this.Styles.ExpandButtonStyle=ExpandButtonStyle.Windows10;
            this.BorderStyle = BorderStyle.None;
            this.Styles.ShowLines = false;
            this.CheckStateChanged += C1TreeView1_CheckStateChanged;


            //this.CheckStateChanged += C1TreeView1_CheckStateChanged2;
            //_dataTable.Columns.Add("ID", Type.GetType("System.String"));
            //_dataTable.Columns.Add("ParentID", Type.GetType("System.String"));
            //_dataTable.Columns.Add("NodeName", Type.GetType("System.String"));
            //BindingSource bindingSource = new BindingSource();
            //bindingSource.DataSource = _dataTable;
            //this.DataSource = bindingSource;
        }

        #endregion

        #region 属性

        public DataTable DataTable
        {
            get { return _dataTable; }
            set
            {
                _dataTable = value;
                this.DataSource = _dataTable;
            }
        }

        #endregion

        private void C1TreeView1_CheckStateChanged(object sender, C1TreeViewEventArgs e)
        {
            if (e.Node.CheckState != CheckState.Indeterminate)
            {
                foreach (C1TreeNode node in e.Node.Nodes)
                    node.Checked = e.Node.Checked;
            }
        }

        private void C1TreeView1_CheckStateChanged2(object sender, C1TreeViewEventArgs e)
        {
            string s33 = e.Node[0].ToString();
            string s3331 = e.Node[1].ToString();
            if (e.Node.ParentCollection.Parent != null)
            {
                C1TreeNode fatherNode = e.Node.ParentCollection.Parent;
                int childNodeCount = fatherNode.Nodes.Count;
                int checkNodeCount = 0;
                int uncheckNodeCount = 0;

                string s = e.Node[0].ToString();
                string s1 = e.Node[1].ToString();

                string s2 = fatherNode[0].ToString();
                string s3 = fatherNode[1].ToString();

                foreach (C1TreeNode node in fatherNode.Nodes)
                {

                    if (node.Checked == true)
                    {
                        checkNodeCount++;
                    }
                    if (node.Checked == false)
                    {
                        uncheckNodeCount++;
                    }

                }

                if (childNodeCount == checkNodeCount)
                {
                    if (fatherNode.CheckState != CheckState.Checked)
                        fatherNode.CheckState = CheckState.Checked;
                }
                else
                {
                    if (childNodeCount == uncheckNodeCount)
                    {
                        if (fatherNode.CheckState != CheckState.Unchecked)
                            fatherNode.CheckState = CheckState.Unchecked;
                    }
                    else
                    {
                        if (fatherNode.CheckState != CheckState.Indeterminate)
                            fatherNode.CheckState = CheckState.Indeterminate;
                    }
                }
            }
        }
    }
}
