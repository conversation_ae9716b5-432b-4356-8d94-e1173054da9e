﻿using System;
using System.Collections;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Command;

namespace Common.BaseForm
{
    public partial class MainBase : Base
    {
        public MainBase()
        {

            InitializeComponent();
        }


        private void MainBase_Load(object sender, System.EventArgs e)
        {
            if (!base.IsDesignMode())
            {
                Common.WinFormVar.Var.MainTab = this.MainTab;
                int leftWidth;

                if (Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "菜单宽度") != null && int.TryParse(Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "菜单宽度"), out leftWidth))
                {
                    c1CommandDock1.Width = leftWidth;
                }
                //调用:
                ChineseCalendar c = new ChineseCalendar(DateTime.Now);
                StringBuilder dayInfo = new StringBuilder();
                dayInfo.Append(c.DateString + " ");
                dayInfo.Append(c.WeekDayStr + " ");
                dayInfo.Append("(" + c.AnimalString + ")年 ");
                dayInfo.Append("" + c.ChineseDateString + " ");
                dayInfo.Append("" + c.ChineseTwentyFourDay + "");
                RblblDate.Text = dayInfo.ToString();
            }
        }

        #region 选项卡动作


        private void MainTab_TabPageClosing(object sender, TabPageCancelEventArgs e)
        {
            foreach (System.Windows.Forms.Form frm in Application.OpenForms)
            {
                foreach (object _ctrl in e.TabPage.Controls)
                {
                    if (_ctrl == frm)
                    {
                        frm.Close();
                        if (frm != null) e.Cancel = true;
                        return;
                    }
                }
            }
        }
        private void MainTab_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                MainTab.Close(MainTab.SelectedTab);
            }
        }

        private void MainTab_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                MainTab.ContextMenuStrip = ContextMenuStrip1;
            }
        }

        private void MainTab_MouseLeave(object sender, System.EventArgs e)
        {
            MainTab.ContextMenuStrip = null;
        }

        private void ToolStripMenuItem1_Click(object sender, System.EventArgs e)
        {
            MainTab.Close(MainTab.SelectedTab);
        }

        private void ToolStripMenuItem2_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            foreach (C1DockingTabPage tap in MainTab.TabPages)
            {
                if (!object.ReferenceEquals(tap, MainTab.SelectedTab))
                {
                    taparray.Add(tap);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem3_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            for (int pageindex = 0; pageindex <= MainTab.TabPages.Count - 1; pageindex++)
            {
                if (pageindex < MainTab.SelectedIndex)
                {
                    taparray.Add(MainTab.TabPages[pageindex]);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem4_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            for (int pageindex = 0; pageindex <= MainTab.TabPages.Count - 1; pageindex++)
            {
                if (pageindex > MainTab.SelectedIndex)
                {
                    taparray.Add(MainTab.TabPages[pageindex]);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem5_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            foreach (C1DockingTabPage tap in MainTab.TabPages)
            {
                taparray.Add(tap);
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void c1CommandDock1_Resize(object sender, EventArgs e)
        {
            if (!base.IsDesignMode())
            {
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "菜单宽度", c1CommandDock1.Width.ToString());
            }
        }

        #endregion

        private void ribbonButton4_Click(object sender, System.EventArgs e)
        {
            Application.Exit();
        }

        #region 函数

        protected void SetText(string text)
        {
            if (c1StatusBar1.InvokeRequired == true)
            {
                c1StatusBar1.BeginInvoke(new Action<string>(SetText), text);
            }
            else
            {
                this.ribbonLabel1.Text = text;
            }
        }

        protected void SetStatusProcess()
        {
            if (c1StatusBar1.InvokeRequired == true)
            {
                c1StatusBar1.BeginInvoke(new Action(SetStatusProcess));
            }
            else
            {
                this.RblblDate.SmallImageIndex = 1;
            }
        }

        protected void SetStatusOk()
        {
            if (c1StatusBar1.InvokeRequired == true)
            {
                c1StatusBar1.BeginInvoke(new Action(SetStatusOk));
            }
            else
            {
                this.RblblDate.SmallImageIndex = 0;
            }
        }




        #endregion


    }
}