﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common
{
    /// <summary>
    /// Object转换类
    /// </summary>
    public class ConvertObject
    {
        public static decimal? ObjToDecimal(object obj)
        {
            if (obj == DBNull.Value || obj + "" == "")
            {
                return null;
            }
            else
            {
                return decimal.Parse(obj.ToString());
            }
        }

        public static int? ObjToInt(object obj)
        {
            if (obj == DBNull.Value || obj + "" == "")
            {
                return null;
            }
            else
            {
                return int.Parse(obj.ToString());
            }

        }

        public static DateTime? ObjToDateTime(object obj)
        {
            if (obj == DBNull.Value)
            {
                return null;
            }
            else
            {
                return DateTime.Parse(obj.ToString());
            }

        }

        public static string ObjToString(object obj)
        {
            return (obj == DBNull.Value || obj == null) ? null : obj.ToString();
        }

        public static string ObjToDateString(object obj, string DateFormat)
        {
            if (obj == DBNull.Value)
            {
                return "";
            }
            else
            {
                return ((DateTime)obj).ToString(DateFormat);
            }

        }

    }
}
