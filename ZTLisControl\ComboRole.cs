using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    public partial class ComboRole : MyDtComobo
    {
        public ComboRole()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            BLL.BllSysRole _bllSysRole = new BllSysRole();
            this.DataView = _bllSysRole.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("RoleName", "名称", 130, "左");
            this.Init_Colum("RoleCode", "编码", 0, "左");
            this.Init_Colum("RolePinYin", "拼音", 0, "左");
            this.DisplayMember = "RoleName";
            this.ValueMember = "RoleCode";
            int width = 230;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "RoleCode+isnull(RolePinYin,'')+RoleName";
        }
    }
}