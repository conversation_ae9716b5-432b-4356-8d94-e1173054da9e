﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictSpecimenCondition.cs
*
* 功 能： N/A
* 类 名： MdlDictSpecimenCondition
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:13   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 标本状态字典：正常、溶血、脂血、黄疸等
	/// </summary>
	[Serializable]
	public partial class MdlDictSpecimenCondition
	{
		public MdlDictSpecimenCondition()
		{}
		#region Model
		private int _speccondid;
		private string _speccondcode;
		private string _speccondname;
		private string _englishname;
		private string _pinyin;
		private int? _sequenceno;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int SpecCondId
		{
			set{ _speccondid=value;}
			get{return _speccondid;}
		}
		/// <summary>
		/// LIS 内部编码
		/// </summary>
		public string SpecCondCode
		{
			set{ _speccondcode=value;}
			get{return _speccondcode;}
		}
		/// <summary>
		/// 中文名称，如 "标本溶血"
		/// </summary>
		public string SpecCondName
		{
			set{ _speccondname=value;}
			get{return _speccondname;}
		}
		/// <summary>
		/// 英文名称，如 Hemolysis / Lipemia
		/// </summary>
		public string EnglishName
		{
			set{ _englishname=value;}
			get{return _englishname;}
		}
		/// <summary>
		/// 助记符（拼音首字母、条码字符等）
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 显示/流程顺序号
		/// </summary>
		public int? SequenceNo
		{
			set{ _sequenceno=value;}
			get{return _sequenceno;}
		}
		/// <summary>
		/// 备注说明
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 启用标志 1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

