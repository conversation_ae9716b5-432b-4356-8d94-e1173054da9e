using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// 串口连接器实现，用于通过串口与仪器进行数据通讯
    /// </summary>
    public class SerialPortConnector : BaseConnector
    {
        #region 私有字段

        private SerialPort _serialPort;
        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public SerialPortConnector()
        {
        }

        #endregion

        #region 基类抽象方法实现

        /// <summary>
        /// 验证连接类型是否支持
        /// </summary>
        /// <param name="instrumentConfig">仪器配置</param>
        protected override void ValidateConnectionType(LabInstrument instrumentConfig)
        {
            if (instrumentConfig.ConnType != ZTLisEnum.ConnType.串口)
                throw new ArgumentException("此连接器只支持串口连接类型");

            if (string.IsNullOrEmpty(instrumentConfig.ComPort))
                throw new ArgumentException("串口号不能为空");
        }

        /// <summary>
        /// 初始化特定连接的配置
        /// </summary>
        protected override void InitializeSpecificConfiguration()
        {
            // 初始化串口配置
            InitializeSerialPort();
        }

        /// <summary>
        /// 启动特定类型的连接
        /// </summary>
        /// <returns>是否成功</returns>
        protected override bool StartSpecificConnection()
        {
            return EstablishConnection();
        }

        /// <summary>
        /// 停止特定类型的连接
        /// </summary>
        protected override void StopSpecificConnection()
        {
            // 关闭串口连接
            CloseConnection();
        }

        /// <summary>
        /// 获取连接器类型名称
        /// </summary>
        /// <returns>连接器类型名称</returns>
        protected override string GetConnectorTypeName()
        {
            return "串口二进制";
        }

        /// <summary>
        /// 获取连接信息字符串
        /// </summary>
        /// <returns>连接信息</returns>
        protected override string GetConnectionInfo()
        {
            return $"{_config.ComPort}";
        }



        /// <summary>
        /// 检查连接是否活跃
        /// </summary>
        /// <returns>连接是否活跃</returns>
        protected override bool IsConnectionAlive()
        {
            return _serialPort?.IsOpen == true;
        }

        /// <summary>
        /// 发送心跳包
        /// </summary>
        protected override void SendHeartbeat()
        {
            // 发送心跳包（可以根据具体协议定制）
            byte[] heartbeatData = { 0x00 }; // 简单的心跳字节
            SendRawData(heartbeatData);
        }

        /// <summary>
        /// 发送原始数据
        /// </summary>
        /// <param name="data">要发送的原始数据</param>
        /// <returns>是否成功</returns>
        protected override bool SendRawData(byte[] data)
        {
            if (data == null || data.Length == 0)
                return false;

            if (!_isRunning || _serialPort?.IsOpen != true)
            {
                WriteLog("串口连接未建立，无法发送原始数据");
                return false;
            }

            try
            {
                lock (_lockObject)
                {
                    _serialPort.Write(data, 0, data.Length);

                    _statistics.SentCommandsCount++;
                    _statistics.LastActivityTime = DateTime.Now;

                    WriteLog($"发送原始数据成功: {ConvertBytesToHexString(data)}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetSendFailureErrorCode(), $"发送原始数据失败: {ex.Message}", ex));
                return false;
            }
        }

        /// <summary>
        /// 释放子类特定的资源
        /// </summary>
        protected override void DisposeSpecificResources()
        {
            CloseConnection();
        }



        #endregion

        #region IConnector接口实现

        /// <summary>
        /// 发送命令到仪器
        /// </summary>
        /// <param name="command">要发送的命令</param>
        /// <returns>操作是否成功</returns>
        public override bool SendCommand(string command)
        {
            if (string.IsNullOrEmpty(command))
                return false;

            if (!_isRunning || _serialPort?.IsOpen != true)
            {
                WriteLog("串口连接未建立，无法发送命令");
                return false;
            }

            try
            {
                byte[] commandBytes = ConvertHexStringToBytes(command);
                return SendBinaryCommand(commandBytes);
            }
            catch
            {
                // 如果不是十六进制格式，按普通字符串发送
                return SendTextCommand(command);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化串口配置
        /// </summary>
        private void InitializeSerialPort()
        {
            try
            {
                CloseConnection();

                _serialPort = new SerialPort
                {
                    PortName = _config.ComPort,
                    BaudRate = _config.BaudRate ?? 9600,
                    DataBits = _config.DataBits ?? 8,
                    StopBits = _config.StopBits == 1 ? StopBits.One :
                              _config.StopBits == 2 ? StopBits.Two : StopBits.One,
                    Parity = GetParityFromChar(_config.Parity),
                    Handshake = Handshake.None,
                    ReadTimeout = 5000,
                    WriteTimeout = 5000,
                    Encoding = Encoding.UTF8
                };

                // 订阅数据接收事件
                _serialPort.DataReceived += SerialPort_DataReceived;
                _serialPort.ErrorReceived += SerialPort_ErrorReceived;
            }
            catch (Exception ex)
            {
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetInitializationFailureErrorCode(),
                    $"初始化串口配置失败: {ex.Message}", ex));
            }
        }

        /// <summary>
        /// 建立串口连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        private bool EstablishConnection()
        {
            try
            {
                if (_serialPort?.IsOpen != true)
                {
                    _serialPort?.Open();
                }

                WriteLog($"串口连接建立成功: {_config.ComPort}");
                return true;
            }
            catch (Exception ex)
            {
                WriteLog($"建立串口连接失败: {ex.Message}");
                CloseConnection();
                return false;
            }
        }

        /// <summary>
        /// 关闭串口连接
        /// </summary>
        private void CloseConnection()
        {
            try
            {
                if (_serialPort != null)
                {
                    try
                    {
                        if (_serialPort.IsOpen)
                        {
                            _serialPort.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"关闭SerialPort时发生错误: {ex.Message}");
                    }

                    // 取消事件订阅
                    try
                    {
                        _serialPort.DataReceived -= SerialPort_DataReceived;
                        _serialPort.ErrorReceived -= SerialPort_ErrorReceived;
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"取消事件订阅时发生错误: {ex.Message}");
                    }

                    // 释放资源
                    try
                    {
                        _serialPort.Dispose();
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"释放SerialPort资源时发生错误: {ex.Message}");
                    }
                    finally
                    {
                        _serialPort = null;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"关闭串口连接时发生未知错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送二进制命令
        /// </summary>
        /// <param name="commandBytes">二进制命令数据</param>
        /// <returns>操作是否成功</returns>
        private bool SendBinaryCommand(byte[] commandBytes)
        {
            return SendRawData(commandBytes);
        }

        /// <summary>
        /// 发送文本命令
        /// </summary>
        /// <param name="command">文本命令</param>
        /// <returns>是否成功</returns>
        private bool SendTextCommand(string command)
        {
            if (!_isRunning || _serialPort?.IsOpen != true)
            {
                WriteLog("串口连接未建立，无法发送文本命令");
                return false;
            }

            try
            {
                lock (_lockObject)
                {
                    _serialPort.WriteLine(command);

                    _statistics.SentCommandsCount++;
                    _statistics.LastActivityTime = DateTime.Now;

                    WriteLog($"发送文本命令成功: {command.Replace("\r", "\\r").Replace("\n", "\\n")}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetSendTextFailureErrorCode(), $"发送文本命令失败: {ex.Message}", ex));
                return false;
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 串口数据接收事件处理
        /// </summary>
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_serialPort?.IsOpen != true)
                    return;

                // 读取所有可用的字节数据
                int bytesToRead = _serialPort.BytesToRead;
                if (bytesToRead > 0)
                {
                    byte[] buffer = new byte[bytesToRead];
                    int bytesRead = _serialPort.Read(buffer, 0, bytesToRead);

                    if (bytesRead > 0)
                    {
                        byte[] receivedData = new byte[bytesRead];
                        Array.Copy(buffer, receivedData, bytesRead);

                        // 使用基类的消息处理机制
                        ProcessRawData(receivedData);
                    }
                }
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetReceiveErrorCode(),
                    $"接收数据时发生错误: {ex.Message}", ex));
            }
        }

        /// <summary>
        /// 串口错误事件处理
        /// </summary>
        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            _statistics.ErrorsCount++;
            OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetHardwareErrorCode(),
                $"串口发生错误: {e.EventType}"));
        }

        #endregion
    }
}