﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using AutoUpdaterDotNET;

namespace Common
{
    public class AutoUpdate
    {
        public void AutoUpdaterStarter(string url, string title)
        {



            //通过将其分配给InstalledVersion字段来提供自己的版本
            //AutoUpdater.InstalledVersion = new Version("1.2");

            //查看中文版本
            Thread.CurrentThread.CurrentCulture = Thread.CurrentThread.CurrentUICulture = System.Globalization.CultureInfo.CreateSpecificCulture("zh");

            //显示自定义的应用程序标题
            AutoUpdater.AppTitle = title;

            //是否显示“稍后提醒”按钮
            AutoUpdater.ShowRemindLaterButton = true;

            //是否允许用户自主选择稍后提醒的时间
            AutoUpdater.LetUserSelectRemindLater = false;
            AutoUpdater.RemindLaterTimeSpan = RemindLaterFormat.Minutes;
            AutoUpdater.RemindLaterAt = 30;

            //是否显示“跳过”按钮
            AutoUpdater.ShowSkipButton = false;

            //强制选项将隐藏稍后提醒，跳过和关闭按钮的标准更新对话框。
            //AutoUpdater.Mandatory = true;
            //AutoUpdater.UpdateMode = Mode.Forced;

            //为XML、更新文件和更改日志提供基本身份验证
            //BasicAuthentication basicAuthentication = new BasicAuthentication("myUserName", "myPassword");
            //AutoUpdater.BasicAuthXML = AutoUpdater.BasicAuthDownload = AutoUpdater.BasicAuthChangeLog = basicAuthentication;

            //为http web请求设置User-Agent
            //AutoUpdater.HttpUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36";

            //启用错误报告
            AutoUpdater.ReportErrors = true;

            AutoUpdater.UpdateFormSize = new System.Drawing.Size(600, 400);

            //将应用程序设定不需要管理员权限来替换旧版本
            //AutoUpdater.RunUpdateAsAdmin = false;

            //不打开下载页面，直接下载最新版本*****
            AutoUpdater.OpenDownloadPage = false;

            //设置为要下载更新文件的文件夹路径。如果没有提供，则默认为临时文件夹。
            //AutoUpdater.DownloadPath = Environment.CurrentDirectory;

            //Uncomment following line if you want to check for update synchronously.
            AutoUpdater.Synchronous = true;

            //设置zip解压路径
            AutoUpdater.InstallationPath = Environment.CurrentDirectory;

            //处理应用程序在下载完成后如何退出
            // AutoUpdater.ApplicationExitEvent += AutoUpdater_ApplicationExitEvent;

            // 自定义处理更新逻辑事件
            AutoUpdater.CheckForUpdateEvent += AutoUpdaterOnCheckForUpdateEvent;


            //Want to check for updates frequently then uncomment following lines.

            // System.Timers.Timer timer = new System.Timers.Timer
            // {
            //     // Interval = 2 * 60 * 1000
            //     Interval =  5 * 1000
            // };
            // timer.Elapsed += delegate
            // {
            //     AutoUpdater.Start(url);
            // };
            // timer.Start();

            //XML文件服务器下载地址
            AutoUpdater.Start(url);
        }


        private void AutoUpdater_ApplicationExitEvent()
        {
            Thread.Sleep(5000);
            Application.Exit();
        }

        private void AutoUpdaterOnCheckForUpdateEvent(UpdateInfoEventArgs args)
        {
            if (args.Error == null)
            {
                if (args.IsUpdateAvailable)
                {
                    DialogResult dialogResult;
                    if (args.Mandatory.Value)
                    {
                        dialogResult =
                            MessageBox.Show(
                                $@"您有新的版本 {args.CurrentVersion} 可用，当前使用的是{
                                        args.InstalledVersion
                                    } 版本，这是必需的更新。",
                                @"发现可用更新",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information);
                    }
                    else
                    {
                        dialogResult =
                            MessageBox.Show(
                                $@"您有新的版本 {args.CurrentVersion} 可用，当前使用的是{args.InstalledVersion} 版本，
                                您现在要更新应用程序吗？", @"发现可用更新",
                                MessageBoxButtons.YesNo,
                                MessageBoxIcon.Information);
                    }


                    if (dialogResult.Equals(DialogResult.Yes) || dialogResult.Equals(DialogResult.OK))
                    {
                        try
                        {
                            //You can use Download Update dialog used by AutoUpdater.NET to download the update.

                            if (AutoUpdater.DownloadUpdate(args))
                            {
                                Application.Exit();
                            }
                        }
                        catch (Exception exception)
                        {
                            MessageBox.Show(exception.Message, exception.GetType().ToString(), MessageBoxButtons.OK,
                                MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    // MessageBox.Show(@"There is no update available. Please try again later.", @"Update Unavailable",
                    //     MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                if (args.Error is System.Net.WebException)
                {
                    MessageBox.Show(
                        @"更新服务器访问失败，请检查您的网络情况，并稍后重试。",
                        @"更新检测失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    MessageBox.Show(args.Error.Message,
                        args.Error.GetType().ToString(), MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
        }
    }
}
