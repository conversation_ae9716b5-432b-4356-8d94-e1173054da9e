﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器统计信息类
    /// </summary>
    public class ConnectorStatistics
    {
        /// <summary>
        /// 连接器启动时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 已接收消息数
        /// </summary>
        public int ReceivedMessagesCount { get; set; }

        /// <summary>
        /// 已发送命令数
        /// </summary>
        public int SentCommandsCount { get; set; }

        /// <summary>
        /// 错误计数
        /// </summary>
        public int ErrorsCount { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityTime { get; set; }
    }

}
