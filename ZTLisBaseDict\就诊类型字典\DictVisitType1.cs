using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictVisitType1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictVisitType _bllDictVisitType = new BllDictVisitType();

        public DictVisitType1()
        {
            InitializeComponent();
        }

        private void DictVisitType1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }

        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "VisitTypeCode", 120, "中", "", false);
            myGrid1.Init_Column("名称", "VisitTypeName", 160, "左", "", false);
            myGrid1.Init_Column("简称", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("英文名称", "EnglishName", 150, "左", "", false);
            myGrid1.Init_Column("HIS编码", "HisVisitTypeCode", 150, "左", "", false);
            myGrid1.Init_Column("备注", "Description", 300, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;
        }

        private void DataInit()
        {
            base.MyTable = _bllDictVisitType.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["VisitTypeId"] };
            base.MyTable.Columns["VisitTypeId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictVisitType2 vform = new DictVisitType2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除就诊类型：" + this.myGrid1.Columns["VisitTypeName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictVisitType.Delete(int.Parse(base.MyRow["VisitTypeId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("VisitTypeCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("VisitTypeName", new Common.MdlExcel(15, "", "名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "简称", true));
            colconfig.Add("EnglishName", new Common.MdlExcel(15, "", "英文名称", true));
            colconfig.Add("HisVisitTypeCode", new Common.MdlExcel(15, "", "HIS编码", true));
            colconfig.Add("Description", new Common.MdlExcel(20, "", "备注", true));
            Common.ExcelHelper.Export(base.MyTable, "就诊类型字典", colconfig);
        }

        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictVisitType mdlDictVisitType = new MdlDictVisitType();
                    if (row["名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictVisitType.VisitTypeCode = row["编码"] + "";
                    mdlDictVisitType.VisitTypeName = row["名称"] + "";
                    mdlDictVisitType.PinYin = base.MyChs2Spell.GetPy(row["名称"].ToString());
                    mdlDictVisitType.EnglishName = row["英文名称"] + "";
                    mdlDictVisitType.HisVisitTypeCode = row["HIS编码"] + "";
                    mdlDictVisitType.Description = row["备注"] + "";
                    mdlDictVisitType.Status = true;
                    mdlDictVisitType.CreatedAt = DateTime.Now;
                    mdlDictVisitType.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictVisitType).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictVisitType, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["VisitTypeId"] = _bllDictVisitType.Add(mdlDictVisitType);
                    base.MyTable.Rows.Add(newRow);
                    UpdateData[i - 1] = "√";
                    i = i + 1;
                }
                base.MyTable.AcceptChanges();
                MessageBox.Show("导入成功：" + successCnt + "条    失败：" + failCnt + "条", "提示");
                this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            }
        }

        private void DataEnable(bool Status)
        {
            if (myGrid1.RowCount == 0) return;
            Model.MdlDictVisitType model1 = new Model.MdlDictVisitType();
            model1 = _bllDictVisitType.GetModel(int.Parse(base.MyRow["VisitTypeId"].ToString()));
            model1.Status = Status;
            base.MyRow["Status"] = Status;
            base.MyRow.AcceptChanges();
            _bllDictVisitType.Update(model1);
        }

        #endregion

        #region 控件动作

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdEdit_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(false);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }

        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("VisitTypeCode+VisitTypeName+PinYin+EnglishName", TxtFilter.Text.Trim());
        }

        #endregion
    }
}