﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictDepartment.cs
*
* 功 能： N/A
* 类 名： MdlDictDepartment
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:11   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 科室字典表
	/// </summary>
	[Serializable]
	public partial class MdlDictDepartment
	{
		public MdlDictDepartment()
		{}
		#region Model
		private int _deptid;
		private string _deptcode;
		private string _deptname;
		private string _hisdeptcode;
		private string _pinyin;
		private int? _parentdeptid;
		private string _depttype;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int DeptId
		{
			set{ _deptid=value;}
			get{return _deptid;}
		}
		/// <summary>
		/// LIS 科室编码
		/// </summary>
		public string DeptCode
		{
			set{ _deptcode=value;}
			get{return _deptcode;}
		}
		/// <summary>
		/// 科室中文名称
		/// </summary>
		public string DeptName
		{
			set{ _deptname=value;}
			get{return _deptname;}
		}
		/// <summary>
		/// HIS 科室编码（nvarchar(200)）
		/// </summary>
		public string HisDeptCode
		{
			set{ _hisdeptcode=value;}
			get{return _hisdeptcode;}
		}
		/// <summary>
		/// 拼音/首字母
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 上级科室ID (递归)
		/// </summary>
		public int? ParentDeptId
		{
			set{ _parentdeptid=value;}
			get{return _parentdeptid;}
		}
		/// <summary>
		/// 科室类型（临床/医技/行政）
		/// </summary>
		public string DeptType
		{
			set{ _depttype=value;}
			get{return _depttype;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 状态 1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

