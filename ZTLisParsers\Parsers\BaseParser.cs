using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZTLisEnum;
using Model;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// 解析器基类，提供通用功能
    /// </summary>
    public abstract class BaseParser : IParser
    {
        #region 公共属性

        /// <summary>
        /// 解析器名称
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// 支持的协议类型
        /// </summary>
        public abstract ProtocolType SupportedProtocol { get; }

        #endregion

        #region IParser接口实现

        /// <summary>
        /// 解析原始报文
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <param name="instrumentId">仪器ID</param>
        /// <returns>解析结果</returns>
        public async Task<ParseResult> ParseAsync(string rawMessage, int instrumentId)
        {
            try
            {
                // 验证输入参数
                if (string.IsNullOrEmpty(rawMessage))
                {
                    return ParseResult.Failure(rawMessage, "原始报文为空");
                }

                // 验证报文格式
                if (!ValidateMessage(rawMessage))
                {
                    return ParseResult.Failure(rawMessage, "报文格式验证失败");
                }

                // 执行具体的解析逻辑
                var results = await ParseMessageAsync(rawMessage, instrumentId);

                // 设置仪器ID
                foreach (var result in results)
                {
                    result.InstrumentId = instrumentId;
                }

                return ParseResult.Success(rawMessage, results);
            }
            catch (Exception ex)
            {
                return ParseResult.Failure(rawMessage, $"解析异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证报文格式（子类可重写）
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <returns>是否有效</returns>
        public virtual bool ValidateMessage(string rawMessage)
        {
            return !string.IsNullOrEmpty(rawMessage);
        }

        #endregion

        #region 抽象方法（子类必须实现）

        /// <summary>
        /// 具体的解析逻辑（子类必须实现）
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <param name="instrumentId">仪器ID</param>
        /// <returns>解析结果列表</returns>
        protected abstract Task<List<MdlLabInstrResultQ>> ParseMessageAsync(string rawMessage, int instrumentId);

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建结果对象的辅助方法
        /// 
        /// 使用说明：
        /// - 对于检验结果，使用 resultType = 0 或调用 CreateLabResult 方法
        /// - 对于标本参数（如HIL指数），使用 resultType = 1 或调用 CreateSpecimenParam 方法
        /// 
        /// 示例：
        /// // 创建检验结果
        /// var labResult = CreateResult(instrumentId, barcode, "GLU", "5.6", "N", 0);
        /// 
        /// // 创建标本参数
        /// var specimenParam = CreateResult(instrumentId, barcode, "H_INDEX", "2", "", 1);
        /// </summary>
        /// <param name="instrumentId">仪器ID</param>
        /// <param name="barcode">样本条码</param>
        /// <param name="instrItemCode">仪器项目代码</param>
        /// <param name="resultValue">结果值</param>
        /// <param name="instrFlag">仪器标志</param>
        /// <param name="resultType">结果类型：0=检验结果 1=标本参数</param>
        /// <param name="measuredAt">测量时间</param>
        /// <returns>结果对象</returns>
        protected MdlLabInstrResultQ CreateResult(int instrumentId, string barcode, string instrItemCode, string resultValue, string instrFlag, int resultType = 0, DateTime? measuredAt = null)
        {
            return new MdlLabInstrResultQ
            {
                InstrumentId = instrumentId,
                Barcode = barcode,
                InstrItemCode = instrItemCode,
                ResultValue = resultValue,
                InstrFlag = instrFlag,
                ResultType = resultType,
                MeasuredAt = measuredAt ?? DateTime.Now,
                ProcessedFlag = 0
            };
        }

        /// <summary>
        /// 创建检验结果对象的便利方法
        /// </summary>
        /// <param name="instrumentId">仪器ID</param>
        /// <param name="barcode">样本条码</param>
        /// <param name="instrItemCode">仪器项目代码</param>
        /// <param name="resultValue">结果值</param>
        /// <param name="instrFlag">仪器标志</param>
        /// <param name="measuredAt">测量时间</param>
        /// <returns>检验结果对象</returns>
        protected MdlLabInstrResultQ CreateLabResult(int instrumentId, string barcode, string instrItemCode, string resultValue, string instrFlag, DateTime? measuredAt = null)
        {
            return CreateResult(instrumentId, barcode, instrItemCode, resultValue, instrFlag, 0, measuredAt);
        }

        /// <summary>
        /// 创建标本参数对象的便利方法
        /// </summary>
        /// <param name="instrumentId">仪器ID</param>
        /// <param name="barcode">样本条码</param>
        /// <param name="instrItemCode">仪器项目代码（标本参数代码）</param>
        /// <param name="resultValue">参数值</param>
        /// <param name="instrFlag">仪器标志</param>
        /// <param name="measuredAt">测量时间</param>
        /// <returns>标本参数对象</returns>
        protected MdlLabInstrResultQ CreateSpecimenParam(int instrumentId, string barcode, string instrItemCode, string resultValue, string instrFlag, DateTime? measuredAt = null)
        {
            return CreateResult(instrumentId, barcode, instrItemCode, resultValue, instrFlag, 1, measuredAt);
        }

        /// <summary>
        /// 写入日志的辅助方法
        /// </summary>
        /// <param name="message">日志消息</param>
        protected virtual void WriteLog(string message)
        {
            string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{Name}] {message}";
            Common.Log.LogHelper.Info(logMessage);
        }

        #endregion
    }
}