﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CustomControl</RootNamespace>
    <AssemblyName>CustomControl</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20141.19619, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.6.20141.828, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.Classic.4, Version=4.6.20141.828, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20141.33356, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.1.20141.251, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1SuperTooltip.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20141.61348, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.TreeView.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DoubleDateEdit.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DoubleDateEdit.Designer.cs">
      <DependentUpon>DoubleDateEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="Hook.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Hook.Designer.cs">
      <DependentUpon>Hook.cs</DependentUpon>
    </Compile>
    <Compile Include="ImageButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="MyButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MyButton.Designer.cs">
      <DependentUpon>MyButton.cs</DependentUpon>
    </Compile>
    <Compile Include="MyDateEdit.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MyDateEdit.Designer.cs">
      <DependentUpon>MyDateEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="MyDtComobo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MyDtComobo.Designer.cs">
      <DependentUpon>MyDtComobo.cs</DependentUpon>
    </Compile>
    <Compile Include="MyFlexGrid.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="MyFlexGrid.Designer.cs">
      <DependentUpon>MyFlexGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="MyGrid.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MyGrid.Designer.cs">
      <DependentUpon>MyGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="MyNumericEdit.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MyNumericEdit.Designer.cs">
      <DependentUpon>MyNumericEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="MySingleComobo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MySingleComobo.Designer.cs">
      <DependentUpon>MySingleComobo.cs</DependentUpon>
    </Compile>
    <Compile Include="MyTextBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MyTextBox.Designer.cs">
      <DependentUpon>MyTextBox.cs</DependentUpon>
    </Compile>
    <Compile Include="MyTreeView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\AbstractProgressBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\AbstractProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\BarberPoleProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\BevelledGradientProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\BevelledProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\CandyCaneBackgroundPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\CandyCaneProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\ChainedGlossPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\ColorTools.cs" />
    <Compile Include="ProgressODoom\DualProgressBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\FlatGlossPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\FruityLoopsBackgroundPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\FruityLoopsProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\GradientBackgroundPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\GradientGlossPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\HSV.cs" />
    <Compile Include="ProgressODoom\IAnimatedProgressPainter.cs" />
    <Compile Include="ProgressODoom\IGlossPainter.cs" />
    <Compile Include="ProgressODoom\IProgressBackgroundPainter.cs" />
    <Compile Include="ProgressODoom\IProgressBorderPainter.cs" />
    <Compile Include="ProgressODoom\IProgressPainter.cs" />
    <Compile Include="ProgressODoom\JavaProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\MetalProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\MiddleGlossPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\PlainBackgroundPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\PlainBorderPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\PlainProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\ProgressBarEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\RarBackgroundPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\RarBorderPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\RarProgressBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\RarProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\RoundGlossPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\StripedProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\StyledBorderPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProgressODoom\WaveProgressPainter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="ScrollPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ScrollPanel.Designer.cs">
      <DependentUpon>ScrollPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="SkinForm\AntiAliasGraphics.cs" />
    <Compile Include="SkinForm\ControlBoxManager.cs" />
    <Compile Include="SkinForm\ControlBoxState.cs" />
    <Compile Include="SkinForm\ControlBoxStyle.cs" />
    <Compile Include="SkinForm\ControlPaintEx.cs" />
    <Compile Include="SkinForm\GraphicsPathHelper.cs" />
    <Compile Include="SkinForm\MouseOperate.cs" />
    <Compile Include="SkinForm\NativeMethods.cs" />
    <Compile Include="SkinForm\RenderHelper.cs" />
    <Compile Include="SkinForm\RoundStyle.cs" />
    <Compile Include="SkinForm\SkinForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SkinForm\SkinFormBackgroundRenderEvent.cs" />
    <Compile Include="SkinForm\SkinFormBorderRenderEvent.cs" />
    <Compile Include="SkinForm\SkinFormCaptionRenderEvent.cs" />
    <Compile Include="SkinForm\SkinFormColorTable.cs" />
    <Compile Include="SkinForm\SkinFormControlBoxRenderEvent.cs" />
    <Compile Include="SkinForm\SkinFormProfessionalRenderer.cs" />
    <Compile Include="SkinForm\SkinFormRenderer.cs" />
    <Compile Include="TabControl\TabControlEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TabControl\TabNativeMethods.cs" />
    <Compile Include="WaitWindow\WaitWindow.cs" />
    <Compile Include="WaitWindow\WaitWindowEventArgs.cs" />
    <Compile Include="WaitWindow\WaitWindowGUI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="WaitWindow\WaitWindowGUI.Designer.cs">
      <DependentUpon>WaitWindowGUI.cs</DependentUpon>
    </Compile>
    <Compile Include="Wizard\Header.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Wizard\HeaderDesigner.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Wizard\InfoContainer.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Wizard\InfoContainerDesigner.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Wizard\InfoPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Wizard\InfoPageDesigner.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Wizard\PageCollection.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Wizard\PageEventHandler.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Wizard\Wizard.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Wizard\WizardDesigner.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Wizard\WizardPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Wizard\WizardPageDesigner.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="提示框\MessageNotify.cs" />
    <Compile Include="提示框\MessageNotifyForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="提示框\MessageNotifyForm.Designer.cs">
      <DependentUpon>MessageNotifyForm.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="DoubleDateEdit.resx">
      <DependentUpon>DoubleDateEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyButton.resx">
      <DependentUpon>MyButton.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyDateEdit.resx">
      <DependentUpon>MyDateEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyDtComobo.resx">
      <DependentUpon>MyDtComobo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyGrid.resx">
      <DependentUpon>MyGrid.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyNumericEdit.resx">
      <DependentUpon>MyNumericEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MySingleComobo.resx">
      <DependentUpon>MySingleComobo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyTextBox.resx">
      <DependentUpon>MyTextBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MyTreeView.resx">
      <DependentUpon>MyTreeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ScrollPanel.resx">
      <DependentUpon>ScrollPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TabControl\TabControlEx.resx">
      <DependentUpon>TabControlEx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="WaitWindow\WaitWindowGUI.resx">
      <DependentUpon>WaitWindowGUI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Wizard\Header.resx">
      <DependentUpon>Header.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Wizard\InfoContainer.resx">
      <DependentUpon>InfoContainer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Wizard\InfoPage.resx">
      <DependentUpon>InfoPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Wizard\Wizard.resx">
      <DependentUpon>Wizard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Wizard\WizardPage.resx">
      <DependentUpon>WizardPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="提示框\MessageNotifyForm.resx">
      <DependentUpon>MessageNotifyForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="ProgressODoom\Icons\BarberPoleProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\BevelledGradientProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\BevelledProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\CandyCaneBackgroundPainter.ico" />
    <Content Include="ProgressODoom\Icons\CandyCaneProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\DualProgressBar.ico" />
    <Content Include="ProgressODoom\Icons\FlatGlossPainter.ico" />
    <Content Include="ProgressODoom\Icons\FruityLoopsBackgroundPainter.ico" />
    <Content Include="ProgressODoom\Icons\FruityLoopsProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\GradientBackgroundPainter.ico" />
    <Content Include="ProgressODoom\Icons\GradientGlossPainter.ico" />
    <Content Include="ProgressODoom\Icons\JavaProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\MetalProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\MiddleGlossPainter.ico" />
    <Content Include="ProgressODoom\Icons\PlainBackgroundPainter.ico" />
    <Content Include="ProgressODoom\Icons\PlainBorderPainter.ico" />
    <Content Include="ProgressODoom\Icons\PlainProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\ProgressBarEx.ico" />
    <Content Include="ProgressODoom\Icons\RarBackgroundPainter.ico" />
    <Content Include="ProgressODoom\Icons\RarBorderPainter.ico" />
    <Content Include="ProgressODoom\Icons\RarProgressBar.ico" />
    <Content Include="ProgressODoom\Icons\RarProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\RoundGlossPainter.ico" />
    <Content Include="ProgressODoom\Icons\StripedProgressPainter.ico" />
    <Content Include="ProgressODoom\Icons\StyledBorderPainter.ico" />
    <Content Include="ProgressODoom\Icons\WaveProgressPainter.ico" />
    <None Include="Resources\重置.png" />
    <Content Include="Wizard\Wizard.bmp" />
    <Content Include="Wizard\wizBigOnlineFolder.gif" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>