﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictSampleType2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictSampleType _bllDictSampleType = new BllDictSampleType();
        Model.MdlDictSampleType _mdlDictSampleType = new MdlDictSampleType();

        public DictSampleType2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtContainerType.GotFocus += new System.EventHandler(base.InputCn);
            TxtPreTreatment.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictSampleType2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictSampleType.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            TxtContainerType.Text = "";
            NumVolume.Value = null;
            TxtPreTreatment.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["SampleTypeCode"] + "";
            TxtName.Text = row["SampleTypeName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            TxtContainerType.Text = row["ContainerType"] + "";
            NumVolume.Value = row["DefaultVolumeMl"];
            TxtPreTreatment.Text = row["PreTreatment"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写标本类型名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写标本类型编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["SampleTypeCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["SampleTypeName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictSampleType.GetRecordCount($"SampleTypeCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictSampleType.GetRecordCount($"SampleTypeName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictSampleType.SampleTypeCode = TxtCode.Text.Trim();
            _mdlDictSampleType.SampleTypeName = TxtName.Text.Trim();
            _mdlDictSampleType.PinYin = TxtJc.Text.Trim();
            _mdlDictSampleType.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictSampleType.ContainerType = TxtContainerType.Text.Trim();
            _mdlDictSampleType.DefaultVolumeMl = NumVolume.Value == DBNull.Value ? null : (decimal?)NumVolume.Value;
            _mdlDictSampleType.PreTreatment = TxtPreTreatment.Text.Trim();
            _mdlDictSampleType.Status = true;
            _mdlDictSampleType.CreatedAt = DateTime.Now;
            _mdlDictSampleType.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictSampleType, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["SampleTypeId"] = _bllDictSampleType.Add(_mdlDictSampleType);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictSampleType = _bllDictSampleType.GetModel(int.Parse(base.MyRow["SampleTypeId"].ToString()));
            _mdlDictSampleType.SampleTypeCode = TxtCode.Text.Trim();
            _mdlDictSampleType.SampleTypeName = TxtName.Text.Trim();
            _mdlDictSampleType.PinYin = TxtJc.Text.Trim();
            _mdlDictSampleType.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictSampleType.ContainerType = TxtContainerType.Text.Trim();
            _mdlDictSampleType.DefaultVolumeMl = NumVolume.Value == DBNull.Value ? null : (decimal?)NumVolume.Value;
            _mdlDictSampleType.PreTreatment = TxtPreTreatment.Text.Trim();
            _mdlDictSampleType.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictSampleType, base.MyRow);

            //数据保存
            try
            {
                _bllDictSampleType.Update(_mdlDictSampleType);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 控件动作
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert == true)
                {
                    this.DataAdd();
                }
                else
                {
                    this.DataEdit();
                }
            }

        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }

        #endregion

    }
}
