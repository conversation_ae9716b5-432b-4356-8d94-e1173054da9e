using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    /// <summary>
    /// 标本参数控件
    /// </summary>
    public partial class ComboSpecimenParam : MyDtComobo
    {
        public ComboSpecimenParam()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            strWhere += " and DictSpecimenParam.Status=1";
            BLL.BllDictSpecimenParam _bllDictSpecimenParam = new BllDictSpecimenParam();
            this.DataView = _bllDictSpecimenParam.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("ParamName", "参数名称", 160, "左");
            this.Init_Colum("ParamCode", "参数编码", 100, "左");
            this.Init_Colum("PinYin", "拼音", 0, "左");
            this.Init_Colum("EnglishName", "英文名", 0, "左");
            this.Init_Colum("Unit", "单位", 60, "左");
            this.DisplayMember = "ParamName";
            this.ValueMember = "ParamId";
            int width = 350;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "ParamCode+isnull(PinYin,'')+isnull(EnglishName,'')+ParamName";
        }
    }
}