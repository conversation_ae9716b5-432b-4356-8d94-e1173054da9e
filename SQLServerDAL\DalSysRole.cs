﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysRole.cs
*
* 功 能： N/A
* 类 名： DalSysRole
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:35   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysRole
	/// </summary>
	public partial class DalSysRole:IDalSysRole
	{
		public DalSysRole()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string RoleCode)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysRole");
			strSql.Append(" where RoleCode=@RoleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2)			};
			parameters[0].Value = RoleCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}

        public bool ExistsName(string Name)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from SysRole ");
            strSql.Append(" where RoleName=@RoleName");
            SqlParameter[] parameters = {
                new SqlParameter ("@RoleName",SqlDbType.VarChar,50)
            };
            parameters[0].Value = Name;
            return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.MdlSysRole model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysRole(");
			strSql.Append("RoleCode,RoleName,RolePinYin,RoleWuBi,RoleMemo)");
			strSql.Append(" values (");
			strSql.Append("@RoleCode,@RoleName,@RolePinYin,@RoleWuBi,@RoleMemo)");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@RoleName", SqlDbType.VarChar,50),
					new SqlParameter("@RolePinYin", SqlDbType.VarChar,50),
					new SqlParameter("@RoleWuBi", SqlDbType.VarChar,50),
					new SqlParameter("@RoleMemo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.RoleCode;
			parameters[1].Value = model.RoleName;
			parameters[2].Value = model.RolePinYin;
			parameters[3].Value = model.RoleWuBi;
			parameters[4].Value = model.RoleMemo;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysRole model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysRole set ");
			strSql.Append("RoleName=@RoleName,");
			strSql.Append("RolePinYin=@RolePinYin,");
			strSql.Append("RoleWuBi=@RoleWuBi,");
			strSql.Append("RoleMemo=@RoleMemo");
			strSql.Append(" where RoleCode=@RoleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleName", SqlDbType.VarChar,50),
					new SqlParameter("@RolePinYin", SqlDbType.VarChar,50),
					new SqlParameter("@RoleWuBi", SqlDbType.VarChar,50),
					new SqlParameter("@RoleMemo", SqlDbType.VarChar,50),
					new SqlParameter("@RoleCode", SqlDbType.Char,2)};
			parameters[0].Value = model.RoleName;
			parameters[1].Value = model.RolePinYin;
			parameters[2].Value = model.RoleWuBi;
			parameters[3].Value = model.RoleMemo;
			parameters[4].Value = model.RoleCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string RoleCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRole ");
			strSql.Append(" where RoleCode=@RoleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2)			};
			parameters[0].Value = RoleCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string RoleCodelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRole ");
			strSql.Append(" where RoleCode in ("+RoleCodelist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
	    public Model.MdlSysRole GetModelName(string RoleName)
	    {

	        StringBuilder strSql = new StringBuilder();
	        strSql.Append("select  top 1 RoleCode,RoleName,RolePinYin,RoleWuBi,RoleMemo from SysRole ");
	        strSql.Append(" where RoleName=@RoleName ");
	        SqlParameter[] parameters = {
	            new SqlParameter("@RoleName", SqlDbType.VarChar,50)         };
	        parameters[0].Value = RoleName;

	        Model.MdlSysRole model = new Model.MdlSysRole();
	        DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
	        if (ds.Tables[0].Rows.Count > 0)
	        {
	            return DataRowToModel(ds.Tables[0].Rows[0]);
	        }
	        else
	        {
	            return null;
	        }
	    }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlSysRole GetModel(string RoleCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 RoleCode,RoleName,RolePinYin,RoleWuBi,RoleMemo from SysRole ");
			strSql.Append(" where RoleCode=@RoleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2)			};
			parameters[0].Value = RoleCode;

			Model.MdlSysRole model=new Model.MdlSysRole();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRole DataRowToModel(DataRow row)
		{
			Model.MdlSysRole model=new Model.MdlSysRole();
			if (row != null)
			{
				if(row["RoleCode"]!=null)
				{
					model.RoleCode=row["RoleCode"].ToString();
				}
				if(row["RoleName"]!=null)
				{
					model.RoleName=row["RoleName"].ToString();
				}
				if(row["RolePinYin"]!=null)
				{
					model.RolePinYin=row["RolePinYin"].ToString();
				}
				if(row["RoleWuBi"]!=null)
				{
					model.RoleWuBi=row["RoleWuBi"].ToString();
				}
				if(row["RoleMemo"]!=null)
				{
					model.RoleMemo=row["RoleMemo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select RoleCode,RoleName,RolePinYin,RoleWuBi,RoleMemo ");
			strSql.Append(" FROM SysRole ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" RoleCode,RoleName,RolePinYin,RoleWuBi,RoleMemo ");
			strSql.Append(" FROM SysRole ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysRole ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.RoleCode desc");
			}
			strSql.Append(")AS Row, T.*  from SysRole T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysRole";
			parameters[1].Value = "RoleCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

	    #region  ExtensionMethod
	    /// <summary>
	    ///生成最大编码
	    /// </summary>
	    public string MaxCode()
	    {
	        string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("select max(RoleCode) from SysRole", 2));
	        return max;
	    }

	    #endregion  ExtensionMethod
    }
}

