﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabSpecimen.cs
*
* 功 能： N/A
* 类 名： DalLabSpecimen
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
namespace IDAL
{
	/// <summary>
	/// 接口层检验标本信息表
	/// </summary>
	public interface IDalLabSpecimen
	{
		#region  成员方法
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		bool Exists(long SpecimenId);
		/// <summary>
		/// 增加一条数据
		/// </summary>
		long Add(Model.MdlLabSpecimen model);
		/// <summary>
		/// 更新一条数据
		/// </summary>
		bool Update(Model.MdlLabSpecimen model);
		/// <summary>
		/// 删除一条数据
		/// </summary>
		bool Delete(long SpecimenId);
		bool DeleteList(string SpecimenIdlist);
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		Model.MdlLabSpecimen GetModel(long SpecimenId);
		Model.MdlLabSpecimen DataRowToModel(DataRow row);
		/// <summary>
		/// 获得数据列表
		/// </summary>
		DataSet GetList(string strWhere);
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		DataSet GetList(int Top, string strWhere, string filedOrder);
		int GetRecordCount(string strWhere);
		DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex);
		/// <summary>
		/// 根据分页获得数据列表
		/// </summary>
		//DataSet GetList(int PageSize,int PageIndex,string strWhere);
		#endregion  成员方法
		#region  MethodEx

		/// <summary>
		/// 根据SeqPrefix和SpecimenDate获取最大的DailySeqNo
		/// </summary>
		/// <param name="seqPrefix">序列前缀</param>
		/// <param name="specimenDate">标本日期</param>
		/// <returns>最大的DailySeqNo，如果没有记录则返回0</returns>
		int GetMaxDailySeqNo(string seqPrefix, DateTime specimenDate);

		/// <summary>
		/// 获取LabSpecimen与LabOrder关联的数据列表
		/// </summary>
		/// <param name="strWhere">查询条件</param>
		/// <returns>关联查询结果</returns>
		DataSet GetListWithLabOrder(string strWhere);

		#endregion  MethodEx
	}
}
