using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ZTLisControl
{
    public partial class SingleStopBits : CustomControl.MySingleComobo
    {
        public SingleStopBits()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        public void Init()
        {
            Additem = "1,1";
            Additem = "1.5,1.5";
            Additem = "2,2";
            DisplayColumns[1].Visible = false;
            DroupDownWidth = Width - (int)CaptainWidth;
            SelectedIndex = 0; // 默认选择1位
            ItemHeight = 20;
        }
    }
}