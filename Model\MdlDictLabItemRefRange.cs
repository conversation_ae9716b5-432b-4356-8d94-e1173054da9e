﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictLabItemRefRange.cs
*
* 功 能： N/A
* 类 名： MdlDictLabItemRefRange
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:13   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验项目参考区间表：按项目、方法学、性别、年龄段、时间段维护参考值上下限
	/// </summary>
	[Serializable]
	public partial class MdlDictLabItemRefRange
	{
		public MdlDictLabItemRefRange()
		{ }
		#region Model
		private long _refrangeid;
		private int _labitemid;
		private int? _methodid;
		private string _sex;
		private decimal? _agelower;
		private decimal? _ageupper;
		private string _ageunit = "Y";
		private string _lowerlimit;
		private string _upperlimit;
		private string _textualrefvalue;
		private string _alertlow;
		private string _alerthigh;
		private string _criticallow;
		private string _criticalhigh;
		private string _unit;
		private bool _status = true;
		private DateTime _effectivefrom = DateTime.Now;
		private DateTime? _effectiveto;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 
		/// </summary>
		public long RefRangeId
		{
			set { _refrangeid = value; }
			get { return _refrangeid; }
		}
		/// <summary>
		/// 检验项目ID (FK→DictLabItem)
		/// </summary>
		public int LabItemId
		{
			set { _labitemid = value; }
			get { return _labitemid; }
		}
		/// <summary>
		/// 方法学ID (FK)，NULL=通用
		/// </summary>
		public int? MethodId
		{
			set { _methodid = value; }
			get { return _methodid; }
		}
		/// <summary>
		/// 性别：M 男；F 女；N 不区分
		/// </summary>
		public string Sex
		{
			set { _sex = value; }
			get { return _sex; }
		}
		/// <summary>
		/// 年龄下限 (岁)
		/// </summary>
		public decimal? AgeLower
		{
			set { _agelower = value; }
			get { return _agelower; }
		}
		/// <summary>
		/// 年龄上限 (岁)
		/// </summary>
		public decimal? AgeUpper
		{
			set { _ageupper = value; }
			get { return _ageupper; }
		}
		/// <summary>
		/// 年龄单位 (Y=年, M=月, W=周, D=天, H=小时)
		/// </summary>
		public string AgeUnit
		{
			set { _ageunit = value; }
			get { return _ageunit; }
		}
		/// <summary>
		/// 参考值下限
		/// </summary>
		public string LowerLimit
		{
			set { _lowerlimit = value; }
			get { return _lowerlimit; }
		}
		/// <summary>
		/// 参考值上限
		/// </summary>
		public string UpperLimit
		{
			set { _upperlimit = value; }
			get { return _upperlimit; }
		}
		/// <summary>
		/// 文本型参考值
		/// </summary>
		public string TextualRefValue
		{
			set { _textualrefvalue = value; }
			get { return _textualrefvalue; }
		}
		/// <summary>
		/// 警告下限
		/// </summary>
		public string AlertLow
		{
			set { _alertlow = value; }
			get { return _alertlow; }
		}
		/// <summary>
		/// 警告上限
		/// </summary>
		public string AlertHigh
		{
			set { _alerthigh = value; }
			get { return _alerthigh; }
		}
		/// <summary>
		/// 危急下限
		/// </summary>
		public string CriticalLow
		{
			set { _criticallow = value; }
			get { return _criticallow; }
		}
		/// <summary>
		/// 危急上限
		/// </summary>
		public string CriticalHigh
		{
			set { _criticalhigh = value; }
			get { return _criticalhigh; }
		}
		/// <summary>
		/// 单位
		/// </summary>
		public string Unit
		{
			set { _unit = value; }
			get { return _unit; }
		}
		/// <summary>
		/// 状态：1=启用，0=停用
		/// </summary>
		public bool Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 生效时间
		/// </summary>
		public DateTime EffectiveFrom
		{
			set { _effectivefrom = value; }
			get { return _effectivefrom; }
		}
		/// <summary>
		/// 失效时间 (NULL=永久有效)
		/// </summary>
		public DateTime? EffectiveTo
		{
			set { _effectiveto = value; }
			get { return _effectiveto; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

