using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictLabItem2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictLabItem _bllDictLabItem = new BllDictLabItem();
        Model.MdlDictLabItem _mdlDictLabItem = new MdlDictLabItem();

        public DictLabItem2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtReportName.GotFocus += new System.EventHandler(base.InputCn);
            comboUnit1.GotFocus += new System.EventHandler(base.InputEn);
            TxtLoincCode.GotFocus += new System.EventHandler(base.InputEn);
        }

        private void DictLabItem2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化控件
            ComboItemCategory.Init();
            ComboSampleType.Init();
            ComboMethod.Init();
            ChkIsCalculated.Checked = false;
            NumPrecision.CustomFormat = "0";
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictLabItem.MaxCode(6);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            TxtReportName.Text = "";
            ComboItemCategory.SelectedIndex = 0;
            ComboSampleType.SelectedIndex = 0;
            comboUnit1.SelectedIndex = -1;
            NumPrecision.Value = null;
            ComboMethod.SelectedIndex = 0;
            TxtLoincCode.Text = "";
            ChkIsCalculated.Checked = false;
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["LabItemCode"] + "";
            TxtName.Text = row["LabItemName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            TxtReportName.Text = row["ReportName"] + "";

            // 设置下拉框值
            if (row["ItemCategoryId"] != DBNull.Value)
                ComboItemCategory.SelectedValue = row["ItemCategoryId"];
            if (row["SampleTypeId"] != DBNull.Value)
                ComboSampleType.SelectedValue = row["SampleTypeId"];
            if (row["DefaultMethodId"] != DBNull.Value)
                ComboMethod.SelectedValue = row["DefaultMethodId"];

            comboUnit1.Text = row["DefaultUnit"] + "";
            NumPrecision.Value = row["Precision"];
            TxtLoincCode.Text = row["LoincCode"] + "";

            if (row["IsCalculated"] != DBNull.Value)
                ChkIsCalculated.Checked = (bool)row["IsCalculated"];

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写检验项目名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写检验项目编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["LabItemCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["LabItemName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictLabItem.GetRecordCount($"LabItemCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictLabItem.GetRecordCount($"LabItemName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictLabItem.LabItemCode = TxtCode.Text.Trim();
            _mdlDictLabItem.LabItemName = TxtName.Text.Trim();
            _mdlDictLabItem.PinYin = TxtJc.Text.Trim();
            _mdlDictLabItem.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictLabItem.ReportName = TxtReportName.Text.Trim();
            _mdlDictLabItem.ItemCategoryId = ComboItemCategory.SelectedValue == null ? 0 : (int)ComboItemCategory.SelectedValue;
            _mdlDictLabItem.SampleTypeId = ComboSampleType.SelectedValue == null ? 0 : (int)ComboSampleType.SelectedValue;
            _mdlDictLabItem.DefaultUnit = comboUnit1.Text.Trim();
            _mdlDictLabItem.Precision = Common.ConvertObject.ObjToInt(NumPrecision.Value);
            _mdlDictLabItem.DefaultMethodId = ComboMethod.SelectedValue == null ? null : (int?)ComboMethod.SelectedValue;
            _mdlDictLabItem.LoincCode = TxtLoincCode.Text.Trim();
            _mdlDictLabItem.IsCalculated = ChkIsCalculated.Checked;
            _mdlDictLabItem.Status = true;
            _mdlDictLabItem.CreatedAt = DateTime.Now;
            _mdlDictLabItem.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabItem, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["LabItemId"] = _bllDictLabItem.Add(_mdlDictLabItem);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictLabItem = _bllDictLabItem.GetModel(int.Parse(base.MyRow["LabItemId"].ToString()));
            _mdlDictLabItem.LabItemCode = TxtCode.Text.Trim();
            _mdlDictLabItem.LabItemName = TxtName.Text.Trim();
            _mdlDictLabItem.PinYin = TxtJc.Text.Trim();
            _mdlDictLabItem.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictLabItem.ReportName = TxtReportName.Text.Trim();
            _mdlDictLabItem.ItemCategoryId = ComboItemCategory.SelectedValue == null ? 0 : (int)ComboItemCategory.SelectedValue;
            _mdlDictLabItem.SampleTypeId = ComboSampleType.SelectedValue == null ? 0 : (int)ComboSampleType.SelectedValue;
            _mdlDictLabItem.DefaultUnit = comboUnit1.Text.Trim();
            _mdlDictLabItem.Precision = Common.ConvertObject.ObjToInt(NumPrecision.Value);
            _mdlDictLabItem.DefaultMethodId = ComboMethod.SelectedValue == null ? null : (int?)ComboMethod.SelectedValue;
            _mdlDictLabItem.LoincCode = TxtLoincCode.Text.Trim();
            _mdlDictLabItem.IsCalculated = ChkIsCalculated.Checked;
            _mdlDictLabItem.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabItem, base.MyRow);

            //数据保存
            try
            {
                _bllDictLabItem.Update(_mdlDictLabItem);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            if (TxtName.Text.Trim() != "")
            {
                TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text.Trim());
                if (string.IsNullOrEmpty(TxtReportName.Text))
                    TxtReportName.Text = TxtName.Text.Trim();
            }
        }
    }
}