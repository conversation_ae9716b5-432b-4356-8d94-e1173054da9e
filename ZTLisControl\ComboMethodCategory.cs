﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    /// <summary>
    /// 给药方式
    /// </summary>
    public partial class ComboMethodCategory : MyDtComobo
    {
        public ComboMethodCategory()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            strWhere += " and Status=1";
            BLL.BllDictMethodCategory _bllDict_MethodCategory = new BllDictMethodCategory();
            this.DataView = _bllDict_MethodCategory.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("MethodCategoryName", "名称", 130, "左");
            this.Init_Colum("MethodCategoryCode", "编码", 0, "左");
            this.Init_Colum("PinYin", "拼音", 0, "左");
            this.Init_Colum("EnglishName", "五笔", 0, "左");
            this.DisplayMember = "MethodCategoryName";
            this.ValueMember = "MethodCategoryId";
            int width = 230;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "MethodCategoryCode+isnull(PinYin,'')+isnull(EnglishName,'')";

        }
    }
}

