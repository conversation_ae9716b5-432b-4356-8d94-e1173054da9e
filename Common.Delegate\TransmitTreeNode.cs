﻿/*********************************************************************************** 
*        Filename :Class1 
*        stateMsg :  
*        Date:2017/06/08 15:21:54 
 *       Machinename:DESKTOP-PJMK973 
*        Created by: <T> 董铮
*        All rights reserved 
* 
*       ChangeLog: 
*       2017/06/08 15:21:54: 
 *          Created! 
************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Common.Delegate
{
    public class TransmitTreeNode
    {
        public event Action<TreeNode> SetTreeNode;

        public void OnSetTreeNode(TreeNode node)
        {
            if (SetTreeNode != null)
            {
                SetTreeNode(node);
            }
        }


        public event Action<TreeNode,TreeNode> SetRecursionTreeNode;

        public void OnSetRecursionTreeNode(TreeNode fatherNode,TreeNode node)
        {
            if (SetRecursionTreeNode != null)
            {
                SetRecursionTreeNode(fatherNode, node);
            }
        }
    }
}
