﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{92E350A0-3691-4B8D-A07E-EBB0F10E6997}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Common</RootNamespace>
    <AssemblyName>Common</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoUpdater.NET, Version=1.6.4.0, Culture=neutral, PublicKeyToken=501435c91b35f4bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\AutoUpdater.NET.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=*******, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="ChnCharInfo">
      <HintPath>..\DLL\ChnCharInfo.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net40\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPinyinPro">
      <HintPath>..\Dll\NPinyinPro.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.3.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="ProgressODoom, Version=1.0.3305.25240, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\ProgressODoom.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml" />
    <Reference Include="WaitWindow">
      <HintPath>..\DLL\WaitWindow.dll</HintPath>
    </Reference>
    <Reference Include="zxing, Version=0.16.9.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.9\lib\net40\zxing.dll</HintPath>
    </Reference>
    <Reference Include="zxing.presentation, Version=0.16.9.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.9\lib\net40\zxing.presentation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Alert\Alert.cs" />
    <Compile Include="Algorithm\PiontByThree.cs" />
    <Compile Include="Algorithm\ThreePoint.cs" />
    <Compile Include="AutoUpdate\AutoUpdate.cs" />
    <Compile Include="Cache\CacheHelper.cs" />
    <Compile Include="ChineseNumber.cs" />
    <Compile Include="ChineseNum.cs" />
    <Compile Include="DEncrypt\SM234\GmUtil.cs" />
    <Compile Include="Form\MagneticMagnager.cs" />
    <Compile Include="LogHelper\Log.cs" />
    <Compile Include="Model\ModelDeepCopy.cs" />
    <Compile Include="Register.cs" />
    <Compile Include="Cmd\Cmd.cs" />
    <Compile Include="Convert\ConvertHelper.cs" />
    <Compile Include="Convert\ConvertObject.cs" />
    <Compile Include="Convert\StringPlus.cs" />
    <Compile Include="DataTable\DataTableToList.cs" />
    <Compile Include="DataTable\EditDataTable.cs" />
    <Compile Include="Date\DateTimeDiff.cs" />
    <Compile Include="Date\DateHelp.cs" />
    <Compile Include="Date\TimeHelp.cs" />
    <Compile Include="Date\CNDate.cs" />
    <Compile Include="DEncrypt\AES.cs" />
    <Compile Include="DEncrypt\EnCode.cs" />
    <Compile Include="DEncrypt\DEncrypt.cs" />
    <Compile Include="DEncrypt\DESEncryptNormal.cs" />
    <Compile Include="DEncrypt\DESEncryptMD5.cs" />
    <Compile Include="DEncrypt\Encrypt.cs" />
    <Compile Include="DEncrypt\HashEncode.cs" />
    <Compile Include="DEncrypt\MySecurity.cs" />
    <Compile Include="DEncrypt\RSACryption.cs" />
    <Compile Include="DEncrypt\RSAHelper.cs" />
    <Compile Include="DEncrypt\Sha1.cs" />
    <Compile Include="DEncrypt\SM234\Cipher.cs" />
    <Compile Include="DEncrypt\SM234\SM2.cs" />
    <Compile Include="DEncrypt\SM234\SM2Uitls.cs" />
    <Compile Include="DEncrypt\SM234\SM3Digest.cs" />
    <Compile Include="DEncrypt\SM234\SM3Uitls.cs" />
    <Compile Include="DEncrypt\SM234\SM4.cs" />
    <Compile Include="DEncrypt\SM234\SM4Utils.cs" />
    <Compile Include="DEncrypt\SM234\SM4_Context.cs" />
    <Compile Include="DEncrypt\UserInfo.cs" />
    <Compile Include="DistributedId\SnowFlake.cs" />
    <Compile Include="Download_Up\DownloadHelper.cs" />
    <Compile Include="Enum\EnumConvert.cs" />
    <Compile Include="ExcelHelper.cs" />
    <Compile Include="File\CsvParsingHelper.cs" />
    <Compile Include="File\DirFileHelper.cs" />
    <Compile Include="File\FileOperateHelper.cs" />
    <Compile Include="File\INIFileHelper.cs" />
    <Compile Include="Enum\EnumExtension.cs" />
    <Compile Include="ConfigHelper.cs" />
    <Compile Include="Cache\DataCache.cs" />
    <Compile Include="FTP\FTPClient.cs" />
    <Compile Include="FTP\FTPHelper.cs" />
    <Compile Include="FTP\FTPOperater.cs" />
    <Compile Include="HttpHelper\HttpCookieHelper.cs" />
    <Compile Include="HttpHelper\HttpHelper.cs" />
    <Compile Include="Image\ImageClass.cs" />
    <Compile Include="Image\ImageDown.cs" />
    <Compile Include="Image\ImageUpload.cs" />
    <Compile Include="Image\PicDeal.cs" />
    <Compile Include="Internet_Url\AdslHelper.cs" />
    <Compile Include="Internet_Url\IpHelper.cs" />
    <Compile Include="Internet_Url\NetHelper.cs" />
    <Compile Include="Internet_Url\UrlOper.cs" />
    <Compile Include="Json\Json.cs" />
    <Compile Include="LogHelper\LogHelper.cs" />
    <Compile Include="Math\Math.cs" />
    <Compile Include="Model\ModelTools.cs" />
    <Compile Include="Model\MdlExcel.cs" />
    <Compile Include="Model\MdlTreeNode.cs" />
    <Compile Include="Network\IcmpPacket.cs" />
    <Compile Include="Network\Ping.cs" />
    <Compile Include="PinYin\Chs2SpellWeb.cs" />
    <Compile Include="PinYin\EcanConvertToCh.cs" />
    <Compile Include="PinYin\PingYinHelper.cs" />
    <Compile Include="PinYin\PinYin.cs" />
    <Compile Include="POS\POSHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PinYin\Chs2Spell.cs" />
    <Compile Include="QrCode\QrCodeUtil.cs" />
    <Compile Include="Random\BaseRandom.cs" />
    <Compile Include="Reflect\Mdl2NewMdl.cs" />
    <Compile Include="RegexHelper.cs" />
    <Compile Include="DataTable\SplitDataTable.cs" />
    <Compile Include="RegisterCode\RegisterCode.cs" />
    <Compile Include="RestClient.cs" />
    <Compile Include="Service\ApplicationLoader.cs" />
    <Compile Include="SocketUtil\TestUtil.cs" />
    <Compile Include="Sound\MediaHandler.cs" />
    <Compile Include="String\QueryString.cs" />
    <Compile Include="String\StringExtension.cs" />
    <Compile Include="String\StringHelper.cs" />
    <Compile Include="System\SysHelper.cs" />
    <Compile Include="Tree\TreeNodeHelper.cs" />
    <Compile Include="Validate_Tool\FormulaExpress.cs" />
    <Compile Include="Validate_Tool\PageValidate.cs" />
    <Compile Include="Validate_Tool\Tools.cs" />
    <Compile Include="Validate_Tool\ValidateImg.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebService\WebServiceHelper.cs" />
    <Compile Include="XML\StringWriterWithEncoding.cs" />
    <Compile Include="XML\XmlHelper.cs" />
    <Compile Include="XML\XMLProcess.cs" />
    <Compile Include="XML\XMLSerialize.cs" />
    <Compile Include="Yzm\YZMHelper.cs" />
    <Compile Include="Zip\SharpZip.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="data\CodeConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>