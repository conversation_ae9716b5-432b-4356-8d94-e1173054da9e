﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DbProviderFactory;
using ZTLis.Properties;

namespace ZTLis
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            LoginForm frm = new LoginForm();

            if (frm.ShowDialog() == DialogResult.OK)
            {
                Application.Run(new MainForm());
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            string str = "";
            string strDateInfo = "出现应用程序未处理的异常：" + DateTime.Now.ToString() + "\n\r";
            Exception error = e.Exception as Exception;
            if (error != null)
                str = string.Format(strDateInfo + "异常类型：{0}" + "\n\r" + "异常消息：{1}" + "\n\r" + "异常信息：{2}" + "\n\r", error.GetType().Name, error.Message, error.StackTrace);
            else
                str = string.Format("应用程序线程错误:{0}", e);
            MessageBox.Show(str);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            string str = "";
            Exception error = e.ExceptionObject as Exception;
            string strDateInfo = "出现应用程序未处理的异常：" + DateTime.Now.ToString() + "\n\r";
            if (error != null)
                str = string.Format(strDateInfo + "Application UnhandledException:{0};" + "\n\r" + "堆栈信息:{1}", error.Message, error.StackTrace);
            else
                str = string.Format("Application UnhandledError:{0}", e);
            MessageBox.Show(str);
        }
    }
}
