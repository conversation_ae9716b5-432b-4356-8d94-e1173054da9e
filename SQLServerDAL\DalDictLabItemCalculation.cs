﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictLabItemCalculation.cs
*
* 功 能： N/A
* 类 名： DalDictLabItemCalculation
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictLabItemCalculation
	/// </summary>
	public partial class DalDictLabItemCalculation:IDalDictLabItemCalculation
	{
		public DalDictLabItemCalculation()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return Common.WinFormVar.Var.DbHelper.GetMaxID("CalculationId", "DictLabItemCalculation"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int CalculationId)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from DictLabItemCalculation");
			strSql.Append(" where CalculationId=@CalculationId");
			SqlParameter[] parameters = {
					new SqlParameter("@CalculationId", SqlDbType.Int,4)
			};
			parameters[0].Value = CalculationId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictLabItemCalculation model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into DictLabItemCalculation(");
			strSql.Append("LabItemId,FormulaExpression,Description,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@LabItemId,@FormulaExpression,@Description,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@FormulaExpression", SqlDbType.NVarChar,500),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.LabItemId;
			parameters[1].Value = model.FormulaExpression;
			parameters[2].Value = model.Description;
			parameters[3].Value = model.Status;
			parameters[4].Value = model.CreatedAt;
			parameters[5].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictLabItemCalculation model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update DictLabItemCalculation set ");
			strSql.Append("LabItemId=@LabItemId,");
			strSql.Append("FormulaExpression=@FormulaExpression,");
			strSql.Append("Description=@Description,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where CalculationId=@CalculationId");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@FormulaExpression", SqlDbType.NVarChar,500),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@CalculationId", SqlDbType.Int,4)};
			parameters[0].Value = model.LabItemId;
			parameters[1].Value = model.FormulaExpression;
			parameters[2].Value = model.Description;
			parameters[3].Value = model.Status;
			parameters[4].Value = model.CreatedAt;
			parameters[5].Value = model.UpdatedAt;
			parameters[6].Value = model.CalculationId;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int CalculationId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DictLabItemCalculation ");
			strSql.Append(" where CalculationId=@CalculationId");
			SqlParameter[] parameters = {
					new SqlParameter("@CalculationId", SqlDbType.Int,4)
			};
			parameters[0].Value = CalculationId;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string CalculationIdlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DictLabItemCalculation ");
			strSql.Append(" where CalculationId in ("+CalculationIdlist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabItemCalculation GetModel(int CalculationId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 CalculationId,LabItemId,FormulaExpression,Description,Status,CreatedAt,UpdatedAt from DictLabItemCalculation ");
			strSql.Append(" where CalculationId=@CalculationId");
			SqlParameter[] parameters = {
					new SqlParameter("@CalculationId", SqlDbType.Int,4)
			};
			parameters[0].Value = CalculationId;

			Model.MdlDictLabItemCalculation model=new Model.MdlDictLabItemCalculation();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabItemCalculation DataRowToModel(DataRow row)
		{
			Model.MdlDictLabItemCalculation model=new Model.MdlDictLabItemCalculation();
			if (row != null)
			{
				if(row["CalculationId"]!=null && row["CalculationId"].ToString()!="")
				{
					model.CalculationId=int.Parse(row["CalculationId"].ToString());
				}
				if(row["LabItemId"]!=null && row["LabItemId"].ToString()!="")
				{
					model.LabItemId=int.Parse(row["LabItemId"].ToString());
				}
				if(row["FormulaExpression"]!=null)
				{
					model.FormulaExpression=row["FormulaExpression"].ToString();
				}
				if(row["Description"]!=null)
				{
					model.Description=row["Description"].ToString();
				}
				if(row["Status"]!=null && row["Status"].ToString()!="")
				{
					if((row["Status"].ToString()=="1")||(row["Status"].ToString().ToLower()=="true"))
					{
						model.Status=true;
					}
					else
					{
						model.Status=false;
					}
				}
				if(row["CreatedAt"]!=null && row["CreatedAt"].ToString()!="")
				{
					model.CreatedAt=DateTime.Parse(row["CreatedAt"].ToString());
				}
				if(row["UpdatedAt"]!=null && row["UpdatedAt"].ToString()!="")
				{
					model.UpdatedAt=DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select CalculationId,LabItemId,FormulaExpression,Description,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictLabItemCalculation ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" CalculationId,LabItemId,FormulaExpression,Description,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictLabItemCalculation ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM DictLabItemCalculation ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.CalculationId desc");
			}
			strSql.Append(")AS Row, T.*  from DictLabItemCalculation T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictLabItemCalculation";
			parameters[1].Value = "CalculationId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

