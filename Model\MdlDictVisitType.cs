﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictVisitType.cs
*
* 功 能： N/A
* 类 名： MdlDictVisitType
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 就诊类型字典表
	/// </summary>
	[Serializable]
	public partial class MdlDictVisitType
	{
		public MdlDictVisitType()
		{}
		#region Model
		private int _visittypeid;
		private string _visittypecode;
		private string _visittypename;
		private string _englishname;
		private string _hisvisittypecode;
		private string _pinyin;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int VisitTypeId
		{
			set{ _visittypeid=value;}
			get{return _visittypeid;}
		}
		/// <summary>
		/// LIS 内部就诊类型编码
		/// </summary>
		public string VisitTypeCode
		{
			set{ _visittypecode=value;}
			get{return _visittypecode;}
		}
		/// <summary>
		/// 中文名称（门诊/住院/急诊…）
		/// </summary>
		public string VisitTypeName
		{
			set{ _visittypename=value;}
			get{return _visittypename;}
		}
		/// <summary>
		/// 英文名称
		/// </summary>
		public string EnglishName
		{
			set{ _englishname=value;}
			get{return _englishname;}
		}
		/// <summary>
		/// HIS 就诊类型编码（NVARCHAR(200)）
		/// </summary>
		public string HisVisitTypeCode
		{
			set{ _hisvisittypecode=value;}
			get{return _hisvisittypecode;}
		}
		/// <summary>
		/// 拼音或首字母，用于快速检索
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 备注说明
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 状态 1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 记录创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间，需在应用层维护
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

