﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabResultReview.cs
*
* 功 能： N/A
* 类 名： DalLabResultReview
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabResultReview
	/// </summary>
	public partial class DalLabResultReview:IDalLabResultReview
	{
		public DalLabResultReview()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long ReviewId)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from LabResultReview");
			strSql.Append(" where ReviewId=@ReviewId");
			SqlParameter[] parameters = {
					new SqlParameter("@ReviewId", SqlDbType.BigInt)
			};
			parameters[0].Value = ReviewId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabResultReview model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into LabResultReview(");
			strSql.Append("ResultId,ReviewLevel,ReviewAction,OperatorId,OperatorName,ResponsibleDoctorId,ResponsibleDoctorName,ResponsibleDoctorTitle,ReviewComment,ReviewedAt)");
			strSql.Append(" values (");
			strSql.Append("@ResultId,@ReviewLevel,@ReviewAction,@OperatorId,@OperatorName,@ResponsibleDoctorId,@ResponsibleDoctorName,@ResponsibleDoctorTitle,@ReviewComment,@ReviewedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@ResultId", SqlDbType.BigInt,8),
					new SqlParameter("@ReviewLevel", SqlDbType.TinyInt,1),
					new SqlParameter("@ReviewAction", SqlDbType.TinyInt,1),
					new SqlParameter("@OperatorId", SqlDbType.Int,4),
					new SqlParameter("@OperatorName", SqlDbType.NVarChar,100),
					new SqlParameter("@ResponsibleDoctorId", SqlDbType.Int,4),
					new SqlParameter("@ResponsibleDoctorName", SqlDbType.NVarChar,100),
					new SqlParameter("@ResponsibleDoctorTitle", SqlDbType.NVarChar,50),
					new SqlParameter("@ReviewComment", SqlDbType.NVarChar,500),
					new SqlParameter("@ReviewedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.ResultId;
			parameters[1].Value = model.ReviewLevel;
			parameters[2].Value = model.ReviewAction;
			parameters[3].Value = model.OperatorId;
			parameters[4].Value = model.OperatorName;
			parameters[5].Value = model.ResponsibleDoctorId;
			parameters[6].Value = model.ResponsibleDoctorName;
			parameters[7].Value = model.ResponsibleDoctorTitle;
			parameters[8].Value = model.ReviewComment;
			parameters[9].Value = model.ReviewedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabResultReview model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update LabResultReview set ");
			strSql.Append("ResultId=@ResultId,");
			strSql.Append("ReviewLevel=@ReviewLevel,");
			strSql.Append("ReviewAction=@ReviewAction,");
			strSql.Append("OperatorId=@OperatorId,");
			strSql.Append("OperatorName=@OperatorName,");
			strSql.Append("ResponsibleDoctorId=@ResponsibleDoctorId,");
			strSql.Append("ResponsibleDoctorName=@ResponsibleDoctorName,");
			strSql.Append("ResponsibleDoctorTitle=@ResponsibleDoctorTitle,");
			strSql.Append("ReviewComment=@ReviewComment,");
			strSql.Append("ReviewedAt=@ReviewedAt");
			strSql.Append(" where ReviewId=@ReviewId");
			SqlParameter[] parameters = {
					new SqlParameter("@ResultId", SqlDbType.BigInt,8),
					new SqlParameter("@ReviewLevel", SqlDbType.TinyInt,1),
					new SqlParameter("@ReviewAction", SqlDbType.TinyInt,1),
					new SqlParameter("@OperatorId", SqlDbType.Int,4),
					new SqlParameter("@OperatorName", SqlDbType.NVarChar,100),
					new SqlParameter("@ResponsibleDoctorId", SqlDbType.Int,4),
					new SqlParameter("@ResponsibleDoctorName", SqlDbType.NVarChar,100),
					new SqlParameter("@ResponsibleDoctorTitle", SqlDbType.NVarChar,50),
					new SqlParameter("@ReviewComment", SqlDbType.NVarChar,500),
					new SqlParameter("@ReviewedAt", SqlDbType.DateTime),
					new SqlParameter("@ReviewId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.ResultId;
			parameters[1].Value = model.ReviewLevel;
			parameters[2].Value = model.ReviewAction;
			parameters[3].Value = model.OperatorId;
			parameters[4].Value = model.OperatorName;
			parameters[5].Value = model.ResponsibleDoctorId;
			parameters[6].Value = model.ResponsibleDoctorName;
			parameters[7].Value = model.ResponsibleDoctorTitle;
			parameters[8].Value = model.ReviewComment;
			parameters[9].Value = model.ReviewedAt;
			parameters[10].Value = model.ReviewId;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long ReviewId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LabResultReview ");
			strSql.Append(" where ReviewId=@ReviewId");
			SqlParameter[] parameters = {
					new SqlParameter("@ReviewId", SqlDbType.BigInt)
			};
			parameters[0].Value = ReviewId;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string ReviewIdlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LabResultReview ");
			strSql.Append(" where ReviewId in ("+ReviewIdlist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabResultReview GetModel(long ReviewId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ReviewId,ResultId,ReviewLevel,ReviewAction,OperatorId,OperatorName,ResponsibleDoctorId,ResponsibleDoctorName,ResponsibleDoctorTitle,ReviewComment,ReviewedAt from LabResultReview ");
			strSql.Append(" where ReviewId=@ReviewId");
			SqlParameter[] parameters = {
					new SqlParameter("@ReviewId", SqlDbType.BigInt)
			};
			parameters[0].Value = ReviewId;

			Model.MdlLabResultReview model=new Model.MdlLabResultReview();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabResultReview DataRowToModel(DataRow row)
		{
			Model.MdlLabResultReview model=new Model.MdlLabResultReview();
			if (row != null)
			{
				if(row["ReviewId"]!=null && row["ReviewId"].ToString()!="")
				{
					model.ReviewId=long.Parse(row["ReviewId"].ToString());
				}
				if(row["ResultId"]!=null && row["ResultId"].ToString()!="")
				{
					model.ResultId=long.Parse(row["ResultId"].ToString());
				}
				if(row["ReviewLevel"]!=null && row["ReviewLevel"].ToString()!="")
				{
					model.ReviewLevel=int.Parse(row["ReviewLevel"].ToString());
				}
				if(row["ReviewAction"]!=null && row["ReviewAction"].ToString()!="")
				{
					model.ReviewAction=int.Parse(row["ReviewAction"].ToString());
				}
				if(row["OperatorId"]!=null && row["OperatorId"].ToString()!="")
				{
					model.OperatorId=int.Parse(row["OperatorId"].ToString());
				}
				if(row["OperatorName"]!=null)
				{
					model.OperatorName=row["OperatorName"].ToString();
				}
				if(row["ResponsibleDoctorId"]!=null && row["ResponsibleDoctorId"].ToString()!="")
				{
					model.ResponsibleDoctorId=int.Parse(row["ResponsibleDoctorId"].ToString());
				}
				if(row["ResponsibleDoctorName"]!=null)
				{
					model.ResponsibleDoctorName=row["ResponsibleDoctorName"].ToString();
				}
				if(row["ResponsibleDoctorTitle"]!=null)
				{
					model.ResponsibleDoctorTitle=row["ResponsibleDoctorTitle"].ToString();
				}
				if(row["ReviewComment"]!=null)
				{
					model.ReviewComment=row["ReviewComment"].ToString();
				}
				if(row["ReviewedAt"]!=null && row["ReviewedAt"].ToString()!="")
				{
					model.ReviewedAt=DateTime.Parse(row["ReviewedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ReviewId,ResultId,ReviewLevel,ReviewAction,OperatorId,OperatorName,ResponsibleDoctorId,ResponsibleDoctorName,ResponsibleDoctorTitle,ReviewComment,ReviewedAt ");
			strSql.Append(" FROM LabResultReview ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ReviewId,ResultId,ReviewLevel,ReviewAction,OperatorId,OperatorName,ResponsibleDoctorId,ResponsibleDoctorName,ResponsibleDoctorTitle,ReviewComment,ReviewedAt ");
			strSql.Append(" FROM LabResultReview ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM LabResultReview ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ReviewId desc");
			}
			strSql.Append(")AS Row, T.*  from LabResultReview T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabResultReview";
			parameters[1].Value = "ReviewId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

