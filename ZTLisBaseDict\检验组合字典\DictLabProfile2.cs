using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictLabProfile2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictLabProfile _bllDictLabProfile = new BllDictLabProfile();
        Model.MdlDictLabProfile _mdlDictLabProfile = new MdlDictLabProfile();

        public DictLabProfile2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtReportTitle.GotFocus += new System.EventHandler(base.InputCn);
            TxtHisCode.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictLabProfile2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictLabProfile.MaxCode(6);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            TxtReportTitle.Text = "";
            TxtHisCode.Text = "";
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["ProfileCode"] + "";
            TxtName.Text = row["ProfileName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            TxtReportTitle.Text = row["ReportTitle"] + "";
            TxtHisCode.Text = row["HisProfileCode"] + "";
            TxtDescription.Text = row["Description"] + "";
            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写检验组合名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写检验组合编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("His编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["ProfileCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["ProfileName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && TxtHisCode.Text.Trim() != base.MyRow["HisProfileCode"].ToString().Trim() && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("His编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictLabProfile.GetRecordCount($"ProfileCode='{code}'");
            return cnt > 0;
        }

        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictLabProfile.GetRecordCount($"ProfileName='{name}'");
            return cnt > 0;
        }

        private bool CheckHisCodeExists(string hisCode)
        {
            int cnt = _bllDictLabProfile.GetRecordCount($"HisProfileCode='{hisCode}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictLabProfile.ProfileCode = TxtCode.Text.Trim();
            _mdlDictLabProfile.ProfileName = TxtName.Text.Trim();
            _mdlDictLabProfile.PinYin = TxtJc.Text.Trim();
            _mdlDictLabProfile.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictLabProfile.ReportTitle = TxtReportTitle.Text.Trim();
            _mdlDictLabProfile.HisProfileCode = TxtHisCode.Text.Trim();
            _mdlDictLabProfile.Description = TxtDescription.Text.Trim();
            _mdlDictLabProfile.Status = true;
            _mdlDictLabProfile.CreatedAt = DateTime.Now;
            _mdlDictLabProfile.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabProfile, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["ProfileId"] = _bllDictLabProfile.Add(_mdlDictLabProfile);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictLabProfile.ProfileId = int.Parse(base.MyRow["ProfileId"].ToString());
            _mdlDictLabProfile.ProfileCode = TxtCode.Text.Trim();
            _mdlDictLabProfile.ProfileName = TxtName.Text.Trim();
            _mdlDictLabProfile.PinYin = TxtJc.Text.Trim();
            _mdlDictLabProfile.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictLabProfile.ReportTitle = TxtReportTitle.Text.Trim();
            _mdlDictLabProfile.HisProfileCode = TxtHisCode.Text.Trim();
            _mdlDictLabProfile.Description = TxtDescription.Text.Trim();
            _mdlDictLabProfile.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabProfile, base.MyRow);

            //数据保存
            try
            {
                _bllDictLabProfile.Update(_mdlDictLabProfile);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }
        #endregion

        #region 事件响应
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (this.DataCheck() == false)
                return;

            if (base.Insert == true)
                this.DataAdd();
            else
                this.DataEdit();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }
        #endregion
    }
}