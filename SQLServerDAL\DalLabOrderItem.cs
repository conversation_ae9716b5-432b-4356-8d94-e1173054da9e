﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabOrderItem.cs
*
* 功 能： N/A
* 类 名： DalLabOrderItem
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:53:37   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabOrderItem
	/// </summary>
	public partial class DalLabOrderItem : IDalLabOrderItem
	{
		public DalLabOrderItem()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long OrderItemId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabOrderItem");
			strSql.Append(" where OrderItemId=@OrderItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderItemId", SqlDbType.BigInt)
			};
			parameters[0].Value = OrderItemId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabOrderItem model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabOrderItem(");
			strSql.Append("SpecimenId,LabItemId,ProfileId,ItemStatus,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@SpecimenId,@LabItemId,@ProfileId,@ItemStatus,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@SpecimenId", SqlDbType.BigInt,8),
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@ProfileId", SqlDbType.Int,4),
					new SqlParameter("@ItemStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.SpecimenId;
			parameters[1].Value = model.LabItemId;
			parameters[2].Value = model.ProfileId;
			parameters[3].Value = model.ItemStatus;
			parameters[4].Value = model.CreatedAt;
			parameters[5].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabOrderItem model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabOrderItem set ");
			strSql.Append("SpecimenId=@SpecimenId,");
			strSql.Append("LabItemId=@LabItemId,");
			strSql.Append("ProfileId=@ProfileId,");
			strSql.Append("ItemStatus=@ItemStatus,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where OrderItemId=@OrderItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@SpecimenId", SqlDbType.BigInt,8),
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@ProfileId", SqlDbType.Int,4),
					new SqlParameter("@ItemStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@OrderItemId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.SpecimenId;
			parameters[1].Value = model.LabItemId;
			parameters[2].Value = model.ProfileId;
			parameters[3].Value = model.ItemStatus;
			parameters[4].Value = model.CreatedAt;
			parameters[5].Value = model.UpdatedAt;
			parameters[6].Value = model.OrderItemId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long OrderItemId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabOrderItem ");
			strSql.Append(" where OrderItemId=@OrderItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderItemId", SqlDbType.BigInt)
			};
			parameters[0].Value = OrderItemId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string OrderItemIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabOrderItem ");
			strSql.Append(" where OrderItemId in (" + OrderItemIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabOrderItem GetModel(long OrderItemId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 OrderItemId,SpecimenId,LabItemId,ProfileId,ItemStatus,CreatedAt,UpdatedAt from LabOrderItem ");
			strSql.Append(" where OrderItemId=@OrderItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderItemId", SqlDbType.BigInt)
			};
			parameters[0].Value = OrderItemId;

			Model.MdlLabOrderItem model = new Model.MdlLabOrderItem();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabOrderItem DataRowToModel(DataRow row)
		{
			Model.MdlLabOrderItem model = new Model.MdlLabOrderItem();
			if (row != null)
			{
				if (row["OrderItemId"] != null && row["OrderItemId"].ToString() != "")
				{
					model.OrderItemId = long.Parse(row["OrderItemId"].ToString());
				}
				if (row["SpecimenId"] != null && row["SpecimenId"].ToString() != "")
				{
					model.SpecimenId = long.Parse(row["SpecimenId"].ToString());
				}
				if (row["LabItemId"] != null && row["LabItemId"].ToString() != "")
				{
					model.LabItemId = int.Parse(row["LabItemId"].ToString());
				}
				if (row["ProfileId"] != null && row["ProfileId"].ToString() != "")
				{
					model.ProfileId = int.Parse(row["ProfileId"].ToString());
				}
				if (row["ItemStatus"] != null && row["ItemStatus"].ToString() != "")
				{
					model.ItemStatus = int.Parse(row["ItemStatus"].ToString());
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select loi.OrderItemId,loi.SpecimenId,loi.LabItemId,loi.ProfileId,loi.ItemStatus,loi.CreatedAt,loi.UpdatedAt,");
			strSql.Append("       dli.LabItemCode,dli.LabItemName,dli.ReportName as ItemReportName,");
			strSql.Append("       dlp.ProfileCode,dlp.ProfileName,dlp.ReportTitle as ProfileReportTitle,");
			strSql.Append("       lr.ResultId,lr.ResultValue,lr.Unit,lr.AbnormalFlag,lr.ReviewStatus ");
			strSql.Append(" FROM LabOrderItem loi ");
			strSql.Append(" INNER JOIN DictLabItem dli ON loi.LabItemId = dli.LabItemId ");
			strSql.Append(" LEFT JOIN DictLabProfile dlp ON loi.ProfileId = dlp.ProfileId ");
			strSql.Append(" LEFT JOIN LabResult lr ON loi.OrderItemId = lr.OrderItemId ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" OrderItemId,SpecimenId,LabItemId,ProfileId,ItemStatus,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM LabOrderItem ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabOrderItem ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.OrderItemId desc");
			}
			strSql.Append(")AS Row, T.*  from LabOrderItem T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabOrderItem";
			parameters[1].Value = "OrderItemId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

