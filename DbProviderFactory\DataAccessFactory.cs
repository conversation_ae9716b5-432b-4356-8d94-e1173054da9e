﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Text;
using DBUtility;
using IDBUtility;

namespace DbProviderFactory
{
    public class DataAccessFactory
    {
        private DataAccessFactory() { }

        private PersistenceProperty pp;

        public PersistenceProperty Pp
        {

            get { return pp; }

            set { pp = value; }

        }


        public static IDbHelper CreateDataAccess(PersistenceProperty pp)
        {

            IDbHelper DbHelper;

            switch (pp.DatabaseType)
            {
                case (DatabaseType.MSSQLServer):
                    DbHelper = new DBHelperSql();
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.Oracle):
                    DbHelper = new DBHelperOracle();
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.SQLite):
                    DbHelper = new DBHelperSqlite(); 
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.DDTekOracle):
                    DbHelper = new DBHelperDDTekOracle(); 
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.Access):
                    DbHelper = new DBHelperOleDb();
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.DB2):
                    DbHelper = new DBHelperDb2();
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.MySQL):
                    DbHelper = new DBHelperMySql();
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
                case (DatabaseType.Cache):
                    DbHelper = new DBHelperCache();
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;     
                //case (DatabaseType.PostgreSQL):
                //    DbHelper = new DBHelperPgSql();
                //    DbHelper.ConnectionString = pp.ConnectionString;
                //    break;
                default:
                    DbHelper = new DBHelperOleDb(); 
                    DbHelper.ConnectionString = pp.ConnectionString;
                    break;
            }
            return DbHelper;
        }

        public static IDbHelper CreateDataAccess(DatabaseType DataType, string ConnectionString)
        {
            PersistenceProperty pp=new PersistenceProperty();
            pp.DatabaseType = DataType;
            pp.ConnectionString = ConnectionString;   
            return CreateDataAccess(pp);
        }

    }
}
