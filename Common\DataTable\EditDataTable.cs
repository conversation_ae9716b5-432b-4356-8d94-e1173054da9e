﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Common
{
    public class EditDataTable
    {
        /// <summary>
        /// 更新DataTable第一列
        /// </summary>
        /// <param name="My_Table"></param>
        /// <param name="V_Id"></param>
        /// <param name="RowCount"></param>
        /// <returns></returns>
        public static int Edit_Col0(System.Data.DataTable My_Table, int V_Id, int RowCount, string colName = "")
        {
            int i = 0;
            foreach (DataRow row in My_Table.Rows)
            {
                if (colName == "")
                {
                    //string a= (i / RowCount + V_Id).ToString();
                    row[0] = i / RowCount + V_Id;
                }
                else
                {
                    row[colName] = i / RowCount + V_Id;
                }
                row.AcceptChanges();
                i ++;
            }

            return i / RowCount + V_Id;
        }


        /// <summary>
        /// 枚举类型转化为DataTable
        /// </summary>
        /// <param name="enumType">枚举类型</param>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <returns></returns>
        public static DataTable EnumToDataTable(Type enumType, string key = "key", string val = "value")
        {
            string[] Names = System.Enum.GetNames(enumType);

            Array Values = System.Enum.GetValues(enumType);

            DataTable table = new DataTable();
            table.Columns.Add(key, System.Type.GetType("System.Int32"));
            table.Columns.Add(val, System.Type.GetType("System.String"));
            table.Columns[key].Unique = true;
            for (int i = 0; i < Values.Length; i++)
            {
                DataRow DR = table.NewRow();
                DR[key] = (int)Values.GetValue(i);
                DR[val] = ModelTools.GetDescription(enumType, Names[i].ToString());
                table.Rows.Add(DR);
            }
            return table;
        }


    }
}
