﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictSampleType1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictSampleType _bllDictSampleType = new BllDictSampleType();

        public DictSampleType1()
        {
            InitializeComponent();
        }

        private void DictSampleType1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "SampleTypeCode", 120, "中", "", false);
            myGrid1.Init_Column("名称", "SampleTypeName", 160, "左", "", false);
            myGrid1.Init_Column("简称", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("英文名称", "EnglishName", 150, "左", "", false);
            myGrid1.Init_Column("容器类型", "ContainerType", 120, "左", "", false);
            myGrid1.Init_Column("采样量(mL)", "DefaultVolumeMl", 100, "右", "", false);
            myGrid1.Init_Column("预处理", "PreTreatment", 300, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllDictSampleType.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["ParamId"] };
            base.MyTable.Columns["SampleTypeId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictSampleType2 vform = new DictSampleType2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除标本类型：" + this.myGrid1.Columns["SampleTypeName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictSampleType.Delete(int.Parse(base.MyRow["SampleTypeId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("SampleTypeCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("SampleTypeName", new Common.MdlExcel(15, "", "名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "简称", true));
            colconfig.Add("EnglishName", new Common.MdlExcel(15, "", "英文名称", true));
            colconfig.Add("ContainerType", new Common.MdlExcel(12, "", "容器类型", true));
            colconfig.Add("DefaultVolumeMl", new Common.MdlExcel(10, "", "采样量(mL)", true));
            colconfig.Add("PreTreatment", new Common.MdlExcel(20, "", "预处理", true));
            Common.ExcelHelper.Export(base.MyTable, "标本类型字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictSampleType mdlDictSampleType = new MdlDictSampleType();
                    if (row["名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictSampleType.SampleTypeCode = row["编码"] + "";
                    mdlDictSampleType.SampleTypeName = row["名称"] + "";
                    mdlDictSampleType.PinYin = base.MyChs2Spell.GetPy(row["名称"].ToString());
                    mdlDictSampleType.EnglishName = row["英文名称"] + "";
                    mdlDictSampleType.ContainerType = row["容器类型"] + "";
                    if (row["采样量(mL)"] != null && row["采样量(mL)"].ToString().Trim() != "")
                    {
                        decimal volume;
                        if (decimal.TryParse(row["采样量(mL)"].ToString(), out volume))
                        {
                            mdlDictSampleType.DefaultVolumeMl = volume;
                        }
                    }
                    mdlDictSampleType.PreTreatment = row["预处理"] + "";
                    mdlDictSampleType.Status = true;
                    mdlDictSampleType.CreatedAt = DateTime.Now;
                    mdlDictSampleType.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictSampleType).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictSampleType, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["SampleTypeId"] = _bllDictSampleType.Add(mdlDictSampleType);
                    base.MyTable.Rows.Add(newRow);
                    base.MyTable.AcceptChanges();

                    UpdateData[i - 1] = "";
                    i++;
                }
                Common.ExcelHelper.UpdateExcel(fileName, 0, UpdateData, tableColCnt, 2);
                MessageBox.Show("成功导入【" + successCnt + "】条记录!失败【" + failCnt + "】条\r\n如存在未导入数据，请查看导入Excel文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
            }

        }
        private void DataEnable(bool Status)
        {
            if (myGrid1.RowCount == 0) return;
            Model.MdlDictSampleType model1 = new Model.MdlDictSampleType();
            model1 = _bllDictSampleType.GetModel(int.Parse(base.MyRow["SampleTypeId"].ToString()));
            model1.Status = Status;
            base.MyRow["Status"] = Status;
            base.MyRow.AcceptChanges();
            _bllDictSampleType.Update(model1);
        }

        #endregion

        #region 控件动作

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEnable(false);
        }
        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("SampleTypeCode+SampleTypeName+PinYin+EnglishName", TxtFilter.Text.Trim());
        }
        #endregion
    }
}