﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabOrder.cs
*
* 功 能： N/A
* 类 名： MdlLabOrder
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:53:37   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验申请主表
	/// </summary>
	[Serializable]
	public partial class MdlLabOrder
	{
		public MdlLabOrder()
		{ }
		#region Model
		private long _orderid;
		private string _hisordercode;
		private string _visitid;
		private string _patientid;
		private string _patientname;
		private string _patientsex;
		private DateTime? _patientbirthdate;
		private int? _patientage;
		private string _patientageunit;
		private string _bedno;
		private int _visittypeid;
		private int _requestingdeptid;
		private int _requestingdoctorid;
		private int _orderstatus = 0;
		private int _priority = 0;
		private string _clinicaldiagnosis;
		private string _reportcomment;
		private DateTime _orderdatetime = DateTime.Now;
		private DateTime _createdat = DateTime.Now;
		private int? _createdbyid;
		private string _createdbyname;
		private DateTime _updatedat = DateTime.Now;
		private int? _updatedbyid;
		private string _updatedbyname;
		/// <summary>
		/// LIS内部申请单ID，自增主键
		/// </summary>
		public long OrderId
		{
			set { _orderid = value; }
			get { return _orderid; }
		}
		/// <summary>
		/// 对应的HIS系统医嘱号/申请单号，用于系统间对接，手工单可为空
		/// </summary>
		public string HisOrderCode
		{
			set { _hisordercode = value; }
			get { return _hisordercode; }
		}
		/// <summary>
		/// 患者就诊ID（门诊号/住院号等）
		/// </summary>
		public string VisitId
		{
			set { _visitid = value; }
			get { return _visitid; }
		}
		/// <summary>
		/// 患者唯一标识（如患者ID/身份证号）
		/// </summary>
		public string PatientId
		{
			set { _patientid = value; }
			get { return _patientid; }
		}
		/// <summary>
		/// 患者姓名（冗余存储，便于查询显示）
		/// </summary>
		public string PatientName
		{
			set { _patientname = value; }
			get { return _patientname; }
		}
		/// <summary>
		/// 患者性别：M=男, F=女, U=未知。匹配参考范围的核心字段，应在创建时冗余。
		/// </summary>
		public string PatientSex
		{
			set { _patientsex = value; }
			get { return _patientsex; }
		}
		/// <summary>
		/// 患者出生日期：用于精确计算年龄，匹配参考范围的核心字段，应在创建时冗余。
		/// </summary>
		public DateTime? PatientBirthDate
		{
			set { _patientbirthdate = value; }
			get { return _patientbirthdate; }
		}
		/// <summary>
		/// 患者年龄（冗余字段便于查询）
		/// </summary>
		public int? PatientAge
		{
			set { _patientage = value; }
			get { return _patientage; }
		}
		/// <summary>
		/// 年龄单位：Y=岁 M=月 D=天
		/// </summary>
		public string PatientAgeUnit
		{
			set { _patientageunit = value; }
			get { return _patientageunit; }
		}
		/// <summary>
		/// 床号：住院患者的关键信息，用于标本采集和报告分发。
		/// </summary>
		public string BedNo
		{
			set { _bedno = value; }
			get { return _bedno; }
		}
		/// <summary>
		/// 就诊类型ID（门诊/住院/急诊/体检）
		/// </summary>
		public int VisitTypeId
		{
			set { _visittypeid = value; }
			get { return _visittypeid; }
		}
		/// <summary>
		/// 申请科室ID
		/// </summary>
		public int RequestingDeptId
		{
			set { _requestingdeptid = value; }
			get { return _requestingdeptid; }
		}
		/// <summary>
		/// 申请医生ID
		/// </summary>
		public int RequestingDoctorId
		{
			set { _requestingdoctorid = value; }
			get { return _requestingdoctorid; }
		}
		/// <summary>
		/// 申请单状态：0=已开立 1=部分采集 2=全部采集 3=部分完成 4=全部完成 9=已取消
		/// </summary>
		public int OrderStatus
		{
			set { _orderstatus = value; }
			get { return _orderstatus; }
		}
		/// <summary>
		/// 优先级：0=常规(Normal) 1=紧急(STAT)
		/// </summary>
		public int Priority
		{
			set { _priority = value; }
			get { return _priority; }
		}
		/// <summary>
		/// 临床诊断信息
		/// </summary>
		public string ClinicalDiagnosis
		{
			set { _clinicaldiagnosis = value; }
			get { return _clinicaldiagnosis; }
		}
		/// <summary>
		/// 报告评价/总结：由审核医生填写的对本次检验的综合性结论或建议，显示在报告单上。
		/// </summary>
		public string ReportComment
		{
			set { _reportcomment = value; }
			get { return _reportcomment; }
		}
		/// <summary>
		/// 医嘱开立时间
		/// </summary>
		public DateTime OrderDateTime
		{
			set { _orderdatetime = value; }
			get { return _orderdatetime; }
		}
		/// <summary>
		/// 记录创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 创建人ID
		/// </summary>
		public int? CreatedById
		{
			set { _createdbyid = value; }
			get { return _createdbyid; }
		}
		/// <summary>
		/// 创建人姓名（冗余）
		/// </summary>
		public string CreatedByName
		{
			set { _createdbyname = value; }
			get { return _createdbyname; }
		}
		/// <summary>
		/// 记录更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		/// <summary>
		/// 最后更新人ID
		/// </summary>
		public int? UpdatedById
		{
			set { _updatedbyid = value; }
			get { return _updatedbyid; }
		}
		/// <summary>
		/// 最后更新人姓名（冗余）
		/// </summary>
		public string UpdatedByName
		{
			set { _updatedbyname = value; }
			get { return _updatedbyname; }
		}
		#endregion Model

	}
}

