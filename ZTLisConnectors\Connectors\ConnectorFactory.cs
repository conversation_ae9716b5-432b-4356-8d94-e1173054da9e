using System;
using ZTLisEnum;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器工厂类，用于根据配置创建不同类型的连接器
    /// </summary>
    public static class ConnectorFactory
    {
        /// <summary>
        /// 根据仪器配置创建对应的连接器
        /// </summary>
        /// <param name="instrumentConfig">仪器配置</param>
        /// <returns>连接器实例</returns>
        public static IConnector CreateConnector(LabInstrument instrumentConfig)
        {
            if (instrumentConfig == null)
                throw new ArgumentNullException(nameof(instrumentConfig));

            IConnector connector = null;

            switch (instrumentConfig.ConnType)
            {
                case ConnType.串口: // 串口连接
                    connector = new SerialPortConnector();
                    break;
                case ConnType.TCP: // TCP连接
                    connector = new TcpConnector();
                    break;
                case ConnType.文件共享: // 文件共享
                    // TODO: 实现文件共享连接器
                    throw new NotImplementedException("文件共享连接器尚未实现");
                case ConnType.中间库: // 中间库
                    // TODO: 实现中间库连接器
                    throw new NotImplementedException("中间库连接器尚未实现");
                case ConnType.REST接口: // REST API
                    // TODO: 实现REST API连接器
                    throw new NotImplementedException("REST API连接器尚未实现");
                case ConnType.HL7_MLLP: // HL7 MLLP
                    // TODO: 实现HL7 MLLP连接器
                    throw new NotImplementedException("HL7 MLLP连接器尚未实现");
                default:
                    throw new ArgumentException($"不支持的连接类型: {instrumentConfig.ConnType}", nameof(instrumentConfig));
            }

            // 配置连接器
            connector.Configure(instrumentConfig);
            return connector;
        }

    }
}