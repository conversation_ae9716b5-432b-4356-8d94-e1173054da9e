using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using ZTLisEnum;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// 解析器工厂类，负责创建和管理解析器实例
    /// </summary>
    public static class ParserFactory
    {
        private static readonly Dictionary<ProtocolType, Func<IParser>> _parserCreators = new Dictionary<ProtocolType, Func<IParser>>();
        private static readonly Dictionary<ProtocolType, IParser> _parserInstances = new Dictionary<ProtocolType, IParser>();
        private static readonly Dictionary<string, IParser> _customParserInstances = new Dictionary<string, IParser>();
        private static readonly object _lock = new object();

        /// <summary>
        /// 注册解析器
        /// </summary>
        /// <param name="protocolType">协议类型</param>
        /// <param name="parserCreator">解析器创建函数</param>
        public static void RegisterParser(ProtocolType protocolType, Func<IParser> parserCreator)
        {
            lock (_lock)
            {
                _parserCreators[protocolType] = parserCreator;
            }
        }

        /// <summary>
        /// 注册解析器（泛型版本）
        /// </summary>
        /// <typeparam name="T">解析器类型</typeparam>
        /// <param name="protocolType">协议类型</param>
        public static void RegisterParser<T>(ProtocolType protocolType) where T : IParser, new()
        {
            RegisterParser(protocolType, () => new T());
        }

        /// <summary>
        /// 注册解析器（别名方法，保持向后兼容）
        /// </summary>
        /// <typeparam name="T">解析器类型</typeparam>
        /// <param name="protocolType">协议类型</param>
        public static void Register<T>(ProtocolType protocolType) where T : IParser, new()
        {
            RegisterParser<T>(protocolType);
        }

        /// <summary>
        /// 获取解析器实例（单例模式）
        /// </summary>
        /// <param name="protocolType">协议类型</param>
        /// <returns>解析器实例，如果未找到则返回null</returns>
        public static IParser GetParser(ProtocolType protocolType)
        {
            lock (_lock)
            {
                // 先检查是否已有实例
                if (_parserInstances.ContainsKey(protocolType))
                {
                    return _parserInstances[protocolType];
                }

                // 如果没有实例，尝试创建
                if (_parserCreators.ContainsKey(protocolType))
                {
                    var parser = _parserCreators[protocolType]();
                    _parserInstances[protocolType] = parser;
                    return parser;
                }

                return null;
            }
        }

        /// <summary>
        /// 通过自定义驱动类名获取解析器实例
        /// </summary>
        /// <param name="customDriverClass">自定义驱动类的完整类名</param>
        /// <returns>解析器实例，如果创建失败则返回null</returns>
        public static IParser GetCustomParser(string customDriverClass)
        {
            if (string.IsNullOrWhiteSpace(customDriverClass))
                return null;

            // 获取真实的类名
            var realClassName = GetRealDriverClassName(customDriverClass);
            if (string.IsNullOrWhiteSpace(realClassName))
            {
                Common.Log.LogHelper.Error($"未找到驱动标识符对应的类名: {customDriverClass}");
                return null;
            }

            lock (_lock)
            {
                // 使用真实类名作为缓存键
                if (_customParserInstances.ContainsKey(realClassName))
                {
                    return _customParserInstances[realClassName];
                }

                try
                {
                    // 尝试通过反射创建实例
                    var parser = CreateParserByClassName(realClassName);
                    if (parser != null)
                    {
                        _customParserInstances[realClassName] = parser;
                        return parser;
                    }
                }
                catch (Exception ex)
                {
                    Common.Log.LogHelper.Error($"创建自定义解析器失败，标识符: {customDriverClass}，类名: {realClassName}，异常: {ex.Message}");
                }

                return null;
            }
        }

        /// <summary>
        /// 根据驱动标识符获取真实的类名
        /// </summary>
        /// <param name="driverIdentifier">驱动标识符</param>
        /// <returns>真实的类名</returns>
        private static string GetRealDriverClassName(string driverIdentifier)
        {
            if (string.IsNullOrWhiteSpace(driverIdentifier))
                return null;

            try
            {
                // 尝试将字符串解析为枚举
                if (Enum.TryParse<ZTLisEnum.InstrumentDriverClass>(driverIdentifier, out var driverClass))
                {
                    switch (driverClass)
                    {
                        case ZTLisEnum.InstrumentDriverClass.希森美康CA6000:
                            return "ZTLisParsers.Parsers.SysmexCA6000Parser";
                        case ZTLisEnum.InstrumentDriverClass.QR100特定蛋白分析仪:
                            return "ZTLisParsers.Parsers.QR100Parser";
                        default:
                            Common.Log.LogHelper.Warn($"未配置解析器的驱动类型: {driverClass}");
                            return null;
                    }
                }
                else
                {
                    // 如果不能解析为枚举，检查是否是完整的类名
                    if (driverIdentifier.Contains(".") && driverIdentifier.Contains("Parser"))
                    {
                        return driverIdentifier;
                    }

                    Common.Log.LogHelper.Warn($"无法解析的驱动标识符: {driverIdentifier}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Common.Log.LogHelper.Error($"解析驱动标识符失败: {driverIdentifier}，异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 通过类名创建解析器实例
        /// </summary>
        /// <param name="className">完整的类名</param>
        /// <returns>解析器实例</returns>
        private static IParser CreateParserByClassName(string className)
        {
            try
            {
                // 首先尝试从当前程序集加载
                var currentAssembly = Assembly.GetExecutingAssembly();
                var type = currentAssembly.GetType(className);

                // 如果当前程序集没有，尝试从所有已加载的程序集中查找
                if (type == null)
                {
                    foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
                    {
                        type = assembly.GetType(className);
                        if (type != null)
                            break;
                    }
                }

                // 检查类型是否实现了IParser接口
                if (type != null && typeof(IParser).IsAssignableFrom(type))
                {
                    return (IParser)Activator.CreateInstance(type);
                }

                Common.Log.LogHelper.Error($"类 {className} 未找到或未实现IParser接口");
                return null;
            }
            catch (Exception ex)
            {
                Common.Log.LogHelper.Error($"反射创建解析器失败，类名: {className}，异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取所有已注册的协议类型
        /// </summary>
        /// <returns>协议类型列表</returns>
        public static List<ProtocolType> GetSupportedProtocols()
        {
            lock (_lock)
            {
                return _parserCreators.Keys.ToList();
            }
        }

        /// <summary>
        /// 检查是否支持指定的协议类型
        /// </summary>
        /// <param name="protocolType">协议类型</param>
        /// <returns>是否支持</returns>
        public static bool IsSupported(ProtocolType protocolType)
        {
            lock (_lock)
            {
                return _parserCreators.ContainsKey(protocolType);
            }
        }

        /// <summary>
        /// 验证自定义驱动类是否有效
        /// </summary>
        /// <param name="customDriverClass">自定义驱动类的完整类名</param>
        /// <returns>是否有效</returns>
        public static bool IsCustomDriverValid(string customDriverClass)
        {
            if (string.IsNullOrWhiteSpace(customDriverClass))
                return false;

            // 获取真实的类名
            var realClassName = GetRealDriverClassName(customDriverClass);
            if (string.IsNullOrWhiteSpace(realClassName))
                return false;

            try
            {
                // 尝试获取类型信息，不创建实例
                var type = Type.GetType(realClassName);
                if (type == null)
                {
                    // 如果当前程序集中找不到，尝试在所有已加载的程序集中查找
                    foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
                    {
                        type = assembly.GetType(realClassName);
                        if (type != null)
                            break;
                    }
                }

                return type != null && typeof(IParser).IsAssignableFrom(type);
            }
            catch (Exception ex)
            {
                Common.Log.LogHelper.Error($"验证自定义驱动类失败，标识符: {customDriverClass}，类名: {realClassName}，异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清除所有解析器实例（用于重新初始化）
        /// </summary>
        public static void ClearInstances()
        {
            lock (_lock)
            {
                _parserInstances.Clear();
                _customParserInstances.Clear();
            }
        }

        /// <summary>
        /// 取消注册解析器
        /// </summary>
        /// <param name="protocolType">协议类型</param>
        public static void UnregisterParser(ProtocolType protocolType)
        {
            lock (_lock)
            {
                _parserCreators.Remove(protocolType);
                _parserInstances.Remove(protocolType);
            }
        }

        /// <summary>
        /// 清除特定自定义解析器实例
        /// </summary>
        /// <param name="customDriverClass">自定义驱动类标识符</param>
        public static void ClearCustomParserInstance(string customDriverClass)
        {
            if (string.IsNullOrWhiteSpace(customDriverClass))
                return;

            // 获取真实的类名
            var realClassName = GetRealDriverClassName(customDriverClass);
            if (string.IsNullOrWhiteSpace(realClassName))
                return;

            lock (_lock)
            {
                if (_customParserInstances.ContainsKey(realClassName))
                {
                    _customParserInstances.Remove(realClassName);
                    Common.Log.LogHelper.Info($"已清除自定义解析器实例，标识符: {customDriverClass}，类名: {realClassName}");
                }
            }
        }
    }
}