﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace Common.Delegate
{
    public class TransmitDataRow
    {

        public event Action<DataRow> SetDataRow;

        public void OnSetDataRow(DataRow row)
        {
            if (SetDataRow != null)
            {
                SetDataRow(row);
            }
        }



        public event Action<DataRow, DataTable> SetDataRowDt1;
        public void OnSetDataRow(DataRow row, DataTable dt1)
        {
            if (SetDataRowDt1 != null)
            {
                SetDataRowDt1(row, dt1);
            }
        }

        public event Action<DataRow, DataTable, DataTable> SetDataRowDt2;
        public void OnSetDataRow(DataRow row, DataTable dt1, DataTable dt2)
        {
            if (SetDataRowDt2 != null)
            {
                SetDataRowDt2(row, dt1, dt2);
            }
        }
    }
}
