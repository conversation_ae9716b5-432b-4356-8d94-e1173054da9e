﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common.Delegate;

namespace Common.BaseForm
{
    public partial class RecursionBaseDict22 : BaseDict2
    {
        private Common.Delegate.TransmitTreeNode _transmitTreeNode = new TransmitTreeNode();
        public Common.Delegate.TransmitTreeNode MyTransmitTreeNode 
        {
            get { return _transmitTreeNode; }
            set
            {
                _transmitTreeNode = value;
            }
        }

        private TreeNode _myTreeNode = new TreeNode();
        public TreeNode MyTreeNode
        {
            get { return _myTreeNode; }
            set
            {
                _myTreeNode = value;
            }
        }

        public RecursionBaseDict22()
        {
            InitializeComponent();
        }

    }
}
