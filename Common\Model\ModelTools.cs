﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Common
{
    public class ModelTools
    {
        public static string GetDescription(Type type, string en)
        {
            MemberInfo[] memberInfos = type.GetMember(en);   //获取成员  
            if (memberInfos != null && memberInfos.Length > 0)
            {
                DescriptionAttribute[] attrs = memberInfos[0].GetCustomAttributes(typeof(DescriptionAttribute), false) as DescriptionAttribute[];   //获取描述特性  
                if (attrs != null && attrs.Length > 0)
                {
                    return attrs[0].Description;    //返回当前描述
                }
            }
            return en.ToString();
        }
        public static Out ToNewModel<In, Out>(In InModel) where In : class, new()
                                                         where Out : class, new()
        {
            Out MdlOut = new Out();
            List<PropertyInfo> plist = new List<PropertyInfo>();

            Type tIn = typeof(In);
            Type tOut = typeof(Out);

            Array.ForEach<PropertyInfo>(tIn.GetProperties(), p => { plist.Add(p); });

            plist.ForEach(p =>
            {
                var value = p.GetValue(InModel, null);
                var property = tOut.GetProperty(p.Name);
                if (property != null)
                {
                    if (property.PropertyType != p.PropertyType)
                    {
                        Type t = property.PropertyType;
                        switch (property.PropertyType.FullName)
                        {
                            case "System.String":
                                if (value != null) property.SetValue(MdlOut, value.ToString(), null);
                                else property.SetValue(MdlOut, null, null);
                                break;
                            case "System.DateTime":
                                property.SetValue(MdlOut, DateTime.Parse(value.ToString()), null);
                                break;
                            case "System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]":
                                property.SetValue(MdlOut, ConvertTo<decimal?>(value), null);
                                break;
                            case "System.Nullable`1[[System.DateTime, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]":
                                property.SetValue(MdlOut, ConvertTo<DateTime?>(value), null);
                                break;
                            default:
                                property.SetValue(MdlOut, value, null);
                                break;

                        }
                    }
                    else
                    {
                        property.SetValue(MdlOut, value, null);
                    }

                }
            });
            return MdlOut;
        }
        public static Out ToNewModel<In, Out>(In InModel, Out MdlOut) where In : class, new()
                                                 where Out : class, new()
        {
            List<PropertyInfo> plist = new List<PropertyInfo>();

            Type tIn = typeof(In);
            Type tOut = typeof(Out);

            Array.ForEach<PropertyInfo>(tIn.GetProperties(), p => { plist.Add(p); });

            plist.ForEach(p =>
            {
                var value = p.GetValue(InModel, null);
                var property = tOut.GetProperty(p.Name);
                if (property != null)
                {
                    if (property.PropertyType != p.PropertyType)
                    {
                        Type t = property.PropertyType;
                        switch (property.PropertyType.FullName)
                        {
                            case "System.String":
                                property.SetValue(MdlOut, value.ToString(), null);
                                break;
                            case "System.DateTime":
                                property.SetValue(MdlOut, DateTime.Parse(value.ToString()), null);
                                break;
                            case "System.Decimal":
                                property.SetValue(MdlOut, decimal.Parse(value.ToString()), null);
                                break;
                            case "System.Int32":
                                property.SetValue(MdlOut, Int32.Parse(value.ToString()), null);
                                break;
                            case "System.Nullable`1[[System.Decimal, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]":
                                property.SetValue(MdlOut, ConvertTo<decimal?>(value), null);
                                break;
                            case "System.Nullable`1[[System.DateTime, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]":
                                property.SetValue(MdlOut, ConvertTo<DateTime?>(value), null);
                                break;
                            default:
                                property.SetValue(MdlOut, value, null);
                                break;

                        }
                    }
                    else
                    {
                        property.SetValue(MdlOut, value, null);
                    }

                }
            });
            return MdlOut;
        }
        private static T ConvertTo<T>(object convertibleValue)
        {
            if (null == convertibleValue)
            {
                return default(T);
            }

            if (convertibleValue == "")
            {
                return default(T);
            }
            if (!typeof(T).IsGenericType)
            {
                return (T)Convert.ChangeType(convertibleValue, typeof(T));
            }
            else
            {
                Type genericTypeDefinition = typeof(T).GetGenericTypeDefinition();
                if (genericTypeDefinition == typeof(Nullable<>))
                {
                    if (typeof(T).FullName.Contains("Decimal"))
                    {
                        convertibleValue = ChangeDataToD(convertibleValue.ToString());
                    }
                    return (T)Convert.ChangeType(convertibleValue, Nullable.GetUnderlyingType(typeof(T)));
                }
            }
            throw new InvalidCastException(string.Format("Invalid cast from type \"{0}\" to type \"{1}\".", convertibleValue.GetType().FullName, typeof(T).FullName));
        }

        private static Decimal ChangeDataToD(string strData)
        {
            Decimal dData = 0.0M;
            if (strData.Contains("E"))
            {
                dData = Convert.ToDecimal(Decimal.Parse(strData.ToString(), System.Globalization.NumberStyles.Float));
            }
            else
            {
                dData = Decimal.Parse(strData);
            }
            return dData;
        }



    }
}
