﻿namespace ZTLisConnectors
{
    partial class MainConnecter
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.myGrid1 = new CustomControl.MyGrid();
            this.c1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.CmdSelectAll = new C1.Win.C1Command.C1Command();
            this.CmdSelectNone = new C1.Win.C1Command.C1Command();
            this.c1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.panel1 = new System.Windows.Forms.Panel();
            this.LblStatus = new System.Windows.Forms.Label();
            this.BtnClear = new Sunny.UI.UISymbolButton();
            this.BtnStart = new Sunny.UI.UISymbolButton();
            this.BtnStop = new Sunny.UI.UISymbolButton();
            this.ListMessages = new System.Windows.Forms.ListBox();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 300F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Controls.Add(this.ListMessages, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.myGrid1, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.c1ToolBar1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.panel1, 1, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1200, 700);
            this.tableLayoutPanel1.TabIndex = 0;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 40);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(106, 80);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 17;
            this.myGrid1.Xmlpath = null;
            // 
            // c1ToolBar1
            // 
            this.c1ToolBar1.AccessibleName = "Tool Bar";
            this.c1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.c1ToolBar1.CommandHolder = this.c1CommandHolder1;
            this.c1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink1,
            this.c1CommandLink2});
            this.c1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.c1ToolBar1.Name = "c1ToolBar1";
            this.c1ToolBar1.Size = new System.Drawing.Size(126, 24);
            this.c1ToolBar1.Text = "c1ToolBar1";
            this.c1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.c1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmdSelectAll);
            this.c1CommandHolder1.Commands.Add(this.CmdSelectNone);
            this.c1CommandHolder1.Owner = this;
            // 
            // CmdSelectAll
            // 
            this.CmdSelectAll.Image = global::ZTLisConnectors.Properties.Resources.全选;
            this.CmdSelectAll.Name = "CmdSelectAll";
            this.CmdSelectAll.ShortcutText = "";
            this.CmdSelectAll.Text = "全选";
            this.CmdSelectAll.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdSelectAll_Click);
            // 
            // CmdSelectNone
            // 
            this.CmdSelectNone.Image = global::ZTLisConnectors.Properties.Resources.反选;
            this.CmdSelectNone.Name = "CmdSelectNone";
            this.CmdSelectNone.ShortcutText = "";
            this.CmdSelectNone.Text = "反选";
            this.CmdSelectNone.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdSelectNone_Click);
            // 
            // c1CommandLink1
            // 
            this.c1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink1.Command = this.CmdSelectAll;
            // 
            // c1CommandLink2
            // 
            this.c1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink2.Command = this.CmdSelectNone;
            this.c1CommandLink2.SortOrder = 1;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.LblStatus);
            this.panel1.Controls.Add(this.BtnClear);
            this.panel1.Controls.Add(this.BtnStart);
            this.panel1.Controls.Add(this.BtnStop);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(300, 0);
            this.panel1.Margin = new System.Windows.Forms.Padding(0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(900, 40);
            this.panel1.TabIndex = 1;
            // 
            // LblStatus
            // 
            this.LblStatus.AutoSize = true;
            this.LblStatus.Font = new System.Drawing.Font("微软雅黑", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LblStatus.Location = new System.Drawing.Point(298, 10);
            this.LblStatus.Name = "LblStatus";
            this.LblStatus.Size = new System.Drawing.Size(133, 20);
            this.LblStatus.TabIndex = 1;
            this.LblStatus.Text = "运行状态：0/0 在线";
            // 
            // BtnClear
            // 
            this.BtnClear.Cursor = System.Windows.Forms.Cursors.Hand;
            this.BtnClear.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(155)))), ((int)(((byte)(40)))));
            this.BtnClear.FillColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(155)))), ((int)(((byte)(40)))));
            this.BtnClear.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(175)))), ((int)(((byte)(83)))));
            this.BtnClear.FillPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(176)))), ((int)(((byte)(124)))), ((int)(((byte)(32)))));
            this.BtnClear.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(176)))), ((int)(((byte)(124)))), ((int)(((byte)(32)))));
            this.BtnClear.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.BtnClear.Location = new System.Drawing.Point(175, 5);
            this.BtnClear.MinimumSize = new System.Drawing.Size(1, 1);
            this.BtnClear.Name = "BtnClear";
            this.BtnClear.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(155)))), ((int)(((byte)(40)))));
            this.BtnClear.RectHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(227)))), ((int)(((byte)(175)))), ((int)(((byte)(83)))));
            this.BtnClear.RectPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(176)))), ((int)(((byte)(124)))), ((int)(((byte)(32)))));
            this.BtnClear.RectSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(176)))), ((int)(((byte)(124)))), ((int)(((byte)(32)))));
            this.BtnClear.Size = new System.Drawing.Size(75, 30);
            this.BtnClear.Style = Sunny.UI.UIStyle.Orange;
            this.BtnClear.StyleCustomMode = true;
            this.BtnClear.Symbol = 61944;
            this.BtnClear.TabIndex = 7;
            this.BtnClear.Text = "清空";
            this.BtnClear.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnClear.ZoomScaleRect = new System.Drawing.Rectangle(0, 0, 0, 0);
            this.BtnClear.Click += new System.EventHandler(this.BtnClear_Click);
            // 
            // BtnStart
            // 
            this.BtnStart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.BtnStart.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.BtnStart.Location = new System.Drawing.Point(13, 5);
            this.BtnStart.MinimumSize = new System.Drawing.Size(1, 1);
            this.BtnStart.Name = "BtnStart";
            this.BtnStart.Size = new System.Drawing.Size(75, 30);
            this.BtnStart.Symbol = 61515;
            this.BtnStart.TabIndex = 5;
            this.BtnStart.Text = "启动";
            this.BtnStart.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnStart.ZoomScaleRect = new System.Drawing.Rectangle(0, 0, 0, 0);
            this.BtnStart.Click += new System.EventHandler(this.BtnStart_Click);
            // 
            // BtnStop
            // 
            this.BtnStop.Cursor = System.Windows.Forms.Cursors.Hand;
            this.BtnStop.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(80)))), ((int)(((byte)(80)))));
            this.BtnStop.FillColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(80)))), ((int)(((byte)(80)))));
            this.BtnStop.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(115)))), ((int)(((byte)(115)))));
            this.BtnStop.FillPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(184)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.BtnStop.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(184)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.BtnStop.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.BtnStop.Location = new System.Drawing.Point(94, 5);
            this.BtnStop.MinimumSize = new System.Drawing.Size(1, 1);
            this.BtnStop.Name = "BtnStop";
            this.BtnStop.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(80)))), ((int)(((byte)(80)))));
            this.BtnStop.RectHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(115)))), ((int)(((byte)(115)))));
            this.BtnStop.RectPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(184)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.BtnStop.RectSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(184)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.BtnStop.Size = new System.Drawing.Size(75, 30);
            this.BtnStop.Style = Sunny.UI.UIStyle.Red;
            this.BtnStop.StyleCustomMode = true;
            this.BtnStop.Symbol = 61517;
            this.BtnStop.TabIndex = 6;
            this.BtnStop.Text = "停止";
            this.BtnStop.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnStop.ZoomScaleRect = new System.Drawing.Rectangle(0, 0, 0, 0);
            this.BtnStop.Click += new System.EventHandler(this.BtnStop_Click);
            // 
            // ListMessages
            // 
            this.ListMessages.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ListMessages.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ListMessages.FormattingEnabled = true;
            this.ListMessages.ItemHeight = 14;
            this.ListMessages.Location = new System.Drawing.Point(303, 43);
            this.ListMessages.Name = "ListMessages";
            this.ListMessages.Size = new System.Drawing.Size(894, 654);
            this.ListMessages.TabIndex = 1;
            // 
            // MainConnecter
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "MainConnecter";
            this.Text = "设备数据接收器";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainConnecter_FormClosing);
            this.Load += new System.EventHandler(this.MainConnecter_Load);
            this.tableLayoutPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.Label LblStatus;
        private C1.Win.C1Command.C1ToolBar c1ToolBar1;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink1;
        private C1.Win.C1Command.C1Command CmdSelectAll;
        private Sunny.UI.UISymbolButton BtnClear;
        private Sunny.UI.UISymbolButton BtnStop;
        private Sunny.UI.UISymbolButton BtnStart;
        private C1.Win.C1Command.C1Command CmdSelectNone;
        private C1.Win.C1Command.C1CommandLink c1CommandLink2;
        private System.Windows.Forms.Panel panel1;
        private CustomControl.MyGrid myGrid1;
        private System.Windows.Forms.ListBox ListMessages;
    }
}