﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabSpecimen.cs
*
* 功 能： N/A
* 类 名： MdlLabSpecimen
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验标本信息表
	/// </summary>
	[Serializable]
	public partial class MdlLabSpecimen
	{
		public MdlLabSpecimen()
		{}
		#region Model
		private long _specimenid;
		private long _orderid;
		private string _barcode;
		private string _seqprefix;
		private int? _dailyseqno;
		private DateTime? _specimendate;
		private int _sampletypeid;
		private int _specimenstatus=10;
		private int? _collectedbyid;
		private string _collectedbyname;
		private DateTime? _collectedat;
		private int? _receivedbyid;
		private string _receivedbyname;
		private DateTime? _receivedat;
		private int? _speccondid;
		private string _rejectreason;
		private string _remarks;
		private DateTime _createdat= DateTime.Now;
		private int? _createdbyid;
		private string _createdbyname;
		private DateTime _updatedat= DateTime.Now;
		private int? _updatedbyid;
		private string _updatedbyname;
		/// <summary>
		/// LIS内部标本ID，自增主键
		/// </summary>
		public long SpecimenId
		{
			set{ _specimenid=value;}
			get{return _specimenid;}
		}
		/// <summary>
		/// 关联的申请单ID
		/// </summary>
		public long OrderId
		{
			set{ _orderid=value;}
			get{return _orderid;}
		}
		/// <summary>
		/// 标本唯一条码
		/// </summary>
		public string Barcode
		{
			set{ _barcode=value;}
			get{return _barcode;}
		}
		/// <summary>
		/// 样本号前缀。可选字段。用于标识样本批次（如 'A' 批）、类型（如 'U' 代表尿液）或机架号。
		/// </summary>
		public string SeqPrefix
		{
			set{ _seqprefix=value;}
			get{return _seqprefix;}
		}
		/// <summary>
		/// 当日流水号。与 SpecimenDate 和可选的 SeqPrefix 组合，构成完整的样本号。
		/// </summary>
		public int? DailySeqNo
		{
			set{ _dailyseqno=value;}
			get{return _dailyseqno;}
		}
		/// <summary>
		/// 标本日期。与 SeqPrefix 和 DailySeqNo 配合使用，确保复合样本号在特定日期内的唯一性。
		/// </summary>
		public DateTime? SpecimenDate
		{
			set{ _specimendate=value;}
			get{return _specimendate;}
		}
		/// <summary>
		/// 标本类型ID（血清/全血/尿液等）
		/// </summary>
		public int SampleTypeId
		{
			set{ _sampletypeid=value;}
			get{return _sampletypeid;}
		}
		/// <summary>
		/// 标本状态：10=待采集 20=已采集 30=已接收 40=检测中/完成 90=已废弃 99=已作废/拒收
		/// </summary>
		public int SpecimenStatus
		{
			set{ _specimenstatus=value;}
			get{return _specimenstatus;}
		}
		/// <summary>
		/// 采集人ID
		/// </summary>
		public int? CollectedById
		{
			set{ _collectedbyid=value;}
			get{return _collectedbyid;}
		}
		/// <summary>
		/// 采集人姓名（冗余）
		/// </summary>
		public string CollectedByName
		{
			set{ _collectedbyname=value;}
			get{return _collectedbyname;}
		}
		/// <summary>
		/// 采集时间
		/// </summary>
		public DateTime? CollectedAt
		{
			set{ _collectedat=value;}
			get{return _collectedat;}
		}
		/// <summary>
		/// 接收人ID
		/// </summary>
		public int? ReceivedById
		{
			set{ _receivedbyid=value;}
			get{return _receivedbyid;}
		}
		/// <summary>
		/// 接收人姓名（冗余）
		/// </summary>
		public string ReceivedByName
		{
			set{ _receivedbyname=value;}
			get{return _receivedbyname;}
		}
		/// <summary>
		/// 接收时间
		/// </summary>
		public DateTime? ReceivedAt
		{
			set{ _receivedat=value;}
			get{return _receivedat;}
		}
		/// <summary>
		/// 标本状态ID（溶血/脂血等）
		/// </summary>
		public int? SpecCondId
		{
			set{ _speccondid=value;}
			get{return _speccondid;}
		}
		/// <summary>
		/// 拒收原因（当SpecimenStatus=99时填写）
		/// </summary>
		public string RejectReason
		{
			set{ _rejectreason=value;}
			get{return _rejectreason;}
		}
		/// <summary>
		/// 标本备注：用于记录非拒收原因的标本相关信息，如"轻微溶血"、"乳糜血"等。
		/// </summary>
		public string Remarks
		{
			set{ _remarks=value;}
			get{return _remarks;}
		}
		/// <summary>
		/// 记录创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 创建人ID
		/// </summary>
		public int? CreatedById
		{
			set{ _createdbyid=value;}
			get{return _createdbyid;}
		}
		/// <summary>
		/// 创建人姓名（冗余）
		/// </summary>
		public string CreatedByName
		{
			set{ _createdbyname=value;}
			get{return _createdbyname;}
		}
		/// <summary>
		/// 记录更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		/// <summary>
		/// 最后更新人ID
		/// </summary>
		public int? UpdatedById
		{
			set{ _updatedbyid=value;}
			get{return _updatedbyid;}
		}
		/// <summary>
		/// 最后更新人姓名（冗余）
		/// </summary>
		public string UpdatedByName
		{
			set{ _updatedbyname=value;}
			get{return _updatedbyname;}
		}
		#endregion Model

	}
}

