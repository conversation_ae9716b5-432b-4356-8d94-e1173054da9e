﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common.Delegate
{
    public class TransmitDoubleTxt
    {
        public event Func<string,string,bool> SetDoubleTxt;

        public bool OnSetDoubleTxt(string txt1,string txt2)
        {
            if (SetDoubleTxt != null)
            {
               return SetDoubleTxt(txt1, txt2);
            }
            else
            {
                return false;
            }
        }
    }
}
