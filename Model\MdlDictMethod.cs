﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictMethod.cs
*
* 功 能： N/A
* 类 名： MdlDictMethod
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:13   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验方法学字典表，记录具体方法（如 化学发光免疫分析、PCR）并归类到方法学类别
	/// </summary>
	[Serializable]
	public partial class MdlDictMethod
	{
		public MdlDictMethod()
		{}
		#region Model
		private int _methodid;
		private string _methodcode;
		private string _methodname;
		private string _englishname;
		private string _pinyin;
		private int _methodcategoryid;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int MethodId
		{
			set{ _methodid=value;}
			get{return _methodid;}
		}
		/// <summary>
		/// 方法学编码，如 CLIA、PCR
		/// </summary>
		public string MethodCode
		{
			set{ _methodcode=value;}
			get{return _methodcode;}
		}
		/// <summary>
		/// 方法中文名称，如 化学发光免疫分析
		/// </summary>
		public string MethodName
		{
			set{ _methodname=value;}
			get{return _methodname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string EnglishName
		{
			set{ _englishname=value;}
			get{return _englishname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 方法学类别ID (FK)
		/// </summary>
		public int MethodCategoryId
		{
			set{ _methodcategoryid=value;}
			get{return _methodcategoryid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

