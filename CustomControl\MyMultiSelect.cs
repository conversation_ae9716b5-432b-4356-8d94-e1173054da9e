﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Input;
using C1.Win.Input;
using C1.Win.Interop;

namespace CustomControl
{
    public partial class MyMultiSelect : UserControl
    {
        public MyMultiSelect()
        {
            InitializeComponent();
            Init_MultiSelect();
        }

        #region  私有函数

        private void Init_MultiSelect()
        {

        }

        #endregion

        #region 公开属性
        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                Invalidate();
            }
        }


        [Description("字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                c1MultiSelect1.Font = value;
                c1MultiSelect1.Styles.CheckList.Font = value;
                c1MultiSelect1.Styles.TagEditor.Common.Font = value;
                Invalidate();
            }
        }

        [Description("标题字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }

        [Description("下拉文本字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font ItemTextFont
        {
            get { return c1MultiSelect1.Font; }
            set
            {
                c1MultiSelect1.Font = value;
                c1MultiSelect1.Styles.CheckList.Font = value;
                c1MultiSelect1.Styles.TagEditor.Common.Font = value;
                Invalidate();
            }
        }

        [Description("控件是否启用"), Browsable(true)]
        public new bool Enabled
        {
            get { return c1MultiSelect1.Enabled; }
            set
            {
                c1MultiSelect1.Enabled = value;
                if (value == true)
                {
                    c1MultiSelect1.TabStop = true;
                    this.TabStop = true;
                }
                else
                {
                    c1MultiSelect1.TabStop = false;
                    this.TabStop = false;
                }

                Invalidate();
            }
        }

        float _CaptainWidth = 70;
        [Description("标题宽度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0)
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }

                Invalidate();
            }
        }

        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }

        [Description("数据源")]
        public MultiSelectBindingInfo BindingInfo
        {
            get { return c1MultiSelect1.BindingInfo; }
        }


        private bool _Captainlink;
        [Description("标题是否超链接"), DefaultValue(false)]
        public bool CaptainLink
        {

            get { return _Captainlink; }
            set
            {
                _Captainlink = value;
                if (value == true)
                {
                    label1.Font = new System.Drawing.Font("宋体", 9, FontStyle.Underline);
                    label1.ForeColor = Color.Blue;
                }
                else
                {
                    label1.Font = new System.Drawing.Font("宋体", 9, FontStyle.Regular);
                    label1.ForeColor = Color.Black;
                }
                Invalidate();
            }
        }


        [Description("下拉高度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public int DropDownHeight
        {
            get { return c1MultiSelect1.DropDownHeight; }
            set
            {
                c1MultiSelect1.DropDownHeight = value;
                Invalidate();
            }
        }
        [Description("下拉宽度")]
        public int DropDownWidth
        {
            get { return c1MultiSelect1.DropDownWidth; }
            set
            {
                c1MultiSelect1.DropDownWidth = value;
                Invalidate();
            }
        }

        [Description("标题是否超链接")]
        public bool TagWrap
        {
            get { return c1MultiSelect1.TagWrap; }
            set
            {
                c1MultiSelect1.TagWrap = value;
                Invalidate();
            }
        }

        public string Placeholder
        {
            get { return c1MultiSelect1.Placeholder; }
            set
            {
                c1MultiSelect1.Placeholder = value;
                Invalidate();
            }
        }

        public AutoSuggestMode AutoSuggestMode
        {
            get { return c1MultiSelect1.AutoSuggestMode; }
            set
            {
                c1MultiSelect1.AutoSuggestMode = value;
                Invalidate();
            }
        }

        public int MaxHeaderItems
        {
            get { return c1MultiSelect1.MaxHeaderItems; }
            set
            {
                c1MultiSelect1.MaxHeaderItems = value;
                Invalidate();
            }
        }

        public C1CheckListItemCollection Items
        {
            get { return c1MultiSelect1.Items; }
        }

        public object[] SelectedValues
        {
            get { return c1MultiSelect1.SelectedValues; }
        }
        public C1CheckListSelectedItemCollection SelectedItems
        {
            get { return c1MultiSelect1.SelectedItems; }
        }
        #endregion

        #region 公开方法
        public new void SelectAll()
        {
            c1MultiSelect1.SelectAll();
        }

        #endregion

        #region 公开事件

        [Description("当选择项发生变化时触发"), Browsable(true)]
        public event EventHandler SelectionChanged
        {
            add
            {
                c1MultiSelect1.SelectionChanged += value;
            }
            remove
            {
                c1MultiSelect1.SelectionChanged -= value;
            }
        }

        #endregion
    }
}
