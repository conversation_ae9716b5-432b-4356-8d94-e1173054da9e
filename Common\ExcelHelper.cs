﻿using System;
using System.IO;
using System.Text;
using System.Web;
using System.Collections.Generic;
using NPOI.HPSF;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.Util;
using NPOI;
using NPOI.HSSF;
using NPOI.POIFS;
using NPOI.HSSF.Record.CF;
using System.Data;
using System.Linq;
using NPOI.Util;

namespace Common
{
    public class ExcelHelper
    {
        #region 输出Excel


        /// <summary>
        /// DataTable 导出到Excel 的MemoryStream
        /// </summary>
        /// <param name="datasource">源DataTable</param>
        /// <param name="headertext">表头文本</param>
        /// <param name="colconfig">字典参数，Object顺序为长度，格式，列名，是否显示</param>
        /// <param name="showHeader">是否显示Excel名称行(非列头)</param>
        public static void Export(System.Data.DataTable datasource, string headertext, Dictionary<string, MdlExcel> colconfig, bool showHeader = true)
        {
            //colconfig 顺序是 长度，格式，列名，是否显示
            string filename;
            System.Windows.Forms.SaveFileDialog save = new System.Windows.Forms.SaveFileDialog();
            {
                save.Filter = "Excel文件(.xls)|*.xls";
                save.FileName = headertext + DateTime.Now.ToString("yyMMdd") + ".xls";
                if (save.ShowDialog() == System.Windows.Forms.DialogResult.Cancel)
                {
                    return;
                }
                filename = save.FileName;
                save.AddExtension = true;
            }

            object[] args = new object[4];
            args[0] = datasource;
            args[1] = headertext;
            args[2] = colconfig;
            args[3] = showHeader;
            using (MemoryStream ms = (MemoryStream)Jacksonsoft.WaitWindow.Show(ExportXls, datasource.Rows.Count, args))
            {
                try
                {
                    using (FileStream fs = new FileStream(filename, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                    System.Windows.Forms.MessageBox.Show("导出完成", "提示", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.Forms.MessageBox.Show(ex.ToString(), "提示", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning);

                }

            }
        }

        public static void ExportXls(object sender, Jacksonsoft.WaitWindowEventArgs e)
        {
            System.Data.DataTable dtSource = (System.Data.DataTable)e.Arguments[0];
            string strHeaderText = (string)e.Arguments[1];
            bool showHeader = (bool)e.Arguments[3];
            Dictionary<string, MdlExcel> colconfig = (Dictionary<string, MdlExcel>)e.Arguments[2];

            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet sheet = (HSSFSheet)workbook.CreateSheet(strHeaderText);
            //创建sheet
            HSSFCellStyle dateCellStyle = (HSSFCellStyle)workbook.CreateCellStyle();

            //通用单元格样式
            dateCellStyle.BorderBottom = BorderStyle.Thin;
            dateCellStyle.BorderLeft = BorderStyle.Thin;
            dateCellStyle.VerticalAlignment = VerticalAlignment.Center;
            dateCellStyle.Alignment = HorizontalAlignment.Center;
            dateCellStyle.BorderRight = BorderStyle.Thin;
            dateCellStyle.BorderTop = BorderStyle.Thin;
            dateCellStyle.WrapText = true;
            dateCellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("@");
            HSSFFont datafont = (HSSFFont)workbook.CreateFont();
            datafont.FontHeightInPoints = 11;
            datafont.FontName = "宋体";
            dateCellStyle.SetFont(datafont);
            int i = 0;
            int rowIndex = 0;
            int ColVisble = 0;
            int ColUnVisble = colconfig.Where(p => p.Value.ColumnVisible == false).Count();
            foreach (DataRow row in dtSource.Rows)
            {
                //新建表，填充表头，填充列头，样式
                if (rowIndex == 65535 || rowIndex == 0)
                {
                    if (rowIndex != 0)
                    {
                        sheet = (HSSFSheet)workbook.CreateSheet();
                    }

                    //#region 表头及样式
                    if (showHeader)
                    {
                        HSSFCellStyle headStyle = (HSSFCellStyle)workbook.CreateCellStyle();
                        //创建单元格样式
                        headStyle.Alignment = HorizontalAlignment.Center;
                        headStyle.BorderBottom = BorderStyle.Thin;
                        headStyle.BorderLeft = BorderStyle.Thin;
                        headStyle.BorderRight = BorderStyle.Thin;
                        headStyle.BorderTop = BorderStyle.Thin;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        HSSFFont font = (HSSFFont)workbook.CreateFont();
                        font.FontHeightInPoints = 24;
                        //设置字体大小
                        font.Color = HSSFColor.Red.Index;
                        font.FontName = "黑体";
                        headStyle.SetFont(font);
                        int dtRowcount = dtSource.Columns.Count - ColUnVisble;
                        //获取表格的列数
                        HSSFRow headerRow = (HSSFRow)sheet.CreateRow(rowIndex);
                        headerRow.HeightInPoints = 45;
                        i = 0;
                        while (i < dtRowcount)
                        {
                            sheet.GetRow(0).CreateCell(i).SetCellValue(strHeaderText);
                            sheet.GetRow(0).GetCell(i).CellStyle = headStyle;
                            sheet.GetRow(0).GetCell(i).SetCellType(CellType.String);
                            //设置列为字符串型
                            i++;
                        }
                        sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, dtSource.Columns.Count - ColUnVisble - 1));
                        //合并单元格
                        rowIndex++;
                    }

                    //#region 列头及样式
                    if (true)
                    {
                        HSSFRow headerRow = (HSSFRow)sheet.CreateRow(rowIndex);
                        HSSFCellStyle headStyle = (HSSFCellStyle)workbook.CreateCellStyle();
                        //headStyle.FillPattern = FillPatternType.SOLID_FOREGROUND
                        headStyle.FillForegroundColor = 41;

                        headStyle.WrapText = true;
                        headStyle.VerticalAlignment = VerticalAlignment.Center;
                        headStyle.Alignment = HorizontalAlignment.Center;
                        headStyle.BorderBottom = BorderStyle.Thin;
                        headStyle.BorderLeft = BorderStyle.Thin;
                        headStyle.BorderRight = BorderStyle.Thin;
                        headStyle.BorderTop = BorderStyle.Thin;

                        HSSFFont font = (HSSFFont)workbook.CreateFont();
                        font.FontHeightInPoints = 12;
                        font.Boldweight = (short)NPOI.SS.UserModel.FontBoldWeight.Bold;
                        font.FontName = "宋体";
                        headStyle.SetFont(font);
                        foreach (DataColumn column in dtSource.Columns)
                        {
                            //设置列宽
                            if (colconfig.ContainsKey(column.ColumnName))
                            {
                                if (Convert.ToBoolean(colconfig[column.ColumnName].ColumnVisible) == false)
                                {
                                    ColVisble = ColVisble + 1;
                                    continue;
                                }
                                headerRow.CreateCell(column.Ordinal - ColVisble).SetCellValue(colconfig[column.ColumnName].HeaderName.ToString());
                                sheet.SetColumnWidth(column.Ordinal - ColVisble, (colconfig[column.ColumnName].ColumnWidth + 1) * 256);
                            }
                            else
                            {
                                headerRow.CreateCell(column.Ordinal - ColVisble).SetCellValue(column.ColumnName);
                                sheet.SetColumnWidth(column.Ordinal - ColVisble, 11 * 256);
                            }
                            headerRow.GetCell(column.Ordinal - ColVisble).CellStyle = headStyle;
                            headerRow.GetCell(column.Ordinal - ColVisble).SetCellType(CellType.String);
                            //设置列为字符串型
                        }

                        rowIndex++;
                    }
                    sheet.CreateFreezePane(0, rowIndex, 0, rowIndex);
                }


                //#region 填充内容
                HSSFRow dataRow = (HSSFRow)sheet.CreateRow(rowIndex);
                ColVisble = 0;
                foreach (DataColumn column in dtSource.Columns)
                {
                    if (colconfig.ContainsKey(column.ColumnName))
                    {
                        if (Convert.ToBoolean(colconfig[column.ColumnName].ColumnVisible) == false)
                        {
                            ColVisble = ColVisble + 1;
                            continue;
                        }
                    }

                    HSSFCell newCell = (HSSFCell)dataRow.CreateCell(column.Ordinal - ColVisble);
                    newCell.CellStyle = dateCellStyle;
                    //设置单元格格式
                    newCell.SetCellType(CellType.String);

                    string drValue = row[column].ToString().Replace(",", "," + "\n");
                    //填充的值
                    switch (column.DataType.ToString())
                    {
                        case "System.String":
                            //字符串类型
                            newCell.SetCellValue(drValue);
                            break;


                        case "System.DateTime":
                            //日期类型     
                            DateTime DateV;
                            DateTime.TryParse(drValue, out DateV);
                            if (colconfig.ContainsKey(column.ColumnName))
                            {
                                drValue = DateV.ToString(colconfig[column.ColumnName].ColumnFormat);
                            }
                            else
                            {
                                drValue = DateV.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            newCell.SetCellValue(drValue);
                            break;


                        case "System.Boolean":
                            //布尔型
                            bool boolV = false;
                            bool.TryParse(drValue, out boolV);
                            newCell.SetCellValue(boolV);
                            break;


                        //整型
                        case "System.Int16":
                        case "System.Int32":
                        case "System.Int64":
                        case "System.Byte":
                            int intV = 0;
                            bool retint = int.TryParse(drValue, out intV);
                            if (retint)
                            {
                                newCell.SetCellValue(intV);
                            }
                            else
                            {
                                newCell.SetCellValue("");
                            }
                            break;
                        //浮点型
                        case "System.Decimal":
                        case "System.Double":
                            double doubV = 0;
                            bool retd = double.TryParse(drValue, out doubV);
                            if (retd)
                            {
                                if (colconfig.ContainsKey(column.ColumnName))
                                {
                                    drValue = doubV.ToString(colconfig[column.ColumnName].ColumnFormat);
                                }
                                newCell.SetCellValue(drValue);
                            }
                            else
                            {
                                newCell.SetCellValue("");
                            }
                            break;


                        case "System.DBNull":
                            //空值处理
                            newCell.SetCellValue("");
                            break;

                        default:
                            newCell.SetCellValue("");
                            break;


                    }
                }
                rowIndex++;
                e.Window.BarValue = 1;
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                ms.Flush();
                ms.Position = 0;
                e.Result = ms;
            }
        }



        /// <summary>     
        /// DataTable导出到Excel文件     
        /// </summary>     
        /// <param name="dtSource">源DataTable</param>     
        /// <param name="strHeaderText">表头文本</param>     
        /// <param name="strFileName">保存位置</param>  
        /// <param name="strSheetName">工作表名称</param>  
        public static void Export(DataTable dtSource, string strHeaderText, string strFileName, string strSheetName, string[] oldColumnNames, string[] newColumnNames)
        {
            if (strSheetName == "")
            {
                strSheetName = "Sheet";
            }
            using (MemoryStream ms = Export(dtSource, strHeaderText, strSheetName, oldColumnNames, newColumnNames))
            {
                using (FileStream fs = new FileStream(strFileName, FileMode.Create, FileAccess.Write))
                {
                    byte[] data = ms.ToArray();
                    fs.Write(data, 0, data.Length);
                    fs.Flush();
                }
            }
        }

        /// <summary>     
        /// DataTable导出到Excel的MemoryStream     
        /// </summary>     
        /// <param name="dtSource">源DataTable</param>     
        /// <param name="strHeaderText">表头文本</param>     
        /// <param name="strSheetName">工作表名称</param>   
        public static MemoryStream Export(DataTable dtSource, string strHeaderText, string strSheetName, string[] oldColumnNames, string[] newColumnNames)
        {
            if (oldColumnNames.Length != newColumnNames.Length)
            {
                return new MemoryStream();
            }
            HSSFWorkbook workbook = new HSSFWorkbook();
            //HSSFSheet sheet = workbook.CreateSheet();// workbook.CreateSheet();  
            ISheet sheet = workbook.CreateSheet(strSheetName);

            #region 右击文件 属性信息  
            {
                DocumentSummaryInformation dsi = PropertySetFactory.CreateDocumentSummaryInformation();
                dsi.Company = "http://....../";
                workbook.DocumentSummaryInformation = dsi;

                SummaryInformation si = PropertySetFactory.CreateSummaryInformation();
                if (HttpContext.Current.Session["realname"] != null)
                {
                    si.Author = HttpContext.Current.Session["realname"].ToString();
                }
                else
                {
                    if (HttpContext.Current.Session["username"] != null)
                    {
                        si.Author = HttpContext.Current.Session["username"].ToString();
                    }
                }                                       //填加xls文件作者信息     
                si.ApplicationName = "NPOI";            //填加xls文件创建程序信息     
                si.LastAuthor = "OA系统";           //填加xls文件最后保存者信息     
                si.Comments = "OA系统自动创建文件";      //填加xls文件作者信息     
                si.Title = strHeaderText;               //填加xls文件标题信息     
                si.Subject = strHeaderText;              //填加文件主题信息     
                si.CreateDateTime = DateTime.Now;
                workbook.SummaryInformation = si;
            }
            #endregion

            ICellStyle dateStyle = workbook.CreateCellStyle();
            IDataFormat format = workbook.CreateDataFormat();
            dateStyle.DataFormat = format.GetFormat("yyyy-mm-dd");

            #region 取得列宽  
            int[] arrColWidth = new int[oldColumnNames.Length];
            for (int i = 0; i < oldColumnNames.Length; i++)
            {
                arrColWidth[i] = Encoding.GetEncoding(936).GetBytes(newColumnNames[i]).Length;
            }
            /* 
            foreach (DataColumn item in dtSource.Columns) 
            { 
                arrColWidth[item.Ordinal] = Encoding.GetEncoding(936).GetBytes(item.ColumnName.ToString()).Length; 
            } 
             * */

            for (int i = 0; i < dtSource.Rows.Count; i++)
            {
                for (int j = 0; j < oldColumnNames.Length; j++)
                {
                    int intTemp = Encoding.GetEncoding(936).GetBytes(dtSource.Rows[i][oldColumnNames[j]].ToString()).Length;
                    if (intTemp > arrColWidth[j])
                    {
                        arrColWidth[j] = intTemp;
                    }
                }
                /* 
                for (int j = 0; j < dtSource.Columns.Count; j++) 
                { 
                    int intTemp = Encoding.GetEncoding(936).GetBytes(dtSource.Rows[i][j].ToString()).Length; 
                    if (intTemp > arrColWidth[j]) 
                    { 
                        arrColWidth[j] = intTemp; 
                    } 
                } 
                 * */
            }
            #endregion
            int rowIndex = 0;

            foreach (DataRow row in dtSource.Rows)
            {
                #region 新建表，填充表头，填充列头，样式  
                if (rowIndex == 65535 || rowIndex == 0)
                {
                    if (rowIndex != 0)
                    {
                        sheet = workbook.CreateSheet(strSheetName + ((int)rowIndex / 65535).ToString());
                    }

                    #region 表头及样式  
                    {
                        IRow headerRow = sheet.CreateRow(0);
                        headerRow.HeightInPoints = 25;
                        headerRow.CreateCell(0).SetCellValue(strHeaderText);

                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Center;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 20;
                        font.Boldweight = 700;
                        headStyle.SetFont(font);

                        headerRow.GetCell(0).CellStyle = headStyle;
                        //sheet.AddMergedRegion(new Region(0, 0, 0, dtSource.Columns.Count - 1));  
                        sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(0, 0, 0, dtSource.Columns.Count - 1));
                    }
                    #endregion


                    #region 列头及样式  
                    {
                        //HSSFRow headerRow = sheet.CreateRow(1);  
                        IRow headerRow = sheet.CreateRow(1);

                        ICellStyle headStyle = workbook.CreateCellStyle();
                        headStyle.Alignment = HorizontalAlignment.Center;
                        IFont font = workbook.CreateFont();
                        font.FontHeightInPoints = 10;
                        font.Boldweight = 700;
                        headStyle.SetFont(font);

                        for (int i = 0; i < oldColumnNames.Length; i++)
                        {
                            headerRow.CreateCell(i).SetCellValue(newColumnNames[i]);
                            headerRow.GetCell(i).CellStyle = headStyle;
                            //设置列宽  
                            sheet.SetColumnWidth(i, (arrColWidth[i] + 1) * 256);
                        }
                        /* 
                        foreach (DataColumn column in dtSource.Columns) 
                        { 
                            headerRow.CreateCell(column.Ordinal).SetCellValue(column.ColumnName); 
                            headerRow.GetCell(column.Ordinal).CellStyle = headStyle; 

                            //设置列宽    
                            sheet.SetColumnWidth(column.Ordinal, (arrColWidth[column.Ordinal] + 1) * 256); 
                        } 
                         * */
                    }
                    #endregion

                    rowIndex = 2;
                }
                #endregion


                #region 填充内容  
                IRow dataRow = sheet.CreateRow(rowIndex);
                //foreach (DataColumn column in dtSource.Columns)  
                for (int i = 0; i < oldColumnNames.Length; i++)
                {
                    ICell newCell = dataRow.CreateCell(i);

                    string drValue = row[oldColumnNames[i]].ToString();

                    switch (dtSource.Columns[oldColumnNames[i]].DataType.ToString())
                    {
                        case "System.String"://字符串类型     
                            newCell.SetCellValue(drValue);
                            break;
                        case "System.DateTime"://日期类型     
                            DateTime dateV;
                            DateTime.TryParse(drValue, out dateV);
                            newCell.SetCellValue(dateV);

                            newCell.CellStyle = dateStyle;//格式化显示     
                            break;
                        case "System.Boolean"://布尔型     
                            bool boolV = false;
                            bool.TryParse(drValue, out boolV);
                            newCell.SetCellValue(boolV);
                            break;
                        case "System.Int16"://整型     
                        case "System.Int32":
                        case "System.Int64":
                        case "System.Byte":
                            int intV = 0;
                            int.TryParse(drValue, out intV);
                            newCell.SetCellValue(intV);
                            break;
                        case "System.Decimal"://浮点型     
                        case "System.Double":
                            double doubV = 0;
                            double.TryParse(drValue, out doubV);
                            newCell.SetCellValue(doubV);
                            break;
                        case "System.DBNull"://空值处理     
                            newCell.SetCellValue("");
                            break;
                        default:
                            newCell.SetCellValue("");
                            break;
                    }

                }
                #endregion

                rowIndex++;
            }


            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                ms.Flush();
                ms.Position = 0;

                //sheet.Dispose();  
                sheet = null;
                workbook = null;
                //workbook.Dispose();//一般只用写这一个就OK了，他会遍历并释放所有资源，但当前版本有问题所以只释放sheet     
                return ms;
            }
        }


        /// <summary>     
        /// WEB导出DataTable到Excel     
        /// </summary>     
        /// <param name="dtSource">源DataTable</param>     
        /// <param name="strHeaderText">表头文本</param>     
        /// <param name="strFileName">文件名</param>     
        /// <Author>柳永法 http://www.yongfa365.com/ 2010-5-8 22:21:41</Author>     
        public static void ExportByWeb(DataTable dtSource, string strHeaderText, string strFileName)
        {
            ExportByWeb(dtSource, strHeaderText, strFileName, "sheet");
        }

        /// <summary>  
        /// WEB导出DataTable到Excel  
        /// </summary>  
        /// <param name="dtSource">源DataTable</param>  
        /// <param name="strHeaderText">表头文本</param>  
        /// <param name="strFileName">输出文件名，包含扩展名</param>  
        /// <param name="oldColumnNames">要导出的DataTable列数组</param>  
        /// <param name="newColumnNames">导出后的对应列名</param>  
        public static void ExportByWeb(DataTable dtSource, string strHeaderText, string strFileName, string[] oldColumnNames, string[] newColumnNames)
        {
            ExportByWeb(dtSource, strHeaderText, strFileName, "sheet", oldColumnNames, newColumnNames);
        }

        /// <summary>  
        /// WEB导出DataTable到Excel  
        /// </summary>  
        /// <param name="dtSource">源DataTable</param>  
        /// <param name="strHeaderText">表头文本</param>  
        /// <param name="strFileName">输出文件名</param>  
        /// <param name="strSheetName">工作表名称</param>  
        public static void ExportByWeb(DataTable dtSource, string strHeaderText, string strFileName, string strSheetName)
        {
            HttpContext curContext = HttpContext.Current;

            // 设置编码和附件格式     
            curContext.Response.ContentType = "application/vnd.ms-excel";
            curContext.Response.ContentEncoding = Encoding.UTF8;
            curContext.Response.Charset = "";
            curContext.Response.AppendHeader("Content-Disposition",
                "attachment;filename=" + HttpUtility.UrlEncode(strFileName, Encoding.UTF8));

            //生成列  
            string columns = "";
            for (int i = 0; i < dtSource.Columns.Count; i++)
            {
                if (i > 0)
                {
                    columns += ",";
                }
                columns += dtSource.Columns[i].ColumnName;
            }

            curContext.Response.BinaryWrite(Export(dtSource, strHeaderText, strSheetName, columns.Split(','), columns.Split(',')).GetBuffer());
            curContext.Response.End();

        }

        /// <summary>  
        /// 导出DataTable到Excel  
        /// </summary>  
        /// <param name="dtSource">要导出的DataTable</param>  
        /// <param name="strHeaderText">标题文字</param>  
        /// <param name="strFileName">文件名，包含扩展名</param>  
        /// <param name="strSheetName">工作表名</param>  
        /// <param name="oldColumnNames">要导出的DataTable列数组</param>  
        /// <param name="newColumnNames">导出后的对应列名</param>  
        public static void ExportByWeb(DataTable dtSource, string strHeaderText, string strFileName, string strSheetName, string[] oldColumnNames, string[] newColumnNames)
        {
            HttpContext curContext = HttpContext.Current;

            // 设置编码和附件格式     
            curContext.Response.ContentType = "application/vnd.ms-excel";
            curContext.Response.ContentEncoding = Encoding.UTF8;
            curContext.Response.Charset = "";
            curContext.Response.AppendHeader("Content-Disposition",
                "attachment;filename=" + HttpUtility.UrlEncode(strFileName, Encoding.UTF8));

            curContext.Response.BinaryWrite(Export(dtSource, strHeaderText, strSheetName, oldColumnNames, newColumnNames).GetBuffer());
            curContext.Response.End();
        }

        #endregion

        #region 输入Excel

        /// <summary>读取excel     
        /// 默认第一行为表头，导入第一个工作表  
        /// </summary>     
        /// <param name="strFileName">excel文档路径</param>     
        /// <returns></returns>     
        public static DataTable Import(string strFileName)
        {
            return RenderDataTableFromExcel(strFileName, 0, 0);
        }
        /// <summary>读取excel     
        /// 默认第一行为表头，工作表自定义
        /// </summary>     
        /// <param name="strFileName">excel文档路径</param> 
        /// <param name="sheetindex">工作表序号，从0开始</param>         
        /// <returns></returns>
        public static DataTable Import(string strFileName, int sheetindex)
        {
            DataTable dt = new DataTable();

            IWorkbook hssfworkbook;
            using (FileStream file = new FileStream(strFileName, FileMode.Open, FileAccess.Read))
            {
                if (Path.GetExtension(strFileName) == ".xls")
                {
                    hssfworkbook = new HSSFWorkbook(file);
                }
                else
                {
                    hssfworkbook = new XSSFWorkbook(file);
                }

            }
            ISheet sheet = hssfworkbook.GetSheetAt(sheetindex);
            System.Collections.IEnumerator rows = sheet.GetRowEnumerator();

            IRow headerRow = sheet.GetRow(0);
            int cellCount = headerRow.LastCellNum;

            for (int j = 0; j < cellCount; j++)
            {
                ICell cell = headerRow.GetCell(j);
                dt.Columns.Add(cell.ToString());
            }

            for (int i = (sheet.FirstRowNum + 1); i <= sheet.LastRowNum; i++)
            {
                IRow row = sheet.GetRow(i);
                DataRow dataRow = dt.NewRow();

                for (int j = row.FirstCellNum; j < cellCount; j++)
                {
                    if (row.GetCell(j) != null)
                        dataRow[j] = row.GetCell(j).ToString();
                }
                dt.Rows.Add(dataRow);
            }
            return dt;
        }

        /// <summary>  
        /// 从Excel中获取数据到DataTable  
        /// </summary>  
        /// <param name="strFileName">Excel文件全路径(服务器路径)</param>  
        /// <param name="SheetName">要获取数据的工作表名称</param>  
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>  
        /// <returns></returns>  
        public static DataTable RenderDataTableFromExcel(string strFileName, string SheetName, int HeaderRowIndex)
        {
            using (FileStream file = new FileStream(strFileName, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workbook = new HSSFWorkbook(file);
                return RenderDataTableFromExcel(workbook, SheetName, HeaderRowIndex);
            }
        }

        /// <summary>  
        /// 从Excel中获取数据到DataTable  
        /// </summary>  
        /// <param name="strFileName">Excel文件全路径(服务器路径)</param>  
        /// <param name="SheetIndex">要获取数据的工作表序号(从0开始)</param>  
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>  
        /// <returns></returns>  
        public static DataTable RenderDataTableFromExcel(string strFileName, int SheetIndex, int HeaderRowIndex)
        {
            using (FileStream file = new FileStream(strFileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            {
                IWorkbook workbook;

                if (Path.GetExtension(strFileName).ToLower() == ".xls")
                {
                    workbook = new HSSFWorkbook(file);
                }
                else
                {
                    workbook = new XSSFWorkbook(file);
                }
                string SheetName = workbook.GetSheetName(SheetIndex);
                return RenderDataTableFromExcel(workbook, SheetName, HeaderRowIndex);
            }
        }

        /// <summary>  
        /// 从Excel中获取数据到DataTable  
        /// </summary>  
        /// <param name="ExcelFileStream">Excel文件流</param>  
        /// <param name="SheetName">要获取数据的工作表名称</param>  
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>  
        /// <returns></returns>  
        public static DataTable RenderDataTableFromExcel(Stream ExcelFileStream, string SheetName, int HeaderRowIndex)
        {
            IWorkbook workbook = new HSSFWorkbook(ExcelFileStream);
            ExcelFileStream.Close();
            return RenderDataTableFromExcel(workbook, SheetName, HeaderRowIndex);
        }

        /// <summary>  
        /// 从Excel中获取数据到DataTable  
        /// </summary>  
        /// <param name="ExcelFileStream">Excel文件流</param>  
        /// <param name="SheetIndex">要获取数据的工作表序号(从0开始)</param>  
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>  
        /// <returns></returns>  
        public static DataTable RenderDataTableFromExcel(Stream ExcelFileStream, int SheetIndex, int HeaderRowIndex)
        {
            IWorkbook workbook = new HSSFWorkbook(ExcelFileStream);
            ExcelFileStream.Close();
            string SheetName = workbook.GetSheetName(SheetIndex);
            return RenderDataTableFromExcel(workbook, SheetName, HeaderRowIndex);
        }

        /// <summary>   
        /// 从Excel中获取数据到DataTable   
        /// </summary>   
        /// <param name="strFileName">Excel文件全路径(服务器路径)</param>   
        /// <param name="SheetIndex">要获取数据的工作表序号(从0开始)</param>   
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>   
        /// <param name="table">需要插入的表名</param>  
        /// <param name="ColCount">需要导入的列数</param>  
        /// <param name="NotNullCol">必填列</param>  
        /// <returns></returns>   
        public static string RenderDataTableFromExcel(string strFileName, int SheetIndex, int HeaderRowIndex, DataTable table, int ColCount, int NotNullCol)
        {
            using (FileStream file = new FileStream(strFileName, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workbook;
                if (Path.GetExtension(strFileName).ToLower() == ".xls")
                {
                    workbook = new HSSFWorkbook(file);
                }
                else
                {
                    workbook = new XSSFWorkbook(file);
                }
                string SheetName = workbook.GetSheetName(SheetIndex);
                return RenderDataTableFromExcel(workbook, SheetName, HeaderRowIndex, table, ColCount, NotNullCol);
            }
        }

        /// <summary>  
        /// 从Excel中获取数据到DataTable  
        /// </summary>  
        /// <param name="workbook">要处理的工作薄</param>  
        /// <param name="SheetName">要获取数据的工作表名称</param>  
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>  
        /// <returns></returns>  
        private static DataTable RenderDataTableFromExcel(IWorkbook workbook, string SheetName, int HeaderRowIndex)
        {
            ISheet sheet = workbook.GetSheet(SheetName);
            DataTable table = new DataTable();
            try
            {
                IRow headerRow = sheet.GetRow(HeaderRowIndex);
                int cellCount = headerRow.LastCellNum;
                int nameFlag;
                for (int i = headerRow.FirstCellNum; i < cellCount; i++)
                {
                    DataColumn column = new DataColumn(headerRow.GetCell(i).StringCellValue);
                    nameFlag = 1;
                    while (table.Columns.Contains(column.ColumnName))
                    {
                        column.ColumnName = column.ColumnName + nameFlag.ToString();
                        nameFlag++;
                    }

                    table.Columns.Add(column);
                }

                int rowCount = sheet.LastRowNum;

                #region 循环各行各列,写入数据到DataTable  
                for (int i = (HeaderRowIndex + 1); i < rowCount + 1; i++)
                {
                    IRow row = sheet.GetRow(i);
                    DataRow dataRow = table.NewRow();
                    for (int j = row.FirstCellNum; j < cellCount; j++)
                    {
                        ICell cell = row.GetCell(j);
                        if (cell == null)
                        {
                            dataRow[j] = null;
                        }
                        else
                        {
                            //dataRow[j] = cell.ToString();  
                            switch (cell.CellType)
                            {
                                case CellType.Blank:
                                    dataRow[j] = null;
                                    break;
                                case CellType.Boolean:
                                    dataRow[j] = cell.BooleanCellValue;
                                    break;
                                case CellType.Numeric:
                                    if (DateUtil.IsValidExcelDate(cell.NumericCellValue) && DateUtil.IsCellDateFormatted(row.GetCell(j)))
                                    {
                                        dataRow[j] = cell.DateCellValue;
                                    }
                                    else
                                    {
                                        dataRow[j] = cell.NumericCellValue;
                                    }
                                    break;
                                case CellType.String:
                                    dataRow[j] = cell.StringCellValue;
                                    break;
                                case CellType.Error:
                                    dataRow[j] = cell.ErrorCellValue;
                                    break;
                                case CellType.Formula:
                                    if (cell.CachedFormulaResultType == CellType.Numeric)
                                    {
                                        dataRow[j] = cell.NumericCellValue;
                                    }
                                    else
                                    {
                                        dataRow[j] = cell.StringCellValue;
                                    }
                                    break;
                                default:
                                    dataRow[j] = "=" + cell.CellFormula;
                                    break;
                            }
                        }
                    }
                    table.Rows.Add(dataRow);
                    //dataRow[j] = row.GetCell(j).ToString();  
                }
                #endregion
            }
            catch (System.Exception ex)
            {
                table.Clear();
                table.Columns.Clear();
                table.Columns.Add("出错了");
                DataRow dr = table.NewRow();
                dr[0] = ex.Message;
                table.Rows.Add(dr);
                return table;
            }
            finally
            {
                //sheet.Dispose();  
                workbook = null;
                sheet = null;
            }
            #region 清除最后的空行  
            for (int i = table.Rows.Count - 1; i > 0; i--)
            {
                bool isnull = true;
                for (int j = 0; j < table.Columns.Count; j++)
                {
                    if (table.Rows[i][j] != null)
                    {
                        if (table.Rows[i][j].ToString() != "")
                        {
                            isnull = false;
                            break;
                        }
                    }
                }
                if (isnull)
                {
                    table.Rows[i].Delete();
                }
            }
            #endregion
            return table;
        }

        /// <summary>   
        /// 从Excel中获取数据到DataTable   
        /// </summary>   
        /// <param name="workbook">要处理的工作薄</param>   
        /// <param name="SheetName">要获取数据的工作表名称</param>   
        /// <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>   
        /// <param name="table">需要插入的表名</param>  
        /// <param name="ColCount">需要导入的列数</param>  
        /// <param name="NotNullCol">必填列</param>  
        /// <returns></returns>   
        private static string RenderDataTableFromExcel(IWorkbook workbook, string SheetName, int HeaderRowIndex, DataTable table, int ColCount, int NotNullCol)
        {
            ISheet sheet = workbook.GetSheet(SheetName);
            int i;
            try
            {
                if (table != null)
                    table.Clear();
                int rowCount = sheet.LastRowNum;

                //#region 循环各行各列,写入数据到DataTable
                i = (HeaderRowIndex + 1);
                while (i < sheet.LastRowNum + 1)
                {
                    IRow row = sheet.GetRow(i);

                    try
                    {
                        if (row.GetCell(NotNullCol) == null)
                        {
                            break;
                        }
                        if (row.GetCell(NotNullCol).ToString() == "")
                        {
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        break;
                    }

                    DataRow dataRow = table.NewRow();
                    int j = row.FirstCellNum;
                    while (j < ColCount)
                    {
                        ICell cell = row.GetCell(j);
                        if (cell == null)
                        {
                            dataRow[j] = null;
                        }
                        else
                        {
                            //dataRow[j] = cell.ToString();   
                            switch (cell.CellType)
                            {
                                case CellType.Blank:
                                    dataRow[j] = DBNull.Value;
                                    break;
                                case CellType.Boolean:
                                    dataRow[j] = cell.BooleanCellValue;
                                    break;
                                case CellType.Numeric:
                                    if (DateUtil.IsValidExcelDate(cell.NumericCellValue) && DateUtil.IsCellDateFormatted(row.GetCell(j)))
                                    {
                                        dataRow[j] = cell.DateCellValue;
                                    }
                                    else
                                    {
                                        dataRow[j] = cell.NumericCellValue;
                                    }
                                    break;
                                case CellType.String:
                                    dataRow[j] = cell.StringCellValue;
                                    break;
                                case CellType.Error:
                                    dataRow[j] = cell.ErrorCellValue;
                                    break;
                                case CellType.Formula:
                                    dataRow[j] = cell.NumericCellValue;
                                    //.CellFormula
                                    break;
                            }
                        }
                        j++;
                    }
                    //dataRow[j] = row.GetCell(j).ToString();   
                    table.Rows.Add(dataRow);
                    i++;
                }
            }
            catch (System.Exception ex)
            {
                table.Clear();
                //table.Columns.Clear()

                //table.Columns.Add("出错了")
                //Dim dr As DataRow = table.NewRow()
                //dr(0) = ex.Message
                //table.Rows.Add(dr)
                return "失败";
            }
            finally
            {
                //sheet.Dispose();   
                workbook = null;
                sheet = null;
            }
            //#region 清除最后的空行
            i = table.Rows.Count - 1;
            while (i > 0)
            {
                bool isnull = true;
                int j = 0;
                while (j < table.Columns.Count)
                {
                    if (table.Rows[i][j] != null)
                    {
                        if (table.Rows[i][j].ToString() != "")
                        {
                            isnull = false;
                            break;
                        }
                    }
                    j++;
                }
                if (isnull)
                {
                    table.Rows[i].Delete();
                }
                i--;
            }
            return "成功";
        }

        #endregion

        #region 更新excel中的数据
        /// <summary>
        /// 更新Excel表格
        /// </summary>
        /// <param name="outputFile">需更新的excel表格路径</param>
        /// <param name="sheetindex">要获取数据的工作表序号(从0开始)</param>
        /// <param name="updateData">需更新的数据</param>
        /// <param name="coluid">需更新的列号</param>
        /// <param name="rowid">需更新的开始行号</param>
        public static void UpdateExcel(string outputFile, int sheetindex, string[] updateData, int coluid, int rowid)
        {
            FileStream readfile = new FileStream(outputFile, FileMode.Open, FileAccess.Read);
            IWorkbook hssfworkbook;
            if (Path.GetExtension(outputFile).ToLower() == ".xls")
            {
                hssfworkbook = new HSSFWorkbook(readfile);
            }
            else
            {
                hssfworkbook = new XSSFWorkbook(readfile);
            }


            string SheetName = hssfworkbook.GetSheetName(sheetindex);
            ISheet sheet1 = hssfworkbook.GetSheet(SheetName);
            int i = 0;
            while (i < updateData.Length)
            {
                try
                {
                    if (sheet1.GetRow(i + rowid) == null)
                    {
                        sheet1.CreateRow(i + rowid);
                    }
                    if (sheet1.GetRow(i + rowid).GetCell(coluid) == null)
                    {
                        sheet1.GetRow(i + rowid).CreateCell(coluid);
                    }

                    sheet1.GetRow(i + rowid).GetCell(coluid).SetCellValue(updateData[i]);
                }
                catch (Exception ex)
                {
                    // wl.WriteLogs(ex.ToString());
                    throw;
                }
                i++;
            }
            try
            {
                readfile.Close();
                FileStream writefile = new FileStream(outputFile, FileMode.Create, FileAccess.Write);
                hssfworkbook.Write(writefile);
                writefile.Close();
                // wl.WriteLogs(ex.ToString());
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 更新Excel表格
        /// </summary>
        /// <param name="outputFile">需更新的excel表格路径</param>
        /// <param name="sheetname">sheet名</param>
        /// <param name="updateData">需更新的数据</param>
        /// <param name="coluid">需更新的列号</param>
        /// <param name="rowid">需更新的开始行号</param>
        public static void UpdateExcel(string outputFile, string sheetname, string[] updateData, int coluid, int rowid)
        {
            FileStream readfile = new FileStream(outputFile, FileMode.Open, FileAccess.Read);

            HSSFWorkbook hssfworkbook = new HSSFWorkbook(readfile);
            ISheet sheet1 = hssfworkbook.GetSheet(sheetname);
            int i = 0;
            while (i < updateData.Length)
            {
                try
                {
                    if (sheet1.GetRow(i + rowid) == null)
                    {
                        sheet1.CreateRow(i + rowid);
                    }
                    if (sheet1.GetRow(i + rowid).GetCell(coluid) == null)
                    {
                        sheet1.GetRow(i + rowid).CreateCell(coluid);
                    }

                    sheet1.GetRow(i + rowid).GetCell(coluid).SetCellValue(updateData[i]);
                }
                catch (Exception ex)
                {
                    // wl.WriteLogs(ex.ToString());
                    throw;
                }
                i++;
            }
            try
            {
                readfile.Close();
                FileStream writefile = new FileStream(outputFile, FileMode.Create, FileAccess.Write);
                hssfworkbook.Write(writefile);
                writefile.Close();
                // wl.WriteLogs(ex.ToString());
            }
            catch (Exception ex)
            {
            }

        }

        /// <summary>
        /// 更新Excel表格
        /// </summary>
        /// <param name="outputFile">需更新的excel表格路径</param>
        /// <param name="sheetname">sheet名</param>
        /// <param name="updateData">需更新的数据</param>
        /// <param name="coluids">需更新的列号</param>
        /// <param name="rowid">需更新的开始行号</param>
        public static void UpdateExcel(string outputFile, string sheetname, string[][] updateData, int[] coluids, int rowid)
        {
            FileStream readfile = new FileStream(outputFile, FileMode.Open, FileAccess.Read);

            HSSFWorkbook hssfworkbook = new HSSFWorkbook(readfile);
            readfile.Close();
            ISheet sheet1 = hssfworkbook.GetSheet(sheetname);
            int j = 0;
            while (j < coluids.Length)
            {
                int i = 0;
                while (i < updateData[j].Length)
                {
                    try
                    {
                        if (sheet1.GetRow(i + rowid) == null)
                        {
                            sheet1.CreateRow(i + rowid);
                        }
                        if (sheet1.GetRow(i + rowid).GetCell(coluids[j]) == null)
                        {
                            sheet1.GetRow(i + rowid).CreateCell(coluids[j]);
                        }
                        sheet1.GetRow(i + rowid).GetCell(coluids[j]).SetCellValue(updateData[j][i]);
                        // wl.WriteLogs(ex.ToString());
                    }
                    catch (Exception ex)
                    {
                    }
                    i++;
                }
                j++;
            }
            try
            {
                FileStream writefile = new FileStream(outputFile, FileMode.Create);
                hssfworkbook.Write(writefile);
                writefile.Close();
                //wl.WriteLogs(ex.ToString());
            }
            catch (Exception ex)
            {
            }
        }

        /// <summary>
        /// 更新Excel表格
        /// </summary>
        /// <param name="outputFile">需更新的excel表格路径</param>
        /// <param name="sheetname">sheet名</param>
        /// <param name="updateData">需更新的数据</param>
        /// <param name="coluid">需更新的列号</param>
        /// <param name="rowid">需更新的开始行号</param>
        public static void UpdateExcel(string outputFile, string sheetname, double[] updateData, int coluid, int rowid)
        {
            FileStream readfile = new FileStream(outputFile, FileMode.Open, FileAccess.Read);

            HSSFWorkbook hssfworkbook = new HSSFWorkbook(readfile);
            ISheet sheet1 = hssfworkbook.GetSheet(sheetname);
            int i = 0;
            while (i < updateData.Length)
            {
                try
                {
                    if (sheet1.GetRow(i + rowid) == null)
                    {
                        sheet1.CreateRow(i + rowid);
                    }
                    if (sheet1.GetRow(i + rowid).GetCell(coluid) == null)
                    {
                        sheet1.GetRow(i + rowid).CreateCell(coluid);
                    }

                    sheet1.GetRow(i + rowid).GetCell(coluid).SetCellValue(updateData[i]);
                }
                catch (Exception ex)
                {
                    //wl.WriteLogs(ex.ToString());
                    throw;
                }
                i++;
            }
            try
            {
                readfile.Close();
                FileStream writefile = new FileStream(outputFile, FileMode.Create, FileAccess.Write);
                hssfworkbook.Write(writefile);
                writefile.Close();
                //wl.WriteLogs(ex.ToString());
            }
            catch (Exception ex)
            {
            }

        }

        /// <summary>
        /// 更新Excel表格
        /// </summary>
        /// <param name="outputFile">需更新的excel表格路径</param>
        /// <param name="sheetname">sheet名</param>
        /// <param name="updateData">需更新的数据</param>
        /// <param name="coluids">需更新的列号</param>
        /// <param name="rowid">需更新的开始行号</param>
        public static void UpdateExcel(string outputFile, string sheetname, double[][] updateData, int[] coluids, int rowid)
        {
            FileStream readfile = new FileStream(outputFile, FileMode.Open, FileAccess.Read);

            HSSFWorkbook hssfworkbook = new HSSFWorkbook(readfile);
            readfile.Close();
            ISheet sheet1 = hssfworkbook.GetSheet(sheetname);
            int j = 0;
            while (j < coluids.Length)
            {
                int i = 0;
                while (i < updateData[j].Length)
                {
                    try
                    {
                        if (sheet1.GetRow(i + rowid) == null)
                        {
                            sheet1.CreateRow(i + rowid);
                        }
                        if (sheet1.GetRow(i + rowid).GetCell(coluids[j]) == null)
                        {
                            sheet1.GetRow(i + rowid).CreateCell(coluids[j]);
                        }
                        sheet1.GetRow(i + rowid).GetCell(coluids[j]).SetCellValue(updateData[j][i]);
                        //wl.WriteLogs(ex.ToString());
                    }
                    catch (Exception ex)
                    {
                    }
                    i++;
                }
                j++;
            }
            try
            {
                FileStream writefile = new FileStream(outputFile, FileMode.Create);
                hssfworkbook.Write(writefile);
                writefile.Close();
                //wl.WriteLogs(ex.ToString());
            }
            catch (Exception ex)
            {
            }
        }
        #endregion
    }

}


