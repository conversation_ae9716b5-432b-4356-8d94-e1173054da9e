﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public partial class ScrollPanel : UserControl
    {
        public ScrollPanel()
        {
            InitializeComponent();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            label1.Location = new Point(label1.Location.X - 1, label1.Location.Y);
            
            if (label1.Right <= 0)
            {
                label1.Left = panel1.Width;
            }
        }

        #region 公开属性

        [Description("控件文本"), Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public override string Text
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                label1.Refresh();
                Invalidate();
            }
        }

        [Description("是否开始滚动"), Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible),DefaultValue(false)]
        public bool TimerEnabled
        {
            get { return timer1.Enabled; }
            set
            {
                label1.Location = new Point(panel1.Width, (panel1.Height-12) / 2);
                timer1.Interval = 18;
                timer1.Enabled = value;
                Invalidate();
            }
        }

        [Description("文字高度"), Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible), DefaultValue(false)]
        public int LabelHeight
        {
            get { return label1.Height; }
            set
            {
                label1.Height = value;
                Invalidate();
            }
        }

        #endregion

        private void panel1_SizeChanged(object sender, EventArgs e)
        {
            label1.Location = new Point(label1.Location.X, (panel1.Height-12) / 2);
            Invalidate();
        }
    }
}
