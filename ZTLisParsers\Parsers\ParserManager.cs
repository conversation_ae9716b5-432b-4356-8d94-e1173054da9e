using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZTLisEnum;
using Model;
using BLL;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// 解析器管理类，负责协调解析器的工作
    /// </summary>
    public class ParserManager
    {
        private readonly BllLabInstrRawMsg _bllLabInstrRawMsg;
        private readonly BllLabInstrResultQ _bllLabInstrResultQ;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ParserManager()
        {
            _bllLabInstrRawMsg = new BllLabInstrRawMsg();
            _bllLabInstrResultQ = new BllLabInstrResultQ();
        }

        /// <summary>
        /// 解析指定的原始报文
        /// </summary>
        /// <param name="rawMsgId">原始报文ID</param>
        /// <returns>解析是否成功</returns>
        public async Task<bool> ParseRawMessageAsync(long rawMsgId)
        {
            try
            {
                var rawMsg = _bllLabInstrRawMsg.GetModel(rawMsgId);
                if (rawMsg == null)
                {
                    WriteLog($"未找到原始报文，ID: {rawMsgId}");
                    return false;
                }

                var instrumentBll = new BllLabInstrument();
                var instrument = instrumentBll.GetModel(rawMsg.InstrumentId);
                if (instrument == null)
                {
                    WriteLog($"未找到仪器配置，InstrumentId: {rawMsg.InstrumentId}");
                    UpdateRawMessageParseStatus(rawMsgId, false, "未找到仪器配置");
                    return false;
                }

                // 获取解析器实例
                var parser = GetParserForInstrument(instrument);
                if (parser == null)
                {
                    var errorMsg = GetParserErrorMessage(instrument);
                    WriteLog($"未找到解析器，InstrumentId: {rawMsg.InstrumentId}，{errorMsg}");
                    UpdateRawMessageParseStatus(rawMsgId, false, errorMsg);
                    return false;
                }

                var parseResult = await parser.ParseAsync(rawMsg.RawText, rawMsg.InstrumentId);

                if (parseResult.IsSuccess)
                {
                    foreach (var result in parseResult.Results)
                    {
                        result.RawId = rawMsgId;
                        _bllLabInstrResultQ.Add(result);
                    }

                    UpdateRawMessageParseStatus(rawMsgId, true, null);
                    WriteLog($"解析成功，RawMsgId: {rawMsgId}，解析出 {parseResult.Results.Count} 条结果");
                    return true;
                }
                else
                {
                    UpdateRawMessageParseStatus(rawMsgId, false, parseResult.ErrorMessage);
                    WriteLog($"解析失败，RawMsgId: {rawMsgId}，错误: {parseResult.ErrorMessage}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                WriteLog($"解析异常，RawMsgId: {rawMsgId}，异常: {ex.Message}");
                UpdateRawMessageParseStatus(rawMsgId, false, $"解析异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 根据仪器配置获取解析器实例
        /// </summary>
        /// <param name="instrument">仪器配置</param>
        /// <returns>解析器实例</returns>
        private IParser GetParserForInstrument(MdlLabInstrument instrument)
        {
            // 当协议类型为自定义(4)且有自定义驱动类时，优先使用自定义驱动类
            if (!string.IsNullOrWhiteSpace(instrument.CustomDriverClass) && instrument.ProtocolType == 4)
            {
                WriteLog($"使用自定义驱动类: {instrument.CustomDriverClass}");
                var customParser = ParserFactory.GetCustomParser(instrument.CustomDriverClass);
                if (customParser != null)
                {
                    return customParser;
                }
                else
                {
                    WriteLog($"自定义驱动类创建失败: {instrument.CustomDriverClass}");
                }
            }

            // 如果自定义驱动类不可用，使用标准协议解析器
            WriteLog($"使用标准协议解析器，ProtocolType: {instrument.ProtocolType}");
            return ParserFactory.GetParser((ProtocolType)instrument.ProtocolType);
        }

        /// <summary>
        /// 获取解析器错误信息
        /// </summary>
        /// <param name="instrument">仪器配置</param>
        /// <returns>错误信息</returns>
        private string GetParserErrorMessage(MdlLabInstrument instrument)
        {
            if (!string.IsNullOrWhiteSpace(instrument.CustomDriverClass) && instrument.ProtocolType == 4)
            {
                return $"自定义驱动类 '{instrument.CustomDriverClass}' 无效，且不支持的协议类型: {instrument.ProtocolType}";
            }
            else
            {
                return $"不支持的协议类型: {instrument.ProtocolType}";
            }
        }

        /// <summary>
        /// 验证仪器配置的解析器是否可用
        /// </summary>
        /// <param name="instrument">仪器配置</param>
        /// <returns>验证结果</returns>
        public ParserValidationResult ValidateInstrumentParser(MdlLabInstrument instrument)
        {
            var result = new ParserValidationResult();

            try
            {
                // 检查自定义驱动类（仅当协议类型为自定义时）
                if (!string.IsNullOrWhiteSpace(instrument.CustomDriverClass) && instrument.ProtocolType == 4)
                {
                    if (ParserFactory.IsCustomDriverValid(instrument.CustomDriverClass))
                    {
                        result.IsValid = true;
                        result.ParserType = "自定义驱动";
                        result.ParserInfo = instrument.CustomDriverClass;
                        return result;
                    }
                    else
                    {
                        result.Warnings.Add($"自定义驱动类 '{instrument.CustomDriverClass}' 无效");
                    }
                }

                // 检查标准协议解析器
                if (ParserFactory.IsSupported((ProtocolType)instrument.ProtocolType))
                {
                    result.IsValid = true;
                    result.ParserType = "标准协议";
                    result.ParserInfo = ((ProtocolType)instrument.ProtocolType).ToString();
                    return result;
                }
                else
                {
                    result.Errors.Add($"不支持的协议类型: {instrument.ProtocolType}");
                }

                result.IsValid = false;
                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add($"验证异常: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 批量解析未处理的原始报文
        /// </summary>
        /// <param name="batchSize">批处理大小</param>
        /// <returns>处理的报文数量</returns>
        public async Task<int> ParsePendingMessagesAsync(int batchSize = 100)
        {
            try
            {
                // 获取未解析的原始报文
                var pendingMessages = _bllLabInstrRawMsg.GetList($"ParsedFlag = 0").Tables[0];

                int processedCount = 0;
                int currentBatch = 0;

                foreach (System.Data.DataRow row in pendingMessages.Rows)
                {
                    if (currentBatch >= batchSize)
                        break;

                    var rawMsg = Common.DataTableToList.ToModel<MdlLabInstrRawMsg>(row);
                    if (await ParseRawMessageAsync(rawMsg.RawId))
                    {
                        processedCount++;
                    }

                    currentBatch++;
                }

                WriteLog($"批量解析完成，处理 {currentBatch} 条报文，成功 {processedCount} 条");
                return processedCount;
            }
            catch (Exception ex)
            {
                WriteLog($"批量解析异常: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 更新原始报文的解析状态
        /// </summary>
        private void UpdateRawMessageParseStatus(long rawMsgId, bool success, string errorMessage)
        {
            try
            {
                var rawMsg = _bllLabInstrRawMsg.GetModel(rawMsgId);
                if (rawMsg != null)
                {
                    rawMsg.ParsedFlag = success;
                    rawMsg.ParseError = errorMessage;
                    _bllLabInstrRawMsg.Update(rawMsg);
                }
            }
            catch (Exception ex)
            {
                WriteLog($"更新解析状态失败，RawMsgId: {rawMsgId}，异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        private void WriteLog(string message)
        {
            Common.Log.LogHelper.Info($"[ParserManager] {message}");
        }
    }

    /// <summary>
    /// 解析器验证结果
    /// </summary>
    public class ParserValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 解析器类型
        /// </summary>
        public string ParserType { get; set; }

        /// <summary>
        /// 解析器信息
        /// </summary>
        public string ParserInfo { get; set; }

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 获取所有消息
        /// </summary>
        /// <returns>消息字符串</returns>
        public string GetAllMessages()
        {
            var messages = new List<string>();
            messages.AddRange(Warnings);
            messages.AddRange(Errors);
            return string.Join("; ", messages);
        }
    }
}