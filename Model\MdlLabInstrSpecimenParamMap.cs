﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabInstrSpecimenParamMap.cs
*
* 功 能： N/A
* 类 名： MdlLabInstrSpecimenParamMap
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/24 10:36:31   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 仪器标本参数映射表：建立仪器原始参数代码与LIS标准参数字典的映射关系
	/// </summary>
	[Serializable]
	public partial class MdlLabInstrSpecimenParamMap
	{
		public MdlLabInstrSpecimenParamMap()
		{ }
		#region Model
		private long _mapid;
		private int _instrumentid;
		private string _instrparamcode;
		private string _instrparamname;
		private int _paramid;
		private decimal? _conversionfactor = 1.0M;
		private decimal? _conversionoffset = 0.0M;
		private bool _status = true;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public long MapId
		{
			set { _mapid = value; }
			get { return _mapid; }
		}
		/// <summary>
		/// 仪器ID（外键关联LabInstrument表）
		/// </summary>
		public int InstrumentId
		{
			set { _instrumentid = value; }
			get { return _instrumentid; }
		}
		/// <summary>
		/// 仪器端参数代码（如 0x21、HCT等）
		/// </summary>
		public string InstrParamCode
		{
			set { _instrparamcode = value; }
			get { return _instrparamcode; }
		}
		/// <summary>
		/// 仪器端参数名称（可选，便于维护）
		/// </summary>
		public string InstrParamName
		{
			set { _instrparamname = value; }
			get { return _instrparamname; }
		}
		/// <summary>
		/// LIS标准参数ID（外键关联DictSpecimenParam表）
		/// </summary>
		public int ParamId
		{
			set { _paramid = value; }
			get { return _paramid; }
		}
		/// <summary>
		/// 转换系数（仪器值 × 系数 = 标准值）
		/// </summary>
		public decimal? ConversionFactor
		{
			set { _conversionfactor = value; }
			get { return _conversionfactor; }
		}
		/// <summary>
		/// 转换偏移量（转换后 + 偏移量）
		/// </summary>
		public decimal? ConversionOffset
		{
			set { _conversionoffset = value; }
			get { return _conversionoffset; }
		}
		/// <summary>
		/// 状态：1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

