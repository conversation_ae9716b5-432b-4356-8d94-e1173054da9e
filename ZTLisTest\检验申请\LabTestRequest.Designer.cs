namespace ZTLisTest
{
    partial class LabTestRequest
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tlpMain = new System.Windows.Forms.TableLayoutPanel();
            this.pnlMiddle = new System.Windows.Forms.Panel();
            this.tlpMiddle = new System.Windows.Forms.TableLayoutPanel();
            this.dtpCheckDate = new CustomControl.MyDateEdit();
            this.cboInstrument = new ZTLisControl.ComboLabInstrument();
            this.txtDailySeqNo = new CustomControl.MyTextBox();
            this.txtReportComment = new CustomControl.MyTextBox();
            this.cboVisitType = new ZTLisControl.ComboVisitType();
            this.txtPatientName = new CustomControl.MyTextBox();
            this.dtpBirthDate = new CustomControl.MyDateEdit();
            this.cboAgeUnit = new ZTLisControl.SingleAgeUnit();
            this.txtBedNo = new CustomControl.MyTextBox();
            this.cboRequestDoctor = new ZTLisControl.ComboDoctor();
            this.cboRequestDept = new ZTLisControl.ComboDept();
            this.cboSampleType = new ZTLisControl.ComboSampleType();
            this.cboSpecimenCondition = new ZTLisControl.ComboSpecimenCondition();
            this.txtRemark = new CustomControl.MyTextBox();
            this.cboCheckDoctor = new ZTLisControl.ComboDoctor();
            this.cboAuditDoctor = new ZTLisControl.ComboDoctor();
            this.numAge = new CustomControl.MyNumericEdit();
            this.txtSeqPrefix = new CustomControl.MyTextBox();
            this.singleSex1 = new ZTLisControl.SingleSex();
            this.pnlRight = new System.Windows.Forms.Panel();
            this.tlpRight = new System.Windows.Forms.TableLayoutPanel();
            this.cboLabProfile = new ZTLisControl.ComboLabProfile();
            this.chkKeepItems = new System.Windows.Forms.CheckBox();
            this.myGrid2 = new CustomControl.MyGrid();
            this.lblResultCount = new System.Windows.Forms.Label();
            this.c1CommandDock1 = new C1.Win.C1Command.C1CommandDock();
            this.c1DockingTab1 = new C1.Win.C1Command.C1DockingTab();
            this.c1DockingTabPage1 = new C1.Win.C1Command.C1DockingTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.pnlFilters = new System.Windows.Forms.Panel();
            this.tlpFilters = new System.Windows.Forms.TableLayoutPanel();
            this.cboSampleStatusFilter = new CustomControl.MySingleComobo();
            this.txtSampleNoStart = new CustomControl.MyTextBox();
            this.txtSampleNoEnd = new CustomControl.MyTextBox();
            this.btnSearch = new CustomControl.MyButton();
            this.txtSearch = new CustomControl.MyTextBox();
            this.btnRefresh = new CustomControl.MyButton();
            this.myGrid1 = new CustomControl.MyGrid();
            this.lblSampleCount = new System.Windows.Forms.Label();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.CmdAdd = new C1.Win.C1Command.C1Command();
            this.CmdSave = new C1.Win.C1Command.C1Command();
            this.CmdDelete = new C1.Win.C1Command.C1Command();
            this.CmdAudit = new C1.Win.C1Command.C1Command();
            this.CmdCancelAudit = new C1.Win.C1Command.C1Command();
            this.CmdAuditPrint = new C1.Win.C1Command.C1Command();
            this.CmdPrint = new C1.Win.C1Command.C1Command();
            this.CmdPreview = new C1.Win.C1Command.C1Command();
            this.c1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink5 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink6 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink7 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink8 = new C1.Win.C1Command.C1CommandLink();
            this.pnlTop = new System.Windows.Forms.Panel();
            this.c1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.c1CommandLink9 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink10 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink11 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink12 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink13 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink14 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink15 = new C1.Win.C1Command.C1CommandLink();
            this.c1CommandLink16 = new C1.Win.C1Command.C1CommandLink();
            this.tlpMain.SuspendLayout();
            this.pnlMiddle.SuspendLayout();
            this.tlpMiddle.SuspendLayout();
            this.pnlRight.SuspendLayout();
            this.tlpRight.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock1)).BeginInit();
            this.c1CommandDock1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1DockingTab1)).BeginInit();
            this.c1DockingTab1.SuspendLayout();
            this.c1DockingTabPage1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.pnlFilters.SuspendLayout();
            this.tlpFilters.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.pnlTop.SuspendLayout();
            this.SuspendLayout();
            // 
            // tlpMain
            // 
            this.tlpMain.ColumnCount = 2;
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpMain.Controls.Add(this.pnlMiddle, 0, 0);
            this.tlpMain.Controls.Add(this.pnlRight, 1, 0);
            this.tlpMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlpMain.Location = new System.Drawing.Point(344, 30);
            this.tlpMain.Margin = new System.Windows.Forms.Padding(0);
            this.tlpMain.Name = "tlpMain";
            this.tlpMain.RowCount = 1;
            this.tlpMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpMain.Size = new System.Drawing.Size(671, 664);
            this.tlpMain.TabIndex = 2;
            // 
            // pnlMiddle
            // 
            this.pnlMiddle.Controls.Add(this.tlpMiddle);
            this.pnlMiddle.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMiddle.Location = new System.Drawing.Point(0, 0);
            this.pnlMiddle.Margin = new System.Windows.Forms.Padding(0);
            this.pnlMiddle.Name = "pnlMiddle";
            this.pnlMiddle.Size = new System.Drawing.Size(250, 664);
            this.pnlMiddle.TabIndex = 1;
            // 
            // tlpMiddle
            // 
            this.tlpMiddle.ColumnCount = 3;
            this.tlpMiddle.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 140F));
            this.tlpMiddle.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpMiddle.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 50F));
            this.tlpMiddle.Controls.Add(this.dtpCheckDate, 0, 0);
            this.tlpMiddle.Controls.Add(this.cboInstrument, 0, 1);
            this.tlpMiddle.Controls.Add(this.txtDailySeqNo, 1, 2);
            this.tlpMiddle.Controls.Add(this.txtReportComment, 0, 16);
            this.tlpMiddle.Controls.Add(this.cboVisitType, 0, 3);
            this.tlpMiddle.Controls.Add(this.txtPatientName, 0, 4);
            this.tlpMiddle.Controls.Add(this.dtpBirthDate, 0, 5);
            this.tlpMiddle.Controls.Add(this.cboAgeUnit, 2, 7);
            this.tlpMiddle.Controls.Add(this.txtBedNo, 0, 8);
            this.tlpMiddle.Controls.Add(this.cboRequestDoctor, 0, 9);
            this.tlpMiddle.Controls.Add(this.cboRequestDept, 0, 10);
            this.tlpMiddle.Controls.Add(this.cboSampleType, 0, 11);
            this.tlpMiddle.Controls.Add(this.cboSpecimenCondition, 0, 12);
            this.tlpMiddle.Controls.Add(this.txtRemark, 0, 13);
            this.tlpMiddle.Controls.Add(this.cboCheckDoctor, 0, 14);
            this.tlpMiddle.Controls.Add(this.cboAuditDoctor, 0, 15);
            this.tlpMiddle.Controls.Add(this.numAge, 0, 7);
            this.tlpMiddle.Controls.Add(this.txtSeqPrefix, 0, 2);
            this.tlpMiddle.Controls.Add(this.singleSex1, 0, 6);
            this.tlpMiddle.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlpMiddle.Location = new System.Drawing.Point(0, 0);
            this.tlpMiddle.Margin = new System.Windows.Forms.Padding(0);
            this.tlpMiddle.Name = "tlpMiddle";
            this.tlpMiddle.RowCount = 18;
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 58F));
            this.tlpMiddle.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tlpMiddle.Size = new System.Drawing.Size(250, 664);
            this.tlpMiddle.TabIndex = 0;
            // 
            // dtpCheckDate
            // 
            this.dtpCheckDate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.dtpCheckDate.Captain = "检验日期";
            this.dtpCheckDate.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpCheckDate.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.dtpCheckDate.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.dtpCheckDate, 3);
            this.dtpCheckDate.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart;
            this.dtpCheckDate.Location = new System.Drawing.Point(3, 3);
            this.dtpCheckDate.MaximumSize = new System.Drawing.Size(100000000, 23);
            this.dtpCheckDate.MinimumSize = new System.Drawing.Size(0, 20);
            this.dtpCheckDate.Name = "dtpCheckDate";
            this.dtpCheckDate.ReadOnly = false;
            this.dtpCheckDate.Size = new System.Drawing.Size(244, 23);
            this.dtpCheckDate.TabIndex = 1;
            this.dtpCheckDate.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpCheckDate.ValueIsDbNull = false;
            this.dtpCheckDate.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom;
            this.dtpCheckDate.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            // 
            // cboInstrument
            // 
            this.cboInstrument.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboInstrument.Bookmark = -1;
            this.cboInstrument.Captain = "检验仪器";
            this.cboInstrument.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboInstrument.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboInstrument.CaptainWidth = 70F;
            this.cboInstrument.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboInstrument, 3);
            this.cboInstrument.DataSource = null;
            this.cboInstrument.DataView = null;
            this.cboInstrument.ItemHeight = 18;
            this.cboInstrument.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboInstrument.Location = new System.Drawing.Point(3, 32);
            this.cboInstrument.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboInstrument.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboInstrument.Name = "cboInstrument";
            this.cboInstrument.ReadOnly = false;
            this.cboInstrument.Row = 0;
            this.cboInstrument.Size = new System.Drawing.Size(244, 23);
            this.cboInstrument.TabIndex = 3;
            this.cboInstrument.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // txtDailySeqNo
            // 
            this.txtDailySeqNo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtDailySeqNo.Captain = "";
            this.txtDailySeqNo.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtDailySeqNo.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtDailySeqNo.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtDailySeqNo.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.txtDailySeqNo, 2);
            this.txtDailySeqNo.ContentForeColor = System.Drawing.Color.Black;
            this.txtDailySeqNo.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtDailySeqNo.EditMask = null;
            this.txtDailySeqNo.Location = new System.Drawing.Point(143, 61);
            this.txtDailySeqNo.Multiline = false;
            this.txtDailySeqNo.Name = "txtDailySeqNo";
            this.txtDailySeqNo.PasswordChar = '\0';
            this.txtDailySeqNo.ReadOnly = false;
            this.txtDailySeqNo.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtDailySeqNo.SelectionStart = 0;
            this.txtDailySeqNo.SelectStart = 0;
            this.txtDailySeqNo.Size = new System.Drawing.Size(104, 23);
            this.txtDailySeqNo.TabIndex = 5;
            this.txtDailySeqNo.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtDailySeqNo.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtDailySeqNo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtDailySeqNo.Watermark = null;
            // 
            // txtReportComment
            // 
            this.txtReportComment.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtReportComment.Captain = "报告备注";
            this.txtReportComment.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtReportComment.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtReportComment.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtReportComment.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.txtReportComment, 3);
            this.txtReportComment.ContentForeColor = System.Drawing.Color.Black;
            this.txtReportComment.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtReportComment.EditMask = null;
            this.txtReportComment.Location = new System.Drawing.Point(3, 467);
            this.txtReportComment.Multiline = true;
            this.txtReportComment.Name = "txtReportComment";
            this.txtReportComment.PasswordChar = '\0';
            this.txtReportComment.ReadOnly = false;
            this.txtReportComment.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtReportComment.SelectionStart = 0;
            this.txtReportComment.SelectStart = 0;
            this.txtReportComment.Size = new System.Drawing.Size(244, 52);
            this.txtReportComment.TabIndex = 34;
            this.txtReportComment.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtReportComment.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtReportComment.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top;
            this.txtReportComment.Watermark = null;
            // 
            // cboVisitType
            // 
            this.cboVisitType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboVisitType.Bookmark = -1;
            this.cboVisitType.Captain = "就诊类型";
            this.cboVisitType.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboVisitType.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboVisitType.CaptainWidth = 70F;
            this.cboVisitType.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboVisitType, 3);
            this.cboVisitType.DataSource = null;
            this.cboVisitType.DataView = null;
            this.cboVisitType.ItemHeight = 18;
            this.cboVisitType.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboVisitType.Location = new System.Drawing.Point(3, 90);
            this.cboVisitType.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboVisitType.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboVisitType.Name = "cboVisitType";
            this.cboVisitType.ReadOnly = false;
            this.cboVisitType.Row = 0;
            this.cboVisitType.Size = new System.Drawing.Size(244, 23);
            this.cboVisitType.TabIndex = 12;
            this.cboVisitType.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // txtPatientName
            // 
            this.txtPatientName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPatientName.Captain = "患者姓名";
            this.txtPatientName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtPatientName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPatientName.CaptainForeColor = System.Drawing.Color.Red;
            this.txtPatientName.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.txtPatientName, 3);
            this.txtPatientName.ContentForeColor = System.Drawing.Color.Black;
            this.txtPatientName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtPatientName.EditMask = null;
            this.txtPatientName.Location = new System.Drawing.Point(3, 119);
            this.txtPatientName.Multiline = false;
            this.txtPatientName.Name = "txtPatientName";
            this.txtPatientName.PasswordChar = '\0';
            this.txtPatientName.ReadOnly = false;
            this.txtPatientName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPatientName.SelectionStart = 0;
            this.txtPatientName.SelectStart = 0;
            this.txtPatientName.Size = new System.Drawing.Size(244, 23);
            this.txtPatientName.TabIndex = 11;
            this.txtPatientName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtPatientName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtPatientName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtPatientName.Watermark = null;
            // 
            // dtpBirthDate
            // 
            this.dtpBirthDate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.dtpBirthDate.Captain = "出生日期";
            this.dtpBirthDate.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpBirthDate.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.dtpBirthDate.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.dtpBirthDate, 3);
            this.dtpBirthDate.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart;
            this.dtpBirthDate.Location = new System.Drawing.Point(3, 148);
            this.dtpBirthDate.MaximumSize = new System.Drawing.Size(100000000, 23);
            this.dtpBirthDate.MinimumSize = new System.Drawing.Size(0, 20);
            this.dtpBirthDate.Name = "dtpBirthDate";
            this.dtpBirthDate.ReadOnly = false;
            this.dtpBirthDate.Size = new System.Drawing.Size(244, 23);
            this.dtpBirthDate.TabIndex = 13;
            this.dtpBirthDate.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dtpBirthDate.ValueIsDbNull = false;
            this.dtpBirthDate.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom;
            this.dtpBirthDate.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            this.dtpBirthDate.ValueChanged += new System.EventHandler(this.dtpBirthDate_ValueChanged);
            // 
            // cboAgeUnit
            // 
            this.cboAgeUnit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboAgeUnit.Captain = "";
            this.cboAgeUnit.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboAgeUnit.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboAgeUnit.CaptainWidth = 7F;
            this.cboAgeUnit.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.cboAgeUnit.ItemHeight = 18;
            this.cboAgeUnit.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboAgeUnit.Location = new System.Drawing.Point(203, 206);
            this.cboAgeUnit.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboAgeUnit.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboAgeUnit.Name = "cboAgeUnit";
            this.cboAgeUnit.ReadOnly = false;
            this.cboAgeUnit.Size = new System.Drawing.Size(44, 23);
            this.cboAgeUnit.TabIndex = 16;
            this.cboAgeUnit.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // txtBedNo
            // 
            this.txtBedNo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtBedNo.Captain = "床    号";
            this.txtBedNo.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtBedNo.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtBedNo.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtBedNo.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.txtBedNo, 3);
            this.txtBedNo.ContentForeColor = System.Drawing.Color.Black;
            this.txtBedNo.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtBedNo.EditMask = null;
            this.txtBedNo.Location = new System.Drawing.Point(3, 235);
            this.txtBedNo.Multiline = false;
            this.txtBedNo.Name = "txtBedNo";
            this.txtBedNo.PasswordChar = '\0';
            this.txtBedNo.ReadOnly = false;
            this.txtBedNo.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtBedNo.SelectionStart = 0;
            this.txtBedNo.SelectStart = 0;
            this.txtBedNo.Size = new System.Drawing.Size(244, 23);
            this.txtBedNo.TabIndex = 18;
            this.txtBedNo.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtBedNo.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtBedNo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtBedNo.Watermark = null;
            // 
            // cboRequestDoctor
            // 
            this.cboRequestDoctor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboRequestDoctor.Bookmark = -1;
            this.cboRequestDoctor.Captain = "申请医生";
            this.cboRequestDoctor.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboRequestDoctor.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboRequestDoctor.CaptainWidth = 70F;
            this.cboRequestDoctor.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboRequestDoctor, 3);
            this.cboRequestDoctor.DataSource = null;
            this.cboRequestDoctor.DataView = null;
            this.cboRequestDoctor.ItemHeight = 18;
            this.cboRequestDoctor.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboRequestDoctor.Location = new System.Drawing.Point(3, 264);
            this.cboRequestDoctor.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboRequestDoctor.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboRequestDoctor.Name = "cboRequestDoctor";
            this.cboRequestDoctor.ReadOnly = false;
            this.cboRequestDoctor.Row = 0;
            this.cboRequestDoctor.Size = new System.Drawing.Size(244, 23);
            this.cboRequestDoctor.TabIndex = 20;
            this.cboRequestDoctor.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // cboRequestDept
            // 
            this.cboRequestDept.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboRequestDept.Bookmark = -1;
            this.cboRequestDept.Captain = "申请科室";
            this.cboRequestDept.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboRequestDept.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboRequestDept.CaptainWidth = 70F;
            this.cboRequestDept.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboRequestDept, 3);
            this.cboRequestDept.DataSource = null;
            this.cboRequestDept.DataView = null;
            this.cboRequestDept.ItemHeight = 18;
            this.cboRequestDept.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboRequestDept.Location = new System.Drawing.Point(3, 293);
            this.cboRequestDept.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboRequestDept.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboRequestDept.Name = "cboRequestDept";
            this.cboRequestDept.ReadOnly = false;
            this.cboRequestDept.Row = 0;
            this.cboRequestDept.Size = new System.Drawing.Size(244, 23);
            this.cboRequestDept.TabIndex = 22;
            this.cboRequestDept.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // cboSampleType
            // 
            this.cboSampleType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboSampleType.Bookmark = -1;
            this.cboSampleType.Captain = "样本类型";
            this.cboSampleType.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboSampleType.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboSampleType.CaptainWidth = 70F;
            this.cboSampleType.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboSampleType, 3);
            this.cboSampleType.DataSource = null;
            this.cboSampleType.DataView = null;
            this.cboSampleType.ItemHeight = 18;
            this.cboSampleType.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboSampleType.Location = new System.Drawing.Point(3, 322);
            this.cboSampleType.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboSampleType.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboSampleType.Name = "cboSampleType";
            this.cboSampleType.ReadOnly = false;
            this.cboSampleType.Row = 0;
            this.cboSampleType.Size = new System.Drawing.Size(244, 23);
            this.cboSampleType.TabIndex = 24;
            this.cboSampleType.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // cboSpecimenCondition
            // 
            this.cboSpecimenCondition.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboSpecimenCondition.Bookmark = -1;
            this.cboSpecimenCondition.Captain = "样本状态";
            this.cboSpecimenCondition.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboSpecimenCondition.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboSpecimenCondition.CaptainWidth = 70F;
            this.cboSpecimenCondition.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboSpecimenCondition, 3);
            this.cboSpecimenCondition.DataSource = null;
            this.cboSpecimenCondition.DataView = null;
            this.cboSpecimenCondition.ItemHeight = 18;
            this.cboSpecimenCondition.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboSpecimenCondition.Location = new System.Drawing.Point(3, 351);
            this.cboSpecimenCondition.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboSpecimenCondition.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboSpecimenCondition.Name = "cboSpecimenCondition";
            this.cboSpecimenCondition.ReadOnly = false;
            this.cboSpecimenCondition.Row = 0;
            this.cboSpecimenCondition.Size = new System.Drawing.Size(244, 23);
            this.cboSpecimenCondition.TabIndex = 26;
            this.cboSpecimenCondition.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // txtRemark
            // 
            this.txtRemark.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtRemark.Captain = "样本备注";
            this.txtRemark.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtRemark.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRemark.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtRemark.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.txtRemark, 3);
            this.txtRemark.ContentForeColor = System.Drawing.Color.Black;
            this.txtRemark.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtRemark.EditMask = null;
            this.txtRemark.Location = new System.Drawing.Point(3, 380);
            this.txtRemark.Multiline = false;
            this.txtRemark.Name = "txtRemark";
            this.txtRemark.PasswordChar = '\0';
            this.txtRemark.ReadOnly = false;
            this.txtRemark.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtRemark.SelectionStart = 0;
            this.txtRemark.SelectStart = 0;
            this.txtRemark.Size = new System.Drawing.Size(244, 23);
            this.txtRemark.TabIndex = 28;
            this.txtRemark.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtRemark.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtRemark.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtRemark.Watermark = null;
            // 
            // cboCheckDoctor
            // 
            this.cboCheckDoctor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboCheckDoctor.Bookmark = -1;
            this.cboCheckDoctor.Captain = "检查医生";
            this.cboCheckDoctor.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboCheckDoctor.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboCheckDoctor.CaptainWidth = 70F;
            this.cboCheckDoctor.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboCheckDoctor, 3);
            this.cboCheckDoctor.DataSource = null;
            this.cboCheckDoctor.DataView = null;
            this.cboCheckDoctor.ItemHeight = 18;
            this.cboCheckDoctor.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboCheckDoctor.Location = new System.Drawing.Point(3, 409);
            this.cboCheckDoctor.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboCheckDoctor.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboCheckDoctor.Name = "cboCheckDoctor";
            this.cboCheckDoctor.ReadOnly = false;
            this.cboCheckDoctor.Row = 0;
            this.cboCheckDoctor.Size = new System.Drawing.Size(244, 23);
            this.cboCheckDoctor.TabIndex = 30;
            this.cboCheckDoctor.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // cboAuditDoctor
            // 
            this.cboAuditDoctor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboAuditDoctor.Bookmark = -1;
            this.cboAuditDoctor.Captain = "审核医生";
            this.cboAuditDoctor.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboAuditDoctor.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboAuditDoctor.CaptainWidth = 70F;
            this.cboAuditDoctor.ColumnCaptionHeight = 20;
            this.tlpMiddle.SetColumnSpan(this.cboAuditDoctor, 3);
            this.cboAuditDoctor.DataSource = null;
            this.cboAuditDoctor.DataView = null;
            this.cboAuditDoctor.ItemHeight = 18;
            this.cboAuditDoctor.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboAuditDoctor.Location = new System.Drawing.Point(3, 438);
            this.cboAuditDoctor.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboAuditDoctor.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboAuditDoctor.Name = "cboAuditDoctor";
            this.cboAuditDoctor.ReadOnly = false;
            this.cboAuditDoctor.Row = 0;
            this.cboAuditDoctor.Size = new System.Drawing.Size(244, 23);
            this.cboAuditDoctor.TabIndex = 32;
            this.cboAuditDoctor.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // numAge
            // 
            this.numAge.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.numAge.Captain = "年    龄";
            this.numAge.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numAge.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.numAge.CaptainWidth = 70F;
            this.tlpMiddle.SetColumnSpan(this.numAge, 2);
            this.numAge.Location = new System.Drawing.Point(3, 206);
            this.numAge.MaximumSize = new System.Drawing.Size(100000, 80);
            this.numAge.MinimumSize = new System.Drawing.Size(0, 20);
            this.numAge.Name = "numAge";
            this.numAge.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.numAge.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.numAge.ReadOnly = false;
            this.numAge.Size = new System.Drawing.Size(194, 23);
            this.numAge.TabIndex = 15;
            this.numAge.ValueIsDbNull = false;
            // 
            // txtSeqPrefix
            // 
            this.txtSeqPrefix.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSeqPrefix.Captain = "样 本 号";
            this.txtSeqPrefix.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtSeqPrefix.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtSeqPrefix.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtSeqPrefix.CaptainWidth = 70F;
            this.txtSeqPrefix.ContentForeColor = System.Drawing.Color.Black;
            this.txtSeqPrefix.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtSeqPrefix.EditMask = null;
            this.txtSeqPrefix.Location = new System.Drawing.Point(3, 61);
            this.txtSeqPrefix.Multiline = false;
            this.txtSeqPrefix.Name = "txtSeqPrefix";
            this.txtSeqPrefix.PasswordChar = '\0';
            this.txtSeqPrefix.ReadOnly = false;
            this.txtSeqPrefix.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSeqPrefix.SelectionStart = 0;
            this.txtSeqPrefix.SelectStart = 0;
            this.txtSeqPrefix.Size = new System.Drawing.Size(134, 23);
            this.txtSeqPrefix.TabIndex = 5;
            this.txtSeqPrefix.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtSeqPrefix.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtSeqPrefix.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtSeqPrefix.Watermark = null;
            this.txtSeqPrefix.TextChanged += new System.EventHandler(this.txtSeqPrefix_TextChanged);
            // 
            // singleSex1
            // 
            this.singleSex1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.singleSex1.Captain = "性    别";
            this.singleSex1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.singleSex1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.singleSex1.CaptainWidth = 69F;
            this.tlpMiddle.SetColumnSpan(this.singleSex1, 3);
            this.singleSex1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.singleSex1.ItemHeight = 18;
            this.singleSex1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.singleSex1.Location = new System.Drawing.Point(3, 177);
            this.singleSex1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.singleSex1.MinimumSize = new System.Drawing.Size(0, 20);
            this.singleSex1.Name = "singleSex1";
            this.singleSex1.ReadOnly = false;
            this.singleSex1.Size = new System.Drawing.Size(244, 23);
            this.singleSex1.TabIndex = 35;
            this.singleSex1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // pnlRight
            // 
            this.pnlRight.Controls.Add(this.tlpRight);
            this.pnlRight.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlRight.Location = new System.Drawing.Point(250, 0);
            this.pnlRight.Margin = new System.Windows.Forms.Padding(0);
            this.pnlRight.Name = "pnlRight";
            this.pnlRight.Size = new System.Drawing.Size(421, 664);
            this.pnlRight.TabIndex = 2;
            // 
            // tlpRight
            // 
            this.tlpRight.ColumnCount = 2;
            this.tlpRight.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpRight.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tlpRight.Controls.Add(this.cboLabProfile, 0, 0);
            this.tlpRight.Controls.Add(this.chkKeepItems, 1, 0);
            this.tlpRight.Controls.Add(this.myGrid2, 0, 1);
            this.tlpRight.Controls.Add(this.lblResultCount, 0, 2);
            this.tlpRight.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlpRight.Location = new System.Drawing.Point(0, 0);
            this.tlpRight.Margin = new System.Windows.Forms.Padding(0);
            this.tlpRight.Name = "tlpRight";
            this.tlpRight.RowCount = 3;
            this.tlpRight.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpRight.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpRight.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 25F));
            this.tlpRight.Size = new System.Drawing.Size(421, 664);
            this.tlpRight.TabIndex = 0;
            // 
            // cboLabProfile
            // 
            this.cboLabProfile.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.cboLabProfile.Bookmark = -1;
            this.cboLabProfile.Captain = "检验项目";
            this.cboLabProfile.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cboLabProfile.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboLabProfile.CaptainWidth = 70F;
            this.cboLabProfile.ColumnCaptionHeight = 20;
            this.cboLabProfile.DataSource = null;
            this.cboLabProfile.DataView = null;
            this.cboLabProfile.ItemHeight = 18;
            this.cboLabProfile.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboLabProfile.Location = new System.Drawing.Point(3, 3);
            this.cboLabProfile.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboLabProfile.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboLabProfile.Name = "cboLabProfile";
            this.cboLabProfile.ReadOnly = false;
            this.cboLabProfile.Row = 0;
            this.cboLabProfile.Size = new System.Drawing.Size(285, 23);
            this.cboLabProfile.TabIndex = 0;
            this.cboLabProfile.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // chkKeepItems
            // 
            this.chkKeepItems.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.chkKeepItems.AutoSize = true;
            this.chkKeepItems.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.chkKeepItems.Location = new System.Drawing.Point(294, 5);
            this.chkKeepItems.Name = "chkKeepItems";
            this.chkKeepItems.Size = new System.Drawing.Size(124, 18);
            this.chkKeepItems.TabIndex = 1;
            this.chkKeepItems.Text = "新增后保留项目";
            this.chkKeepItems.UseVisualStyleBackColor = true;
            // 
            // myGrid2
            // 
            this.myGrid2.AllowColMove = true;
            this.myGrid2.AllowFilter = true;
            this.myGrid2.CanCustomCol = false;
            this.myGrid2.Caption = "";
            this.myGrid2.ChildGrid = null;
            this.myGrid2.Col = 0;
            this.myGrid2.ColumnFooters = false;
            this.myGrid2.ColumnHeaders = true;
            this.tlpRight.SetColumnSpan(this.myGrid2, 2);
            this.myGrid2.DataMember = "";
            this.myGrid2.DataSource = null;
            this.myGrid2.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid2.FetchRowStyles = false;
            this.myGrid2.FilterBar = false;
            this.myGrid2.GroupByAreaVisible = true;
            this.myGrid2.Location = new System.Drawing.Point(0, 29);
            this.myGrid2.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid2.Name = "myGrid2";
            this.myGrid2.Size = new System.Drawing.Size(421, 610);
            this.myGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid2.TabIndex = 1;
            this.myGrid2.Xmlpath = null;
            // 
            // lblResultCount
            // 
            this.lblResultCount.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tlpRight.SetColumnSpan(this.lblResultCount, 2);
            this.lblResultCount.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblResultCount.Location = new System.Drawing.Point(3, 639);
            this.lblResultCount.Name = "lblResultCount";
            this.lblResultCount.Size = new System.Drawing.Size(415, 25);
            this.lblResultCount.TabIndex = 2;
            this.lblResultCount.Text = "检验项目数: 0";
            this.lblResultCount.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // c1CommandDock1
            // 
            this.c1CommandDock1.Controls.Add(this.c1DockingTab1);
            this.c1CommandDock1.Dock = System.Windows.Forms.DockStyle.Left;
            this.c1CommandDock1.Id = 3;
            this.c1CommandDock1.Location = new System.Drawing.Point(0, 30);
            this.c1CommandDock1.Margin = new System.Windows.Forms.Padding(0);
            this.c1CommandDock1.Name = "c1CommandDock1";
            this.c1CommandDock1.Size = new System.Drawing.Size(344, 664);
            // 
            // c1DockingTab1
            // 
            this.c1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.c1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.c1DockingTab1.CanAutoHide = true;
            this.c1DockingTab1.Controls.Add(this.c1DockingTabPage1);
            this.c1DockingTab1.Location = new System.Drawing.Point(0, 0);
            this.c1DockingTab1.Margin = new System.Windows.Forms.Padding(0);
            this.c1DockingTab1.Name = "c1DockingTab1";
            this.c1DockingTab1.ShowCaption = true;
            this.c1DockingTab1.ShowSingleTab = false;
            this.c1DockingTab1.Size = new System.Drawing.Size(344, 664);
            this.c1DockingTab1.TabIndex = 0;
            this.c1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit;
            this.c1DockingTab1.TabsSpacing = 5;
            this.c1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            // 
            // c1DockingTabPage1
            // 
            this.c1DockingTabPage1.CaptionText = "检验列表";
            this.c1DockingTabPage1.CaptionVisible = true;
            this.c1DockingTabPage1.Controls.Add(this.tableLayoutPanel2);
            this.c1DockingTabPage1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.c1DockingTabPage1.Location = new System.Drawing.Point(0, 0);
            this.c1DockingTabPage1.Margin = new System.Windows.Forms.Padding(0);
            this.c1DockingTabPage1.Name = "c1DockingTabPage1";
            this.c1DockingTabPage1.Size = new System.Drawing.Size(341, 663);
            this.c1DockingTabPage1.TabIndex = 0;
            this.c1DockingTabPage1.Text = "查询条件";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.pnlFilters, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.myGrid1, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.lblSampleCount, 0, 2);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 26);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 3;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 125F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 25F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(341, 637);
            this.tableLayoutPanel2.TabIndex = 7;
            // 
            // pnlFilters
            // 
            this.pnlFilters.Controls.Add(this.tlpFilters);
            this.pnlFilters.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlFilters.Location = new System.Drawing.Point(3, 3);
            this.pnlFilters.Name = "pnlFilters";
            this.pnlFilters.Size = new System.Drawing.Size(335, 119);
            this.pnlFilters.TabIndex = 0;
            // 
            // tlpFilters
            // 
            this.tlpFilters.ColumnCount = 4;
            this.tlpFilters.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tlpFilters.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tlpFilters.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tlpFilters.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 63F));
            this.tlpFilters.Controls.Add(this.cboSampleStatusFilter, 0, 0);
            this.tlpFilters.Controls.Add(this.txtSampleNoStart, 0, 1);
            this.tlpFilters.Controls.Add(this.txtSampleNoEnd, 2, 1);
            this.tlpFilters.Controls.Add(this.btnSearch, 3, 1);
            this.tlpFilters.Controls.Add(this.txtSearch, 0, 2);
            this.tlpFilters.Controls.Add(this.btnRefresh, 3, 2);
            this.tlpFilters.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tlpFilters.Location = new System.Drawing.Point(0, 0);
            this.tlpFilters.Name = "tlpFilters";
            this.tlpFilters.RowCount = 4;
            this.tlpFilters.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpFilters.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpFilters.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpFilters.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tlpFilters.Size = new System.Drawing.Size(335, 119);
            this.tlpFilters.TabIndex = 0;
            // 
            // cboSampleStatusFilter
            // 
            this.cboSampleStatusFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.cboSampleStatusFilter.Captain = "样本状态";
            this.cboSampleStatusFilter.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboSampleStatusFilter.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.cboSampleStatusFilter.CaptainWidth = 69F;
            this.tlpFilters.SetColumnSpan(this.cboSampleStatusFilter, 4);
            this.cboSampleStatusFilter.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.cboSampleStatusFilter.ItemHeight = 18;
            this.cboSampleStatusFilter.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.cboSampleStatusFilter.Location = new System.Drawing.Point(3, 3);
            this.cboSampleStatusFilter.MaximumSize = new System.Drawing.Size(10000, 23);
            this.cboSampleStatusFilter.MinimumSize = new System.Drawing.Size(0, 20);
            this.cboSampleStatusFilter.Name = "cboSampleStatusFilter";
            this.cboSampleStatusFilter.ReadOnly = false;
            this.cboSampleStatusFilter.Size = new System.Drawing.Size(329, 23);
            this.cboSampleStatusFilter.TabIndex = 0;
            this.cboSampleStatusFilter.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // txtSampleNoStart
            // 
            this.txtSampleNoStart.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSampleNoStart.Captain = "样本号段";
            this.txtSampleNoStart.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtSampleNoStart.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.txtSampleNoStart.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtSampleNoStart.CaptainWidth = 70F;
            this.tlpFilters.SetColumnSpan(this.txtSampleNoStart, 2);
            this.txtSampleNoStart.ContentForeColor = System.Drawing.Color.Black;
            this.txtSampleNoStart.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtSampleNoStart.EditMask = null;
            this.txtSampleNoStart.Location = new System.Drawing.Point(3, 32);
            this.txtSampleNoStart.Multiline = false;
            this.txtSampleNoStart.Name = "txtSampleNoStart";
            this.txtSampleNoStart.PasswordChar = '\0';
            this.txtSampleNoStart.ReadOnly = false;
            this.txtSampleNoStart.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSampleNoStart.SelectionStart = 0;
            this.txtSampleNoStart.SelectStart = 0;
            this.txtSampleNoStart.Size = new System.Drawing.Size(145, 23);
            this.txtSampleNoStart.TabIndex = 1;
            this.txtSampleNoStart.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtSampleNoStart.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.txtSampleNoStart.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtSampleNoStart.Watermark = null;
            // 
            // txtSampleNoEnd
            // 
            this.txtSampleNoEnd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSampleNoEnd.Captain = "到";
            this.txtSampleNoEnd.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtSampleNoEnd.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.txtSampleNoEnd.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtSampleNoEnd.CaptainWidth = 20F;
            this.txtSampleNoEnd.ContentForeColor = System.Drawing.Color.Black;
            this.txtSampleNoEnd.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtSampleNoEnd.EditMask = null;
            this.txtSampleNoEnd.Location = new System.Drawing.Point(154, 32);
            this.txtSampleNoEnd.Multiline = false;
            this.txtSampleNoEnd.Name = "txtSampleNoEnd";
            this.txtSampleNoEnd.PasswordChar = '\0';
            this.txtSampleNoEnd.ReadOnly = false;
            this.txtSampleNoEnd.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSampleNoEnd.SelectionStart = 0;
            this.txtSampleNoEnd.SelectStart = 0;
            this.txtSampleNoEnd.Size = new System.Drawing.Size(115, 23);
            this.txtSampleNoEnd.TabIndex = 2;
            this.txtSampleNoEnd.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtSampleNoEnd.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.txtSampleNoEnd.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtSampleNoEnd.Watermark = null;
            // 
            // btnSearch
            // 
            this.btnSearch.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnSearch.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnSearch.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnSearch.Font = new System.Drawing.Font("宋体", 10.5F);
            this.btnSearch.Location = new System.Drawing.Point(275, 32);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(57, 23);
            this.btnSearch.TabIndex = 3;
            this.btnSearch.Text = "查询";
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtSearch
            // 
            this.txtSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSearch.Captain = "搜索";
            this.txtSearch.CaptainBackColor = System.Drawing.Color.Transparent;
            this.txtSearch.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.txtSearch.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.txtSearch.CaptainWidth = 50F;
            this.tlpFilters.SetColumnSpan(this.txtSearch, 3);
            this.txtSearch.ContentForeColor = System.Drawing.Color.Black;
            this.txtSearch.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.txtSearch.EditMask = null;
            this.txtSearch.Location = new System.Drawing.Point(3, 61);
            this.txtSearch.Multiline = false;
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.PasswordChar = '\0';
            this.txtSearch.ReadOnly = false;
            this.txtSearch.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSearch.SelectionStart = 0;
            this.txtSearch.SelectStart = 0;
            this.txtSearch.Size = new System.Drawing.Size(266, 23);
            this.txtSearch.TabIndex = 4;
            this.txtSearch.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtSearch.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.txtSearch.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.txtSearch.Watermark = null;
            // 
            // btnRefresh
            // 
            this.btnRefresh.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnRefresh.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnRefresh.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btnRefresh.Font = new System.Drawing.Font("宋体", 10.5F);
            this.btnRefresh.Location = new System.Drawing.Point(275, 61);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(57, 23);
            this.btnRefresh.TabIndex = 5;
            this.btnRefresh.Text = "刷新";
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 125);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(341, 487);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 1;
            this.myGrid1.Xmlpath = null;
            this.myGrid1.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.myGrid1_MouseDoubleClick);
            // 
            // lblSampleCount
            // 
            this.lblSampleCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.lblSampleCount.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblSampleCount.Location = new System.Drawing.Point(0, 612);
            this.lblSampleCount.Margin = new System.Windows.Forms.Padding(0);
            this.lblSampleCount.Name = "lblSampleCount";
            this.lblSampleCount.Size = new System.Drawing.Size(341, 25);
            this.lblSampleCount.TabIndex = 2;
            this.lblSampleCount.Text = "样本数量: 0";
            this.lblSampleCount.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmdAdd);
            this.c1CommandHolder1.Commands.Add(this.CmdSave);
            this.c1CommandHolder1.Commands.Add(this.CmdDelete);
            this.c1CommandHolder1.Commands.Add(this.CmdAudit);
            this.c1CommandHolder1.Commands.Add(this.CmdCancelAudit);
            this.c1CommandHolder1.Commands.Add(this.CmdAuditPrint);
            this.c1CommandHolder1.Commands.Add(this.CmdPrint);
            this.c1CommandHolder1.Commands.Add(this.CmdPreview);
            this.c1CommandHolder1.Owner = this;
            // 
            // CmdAdd
            // 
            this.CmdAdd.Name = "CmdAdd";
            this.CmdAdd.Shortcut = System.Windows.Forms.Shortcut.F1;
            this.CmdAdd.ShortcutText = "";
            this.CmdAdd.Text = "新增";
            this.CmdAdd.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdAdd_Click);
            // 
            // CmdSave
            // 
            this.CmdSave.Name = "CmdSave";
            this.CmdSave.Shortcut = System.Windows.Forms.Shortcut.F2;
            this.CmdSave.ShortcutText = "";
            this.CmdSave.Text = "保存";
            this.CmdSave.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdSave_Click);
            // 
            // CmdDelete
            // 
            this.CmdDelete.Name = "CmdDelete";
            this.CmdDelete.Shortcut = System.Windows.Forms.Shortcut.F3;
            this.CmdDelete.ShortcutText = "";
            this.CmdDelete.Text = "删除";
            // 
            // CmdAudit
            // 
            this.CmdAudit.Name = "CmdAudit";
            this.CmdAudit.Shortcut = System.Windows.Forms.Shortcut.F5;
            this.CmdAudit.ShortcutText = "";
            this.CmdAudit.Text = "审核";
            // 
            // CmdCancelAudit
            // 
            this.CmdCancelAudit.Name = "CmdCancelAudit";
            this.CmdCancelAudit.ShortcutText = "";
            this.CmdCancelAudit.Text = "取消审核";
            // 
            // CmdAuditPrint
            // 
            this.CmdAuditPrint.Name = "CmdAuditPrint";
            this.CmdAuditPrint.Shortcut = System.Windows.Forms.Shortcut.F6;
            this.CmdAuditPrint.ShortcutText = "";
            this.CmdAuditPrint.Text = "审核打印";
            // 
            // CmdPrint
            // 
            this.CmdPrint.Name = "CmdPrint";
            this.CmdPrint.Shortcut = System.Windows.Forms.Shortcut.F7;
            this.CmdPrint.ShortcutText = "";
            this.CmdPrint.Text = "打印";
            // 
            // CmdPreview
            // 
            this.CmdPreview.Name = "CmdPreview";
            this.CmdPreview.Shortcut = System.Windows.Forms.Shortcut.F8;
            this.CmdPreview.ShortcutText = "";
            this.CmdPreview.Text = "预览";
            // 
            // c1CommandLink1
            // 
            this.c1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink1.Command = this.CmdAdd;
            this.c1CommandLink1.SortOrder = 1;
            // 
            // c1CommandLink2
            // 
            this.c1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink2.Command = this.CmdSave;
            this.c1CommandLink2.SortOrder = 2;
            // 
            // c1CommandLink3
            // 
            this.c1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink3.Command = this.CmdDelete;
            this.c1CommandLink3.SortOrder = 3;
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink4.Command = this.CmdAudit;
            this.c1CommandLink4.SortOrder = 4;
            // 
            // c1CommandLink5
            // 
            this.c1CommandLink5.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink5.Command = this.CmdCancelAudit;
            this.c1CommandLink5.SortOrder = 5;
            // 
            // c1CommandLink6
            // 
            this.c1CommandLink6.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink6.Command = this.CmdAuditPrint;
            this.c1CommandLink6.SortOrder = 6;
            // 
            // c1CommandLink7
            // 
            this.c1CommandLink7.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink7.Command = this.CmdPrint;
            this.c1CommandLink7.SortOrder = 7;
            // 
            // c1CommandLink8
            // 
            this.c1CommandLink8.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink8.Command = this.CmdPreview;
            this.c1CommandLink8.SortOrder = 8;
            // 
            // pnlTop
            // 
            this.pnlTop.AutoSize = true;
            this.pnlTop.Controls.Add(this.c1ToolBar1);
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.pnlTop.Location = new System.Drawing.Point(0, 0);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new System.Drawing.Size(1015, 30);
            this.pnlTop.TabIndex = 3;
            // 
            // c1ToolBar1
            // 
            this.c1ToolBar1.AccessibleName = "Tool Bar";
            this.c1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.c1ToolBar1.CommandHolder = this.c1CommandHolder1;
            this.c1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink9,
            this.c1CommandLink10,
            this.c1CommandLink11,
            this.c1CommandLink12,
            this.c1CommandLink13,
            this.c1CommandLink14,
            this.c1CommandLink15,
            this.c1CommandLink16});
            this.c1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.c1ToolBar1.Name = "c1ToolBar1";
            this.c1ToolBar1.Size = new System.Drawing.Size(562, 24);
            this.c1ToolBar1.Text = "c1ToolBar1";
            this.c1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.c1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // c1CommandLink9
            // 
            this.c1CommandLink9.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink9.Command = this.CmdAdd;
            // 
            // c1CommandLink10
            // 
            this.c1CommandLink10.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink10.Command = this.CmdSave;
            this.c1CommandLink10.SortOrder = 1;
            // 
            // c1CommandLink11
            // 
            this.c1CommandLink11.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink11.Command = this.CmdDelete;
            this.c1CommandLink11.SortOrder = 2;
            // 
            // c1CommandLink12
            // 
            this.c1CommandLink12.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink12.Command = this.CmdAudit;
            this.c1CommandLink12.SortOrder = 3;
            // 
            // c1CommandLink13
            // 
            this.c1CommandLink13.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink13.Command = this.CmdCancelAudit;
            this.c1CommandLink13.SortOrder = 4;
            // 
            // c1CommandLink14
            // 
            this.c1CommandLink14.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink14.Command = this.CmdAuditPrint;
            this.c1CommandLink14.SortOrder = 5;
            // 
            // c1CommandLink15
            // 
            this.c1CommandLink15.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink15.Command = this.CmdPrint;
            this.c1CommandLink15.SortOrder = 6;
            // 
            // c1CommandLink16
            // 
            this.c1CommandLink16.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink16.Command = this.CmdPreview;
            this.c1CommandLink16.SortOrder = 7;
            // 
            // LabTestRequest
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(1015, 694);
            this.Controls.Add(this.tlpMain);
            this.Controls.Add(this.c1CommandDock1);
            this.Controls.Add(this.pnlTop);
            this.Name = "LabTestRequest";
            this.Text = "检验申请";
            this.Load += new System.EventHandler(this.LabTestRequest_Load);
            this.tlpMain.ResumeLayout(false);
            this.pnlMiddle.ResumeLayout(false);
            this.tlpMiddle.ResumeLayout(false);
            this.pnlRight.ResumeLayout(false);
            this.tlpRight.ResumeLayout(false);
            this.tlpRight.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock1)).EndInit();
            this.c1CommandDock1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1DockingTab1)).EndInit();
            this.c1DockingTab1.ResumeLayout(false);
            this.c1DockingTabPage1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.pnlFilters.ResumeLayout(false);
            this.tlpFilters.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.pnlTop.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TableLayoutPanel tlpMain;
        private C1.Win.C1Command.C1CommandDock c1CommandDock1;
        private C1.Win.C1Command.C1DockingTab c1DockingTab1;
        private C1.Win.C1Command.C1DockingTabPage c1DockingTabPage1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.Panel pnlFilters;
        private System.Windows.Forms.TableLayoutPanel tlpFilters;
        private CustomControl.MySingleComobo cboSampleStatusFilter;
        private CustomControl.MyTextBox txtSampleNoStart;
        private CustomControl.MyTextBox txtSampleNoEnd;
        private CustomControl.MyButton btnSearch;
        private CustomControl.MyTextBox txtSearch;
        private CustomControl.MyButton btnRefresh;
        private CustomControl.MyGrid myGrid1;
        private System.Windows.Forms.Label lblSampleCount;
        private System.Windows.Forms.Panel pnlMiddle;
        private System.Windows.Forms.TableLayoutPanel tlpMiddle;
        private CustomControl.MyDateEdit dtpCheckDate;
        private ZTLisControl.ComboLabInstrument cboInstrument;
        private CustomControl.MyTextBox txtDailySeqNo;
        private CustomControl.MyTextBox txtPatientName;
        private ZTLisControl.ComboVisitType cboVisitType;
        private CustomControl.MyDateEdit dtpBirthDate;
        private CustomControl.MyNumericEdit numAge;
        private ZTLisControl.SingleAgeUnit cboAgeUnit;
        private CustomControl.MyTextBox txtBedNo;
        private ZTLisControl.ComboDoctor cboRequestDoctor;
        private ZTLisControl.ComboDept cboRequestDept;
        private ZTLisControl.ComboSampleType cboSampleType;
        private ZTLisControl.ComboSpecimenCondition cboSpecimenCondition;
        private CustomControl.MyTextBox txtRemark;
        private ZTLisControl.ComboDoctor cboCheckDoctor;
        private ZTLisControl.ComboDoctor cboAuditDoctor;
        private CustomControl.MyTextBox txtReportComment;
        private System.Windows.Forms.Panel pnlRight;
        private ZTLisControl.ComboLabProfile cboLabProfile;
        private System.Windows.Forms.CheckBox chkKeepItems;
        private System.Windows.Forms.TableLayoutPanel tlpRight;
        private CustomControl.MyGrid myGrid2;
        private System.Windows.Forms.Label lblResultCount;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1Command CmdAdd;
        private C1.Win.C1Command.C1Command CmdSave;
        private C1.Win.C1Command.C1Command CmdDelete;
        private C1.Win.C1Command.C1Command CmdAudit;
        private C1.Win.C1Command.C1Command CmdCancelAudit;
        private C1.Win.C1Command.C1Command CmdAuditPrint;
        private C1.Win.C1Command.C1Command CmdPrint;
        private C1.Win.C1Command.C1Command CmdPreview;
        private C1.Win.C1Command.C1CommandLink c1CommandLink1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink2;
        private C1.Win.C1Command.C1CommandLink c1CommandLink3;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1CommandLink c1CommandLink5;
        private C1.Win.C1Command.C1CommandLink c1CommandLink6;
        private C1.Win.C1Command.C1CommandLink c1CommandLink7;
        private C1.Win.C1Command.C1CommandLink c1CommandLink8;
        private System.Windows.Forms.Panel pnlTop;
        private C1.Win.C1Command.C1ToolBar c1ToolBar1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink9;
        private C1.Win.C1Command.C1CommandLink c1CommandLink10;
        private C1.Win.C1Command.C1CommandLink c1CommandLink11;
        private C1.Win.C1Command.C1CommandLink c1CommandLink12;
        private C1.Win.C1Command.C1CommandLink c1CommandLink13;
        private C1.Win.C1Command.C1CommandLink c1CommandLink14;
        private C1.Win.C1Command.C1CommandLink c1CommandLink15;
        private C1.Win.C1Command.C1CommandLink c1CommandLink16;
        private CustomControl.MyTextBox txtSeqPrefix;
        private ZTLisControl.SingleSex singleSex1;
    }
}