﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysRpt_Class_Level.cs
*
* 功 能： N/A
* 类 名： MdlSysRpt_Class_Level
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-22 11:15:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 店铺编码规则
	/// </summary>
	[Serializable]
	public partial class MdlSysRpt_Class_Level
	{
		public MdlSysRpt_Class_Level()
		{}
		#region Model
		private int _level_code;
		private int? _level_width;
		private string _level_name;
		/// <summary>
		/// 
		/// </summary>
		public int Level_Code
		{
			set{ _level_code=value;}
			get{return _level_code;}
		}
		/// <summary>
		/// 级别长度
		/// </summary>
		public int? Level_Width
		{
			set{ _level_width=value;}
			get{return _level_width;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Level_Name
		{
			set{ _level_name=value;}
			get{return _level_name;}
		}
		#endregion Model

	}
}

