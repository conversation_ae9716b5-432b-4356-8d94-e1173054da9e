﻿namespace ZTLisSysManage
{
    partial class DictRole1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DictRole1));
            C1.Win.TreeView.C1TreeColumn c1TreeColumn1 = new C1.Win.TreeView.C1TreeColumn();
            C1.Win.TreeView.C1TreeColumn c1TreeColumn2 = new C1.Win.TreeView.C1TreeColumn();
            this.C1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.CmdAdd = new C1.Win.C1Command.C1Command();
            this.CmdDelete = new C1.Win.C1Command.C1Command();
            this.CmdRefresh = new C1.Win.C1Command.C1Command();
            this.CmdMoveUp = new C1.Win.C1Command.C1Command();
            this.CmdMoveDown = new C1.Win.C1Command.C1Command();
            this.CmdExport = new C1.Win.C1Command.C1Command();
            this.CmdImport = new C1.Win.C1Command.C1Command();
            this.CmdEnable = new C1.Win.C1Command.C1Command();
            this.CmdDisable = new C1.Win.C1Command.C1Command();
            this.myGrid1 = new CustomControl.MyGrid();
            this.treeView1 = new System.Windows.Forms.TreeView();
            this.imageList3 = new System.Windows.Forms.ImageList(this.components);
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.TvwRoleAuth = new CustomControl.MyTreeView();
            this.LblTotal = new System.Windows.Forms.Label();
            this.TxtFilter = new CustomControl.MyTextBox();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.C1CommandLink10 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink9 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink8 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink5 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.C1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.C1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.C1CommandLink7 = new C1.Win.C1Command.C1CommandLink();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            ((System.ComponentModel.ISupportInitialize)(this.C1CommandHolder1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.TvwRoleAuth)).BeginInit();
            this.tableLayoutPanel2.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // imageList2
            // 
            this.imageList2.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList2.ImageStream")));
            this.imageList2.Images.SetKeyName(0, "Cabinet_24px_1109498_easyicon.net.png");
            this.imageList2.Images.SetKeyName(1, "Folder_Closed_24px_1079181_easyicon.net.png");
            this.imageList2.Images.SetKeyName(2, "Folder_Open_24px_1079182_easyicon.net.png");
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.Images.SetKeyName(0, "增加.png");
            this.imageList1.Images.SetKeyName(1, "删除.png");
            this.imageList1.Images.SetKeyName(2, "打印.png");
            this.imageList1.Images.SetKeyName(3, "导入.png");
            this.imageList1.Images.SetKeyName(4, "导出.png");
            this.imageList1.Images.SetKeyName(5, "上移.png");
            this.imageList1.Images.SetKeyName(6, "下移.png");
            this.imageList1.Images.SetKeyName(7, "启用.png");
            this.imageList1.Images.SetKeyName(8, "停用.png");
            this.imageList1.Images.SetKeyName(9, "刷新.png");
            // 
            // C1CommandHolder1
            // 
            this.C1CommandHolder1.Commands.Add(this.CmdAdd);
            this.C1CommandHolder1.Commands.Add(this.CmdDelete);
            this.C1CommandHolder1.Commands.Add(this.CmdRefresh);
            this.C1CommandHolder1.Commands.Add(this.CmdMoveUp);
            this.C1CommandHolder1.Commands.Add(this.CmdMoveDown);
            this.C1CommandHolder1.Commands.Add(this.CmdExport);
            this.C1CommandHolder1.Commands.Add(this.CmdImport);
            this.C1CommandHolder1.Commands.Add(this.CmdEnable);
            this.C1CommandHolder1.Commands.Add(this.CmdDisable);
            this.C1CommandHolder1.Owner = this;
            // 
            // CmdAdd
            // 
            this.CmdAdd.Image = global::ZTLisSysManage.Properties.Resources.增加;
            this.CmdAdd.Name = "CmdAdd";
            this.CmdAdd.ShortcutText = "";
            this.CmdAdd.Text = "增加";
            this.CmdAdd.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdAdd_Click);
            // 
            // CmdDelete
            // 
            this.CmdDelete.Image = global::ZTLisSysManage.Properties.Resources.删除;
            this.CmdDelete.Name = "CmdDelete";
            this.CmdDelete.ShortcutText = "";
            this.CmdDelete.Text = "删除";
            this.CmdDelete.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDelete_Click);
            // 
            // CmdRefresh
            // 
            this.CmdRefresh.Image = global::ZTLisSysManage.Properties.Resources.刷新;
            this.CmdRefresh.Name = "CmdRefresh";
            this.CmdRefresh.ShortcutText = "";
            this.CmdRefresh.Text = "刷新";
            this.CmdRefresh.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdRefresh_Click);
            // 
            // CmdMoveUp
            // 
            this.CmdMoveUp.Image = global::ZTLisSysManage.Properties.Resources.上移;
            this.CmdMoveUp.Name = "CmdMoveUp";
            this.CmdMoveUp.ShortcutText = "";
            this.CmdMoveUp.Text = "上移";
            this.CmdMoveUp.Visible = false;
            // 
            // CmdMoveDown
            // 
            this.CmdMoveDown.Image = global::ZTLisSysManage.Properties.Resources.下移;
            this.CmdMoveDown.Name = "CmdMoveDown";
            this.CmdMoveDown.ShortcutText = "";
            this.CmdMoveDown.Text = "下移";
            this.CmdMoveDown.Visible = false;
            // 
            // CmdExport
            // 
            this.CmdExport.Image = global::ZTLisSysManage.Properties.Resources.导出;
            this.CmdExport.Name = "CmdExport";
            this.CmdExport.ShortcutText = "";
            this.CmdExport.Text = "导出";
            this.CmdExport.Visible = false;
            this.CmdExport.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdExport_Click);
            // 
            // CmdImport
            // 
            this.CmdImport.Image = global::ZTLisSysManage.Properties.Resources.导入;
            this.CmdImport.Name = "CmdImport";
            this.CmdImport.ShortcutText = "";
            this.CmdImport.Text = "导入";
            this.CmdImport.ToolTipText = "使用导出模版导入，编码，拼音，五笔，是否启用不需填写";
            this.CmdImport.Visible = false;
            this.CmdImport.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdImport_Click);
            // 
            // CmdEnable
            // 
            this.CmdEnable.Image = global::ZTLisSysManage.Properties.Resources.启用;
            this.CmdEnable.Name = "CmdEnable";
            this.CmdEnable.ShortcutText = "";
            this.CmdEnable.Text = "启用";
            this.CmdEnable.Visible = false;
            // 
            // CmdDisable
            // 
            this.CmdDisable.Image = global::ZTLisSysManage.Properties.Resources.停用;
            this.CmdDisable.Name = "CmdDisable";
            this.CmdDisable.ShortcutText = "";
            this.CmdDisable.Text = "停用";
            this.CmdDisable.Visible = false;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(33, 85);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(97, 103);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 2;
            this.myGrid1.Xmlpath = null;
            // 
            // treeView1
            // 
            this.treeView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeView1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.treeView1.ImageIndex = 1;
            this.treeView1.ImageList = this.imageList3;
            this.treeView1.Location = new System.Drawing.Point(0, 0);
            this.treeView1.Margin = new System.Windows.Forms.Padding(0);
            this.treeView1.Name = "treeView1";
            this.treeView1.SelectedImageIndex = 0;
            this.treeView1.Size = new System.Drawing.Size(173, 504);
            this.treeView1.TabIndex = 3;
            this.treeView1.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeView1_AfterSelect);
            // 
            // imageList3
            // 
            this.imageList3.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList3.ImageStream")));
            this.imageList3.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList3.Images.SetKeyName(0, "");
            this.imageList3.Images.SetKeyName(1, "");
            this.imageList3.Images.SetKeyName(2, "");
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(173, 0);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(0);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.myGrid1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.tableLayoutPanel3);
            this.splitContainer1.Size = new System.Drawing.Size(695, 504);
            this.splitContainer1.SplitterDistance = 231;
            this.splitContainer1.TabIndex = 4;
            // 
            // tableLayoutPanel3
            // 
            this.tableLayoutPanel3.ColumnCount = 1;
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel3.Controls.Add(this.TvwRoleAuth, 0, 0);
            this.tableLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel3.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel3.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            this.tableLayoutPanel3.RowCount = 1;
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel3.Size = new System.Drawing.Size(460, 504);
            this.tableLayoutPanel3.TabIndex = 0;
            // 
            // TvwRoleAuth
            // 
            this.TvwRoleAuth.BindingInfo.DataMember = null;
            this.TvwRoleAuth.BindingInfo.KeyField = null;
            this.TvwRoleAuth.BindingInfo.ParentKeyField = null;
            // 
            // 
            // 
            this.TvwRoleAuth.ButtonImageList.ColorDepth = System.Windows.Forms.ColorDepth.Depth8Bit;
            this.TvwRoleAuth.ButtonImageList.ImageSize = new System.Drawing.Size(16, 16);
            // 
            // 
            // 
            this.TvwRoleAuth.CheckImageList.ColorDepth = System.Windows.Forms.ColorDepth.Depth8Bit;
            this.TvwRoleAuth.CheckImageList.ImageSize = new System.Drawing.Size(16, 16);
            c1TreeColumn1.DisplayFieldName = null;
            c1TreeColumn1.HeaderText = "新规列";
            c1TreeColumn1.Name = "新规列";
            c1TreeColumn2.DisplayFieldName = null;
            c1TreeColumn2.HeaderText = "新规列";
            c1TreeColumn2.Name = "新规列";
            this.TvwRoleAuth.Columns.Add(c1TreeColumn1);
            this.TvwRoleAuth.Columns.Add(c1TreeColumn2);
            this.TvwRoleAuth.DataTable = null;
            this.TvwRoleAuth.Dock = System.Windows.Forms.DockStyle.Left;
            this.TvwRoleAuth.Location = new System.Drawing.Point(3, 3);
            this.TvwRoleAuth.Name = "TvwRoleAuth";
            this.TvwRoleAuth.Size = new System.Drawing.Size(241, 498);
            this.TvwRoleAuth.TabIndex = 0;
            this.TvwRoleAuth.CheckStateChanged += new C1.Win.TreeView.C1TreeViewEventHandler(this.TvwRoleAuth_CheckStateChanged);
            // 
            // LblTotal
            // 
            this.LblTotal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.LblTotal.AutoSize = true;
            this.LblTotal.Location = new System.Drawing.Point(669, 18);
            this.LblTotal.Name = "LblTotal";
            this.LblTotal.Size = new System.Drawing.Size(144, 14);
            this.LblTotal.TabIndex = 0;
            this.LblTotal.Text = "label1";
            // 
            // TxtFilter
            // 
            this.TxtFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtFilter.Captain = "过滤框";
            this.TxtFilter.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFilter.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFilter.CaptainWidth = 50F;
            this.TxtFilter.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFilter.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFilter.EditMask = null;
            this.TxtFilter.Location = new System.Drawing.Point(360, 13);
            this.TxtFilter.Multiline = false;
            this.TxtFilter.Name = "TxtFilter";
            this.TxtFilter.PasswordChar = '\0';
            this.TxtFilter.ReadOnly = false;
            this.TxtFilter.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFilter.SelectionStart = 0;
            this.TxtFilter.SelectStart = 0;
            this.TxtFilter.Size = new System.Drawing.Size(303, 23);
            this.TxtFilter.TabIndex = 1;
            this.TxtFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFilter.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFilter.Watermark = "请填入名称、拼音、五笔简码进行过滤";
            this.TxtFilter.TextChanged += new System.EventHandler(this.TxtFilter_TextChanged);
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 2;
            this.tableLayoutPanel1.SetColumnSpan(this.tableLayoutPanel2, 3);
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel2.Controls.Add(this.treeView1, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.splitContainer1, 1, 0);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 50);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 1;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(816, 504);
            this.tableLayoutPanel2.TabIndex = 3;
            // 
            // C1CommandLink10
            // 
            this.C1CommandLink10.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink10.Command = this.CmdDisable;
            this.C1CommandLink10.SortOrder = 8;
            // 
            // C1CommandLink9
            // 
            this.C1CommandLink9.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink9.Command = this.CmdEnable;
            this.C1CommandLink9.SortOrder = 7;
            // 
            // C1CommandLink8
            // 
            this.C1CommandLink8.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink8.Command = this.CmdImport;
            this.C1CommandLink8.SortOrder = 6;
            // 
            // C1CommandLink5
            // 
            this.C1CommandLink5.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink5.Command = this.CmdMoveDown;
            this.C1CommandLink5.SortOrder = 4;
            // 
            // C1CommandLink4
            // 
            this.C1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink4.Command = this.CmdMoveUp;
            this.C1CommandLink4.SortOrder = 3;
            // 
            // C1CommandLink3
            // 
            this.C1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink3.Command = this.CmdRefresh;
            this.C1CommandLink3.SortOrder = 2;
            // 
            // C1CommandLink2
            // 
            this.C1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink2.Command = this.CmdDelete;
            this.C1CommandLink2.SortOrder = 1;
            // 
            // C1CommandLink1
            // 
            this.C1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink1.Command = this.CmdAdd;
            // 
            // C1ToolBar1
            // 
            this.C1ToolBar1.AccessibleName = "Tool Bar";
            this.C1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.C1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.C1ToolBar1.CommandHolder = null;
            this.C1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.C1CommandLink1,
            this.C1CommandLink2,
            this.C1CommandLink3,
            this.C1CommandLink4,
            this.C1CommandLink5,
            this.C1CommandLink7,
            this.C1CommandLink8,
            this.C1CommandLink9,
            this.C1CommandLink10});
            this.C1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.C1ToolBar1.Name = "C1ToolBar1";
            this.C1ToolBar1.Size = new System.Drawing.Size(351, 44);
            this.C1ToolBar1.Text = "C1ToolBar1";
            this.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // C1CommandLink7
            // 
            this.C1CommandLink7.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink7.Command = this.CmdExport;
            this.C1CommandLink7.SortOrder = 5;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.Controls.Add(this.C1ToolBar1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.LblTotal, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.TxtFilter, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel2, 0, 1);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(816, 554);
            this.tableLayoutPanel1.TabIndex = 4;
            // 
            // DictRole1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(816, 554);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "DictRole1";
            this.Text = "DictRole1";
            this.Load += new System.EventHandler(this.DictRole1_Load);
            ((System.ComponentModel.ISupportInitialize)(this.C1CommandHolder1)).EndInit();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.tableLayoutPanel3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.TvwRoleAuth)).EndInit();
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        internal C1.Win.C1Command.C1CommandHolder C1CommandHolder1;
        private C1.Win.C1Command.C1Command CmdAdd;
        private C1.Win.C1Command.C1Command CmdDelete;
        private C1.Win.C1Command.C1Command CmdRefresh;
        private C1.Win.C1Command.C1Command CmdMoveUp;
        private C1.Win.C1Command.C1Command CmdMoveDown;
        private C1.Win.C1Command.C1Command CmdExport;
        private C1.Win.C1Command.C1Command CmdImport;
        private C1.Win.C1Command.C1Command CmdEnable;
        private C1.Win.C1Command.C1Command CmdDisable;
        private CustomControl.MyGrid myGrid1;
        private System.Windows.Forms.TreeView treeView1;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel3;
        private CustomControl.MyTreeView TvwRoleAuth;
        private System.Windows.Forms.Label LblTotal;
        internal CustomControl.MyTextBox TxtFilter;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        internal C1.Win.C1Command.C1ToolBar C1ToolBar1;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink1;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink2;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink3;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink4;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink5;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink7;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink8;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink9;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink10;
        private System.Windows.Forms.ImageList imageList3;
    }
}