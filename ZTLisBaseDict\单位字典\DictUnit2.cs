using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictUnit2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictUnit _bllDictUnit = new BllDictUnit();
        Model.MdlDictUnit _mdlDictUnit = new MdlDictUnit();

        public DictUnit2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictUnit2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtName.Text = "";
            TxtName.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtName.Text = row["UnitName"] + "";
            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写单位名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }

            // 检查名称重复
            if (base.Insert == true)
            {
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("单位名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtName.Text.Trim() != base.MyRow["UnitName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("单位名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictUnit.GetRecordCount($"UnitName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictUnit.UnitName = TxtName.Text.Trim();
            _mdlDictUnit.Status = true;
            _mdlDictUnit.CreatedAt = DateTime.Now;
            _mdlDictUnit.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictUnit, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["UnitId"] = _bllDictUnit.Add(_mdlDictUnit);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictUnit = _bllDictUnit.GetModel(int.Parse(base.MyRow["UnitId"].ToString()));
            _mdlDictUnit.UnitName = TxtName.Text.Trim();
            _mdlDictUnit.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictUnit, base.MyRow);

            //数据保存
            try
            {
                _bllDictUnit.Update(_mdlDictUnit);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 事件

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
            {
                return;
            }

            if (base.Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}