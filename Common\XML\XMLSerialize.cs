﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml.Serialization;
using Common.XML;

namespace Common
{
    public class XMLSerialize
    {
        public static string XmlSerialize<T>(T obj, Encoding encoding = null)
        {
            if (encoding == null) encoding = Encoding.GetEncoding("UTF-8");
            using (StringWriterWithEncoding sw = new StringWriterWithEncoding(encoding))
            {

                Type t = obj.GetType();
                // 强制指定命名空间，覆盖默认的命名空间    
                XmlSerializerNamespaces namespaces = new XmlSerializerNamespaces();
                namespaces.Add(string.Empty, string.Empty);


                XmlSerializer serializer = new XmlSerializer(obj.GetType());

                // 序列化时增加namespaces  
                serializer.Serialize(sw, obj, namespaces);


                sw.Close();
                return sw.ToString();
            }

        }

        public static T DESerializer<T>(string strXML) where T : class
        {
            try
            {
                using (StringReader sr = new StringReader(strXML)) { 
                    XmlSerializer serializer = new XmlSerializer(typeof(T));
                return serializer.Deserialize(sr) as T;
            }
            }
            catch (Exception ex)
            {
                return null;
            }
}
    }

}
