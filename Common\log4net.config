﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>
  <!-- Debug 将日志以回滚文件的形式写到文件中 -->
  <appender name="RollingFileDebug" type="log4net.Appender.RollingFileAppender" >
    <!-- 日志文件存放位置，可以为绝对路径也可以为相对路径 -->
    <file value="log/" />
    <!-- 日志文件的命名规则 -->
    <datePattern value="yyyy-MM-dd/'Debug.log'"/>
    <!-- 将日志信息追加到已有的日志文件中-->
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="100" />
    <maximumFileSize value="1MB" />
    <!-- 指定按日期切分日志文件 -->
    <rollingStyle value="Date"/>
    <!-- 当将日期作为日志文件的名字时，必须将staticLogFileName的值设置为false -->
    <staticLogFileName value="false" />
    <!-- 最小锁定模式，以允许多个进程可以写入同一个文件 -->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value=" &#13;&#10;【日志时间】： %d 【线程ID】： %thread &#13;&#10;【日志级别】： %-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="Debug" />
      <param name="LevelMax" value="Debug" />
    </filter>
  </appender>

  <!-- Info 将日志以回滚文件的形式写到文件中 -->
  <appender name="RollingFileInfo" type="log4net.Appender.RollingFileAppender" >
    <!-- 日志文件存放位置，可以为绝对路径也可以为相对路径 -->
    <file value="log/" />
    <!-- 日志文件的命名规则 -->
    <datePattern value="yyyy-MM-dd/'Info.log'"/>
    <!-- 将日志信息追加到已有的日志文件中-->
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="100" />
    <maximumFileSize value="1MB" />
    <!-- 指定按日期切分日志文件 -->
    <rollingStyle value="Date"/>
    <!-- 当将日期作为日志文件的名字时，必须将staticLogFileName的值设置为false -->
    <staticLogFileName value="false" />
    <!-- 最小锁定模式，以允许多个进程可以写入同一个文件 -->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value=" &#13;&#10;【日志时间】：%d 【线程ID】：%thread &#13;&#10;【日志级别】：%-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="Info" />
      <param name="LevelMax" value="Info" />
    </filter>
  </appender>

  <!-- WARN 将日志以回滚文件的形式写到文件中 -->
  <appender name="RollingFileWarn" type="log4net.Appender.RollingFileAppender" >
    <!-- 日志文件存放位置，可以为绝对路径也可以为相对路径 -->
    <file value="log/" />
    <!-- 日志文件的命名规则 -->
    <datePattern value="yyyy-MM-dd/'Warn.log'"/>
    <!-- 将日志信息追加到已有的日志文件中-->
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="100" />
    <maximumFileSize value="1MB" />
    <!-- 指定按日期切分日志文件 -->
    <rollingStyle value="Date"/>
    <!-- 当将日期作为日志文件的名字时，必须将staticLogFileName的值设置为false -->
    <staticLogFileName value="false" />
    <!-- 最小锁定模式，以允许多个进程可以写入同一个文件 -->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value=" &#13;&#10;【日志时间】：%d 【线程ID】：%thread &#13;&#10;【日志级别】：%-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="Warn" />
      <param name="LevelMax" value="Warn" />
    </filter>
  </appender>

  <!-- Error 将日志以回滚文件的形式写到文件中 -->
  <appender name="RollingFileError" type="log4net.Appender.RollingFileAppender" >
    <!-- 日志文件存放位置，可以为绝对路径也可以为相对路径 -->
    <file value="log/" />
    <!-- 日志文件的命名规则 -->
    <datePattern value="yyyy-MM-dd/'Error.log'"/>
    <!-- 将日志信息追加到已有的日志文件中-->
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="100" />
    <maximumFileSize value="1MB" />
    <!-- 指定按日期切分日志文件 -->
    <rollingStyle value="Date"/>
    <!-- 当将日期作为日志文件的名字时，必须将staticLogFileName的值设置为false -->
    <staticLogFileName value="false" />
    <!-- 最小锁定模式，以允许多个进程可以写入同一个文件 -->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value=" &#13;&#10;【日志时间】：%d 【线程ID】：%thread &#13;&#10;【日志级别】：%-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="Error" />
      <param name="LevelMax" value="Error" />
    </filter>
  </appender>

  <!-- Fatal 将日志以回滚文件的形式写到文件中 -->
  <appender name="RollingFileFatal" type="log4net.Appender.RollingFileAppender" >
    <!-- 日志文件存放位置，可以为绝对路径也可以为相对路径 -->
    <file value="log/" />
    <!-- 日志文件的命名规则 -->
    <datePattern value="yyyy-MM-dd/'Fatal.log'"/>
    <!-- 将日志信息追加到已有的日志文件中-->
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="100" />
    <maximumFileSize value="1MB" />
    <!-- 指定按日期切分日志文件 -->
    <rollingStyle value="Date"/>
    <!-- 当将日期作为日志文件的名字时，必须将staticLogFileName的值设置为false -->
    <staticLogFileName value="false" />
    <!-- 最小锁定模式，以允许多个进程可以写入同一个文件 -->
    <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value=" &#13;&#10;【日志时间】：%d 【线程ID】：%thread &#13;&#10;【日志级别】：%-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <param name="LevelMin" value="Fatal" />
      <param name="LevelMax" value="Fatal" />
    </filter>
  </appender>

    <!-- 将日志输出到控制台 -->
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
    </layout>
  </appender>


  <root>
    <!--控制级别，由低到高：ALL|DEBUG|INFO|WARN|ERROR|FATAL|OFF
比如定义级别为INFO，则INFO级别向下的级别，比如DEBUG日志将不会被记录
如果没有定义LEVEL的值，则缺省为DEBUG-->
    <appender-ref ref="RollingFileDebug" />
    <appender-ref ref="RollingFileInfo" />
    <appender-ref ref="RollingFileWarn" />
    <appender-ref ref="RollingFileError" />
    <appender-ref ref="RollingFileFatal" />
     <!-- 将日志输出到控制台 -->
    <appender-ref ref="ConsoleAppender" />
    <level value="DEBUG" />
  </root>

  
  <logger name="NHibernate">
    <level value="WARN" />
  </logger>
</log4net>