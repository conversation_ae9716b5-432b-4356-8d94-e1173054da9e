use lis
go
/****** Object:  Table [dbo].[SysMenu1]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysMenu1]
(
     [FrsCode] [char](2) NOT NULL,
     [FrsName] [varchar](50) NULL,
     [SortNo] [int] NULL,
     CONSTRAINT [PK_SYSMENU1] PRIMARY KEY NONCLUSTERED 
(
	[FrsCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysMenu2]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysMenu2]
(
     [ScndCode] [char](3) NOT NULL,
     [FrsCode] [char](2) NOT NULL,
     [ScndName] [varchar](50) NULL,
     [SortNo] [int] NULL,
     CONSTRAINT [PK_SYSMENU2] PRIMARY KEY NONCLUSTERED 
(
	[ScndCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysModule]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysModule]
(
     [ScndCode] [char](3) NOT NULL,
     [ModuleCode] [char](4) NOT NULL,
     [ModuleName] [varchar](50) NULL,
     [SortNo] [int] NULL,
     CONSTRAINT [PK_SYSMODULE] PRIMARY KEY NONCLUSTERED 
(
	[ModuleCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysModuleAuth]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysModuleAuth]
(
     [ModuleCode] [char](4) NOT NULL,
     [AuthCode] [char](6) NOT NULL,
     [AuthName] [varchar](50) NULL,
     CONSTRAINT [PK_SYSMODULEAUTH] PRIMARY KEY NONCLUSTERED 
(
	[AuthCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysPara]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysPara]
(
     [ParaCode] [char](4) NOT NULL,
     [ParaType] [varchar](50) NULL,
     [ParaName] [varchar](50) NULL,
     [ParaValue] [varchar](50) NULL,
     [ParaMemo] [varchar](500) NULL,
     CONSTRAINT [PK_SYSPARA] PRIMARY KEY NONCLUSTERED 
(
	[ParaCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRole]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRole]
(
     [RoleCode] [char](2) NOT NULL,
     [RoleName] [varchar](50) NULL,
     [RolePinYin] [varchar](50) NULL,
     [RoleWuBi] [varchar](50) NULL,
     [RoleMemo] [varchar](50) NULL,
     CONSTRAINT [PK_SYSROLE] PRIMARY KEY NONCLUSTERED 
(
	[RoleCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRoleAuth]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRoleAuth]
(
     [RoleCode] [char](2) NOT NULL,
     [AuthCode] [char](6) NOT NULL,
     CONSTRAINT [PK_SYSROLEAUTH] PRIMARY KEY NONCLUSTERED 
(
	[RoleCode] ASC,
	[AuthCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRoleModule]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRoleModule]
(
     [RoleCode] [char](2) NOT NULL,
     [ModuleCode] [char](4) NOT NULL,
     CONSTRAINT [PK_SYSROLEMODULE] PRIMARY KEY NONCLUSTERED 
(
	[RoleCode] ASC,
	[ModuleCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRpt]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRpt]
(
     [Rpt_Class_Code] [varchar](50) NOT NULL,
     [RptCode] [char](4) NOT NULL,
     [RptName] [varchar](50) NULL,
     [RptPath] [varchar](50) NULL,
     [RptContent] [image] NULL,
     [EditDTime] [smalldatetime] NULL,
     [Editor] [varchar](50) NULL,
     CONSTRAINT [PK_SYSRPT] PRIMARY KEY NONCLUSTERED 
(
	[RptCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRpt_Class]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRpt_Class]
(
     [Rpt_Class_Code] [varchar](50) NOT NULL,
     [Rpt_Class_Name] [varchar](50) NULL,
     CONSTRAINT [PK_SYSRPT_CLASS] PRIMARY KEY NONCLUSTERED 
(
	[Rpt_Class_Code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SysRpt_Class_Level]    Script Date: 2025/5/23 17:17:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SysRpt_Class_Level]
(
     [Level_Code] [int] NOT NULL,
     [Level_Name] [varchar](50) NULL,
     [Level_Width] [int] NULL,
     CONSTRAINT [PK_SYSRPT_CLASS_LEVEL] PRIMARY KEY NONCLUSTERED 
(
	[Level_Code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[SysMenu2]  WITH CHECK ADD  CONSTRAINT [FK_SysMenu2_REFERENCE_SysMenu1] FOREIGN KEY([FrsCode])
REFERENCES [dbo].[SysMenu1] ([FrsCode])
GO
ALTER TABLE [dbo].[SysMenu2] CHECK CONSTRAINT [FK_SysMenu2_REFERENCE_SysMenu1]
GO
ALTER TABLE [dbo].[SysModule]  WITH CHECK ADD  CONSTRAINT [FK_SysModule_REFERENCE_SysMenu2] FOREIGN KEY([ScndCode])
REFERENCES [dbo].[SysMenu2] ([ScndCode])
GO
ALTER TABLE [dbo].[SysModule] CHECK CONSTRAINT [FK_SysModule_REFERENCE_SysMenu2]
GO
ALTER TABLE [dbo].[SysModuleAuth]  WITH CHECK ADD  CONSTRAINT [FK_SysModuleAuth_REFERENCE_SysModule] FOREIGN KEY([ModuleCode])
REFERENCES [dbo].[SysModule] ([ModuleCode])
GO
ALTER TABLE [dbo].[SysModuleAuth] CHECK CONSTRAINT [FK_SysModuleAuth_REFERENCE_SysModule]
GO
ALTER TABLE [dbo].[SysRoleAuth]  WITH CHECK ADD  CONSTRAINT [FK_SysRoleAuth_REFERENCE_SysModuleAuth] FOREIGN KEY([AuthCode])
REFERENCES [dbo].[SysModuleAuth] ([AuthCode])
GO
ALTER TABLE [dbo].[SysRoleAuth] CHECK CONSTRAINT [FK_SysRoleAuth_REFERENCE_SysModuleAuth]
GO
ALTER TABLE [dbo].[SysRoleAuth]  WITH CHECK ADD  CONSTRAINT [FK_SysRoleAuth_REFERENCE_SysRole] FOREIGN KEY([RoleCode])
REFERENCES [dbo].[SysRole] ([RoleCode])
GO
ALTER TABLE [dbo].[SysRoleAuth] CHECK CONSTRAINT [FK_SysRoleAuth_REFERENCE_SysRole]
GO
ALTER TABLE [dbo].[SysRoleModule]  WITH CHECK ADD  CONSTRAINT [FK_SysRoleModule_REFERENCE_SysModule] FOREIGN KEY([ModuleCode])
REFERENCES [dbo].[SysModule] ([ModuleCode])
GO
ALTER TABLE [dbo].[SysRoleModule] CHECK CONSTRAINT [FK_SysRoleModule_REFERENCE_SysModule]
GO
ALTER TABLE [dbo].[SysRoleModule]  WITH CHECK ADD  CONSTRAINT [FK_SysRoleModule_REFERENCE_SysRole] FOREIGN KEY([RoleCode])
REFERENCES [dbo].[SysRole] ([RoleCode])
GO
ALTER TABLE [dbo].[SysRoleModule] CHECK CONSTRAINT [FK_SysRoleModule_REFERENCE_SysRole]
GO
ALTER TABLE [dbo].[SysRpt]  WITH CHECK ADD  CONSTRAINT [FK_SysRpt_REFERENCE_SysRpt_Class] FOREIGN KEY([Rpt_Class_Code])
REFERENCES [dbo].[SysRpt_Class] ([Rpt_Class_Code])
GO
ALTER TABLE [dbo].[SysRpt] CHECK CONSTRAINT [FK_SysRpt_REFERENCE_SysRpt_Class]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'一级编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu1', @level2type=N'COLUMN',@level2name=N'FrsCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu1', @level2type=N'COLUMN',@level2name=N'FrsName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu1', @level2type=N'COLUMN',@level2name=N'SortNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'一级菜单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu1'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'二级编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu2', @level2type=N'COLUMN',@level2name=N'ScndCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'一级编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu2', @level2type=N'COLUMN',@level2name=N'FrsCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'二级名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu2', @level2type=N'COLUMN',@level2name=N'ScndName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu2', @level2type=N'COLUMN',@level2name=N'SortNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'二级菜单' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysMenu2'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'二级编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModule', @level2type=N'COLUMN',@level2name=N'ScndCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'模块编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModule', @level2type=N'COLUMN',@level2name=N'ModuleCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'模块名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModule', @level2type=N'COLUMN',@level2name=N'ModuleName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModule', @level2type=N'COLUMN',@level2name=N'SortNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统模块' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModule'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'模块编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModuleAuth', @level2type=N'COLUMN',@level2name=N'ModuleCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModuleAuth', @level2type=N'COLUMN',@level2name=N'AuthCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModuleAuth', @level2type=N'COLUMN',@level2name=N'AuthName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统模块权限' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysModuleAuth'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysPara', @level2type=N'COLUMN',@level2name=N'ParaCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数类别' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysPara', @level2type=N'COLUMN',@level2name=N'ParaType'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysPara', @level2type=N'COLUMN',@level2name=N'ParaName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数值' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysPara', @level2type=N'COLUMN',@level2name=N'ParaValue'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数说明' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysPara', @level2type=N'COLUMN',@level2name=N'ParaMemo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统参数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysPara'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRole', @level2type=N'COLUMN',@level2name=N'RoleCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRole', @level2type=N'COLUMN',@level2name=N'RoleName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'拼音' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRole', @level2type=N'COLUMN',@level2name=N'RolePinYin'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'五笔' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRole', @level2type=N'COLUMN',@level2name=N'RoleWuBi'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRole', @level2type=N'COLUMN',@level2name=N'RoleMemo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统角色' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRole'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRoleAuth', @level2type=N'COLUMN',@level2name=N'RoleCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRoleAuth', @level2type=N'COLUMN',@level2name=N'AuthCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色权限' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRoleAuth'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRoleModule', @level2type=N'COLUMN',@level2name=N'RoleCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'模块编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRoleModule', @level2type=N'COLUMN',@level2name=N'ModuleCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色模块' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRoleModule'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类别编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'Rpt_Class_Code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'RptCode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'RptName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'路径' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'RptPath'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'内容' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'RptContent'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'编辑时间' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'EditDTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'编辑人' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt', @level2type=N'COLUMN',@level2name=N'Editor'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统报表' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'类别编码' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class', @level2type=N'COLUMN',@level2name=N'Rpt_Class_Code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class', @level2type=N'COLUMN',@level2name=N'Rpt_Class_Name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'报表类别' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'规则级别' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class_Level', @level2type=N'COLUMN',@level2name=N'Level_Code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'规则名称' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class_Level', @level2type=N'COLUMN',@level2name=N'Level_Name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'级别长度' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class_Level', @level2type=N'COLUMN',@level2name=N'Level_Width'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'报表类别规则' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'SysRpt_Class_Level'
GO

/*==============================================================
  表名：  LabInstrument   （仪器主档 / 连接参数）
  说明：  统一登记院内所有检验设备的静态信息与通讯配置。
          其他子表（项目映射、通道、质控计划等）均以 InstrumentId 关联。
  创建人：LIS系统
  创建日：2025-05-19
==============================================================*/

CREATE TABLE dbo.LabInstrument
(
     /* —— 主键 —— */
     InstrumentId INT IDENTITY(1,1) NOT NULL
          CONSTRAINT PK_LabInstrument PRIMARY KEY,

     /* —— 基础信息 —— */
     InstrCode NVARCHAR(20) NOT NULL UNIQUE            -- 设备唯一代号（英文/拼音）
        /* 如：BS240、CS600· … 方便代码层快速定位 */,
     InstrName NVARCHAR(100) NOT NULL,
     -- 中文全称：迈瑞 BS-240 生化分析仪
     Vendor NVARCHAR(60) NULL,
     -- 厂商：Mindray / Roche …
     Model NVARCHAR(60) NULL,
     -- 型号：BS-240
     SerialNo NVARCHAR(60) NULL,
     -- 出厂序列号
     Location NVARCHAR(60) NULL,
     -- 设备放置地点：临检实验室 1 号位

     /* —— 通讯方式 —— 
       0=RS232 (串口)  1=TCP  2=文件共享  3=中间库  4=REST API  5=HL7 MLLP
    */
     ConnType TINYINT NOT NULL
          CONSTRAINT CHK_Instr_ConnType CHECK (ConnType BETWEEN 0 AND 5),

     /* —— 消息格式类型 —— */
     MsgFormatType TINYINT NOT NULL DEFAULT 0
          CONSTRAINT CHK_Instr_MsgFormat CHECK (MsgFormatType BETWEEN 0 AND 1),
     -- 消息格式类型：0=字符串 1=字节流

     /* —— 串口参数 —— */
     ComPort NVARCHAR(10) NULL,
     -- COM3 / \\.\COM5
     BaudRate INT NULL,
     -- 9600 / 115200
     Parity CHAR(1) NULL CHECK (Parity IN ('N','E','O','M','S')),
     -- N,E,O,M,S
     DataBits TINYINT NULL CHECK (DataBits IN (5,6,7,8)),
     StopBits VARCHAR(10) NULL CHECK (StopBits IN ('1','1.5','2')),

     /* —— TCP/IP 参数 —— */
     IpHost VARCHAR(255) NULL,
     -- ********** / 域名
     IpPort INT NULL,
     -- 5001 / 5555
     KeepAliveSec SMALLINT NULL,
     -- 心跳秒数

     /* —— 文件共享 / 中间库 / REST —— */
     FilePath NVARCHAR(260) NULL,
     -- 结果文件掉落目录
     DbType NVARCHAR(50) NULL,
     -- 数据库类型
     DbConnStr NVARCHAR(400) NULL,
     -- 厂商中间库连接字符串
     ApiBaseUrl NVARCHAR(200) NULL,
     -- REST 起始 URL
     ApiAuthToken NVARCHAR(400) NULL,
     -- REST 令牌（加密存储推荐 Always Encrypted）

     /* —— 协议 & 版本 —— 
       0 ASTM  1 LIS2  2 POCT1-A  3 HL7  4 自定义
    */
     ProtocolType TINYINT NOT NULL
          CONSTRAINT CHK_Instr_Protocol CHECK (ProtocolType BETWEEN 0 AND 4),

     /* —— 通讯控制字符 —— */
     MsgHeaderFormatType TINYINT NOT NULL DEFAULT 0
          CONSTRAINT CHK_Instr_MsgHeaderFormat CHECK (MsgHeaderFormatType BETWEEN 0 AND 1),
     -- 消息头格式类型：0=字符串 1=字节流
     MsgHeader NVARCHAR(100) NULL,
     -- 消息头（ASCII码16进制，多个用空格分割）
     MsgTail NVARCHAR(100) NULL,
     -- 消息尾（ASCII码16进制，多个用空格分割）
     AckSuccess NVARCHAR(100) NULL,
     -- 接收成功应答（ASCII码16进制，多个用空格分割）
     AckFailure NVARCHAR(100) NULL,
     -- 接收失败应答（ASCII码16进制，多个用空格分割）

     DriverVersion NVARCHAR(20) NULL,
     -- 驱动 DLL 版本
     FirmwareVersion NVARCHAR(40) NULL,
     -- 设备固件
     CustomDriverClass NVARCHAR(255) NULL,
     -- 自定义驱动类名

     /* —— 状态 & 维护 —— */
     Status BIT NOT NULL -- 1=启用 0=停用
          CONSTRAINT DF_Instr_Status DEFAULT 1,
     Description NVARCHAR(200) NULL,
     -- 备注 / 停用原因

     CreatedAt DATETIME NOT NULL DEFAULT getdate(),
     UpdatedAt DATETIME NOT NULL DEFAULT getdate()
);
GO

/*==============================================================
  辅助索引
==============================================================*/
CREATE INDEX IX_Instr_Vendor_Model ON dbo.LabInstrument(Vendor, Model);
CREATE INDEX IX_Instr_ConnType    ON dbo.LabInstrument(ConnType);
GO

/*==============================================================
  字段中文注释（Extended Properties）
==============================================================*/
EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'设备唯一代号（英文/拼音）', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'InstrCode';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'设备中文全称', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'InstrName';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'厂商名称', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'Vendor';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'设备型号', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'Model';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'出厂序列号', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'SerialNo';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'设备放置地点', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'Location';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'通讯类型：0=串口 1=TCP 2=文件共享 3=中间库 4=REST 5=HL7', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'ConnType';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'消息格式类型：0=字符串 1=字节流', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'MsgFormatType';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'串口号', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'ComPort';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'波特率', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'BaudRate';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'校验位', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'Parity';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'数据位', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'DataBits';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'停止位', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'StopBits';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'IP地址或域名', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'IpHost';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'IP端口', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'IpPort';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'心跳间隔(秒)', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'KeepAliveSec';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'文件共享路径', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'FilePath';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'数据库类型', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'DbType';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'中间库连接字符串', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'DbConnStr';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'API基础URL', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'ApiBaseUrl';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'API认证令牌', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'ApiAuthToken';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'协议类型：0=ASTM 1=LIS2 2=POCT1-A 3=HL7 4=自定义', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'ProtocolType';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'消息头格式类型：0=字符串 1=字节流', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'MsgHeaderFormatType';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'消息头（ASCII码16进制，多个用空格分割）', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'MsgHeader';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'消息尾（ASCII码16进制，多个用空格分割）', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'MsgTail';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'接收成功应答（ASCII码16进制，多个用空格分割）', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'AckSuccess';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'接收失败应答（ASCII码16进制，多个用空格分割）', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'AckFailure';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'驱动版本', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'DriverVersion';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'固件版本', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'FirmwareVersion';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'自定义驱动类名', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'CustomDriverClass';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'状态：1=启用 0=停用', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'Status';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'备注信息', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'Description';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'创建时间', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'CreatedAt';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'更新时间', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument',
     @level2type = N'COLUMN',@level2name = 'UpdatedAt';

EXEC sys.sp_addextendedproperty 
     @name = N'MS_Description', @value = N'仪器主档/连接参数表', 
     @level0type = N'SCHEMA',@level0name = 'dbo',
     @level1type = N'TABLE', @level1name = 'LabInstrument';
GO

/*==============================================================
  示例数据插入
==============================================================*/
-- 示例1：（希森美康）全自动血凝仪CA-1500CA 串口 
INSERT INTO dbo.LabInstrument
     (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType, ComPort,
     BaudRate, Parity, DataBits, StopBits,
     CustomDriverClass, Location,ProtocolType,MsgHeaderFormatType,MsgHeader,MsgTail,AckSuccess,AckFailure)
VALUES
     ('CA-1500CA', N'（希森美康）全自动血凝仪CA-1500CA', N'希森美康', N'CA-1500CA',
          0, 0, 'COM3',
          9600, 'E', 8, '1',
          '希森美康CA6000', N'', 4, 1, '02', '03', '06', '15');

-- 示例2：QR-100 特定蛋白分析仪 串口
INSERT INTO dbo.LabInstrument
     (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType, ComPort,
     BaudRate, Parity, DataBits, StopBits,
     CustomDriverClass, Location,ProtocolType,MsgHeaderFormatType,MsgHeader,MsgTail,AckSuccess,AckFailure)
VALUES
     ('QR-100', N'QR-100特定蛋白分析仪', N'', N'QR-100',
          0, 1, 'COM3',
          115200, 'N', 8, '1',
          'QR100特定蛋白分析仪', N'', 4, 1, '   73 74 61 72 74', '65 6E 64', '', '');

-- 示例2：罗氏 Cobas 8000 TCP连接 LIS2协议
INSERT INTO dbo.LabInstrument
     (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType,
     IpHost, IpPort, KeepAliveSec,
     ProtocolType, DriverVersion, Location)
VALUES
     ('COBAS8000', N'罗氏 Cobas 8000 生化免疫分析系统', N'Roche', N'Cobas 8000',
          1, 0, '*************', 5001, 60,
          1, '2.5.0', N'生化免疫实验室');

-- 示例3：希森美康 XN-9000 文件共享方式
INSERT INTO dbo.LabInstrument
     (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType,
     FilePath, ProtocolType, DriverVersion, Location)
VALUES
     ('XN9000', N'希森美康 XN-9000 血液分析系统', N'Sysmex', N'XN-9000',
          2, 0, '\\\\fileserver\\share\\xn9000\\results',
          0, '1.8.3', N'血液学实验室');
GO

/*==============================================================
  表名：  LabInstrRawMsg   —— 仪器原始报文缓冲
  作用：  存储由各连接器（Connector）接收到的完整报文，
          解析器解析成功后将 ParsedFlag 置 1 并写入队列表。
==============================================================*/
CREATE TABLE dbo.LabInstrRawMsg
(
     RawId BIGINT IDENTITY(1,1) NOT NULL
          CONSTRAINT PK_LabInstrRawMsg PRIMARY KEY,
     -- 主键

     InstrumentId INT NOT NULL -- 关联仪器
          CONSTRAINT FK_LabInstrRawMsg_Instrument
                   REFERENCES dbo.LabInstrument(InstrumentId),

     RawText NVARCHAR(MAX) NOT NULL,
     -- 原始报文（含控制字符）
     AcquiredAt DATETIME NOT NULL
          CONSTRAINT DF_LabInstrRawMsg_Acq DEFAULT getdate(),
     -- 接收时间

     ParsedFlag BIT NOT NULL
          CONSTRAINT DF_LabInstrRawMsg_Flag DEFAULT 0,
     -- 0 未解析 1 已解析

     ParseError NVARCHAR(400) NULL
     -- 解析失败原因（可空）
);
GO

/*—— 辅助索引：按仪器与时间倒序 ——*/
CREATE INDEX IX_LabInstrRawMsg_InstrTime
ON dbo.LabInstrRawMsg(InstrumentId, AcquiredAt DESC);
GO

/*==============================================================
  字段中文注释（Extended Properties）
==============================================================*/
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器原始报文缓冲表',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'主键 ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg',@level2type=N'COLUMN',@level2name=N'RawId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联仪器 ID (LabInstrument)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg',@level2type=N'COLUMN',@level2name=N'InstrumentId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'完整原始报文文本',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg',@level2type=N'COLUMN',@level2name=N'RawText';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'接收时间（UTC）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg',@level2type=N'COLUMN',@level2name=N'AcquiredAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'解析标志：0 未解析 1 已解析',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg',@level2type=N'COLUMN',@level2name=N'ParsedFlag';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'解析失败原因',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrRawMsg',@level2type=N'COLUMN',@level2name=N'ParseError';
GO

/*==============================================================
  示例数据插入 - LabInstrRawMsg
==============================================================*/
-- 插入一条ASTM协议的原始报文示例 (BS240仪器)
INSERT INTO dbo.LabInstrRawMsg
     (InstrumentId, RawText)
SELECT InstrumentId,
     N'H|\^&|||BS-240^03.02.00^1234567890||||||P|LIS2-A|20230515123045' + CHAR(13) + CHAR(10) +
N'P|1||123456789|^||19850101|M|||||||||||||||||||||||||||' + CHAR(13) + CHAR(10) +
N'O|1|SID12345||^^^ALT+^^^AST+^^^GGT+^^^ALP|R||20230515122000|||||A||||||||||||||||' + CHAR(13) + CHAR(10) +
N'R|1|^^^ALT|42|U/L|5-40|H||F||||20230515123045|' + CHAR(13) + CHAR(10) +
N'R|2|^^^AST|38|U/L|5-40||||||20230515123045|' + CHAR(13) + CHAR(10) +
N'R|3|^^^GGT|75|U/L|11-50|H||F||||20230515123045|' + CHAR(13) + CHAR(10) +
N'R|4|^^^ALP|88|U/L|45-125||||||20230515123045|' + CHAR(13) + CHAR(10) +
N'L|1|N'
FROM dbo.LabInstrument
WHERE InstrCode = 'BS240';

-- 插入一条HL7协议的原始报文示例 (COBAS8000仪器)
INSERT INTO dbo.LabInstrRawMsg
     (InstrumentId, RawText)
SELECT InstrumentId,
     N'MSH|^~\&|COBAS8000|LAB|LIS|HOSPITAL|20230515123045||ORU^R01|20230515123045|P|2.3.1||||||UNICODE UTF-8' + CHAR(13) + 
N'PID|||987654321||DOE^JOHN||19750615|M|||123 MAIN ST^^ANYTOWN^CA^12345||5551234567|||123-45-6789||||' + CHAR(13) + 
N'OBR|1||LAB23456|CBC^COMPLETE BLOOD COUNT^L|||20230515120000|20230515120500|||||||20230515121000||DR. SMITH||||LAB|F||^^^' + CHAR(13) + 
N'OBX|1|NM|WBC^WHITE BLOOD CELL COUNT^L||6.8|10*3/uL|4.5-11.0||||F|||20230515123045||' + CHAR(13) + 
N'OBX|2|NM|RBC^RED BLOOD CELL COUNT^L||4.67|10*6/uL|4.50-5.90||||F|||20230515123045||' + CHAR(13) + 
N'OBX|3|NM|HGB^HEMOGLOBIN^L||14.2|g/dL|13.0-17.0||||F|||20230515123045||' + CHAR(13) + 
N'OBX|4|NM|HCT^HEMATOCRIT^L||42.1|%|40.0-52.0||||F|||20230515123045||' + CHAR(13) + 
N'OBX|5|NM|PLT^PLATELET COUNT^L||267|10*3/uL|150-450||||F|||20230515123045||'
FROM dbo.LabInstrument
WHERE InstrCode = 'COBAS8000';
GO

/*==============================================================
  表名： LabInstrResultQ  —— 解析结果队列         （Pascal 命名）
  作用： 存放 Parser 已解析、Importer 待入库的单项结果记录。
         通过 ProcessedFlag 管理成功 / 失败 / 待处理状态。
  作者： <Your Name>         创建日期：2025-05-19
==============================================================*/

CREATE TABLE dbo.LabInstrResultQ
(
     QId BIGINT IDENTITY(1,1) NOT NULL
          CONSTRAINT PK_LabInstrResultQ PRIMARY KEY,
     -- 主键

     RawId BIGINT NOT NULL
          CONSTRAINT FK_LabInstrResultQ_RawMsg
                   REFERENCES dbo.LabInstrRawMsg(RawId),
     -- FK → LabInstrRawMsg
     InstrumentId INT NOT NULL,
     -- FK → LabInstrument
     Barcode NVARCHAR(30) NOT NULL,
     -- 样本条码
     InstrItemCode NVARCHAR(30) NOT NULL,
     -- 仪器项目代码
     ResultValue NVARCHAR(60) NULL,
     -- 结果值
     InstrFlag NVARCHAR(10) NULL,
     -- 仪器标志
     ResultType TINYINT NOT NULL
          CONSTRAINT DF_LabInstrResultQ_ResultType DEFAULT 0
          CONSTRAINT CK_LabInstrResultQ_ResultType CHECK
     (ResultType IN
     (0,1,2)),
     -- 结果类型
     MeasuredAt DATETIME NULL,
     -- 仪器测定时间
     ProcessError NVARCHAR
     (200) NULL,
     -- 入库阶段的错误信息（为空表示入库成功）

     /* ProcessedFlag：0 待入库  1 入库成功  2 入库失败 */
     ProcessedFlag TINYINT NOT NULL
          CONSTRAINT DF_LabInstrResultQ_Flag DEFAULT 0
          CONSTRAINT CK_LabInstrResultQ_Flag CHECK
     (ProcessedFlag IN
     (0,1,2))
);
GO

/*—— 辅助索引 ——*/
CREATE INDEX IX_LabInstrResultQ_Flag 
    ON dbo.LabInstrResultQ(ProcessedFlag);

CREATE INDEX IX_LabInstrResultQ_Barcode 
    ON dbo.LabInstrResultQ(Barcode);
GO


/*==============================================================
  Extended Properties —— 全字段中文注释
==============================================================*/
EXEC sys.sp_addextendedproperty
 @name  = N'MS_Description',
 @value = N'解析结果中间队列表：存放解析完成但尚未写入 LabResult 的记录',
 @level0type=N'SCHEMA',@level0name=N'dbo',
 @level1type=N'TABLE',@level1name=N'LabInstrResultQ';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'主键 ID（IDENTITY）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'QId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联LabInstrRawMsg的RawId',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'RawId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联仪器 ID（LabInstrument.InstrumentId）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'InstrumentId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'样本条码',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'Barcode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器项目代码（待映射到 LIS 项目）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'InstrItemCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'结果值（字符或数字）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'ResultValue';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器标志（H/L/A等异常标记）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'InstrFlag';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'结果类型：0 正常 1 异常 2 警告',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'ResultType';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器测定时间（DATETIME）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'MeasuredAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'入库阶段的错误信息（为空表示入库成功）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'ProcessError';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'处理标记：0待入库 1入库成功 2入库失败',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrResultQ',@level2type=N'COLUMN',@level2name=N'ProcessedFlag';
GO



/*==============================================================
  表名： DictSampleType —— 标本类型字典表
  作用： 用于维护样本类型基础信息并在 LIS、HIS、仪器之间保持一致
  说明： 使用 Status 取代 IsActive 作为状态标识
==============================================================*/

CREATE TABLE dbo.DictSampleType
(
     SampleTypeId INT IDENTITY(1,1) PRIMARY KEY,
     -- 自增主键
     SampleTypeCode NVARCHAR(20) NOT NULL UNIQUE,
     -- 业务唯一编码
     SampleTypeName NVARCHAR(100) NOT NULL,
     -- 中文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音 / 首字母
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     ColorHex CHAR(6) NULL,
     -- 采样管帽颜色 HEX
     ContainerType NVARCHAR(50) NULL,
     -- 容器类型
     DefaultVolumeMl DECIMAL(5,2) NULL,
     -- 建议采样量 (mL)
     PreTreatment NVARCHAR(200) NULL,
     -- 预处理要求
     Status BIT NOT NULL DEFAULT 1,
     -- 1=Active,0=Inactive
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 最近更新时间（应用层维护）
);
GO

/* ---------- 表说明 ---------- */
EXEC sys.sp_addextendedproperty 
     @name   = N'MS_Description',
     @value  = N'标本类型字典表，用于维护样本类型基础信息并在 LIS、HIS、仪器之间保持一致',
     @level0type = N'SCHEMA', @level0name = N'dbo',
     @level1type = N'TABLE',  @level1name = N'DictSampleType';
GO

/* ---------- 字段说明 ---------- */
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'SampleTypeId';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务唯一编码（如 SR01 = Serum）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'SampleTypeCode';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'中文名称（血清、全血等）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'SampleTypeName';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'中文名称对应拼音或首字母，方便检索',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'PinYin';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'英文名称',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'EnglishName';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'采样管帽颜色（HEX 码，例 FF0000 = 红帽）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'ColorHex';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'容器/试管类型',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'ContainerType';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'建议采样量（毫升）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'DefaultVolumeMl';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'预处理要求（如 离心 3000g×10min）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'PreTreatment';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1 = Active (启用)，0 = Inactive (停用)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'Status';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'记录创建时间 (DATETIME, 默认 GETDATE())',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'CreatedAt';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最近更新时间 (DATETIME, 默认 GETDATE())；需在应用层维护',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSampleType',
     @level2type=N'COLUMN',@level2name=N'UpdatedAt';
GO

/* ---------- 推荐索引 ---------- */
CREATE INDEX IX_DictSampleType_Status
    ON dbo.DictSampleType (Status);

CREATE INDEX IX_DictSampleType_PinYin
    ON dbo.DictSampleType (PinYin);
GO

/*==============================================================
  示例数据插入 - DictSampleType
==============================================================*/
-- 插入常见标本类型示例数据
INSERT INTO dbo.DictSampleType
     (SampleTypeCode, SampleTypeName, PinYin, EnglishName, ColorHex, ContainerType, DefaultVolumeMl, PreTreatment)
VALUES
     ('SR01', N'血清', N'XQ', N'Serum', 'FF0000', N'红帽真空管', 5.00, N'离心 3000g×10min'),
     ('PL01', N'血浆', N'XJ', N'Plasma', '8A2BE2', N'紫帽真空管(EDTA)', 3.00, N'离心 2500g×8min'),
     ('WB01', N'全血', N'QX', N'Whole Blood', '8A2BE2', N'紫帽真空管(EDTA)', 2.00, NULL),
     ('UR01', N'尿液', N'NY', N'Urine', 'FFFF00', N'尿杯', 10.00, N'离心 1500g×5min'),
     ('CSF01', N'脑脊液', N'NJY', N'Cerebrospinal Fluid', 'FFFFFF', N'无菌管', 2.00, NULL),
     ('ST01', N'粪便', N'FB', N'Stool', 'A52A2A', N'粪便采集盒', NULL, NULL);
GO

/* ===========================================================
   检验项目类别字典表  DictLabItemCategory
   =========================================================== */
CREATE TABLE dbo.DictLabItemCategory
(
     ItemCategoryId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     ItemCategoryCode NVARCHAR(20) NOT NULL UNIQUE,
     -- 类别编码
     ItemCategoryName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母
     Description NVARCHAR(200) NULL,
     -- 备注
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用,0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 更新时间
);
GO
EXEC sys.sp_addextendedproperty
     @name  = N'MS_Description',
     @value = N'检验项目类别字典表，例如 生化、血液、免疫、微生物等',
     @level0type=N'SCHEMA',@level0name=N'dbo',
     @level1type=N'TABLE', @level1name=N'DictLabItemCategory';
GO
/* 字段说明（示例，仅列出关键字段，其余可按需补充） */
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCategory',
     @level2type=N'COLUMN',@level2name=N'ItemCategoryId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'类别唯一编码，如 BIO、HEM',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCategory',
     @level2type=N'COLUMN',@level2name=N'ItemCategoryCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'中文名称，如 生化',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCategory',
     @level2type=N'COLUMN',@level2name=N'ItemCategoryName';
GO
CREATE INDEX IX_DictLabItemCategory_Status ON dbo.DictLabItemCategory(Status);
CREATE INDEX IX_DictLabItemCategory_PinYin ON dbo.DictLabItemCategory(PinYin);
GO

/*==============================================================
  示例数据插入 - DictLabItemCategory
==============================================================*/
-- 插入常见标本类型示例数据
INSERT INTO dbo.DictLabItemCategory
     (ItemCategoryCode, ItemCategoryName, PinYin, EnglishName, Description)
VALUES
     ('BIO', N'生化检验', N'SHJY', N'Biochemistry', N'常规生化项目，如肝功、肾功、血糖、血脂等'),
     ('IMM', N'免疫检验', N'MYJY', N'Immunology', N'免疫相关项目，如肿瘤标志物、激素、抗体检测等'),
     ('HEM', N'血液学检验', N'XYXJY', N'Hematology', N'血常规、凝血功能等相关检验'),
     ('MIC', N'微生物检验', N'WSWJY', N'Microbiology', N'细菌培养、药敏试验、真菌检测等'),
     ('MOL', N'分子诊断', N'FZZD', N'Molecular Diagnostics', N'基因检测、PCR 等分子生物学检验');
GO

/* ===========================================================
   标本状态字典表  DictSpecimenCondition
   =========================================================== */
/*****************************************************************
** DictSpecimenCondition — 标本质量 / 外观异常字典
** 典型值：正常、溶血、脂血、黄疸、含血、凝块、餐后…
******************************************************************/
CREATE TABLE dbo.DictSpecimenCondition
(
     SpecCondId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,-- 自增主键
     SpecCondCode NVARCHAR(20) NOT NULL UNIQUE,
     -- LIS 编码
     SpecCondName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 助记符/首字母
     SequenceNo INT NULL,
     -- 显示顺序
     Description NVARCHAR(200) NULL,
     -- 备注
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用 0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
);
GO

/* —— 表说明 —— */
EXEC sys.sp_addextendedproperty
  @name  = N'MS_Description',
  @value = N'标本状态字典：正常、溶血、脂血、黄疸等',
  @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE', @level1name=N'DictSpecimenCondition';

/* —— 每列说明 —— */
DECLARE @tbl NVARCHAR(50)=N'DictSpecimenCondition';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'SpecCondId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS 内部编码',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'SpecCondCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'中文名称，如 "标本溶血"',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'SpecCondName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'英文名称，如 Hemolysis / Lipemia',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'EnglishName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'助记符（拼音首字母、条码字符等）',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'PinYin';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'显示/流程顺序号',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'SequenceNo';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'备注说明',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'启用标志 1=启用 0=停用',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建时间',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',
  @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=@tbl,@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE INDEX IX_DictSpecimenCondition_Status ON dbo.DictSpecimenCondition(Status);
CREATE INDEX IX_DictSpecimenCondition_PinYin ON dbo.DictSpecimenCondition(PinYin);
GO

/*==============================================================
  示例数据插入 - DictSpecimenCondition
==============================================================*/
INSERT INTO dbo.DictSpecimenCondition
     (SpecCondCode, SpecCondName, EnglishName, PinYin, SequenceNo, Description)
VALUES
     ('NORMAL', N'正常', 'Normal', 'ZC', 1, N'标本外观正常，无异常'),
     ('HEMOLYSIS', N'溶血', 'Hemolysis', 'RX', 2, N'标本出现溶血现象'),
     ('LIPEMIA', N'脂血', 'Lipemia', 'ZX', 3, N'标本呈乳白色，脂质含量高'),
     ('ICTERUS', N'黄疸', 'Icterus', 'HD', 4, N'标本呈黄色，胆红素含量高'),
     ('CLOT', N'凝块', 'Clot', 'NK', 5, N'标本中有凝血块'),
     ('INSUFFICIENT', N'标本量不足', 'Insufficient Volume', 'BBLBZ', 6, N'采集的标本量不够检测要求');
GO

/* ===========================================================
   方法学类别字典表  DictMethodCategory
   =========================================================== */
CREATE TABLE dbo.DictMethodCategory
(
     MethodCategoryId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     MethodCategoryCode NVARCHAR(20) NOT NULL UNIQUE,
     -- 类别编码
     MethodCategoryName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母
     Description NVARCHAR(200) NULL,
     -- 备注
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用,0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 更新时间
);
GO
EXEC sys.sp_addextendedproperty
     @name  = N'MS_Description',
     @value = N'方法学类别字典表，例如 生化法、免疫法、分子法等',
     @level0type=N'SCHEMA',@level0name=N'dbo',
     @level1type=N'TABLE', @level1name=N'DictMethodCategory';
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethodCategory',
     @level2type=N'COLUMN',@level2name=N'MethodCategoryId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'方法学类别编码，如 BIO、IMM、MOL',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethodCategory',
     @level2type=N'COLUMN',@level2name=N'MethodCategoryCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'中文名称，如 免疫法',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethodCategory',
     @level2type=N'COLUMN',@level2name=N'MethodCategoryName';
GO
CREATE INDEX IX_DictMethodCategory_Status ON dbo.DictMethodCategory(Status);
CREATE INDEX IX_DictMethodCategory_PinYin ON dbo.DictMethodCategory(PinYin);
GO

INSERT dbo.DictMethodCategory
     (MethodCategoryCode, MethodCategoryName, EnglishName, PinYin, Description)
VALUES
     ('BIO', N'生化法', 'Biochemical', 'SHF', N'干式/湿式生化分析方法'),
     ('IMM', N'免疫法', 'Immunological', 'MMF', N'化学发光、ELISA 等免疫学检测方法'),
     ('MOL', N'分子法', 'Molecular', 'FZF', N'PCR、测序等分子生物学方法');
GO

/* ===========================================================
   检验方法学字典表  DictMethod
   =========================================================== */
CREATE TABLE dbo.DictMethod
(
     MethodId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     MethodCode NVARCHAR(20) NOT NULL UNIQUE,
     -- 方法编码 (CLIA、PCR)
     MethodName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母
     MethodCategoryId INT NOT NULL,
     -- 方法学类别ID
     Description NVARCHAR(200) NULL,
     -- 备注
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用,0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 更新时间
     CONSTRAINT FK_DictMethod_Category
        FOREIGN KEY (MethodCategoryId)
        REFERENCES dbo.DictMethodCategory(MethodCategoryId)
);
GO
EXEC sys.sp_addextendedproperty
     @name  = N'MS_Description',
     @value = N'检验方法学字典表，记录具体方法（如 化学发光免疫分析、PCR）并归类到方法学类别',
     @level0type=N'SCHEMA',@level0name=N'dbo',
     @level1type=N'TABLE', @level1name=N'DictMethod';
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethod',
     @level2type=N'COLUMN',@level2name=N'MethodId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'方法学编码，如 CLIA、PCR',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethod',
     @level2type=N'COLUMN',@level2name=N'MethodCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'方法中文名称，如 化学发光免疫分析',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethod',
     @level2type=N'COLUMN',@level2name=N'MethodName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'方法学类别ID (FK)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictMethod',
     @level2type=N'COLUMN',@level2name=N'MethodCategoryId';
GO
CREATE INDEX IX_DictMethod_Status ON dbo.DictMethod(Status);
CREATE INDEX IX_DictMethod_PinYin ON dbo.DictMethod(PinYin);
CREATE INDEX IX_DictMethod_MethodCategory ON dbo.DictMethod(MethodCategoryId);
GO

INSERT dbo.DictMethod
     (MethodCode, MethodName, EnglishName, PinYin, MethodCategoryId, Description)
     SELECT 'Enz', N'酶速率法', 'Enzymatic Rate', 'MSLF', MethodCategoryId, N'计速法测定酶活'
     FROM dbo.DictMethodCategory
     WHERE MethodCategoryCode = 'BIO'
UNION ALL
     SELECT 'CLIA', N'化学发光免疫分析', 'Chemiluminescent Immunoassay', 'HXFG', MethodCategoryId, N'全自动化学发光'
     FROM dbo.DictMethodCategory
     WHERE MethodCategoryCode = 'IMM'
UNION ALL
     SELECT 'ELISA', N'酶联免疫吸附测定', 'Enzyme-linked Immunosorbent Assay', 'ELISA', MethodCategoryId, N'板式/条式 ELISA'
     FROM dbo.DictMethodCategory
     WHERE MethodCategoryCode = 'IMM'
UNION ALL
     SELECT 'PCR', N'聚合酶链式反应', 'Polymerase Chain Reaction', 'PCR', MethodCategoryId, N'荧光定量 PCR'
     FROM dbo.DictMethodCategory
     WHERE MethodCategoryCode = 'MOL'
UNION ALL
     SELECT 'NGS', N'高通量测序', 'Next-Generation Sequencing', 'NGS', MethodCategoryId, N'Illumina / Ion 平台测序'
     FROM dbo.DictMethodCategory
     WHERE MethodCategoryCode = 'MOL';
GO

/* ===========================================================
   检验项目字典表  DictLabItem
   =========================================================== */
CREATE TABLE dbo.DictLabItem
(
     LabItemId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     LabItemCode NVARCHAR(30) NOT NULL UNIQUE,
     -- 项目编码
     LabItemName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母
     ReportName NVARCHAR(100) NULL,
     -- 报告显示名称
     ItemCategoryId INT NOT NULL,
     -- 项目类别ID
     SampleTypeId INT NOT NULL,
     -- 默认标本类型ID
     DefaultUnit NVARCHAR(20) NULL,
     -- 默认单位
     Precision TINYINT NULL,
     -- 默认小数位
     DefaultMethodId INT NULL,
     -- 默认方法学ID
     LoincCode NVARCHAR(20) NULL,
     -- LOINC 码
     IsCalculated BIT NOT NULL DEFAULT 0,
     -- 是否为计算项目 (1=是, 0=否)
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用,0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 更新时间

     /* 外键 */
     CONSTRAINT FK_LabItem_Category
        FOREIGN KEY (ItemCategoryId)
        REFERENCES dbo.DictLabItemCategory(ItemCategoryId),

     CONSTRAINT FK_LabItem_SampleType
        FOREIGN KEY (SampleTypeId)
        REFERENCES dbo.DictSampleType(SampleTypeId),

     CONSTRAINT FK_LabItem_Method
        FOREIGN KEY (DefaultMethodId)
        REFERENCES dbo.DictMethod(MethodId)
);
GO

/* ---------- 表说明 ---------- */
EXEC sys.sp_addextendedproperty
     @name  = N'MS_Description',
     @value = N'检验项目字典表：统一管理项目编码、名称、类别、标本、方法学、LOINC 等信息',
     @level0type=N'SCHEMA',@level0name=N'dbo',
     @level1type=N'TABLE', @level1name=N'DictLabItem';
GO

/* ---------- 关键字段说明 ---------- */
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'LabItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'项目唯一编码（院内统一）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'LabItemCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'项目中文名称',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'LabItemName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验项目类别ID (FK)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'ItemCategoryId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'默认标本类型ID (FK)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'SampleTypeId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'默认方法学ID (FK)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'DefaultMethodId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LOINC 国际统一编码',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'LoincCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'报告显示名称',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'ReportName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'是否为计算项目 (1=是, 0=否)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItem',
     @level2type=N'COLUMN',@level2name=N'IsCalculated';
GO

/* ---------- 常用索引 ---------- */
CREATE INDEX IX_DictLabItem_Status   ON dbo.DictLabItem(Status);
CREATE INDEX IX_DictLabItem_PinYin   ON dbo.DictLabItem(PinYin);
CREATE INDEX IX_DictLabItem_Category ON dbo.DictLabItem(ItemCategoryId);
CREATE INDEX IX_DictLabItem_SampleType ON dbo.DictLabItem(SampleTypeId);
CREATE INDEX IX_DictLabItem_Method ON dbo.DictLabItem(DefaultMethodId);
CREATE INDEX IX_DictLabItem_IsCalculated ON dbo.DictLabItem(IsCalculated);
GO

INSERT dbo.DictLabItem
     (LabItemCode, LabItemName, EnglishName, PinYin,
     ItemCategoryId, SampleTypeId, DefaultUnit, Precision,
     DefaultMethodId, LoincCode)
     SELECT 'BIO_ALT', N'谷丙转氨酶', 'ALT', 'GBZGA',
          c.ItemCategoryId, s.SampleTypeId, 'U/L', 0, m.MethodId, '1742-6'
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s, dbo.DictMethod m
     WHERE c.ItemCategoryCode = 'BIO' AND s.SampleTypeCode = 'SR01' AND m.MethodCode = 'Enz'
UNION ALL
     SELECT 'HEM_HGB', N'血红蛋白', 'HGB', 'XHDB',
          c.ItemCategoryId, s.SampleTypeId, 'g/L', 1, NULL, '718-7'
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s
     WHERE c.ItemCategoryCode = 'HEM' AND s.SampleTypeCode = 'WB01'
UNION ALL
     SELECT 'IMM_TSH', N'促甲状腺激素', 'TSH', 'CJZXJS',
          c.ItemCategoryId, s.SampleTypeId, 'mIU/L', 2, m.MethodId, '3016-3'
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s, dbo.DictMethod m
     WHERE c.ItemCategoryCode = 'IMM' AND s.SampleTypeCode = 'SR01' AND m.MethodCode = 'CLIA';
GO


/*==============================================================
  表名： LabInstrItemMap —— 仪器-项目映射表（Pascal 命名）
  作用： 建立"仪器项目 ↔ LIS 项目"一一对应关系，
         解析器解析结果时用 InstrItemCode 找到 LisItemCode，
         便于不同仪器共用统一项目字典。
==============================================================*/
IF OBJECT_ID('dbo.LabInstrItemMap','U') IS NOT NULL
    DROP TABLE dbo.LabInstrItemMap;
GO

CREATE TABLE dbo.LabInstrItemMap
(
     MapId BIGINT NOT NULL IDENTITY(1,1),
     InstrumentId INT NOT NULL,
     InstrItemCode NVARCHAR(30) NOT NULL,
     InstrItemName NVARCHAR(100) NOT NULL,
     LabItemId INT NOT NULL,
     LisItemName NVARCHAR(100) NULL,
     ResultFormat CHAR(3) NOT NULL CHECK (ResultFormat IN ('NUM','TXT','NEG')),
     Decimals TINYINT NULL,
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),

     CONSTRAINT PK_LabInstrItemMap PRIMARY KEY (MapId),
     CONSTRAINT UQ_LabItemMap UNIQUE (InstrumentId, InstrItemCode),
     CONSTRAINT FK_LabItemMap_Instrument FOREIGN KEY (InstrumentId) REFERENCES dbo.LabInstrument(InstrumentId),
     CONSTRAINT FK_LabItemMap_LabItem FOREIGN KEY (LabItemId) REFERENCES dbo.DictLabItem(LabItemId)
);
GO

/*—— 索引：常按 LabItemId 聚合统计 ——*/
CREATE INDEX IX_LabItemMap_LabItemId ON dbo.LabInstrItemMap(LabItemId);
GO


/*==============================================================
  中文字段注释（Extended Properties）
==============================================================*/
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器项目映射表：连接仪器项目与 LIS 统一项目字典',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'主键 ID（IDENTITY）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'MapId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器 ID（LabInstrument.InstrumentId）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'InstrumentId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器项目代码（Raw 报文中的项目标识）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'InstrItemCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器项目名称',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'InstrItemName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS 统一项目ID（内部字典）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'LabItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS项目名称（冗余存储，优化UI查询）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'LisItemName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'结果格式：NUM=数值 TXT=文本 NEG=阴阳性',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'ResultFormat';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'数值结果保留的小数位数（仅 NUM 时有效）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrItemMap',@level2type=N'COLUMN',@level2name=N'Decimals';
GO

/* ===========================================================
   检验项目参考区间表  DictLabItemRefRange
   说明：按项目、方法学、性别、年龄段、时间段维护参考值上下限
   =========================================================== */
CREATE TABLE dbo.DictLabItemRefRange
(
     RefRangeId BIGINT IDENTITY(1,1) PRIMARY KEY,
     -- 主键
     LabItemId INT NOT NULL,
     -- 检验项目ID
     MethodId INT NULL,
     -- 方法学ID (NULL=通用)
     Sex NVARCHAR(2) NULL,
     -- M / F / N
     AgeLower DECIMAL(5,2) NULL,
     -- 年龄下限 (岁)
     AgeUpper DECIMAL(5,2) NULL,
     -- 年龄上限 (岁)
     AgeUnit NVARCHAR(2) NOT NULL DEFAULT 'Y',
     -- 岁, 月, 周, 天
     LowerLimit NVARCHAR(20) NULL,
     -- 参考下限
     UpperLimit NVARCHAR(20) NULL,
     -- 参考上限
     TextualRefValue NVARCHAR(100) NULL,
     -- 文本型参考值（如"阴性"、"正常"等）
     AlertLow NVARCHAR(20) NULL,
     -- 警告下限
     AlertHigh NVARCHAR(20) NULL,
     -- 警告上限
     CriticalLow NVARCHAR(20) NULL,
     -- 危急下限
     CriticalHigh NVARCHAR(20) NULL,
     -- 危急上限
     Unit NVARCHAR(20) NULL,
     -- 单位
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用,0=停用
     EffectiveFrom DATETIME NOT NULL DEFAULT GETDATE(),
     -- 生效时间
     EffectiveTo DATETIME NULL,
     -- 失效时间
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 更新时间

     /* 外键 */
     CONSTRAINT FK_RefRange_LabItem
        FOREIGN KEY (LabItemId)
        REFERENCES dbo.DictLabItem (LabItemId),

     CONSTRAINT FK_RefRange_Method
        FOREIGN KEY (MethodId)
        REFERENCES dbo.DictMethod (MethodId)
);
GO

/* ---------- 表说明 ---------- */
EXEC sys.sp_addextendedproperty
     @name  = N'MS_Description',
     @value = N'检验项目参考区间表：按项目、方法学、性别、年龄段、时间段维护参考值上下限',
     @level0type=N'SCHEMA',@level0name=N'dbo',
     @level1type=N'TABLE', @level1name=N'DictLabItemRefRange';
GO

/* ---------- 关键字段说明 ---------- */
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验项目ID (FK→DictLabItem)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'LabItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'方法学ID (FK)，NULL=通用',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'MethodId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'性别：M 男；F 女；N 不区分',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'Sex';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'年龄下限 (岁)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'AgeLower';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'年龄上限 (岁)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'AgeUpper';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'年龄单位：岁, 月, 周, 天',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'AgeUnit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'参考值下限',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'LowerLimit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'参考值上限',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'UpperLimit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'文本型参考值（如"阴性"、"正常"等）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'TextualRefValue';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'警告下限',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'AlertLow';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'警告上限',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'AlertHigh';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'危急下限',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'CriticalLow';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'危急上限',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'CriticalHigh';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'单位',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'Unit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态：1=启用，0=停用',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'生效时间',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'EffectiveFrom';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'失效时间 (NULL=永久有效)',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemRefRange',
     @level2type=N'COLUMN',@level2name=N'EffectiveTo';
GO

/* ---------- 索引：加速区间匹配 ---------- */
CREATE INDEX IX_RefRange_Match
ON dbo.DictLabItemRefRange
    (LabItemId, MethodId, Sex, EffectiveFrom DESC, EffectiveTo);
GO

/* ===========================================================
   插入参考区间示例数据
   =========================================================== */

/* 添加Color方法学 */
IF NOT EXISTS(SELECT 1
FROM dbo.DictMethod
WHERE MethodCode = 'Color')
BEGIN
     INSERT INTO dbo.DictMethod
          (MethodCode, MethodName, EnglishName, PinYin, MethodCategoryId, Description)
     SELECT 'Color', N'比色法', 'Colorimetric', 'BSF', MethodCategoryId, N'化学比色分析法'
     FROM dbo.DictMethodCategory
     WHERE MethodCategoryCode = 'BIO';
END
GO

/* ALT — 酶速率法专用区间（成人通用） */
INSERT dbo.DictLabItemRefRange
     (LabItemId, MethodId, Sex, AgeLower, AgeUpper,
     LowerLimit, UpperLimit, Unit)
SELECT l.LabItemId, m.MethodId, 'N', 0, 150, '7', '40', 'U/L'
FROM dbo.DictLabItem l, dbo.DictMethod m
WHERE l.LabItemCode = 'BIO_ALT' AND m.MethodCode = 'Enz';

/* ALT — 比色法专用区间（成人通用） */
INSERT dbo.DictLabItemRefRange
     (LabItemId, MethodId, Sex, AgeLower, AgeUpper,
     LowerLimit, UpperLimit, Unit)
SELECT l.LabItemId, m.MethodId, 'N', 0, 150, '5', '35', 'U/L'
FROM dbo.DictLabItem l, dbo.DictMethod m
WHERE l.LabItemCode = 'BIO_ALT' AND m.MethodCode = 'Color';

/* HGB — 男女通用方法（方法ID NULL 表示通用） */
INSERT dbo.DictLabItemRefRange
     (LabItemId, MethodId, Sex, AgeLower, AgeUpper,
     LowerLimit, UpperLimit, Unit)
     SELECT l.LabItemId, NULL, 'M', 18, 60, '130', '175', 'g/L'
     FROM dbo.DictLabItem l
     WHERE l.LabItemCode = 'HEM_HGB'
UNION ALL
     SELECT l.LabItemId, NULL, 'F', 18, 60, '115', '150', 'g/L'
     FROM dbo.DictLabItem l
     WHERE l.LabItemCode = 'HEM_HGB';
GO

/*==============================================================
  表名： DictSpecimenParam —— 标本参数字典表
  作用： 标准化管理标本参数（如HCT、HIL指数等），为系统提供统一的参数定义
  说明： 支持中英文名称、拼音检索，便于仪器接口和质量控制
==============================================================*/
CREATE TABLE dbo.DictSpecimenParam
(
     ParamId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     ParamCode NVARCHAR(20) NOT NULL UNIQUE,
     -- 参数唯一编码（如 HCT、HIL_H、HIL_I、HIL_L）
     ParamName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母，用于快速检索
     ParamType TINYINT NOT NULL DEFAULT 0,
     -- 参数类型：0=常规参数 1=质量指数 2=其他
     Unit NVARCHAR(20) NULL,
     -- 单位（如 %、Index等）
     Description NVARCHAR(200) NULL,
     -- 参数描述和说明
     Status BIT NOT NULL DEFAULT 1,
     -- 状态：1=启用 0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 更新时间
);
GO

/* ---------- 表说明 ---------- */
EXEC sys.sp_addextendedproperty 
     @name   = N'MS_Description',
     @value  = N'标本参数字典表：标准化管理标本参数（如HCT、HIL指数等），为系统提供统一的参数定义',
     @level0type = N'SCHEMA', @level0name = N'dbo',
     @level1type = N'TABLE',  @level1name = N'DictSpecimenParam';
GO

/* ---------- 字段说明 ---------- */
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'ParamId';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数唯一编码（如 HCT、HIL_H、HIL_I、HIL_L）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'ParamCode';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数中文名称',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'ParamName';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数英文名称',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'EnglishName';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'拼音/首字母，用于快速检索',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'PinYin';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数类型：0=常规参数 1=质量指数 2=其他',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'ParamType';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数单位（如 %、Index等）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'Unit';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'参数描述和说明',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'Description';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1=启用 0=停用',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'Status';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'CreatedAt';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictSpecimenParam',
     @level2type=N'COLUMN',@level2name=N'UpdatedAt';
GO

/* ---------- 推荐索引 ---------- */
CREATE INDEX IX_DictSpecimenParam_Status ON dbo.DictSpecimenParam(Status);
CREATE INDEX IX_DictSpecimenParam_PinYin ON dbo.DictSpecimenParam(PinYin);
CREATE INDEX IX_DictSpecimenParam_ParamType ON dbo.DictSpecimenParam(ParamType);
GO

/*==============================================================
  示例数据插入 - DictSpecimenParam
==============================================================*/
INSERT INTO dbo.DictSpecimenParam
     (ParamCode, ParamName, EnglishName, PinYin, ParamType, Unit, Description)
VALUES
     ('HCT', N'红细胞压积', N'Hematocrit', N'HXBYJF', 0, N'%', N'血液中红细胞所占体积百分比'),
     ('HIL_H', N'溶血指数', N'Hemolysis Index', N'RXZS', 1, N'Index', N'标本溶血程度指数'),
     ('HIL_I', N'黄疸指数', N'Icterus Index', N'HDZS', 1, N'Index', N'标本黄疸程度指数'),
     ('HIL_L', N'脂血指数', N'Lipemia Index', N'ZXZS', 1, N'Index', N'标本脂血程度指数'),
     ('TEMP', N'温度', N'Temperature', N'WD', 0, N'°C', N'标本或仪器温度参数'),
     ('PH', N'酸碱度', N'pH Value', N'SGD', 0, N'pH', N'标本pH值参数');
GO

/*==============================================================
  表名： LabInstrSpecimenParamMap —— 仪器标本参数映射表
  作用： 建立仪器原始参数代码与LIS标准参数字典的映射关系
  说明： 解决不同厂商仪器对同一参数使用不同编码的问题，支持数据解析和标准化
==============================================================*/
CREATE TABLE dbo.LabInstrSpecimenParamMap
(
     MapId BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     InstrumentId INT NOT NULL,
     -- 仪器ID（外键）
     InstrParamCode NVARCHAR(30) NOT NULL,
     -- 仪器端参数代码（如 0x21、HCT等）
     InstrParamName NVARCHAR(100) NULL,
     -- 仪器端参数名称（可选，便于维护）
     ParamId INT NOT NULL,
     -- LIS标准参数ID（外键）
     ConversionFactor DECIMAL(10,4) NULL DEFAULT 1.0,
     -- 转换系数（仪器值 × 系数 = 标准值）
     ConversionOffset DECIMAL(10,4) NULL DEFAULT 0.0,
     -- 转换偏移量（转换后 + 偏移量）
     Status BIT NOT NULL DEFAULT 1,
     -- 状态：1=启用 0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 更新时间

     /* 外键约束 */
     CONSTRAINT FK_LabInstrSpecimenParamMap_Instrument
        FOREIGN KEY (InstrumentId)
        REFERENCES dbo.LabInstrument(InstrumentId),

     CONSTRAINT FK_LabInstrSpecimenParamMap_Param
        FOREIGN KEY (ParamId)
        REFERENCES dbo.DictSpecimenParam(ParamId),

     /* 唯一约束：同一仪器的同一参数代码只能映射到一个LIS参数 */
     CONSTRAINT UQ_LabInstrSpecimenParamMap 
        UNIQUE (InstrumentId, InstrParamCode)
);
GO

/* ---------- 表说明 ---------- */
EXEC sys.sp_addextendedproperty 
     @name   = N'MS_Description',
     @value  = N'仪器标本参数映射表：建立仪器原始参数代码与LIS标准参数字典的映射关系',
     @level0type = N'SCHEMA', @level0name = N'dbo',
     @level1type = N'TABLE',  @level1name = N'LabInstrSpecimenParamMap';
GO

/* ---------- 字段说明 ---------- */
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自增主键',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'MapId';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仪器ID（外键关联LabInstrument表）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'InstrumentId';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仪器端参数代码（如 0x21、HCT等）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'InstrParamCode';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仪器端参数名称（可选，便于维护）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'InstrParamName';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'LIS标准参数ID（外键关联DictSpecimenParam表）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'ParamId';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'转换系数（仪器值 × 系数 = 标准值）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'ConversionFactor';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'转换偏移量（转换后 + 偏移量）',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'ConversionOffset';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1=启用 0=停用',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'Status';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'创建时间',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'CreatedAt';

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新时间',
     @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabInstrSpecimenParamMap',
     @level2type=N'COLUMN',@level2name=N'UpdatedAt';
GO

/* ---------- 推荐索引 ---------- */
CREATE INDEX IX_LabInstrSpecimenParamMap_Status ON dbo.LabInstrSpecimenParamMap(Status);
CREATE INDEX IX_LabInstrSpecimenParamMap_ParamId ON dbo.LabInstrSpecimenParamMap(ParamId);
GO

/*==============================================================
  示例数据插入 - LabInstrSpecimenParamMap
==============================================================*/
-- 为CA-1500CA仪器配置标本参数映射
INSERT INTO dbo.LabInstrSpecimenParamMap
     (InstrumentId, InstrParamCode, InstrParamName, ParamId, ConversionFactor, ConversionOffset)
     SELECT i.InstrumentId, '0x21', N'红细胞压积', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'CA-1500CA' AND p.ParamCode = 'HCT'
UNION ALL
     SELECT i.InstrumentId, '0x22', N'溶血指数', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'CA-1500CA' AND p.ParamCode = 'HIL_H'
UNION ALL
     SELECT i.InstrumentId, '0x23', N'黄疸指数', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'CA-1500CA' AND p.ParamCode = 'HIL_I'
UNION ALL
     SELECT i.InstrumentId, '0x24', N'脂血指数', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'CA-1500CA' AND p.ParamCode = 'HIL_L';

-- 为COBAS8000仪器配置标本参数映射（使用字符串代码）
INSERT INTO dbo.LabInstrSpecimenParamMap
     (InstrumentId, InstrParamCode, InstrParamName, ParamId, ConversionFactor, ConversionOffset)
     SELECT i.InstrumentId, 'HCT', N'Hematocrit', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'COBAS8000' AND p.ParamCode = 'HCT'
UNION ALL
     SELECT i.InstrumentId, 'HIL_H', N'Hemolysis Index', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'COBAS8000' AND p.ParamCode = 'HIL_H'
UNION ALL
     SELECT i.InstrumentId, 'HIL_I', N'Icterus Index', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'COBAS8000' AND p.ParamCode = 'HIL_I'
UNION ALL
     SELECT i.InstrumentId, 'HIL_L', N'Lipemia Index', p.ParamId, 1.0, 0.0
     FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
     WHERE i.InstrCode = 'COBAS8000' AND p.ParamCode = 'HIL_L';
GO

/*=============================================================
  12. DictVisitType  — 就诊类型字典
=============================================================*/
CREATE TABLE dbo.DictVisitType
(
     VisitTypeId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     VisitTypeCode NVARCHAR(20) NOT NULL UNIQUE,
     -- LIS 编码
     VisitTypeName NVARCHAR(100) NOT NULL,
     -- 中文名称
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     HisVisitTypeCode NVARCHAR(200) NULL,
     -- HIS 编码
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母
     Description NVARCHAR(200) NULL,
     -- 备注
     Status BIT NOT NULL DEFAULT 1,
     -- 1=启用,0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 最近更新时间
);
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'就诊类型字典表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictVisitType';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'VisitTypeId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS 内部就诊类型编码',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'VisitTypeCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'中文名称（门诊/住院/急诊…）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'VisitTypeName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'英文名称',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'EnglishName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'HIS 就诊类型编码（NVARCHAR(200)）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'HisVisitTypeCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'拼音或首字母，用于快速检索',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'PinYin';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'备注说明',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态 1=启用 0=停用',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间，需在应用层维护',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictVisitType',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE INDEX IX_DictVisitType_Status ON dbo.DictVisitType(Status);
GO

/*=============================================================
  13. DictDepartment — 科室字典
=============================================================*/
CREATE TABLE dbo.DictDepartment
(
     DeptId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     DeptCode NVARCHAR(20) NOT NULL UNIQUE,
     -- LIS 科室码
     DeptName NVARCHAR(100) NOT NULL,
     -- 科室中文
     HisDeptCode NVARCHAR(200) NULL,
     -- HIS 编码
     PinYin NVARCHAR(100) NULL,
     -- 拼音
     ParentDeptId INT NULL,
     -- 上级科室ID
     DeptType NVARCHAR(20) NULL,
     -- 类别(临床/医技…)
     Description NVARCHAR(200) NULL,
     Status BIT NOT NULL DEFAULT 1,
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     CONSTRAINT FK_Department_Parent
        FOREIGN KEY (ParentDeptId) REFERENCES dbo.DictDepartment(DeptId)
);
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'科室字典表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictDepartment';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'DeptId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS 科室编码',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'DeptCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'科室中文名称',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'DeptName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'HIS 科室编码（nvarchar(200)）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'HisDeptCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'拼音/首字母',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'PinYin';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'上级科室ID (递归)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'ParentDeptId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'科室类型（临床/医技/行政）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'DeptType';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'备注',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态 1=启用 0=停用',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDepartment',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE INDEX IX_DictDepartment_Status ON dbo.DictDepartment(Status);
GO

/*=============================================================
  14. DictDoctor — 医生字典
=============================================================*/
CREATE TABLE dbo.DictDoctor
(
     DoctorId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 自增主键
     DoctorCode NVARCHAR(20) NOT NULL UNIQUE,
     -- LIS 工号
     DoctorName NVARCHAR(100) NOT NULL,
     -- 姓名
     HisDoctorCode NVARCHAR(200) NULL,
     -- HIS 编码
     PinYin NVARCHAR(100) NULL,
     -- 拼音
     DeptId INT NOT NULL,
     -- 所属科室ID
     Title NVARCHAR(50) NULL,
     -- 职称
     LicenseNo NVARCHAR(50) NULL,
     -- 执业证书号
     Mobile NVARCHAR(20) NULL,
     -- 电话
     Status BIT NOT NULL DEFAULT 1,
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     CONSTRAINT FK_Doctor_Department
        FOREIGN KEY (DeptId) REFERENCES dbo.DictDepartment(DeptId)
);
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'医生/检验人员字典表',@level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictDoctor';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'DoctorId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS 医生编码/工号',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'DoctorCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'中文姓名',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'DoctorName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'HIS 医生编码（nvarchar(200)）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'HisDoctorCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'拼音/首字母',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'PinYin';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'所属科室ID (FK)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'DeptId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'职称',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'Title';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'执业证书号',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'LicenseNo';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'联系方式',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'Mobile';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态 1=启用 0=停用',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictDoctor',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE INDEX IX_DictDoctor_Status ON dbo.DictDoctor(Status);
CREATE INDEX IX_DictDoctor_DeptId ON dbo.DictDoctor(DeptId);
GO

/*=============================================================
  23. SysUser — 系统用户表
=============================================================*/
CREATE TABLE dbo.SysUser
(
     UserId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 用户ID，自增主键
     LoginName NVARCHAR(30) NOT NULL UNIQUE,
     -- 登录账号，系统内唯一
     PasswordHash NVARCHAR(255) NOT NULL,
     -- 密码哈希值（严禁明文存储密码，必须使用强哈希算法如Argon2, bcrypt）
     UserName NVARCHAR(100) NOT NULL,
     -- 用户真实姓名
     DoctorId INT NULL,
     -- 关联的医生/技师ID，便于获取职称、科室等信息，实现单点登录或信息同步
     RoleCode CHAR(2) NULL,
     -- 角色编码，关联角色权限系统
     IsLockedOut BIT NOT NULL DEFAULT 0,
     -- 是否被锁定，因密码错误次数过多等原因被锁定
     LastLoginAt DATETIME NULL,
     -- 最后登录时间
     Status BIT NOT NULL DEFAULT 1,
     -- 状态：1=启用 0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 更新时间
     CreatedBy INT NULL,
     -- 创建此用户的操作员ID
     UpdatedBy INT NULL,
     -- 最后更新此用户的操作员ID
     CONSTRAINT FK_SysUser_Doctor FOREIGN KEY (DoctorId) REFERENCES dbo.DictDoctor(DoctorId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'系统用户表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'SysUser';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'用户ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'UserId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'登录账号，系统内唯一',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'LoginName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'密码哈希值（严禁明文存储密码，必须使用强哈希算法如Argon2, bcrypt）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'PasswordHash';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'用户真实姓名',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'UserName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的医生/技师ID，便于获取职称、科室等信息，实现单点登录或信息同步',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'DoctorId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'角色编码，关联角色权限系统',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'RoleCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'是否被锁定，因密码错误次数过多等原因被锁定',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'IsLockedOut';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最后登录时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'LastLoginAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态：1=启用 0=停用',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'UpdatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建此用户的操作员ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'CreatedBy';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最后更新此用户的操作员ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'SysUser',@level2type=N'COLUMN',@level2name=N'UpdatedBy';

CREATE INDEX IX_SysUser_Status ON dbo.SysUser(Status);
CREATE INDEX IX_SysUser_DoctorId ON dbo.SysUser(DoctorId);
CREATE INDEX IX_SysUser_RoleCode ON dbo.SysUser(RoleCode);
CREATE INDEX IX_SysUser_IsLockedOut ON dbo.SysUser(IsLockedOut);
GO

/*=============================================================
  示例数据插入
=============================================================*/

-- 插入就诊类型示例数据
INSERT INTO dbo.DictVisitType
     (VisitTypeCode, VisitTypeName, EnglishName, HisVisitTypeCode, PinYin, Description)
VALUES
     ('OP', N'门诊', N'Outpatient', 'OP001', 'MZ', N'门诊患者'),
     ('IP', N'住院', N'Inpatient', 'IP001', 'ZY', N'住院患者'),
     ('ER', N'急诊', N'Emergency', 'ER001', 'JZ', N'急诊患者'),
     ('HC', N'体检', N'Health Check', 'HC001', 'TJ', N'健康体检');
GO

-- 插入科室示例数据
INSERT INTO dbo.DictDepartment
     (DeptCode, DeptName, HisDeptCode, PinYin, DeptType, Description)
VALUES
     ('LAB', N'检验科', 'LAB001', 'JYK', N'医技', N'临床检验科'),
     ('ICU', N'重症医学科', 'ICU001', 'ZSYXK', N'临床', N'重症监护病房'),
     ('CARD', N'心内科', 'CARD001', 'XNK', N'临床', N'心血管内科'),
     ('ORTH', N'骨科', 'ORTH001', 'GK', N'临床', N'骨科'),
     ('ADMIN', N'行政办公室', 'ADMIN001', 'XZBGS', N'行政', N'医院行政管理');
GO

-- 插入医生示例数据
INSERT INTO dbo.DictDoctor
     (DoctorCode, DoctorName, HisDoctorCode, PinYin, DeptId, Title, LicenseNo, Mobile)
     SELECT 'DOC001', N'张三', 'HIS_DOC001', 'ZS', DeptId, N'主任技师', 'LIC001', '13800138001'
     FROM dbo.DictDepartment
     WHERE DeptCode = 'LAB'
UNION ALL
     SELECT 'DOC002', N'李四', 'HIS_DOC002', 'LS', DeptId, N'主任医师', 'LIC002', '13800138002'
     FROM dbo.DictDepartment
     WHERE DeptCode = 'ICU'
UNION ALL
     SELECT 'DOC003', N'王五', 'HIS_DOC003', 'WW', DeptId, N'副主任医师', 'LIC003', '13800138003'
     FROM dbo.DictDepartment
     WHERE DeptCode = 'CARD'
UNION ALL
     SELECT 'DOC004', N'赵六', 'HIS_DOC004', 'ZL', DeptId, N'主治医师', 'LIC004', '13800138004'
     FROM dbo.DictDepartment
     WHERE DeptCode = 'ORTH';
GO


/*=============================================================
  15. LabOrder — 检验申请主表
=============================================================*/
CREATE TABLE dbo.LabOrder
(
     OrderId BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- LIS内部申请单ID，自增主键
     HisOrderCode NVARCHAR(50) NULL,
     -- 对应的HIS系统医嘱号/申请单号，用于系统间对接，手工单可为空
     VisitId NVARCHAR(50) NOT NULL,
     -- 患者就诊ID（门诊号/住院号等）
     PatientId NVARCHAR(50) NOT NULL,
     -- 患者唯一标识（如患者ID/身份证号）
     PatientName NVARCHAR(100) NOT NULL,
     -- 患者姓名（冗余存储，便于查询显示）
     PatientSex CHAR(2) NULL,
     -- 患者性别：M=男, F=女, U=未知。匹配参考范围的核心字段，应在创建时冗余。
     PatientBirthDate DATE NULL,
     -- 患者出生日期：用于精确计算年龄，匹配参考范围的核心字段，应在创建时冗余。
     PatientAge INT NULL,
     -- 患者年龄（冗余字段便于查询）
     PatientAgeUnit CHAR(2),
     -- 年龄单位：岁 月 天
     BedNo NVARCHAR(20) NULL,
     -- 床号：住院患者的关键信息，用于标本采集和报告分发。
     VisitTypeId INT NOT NULL,
     -- 就诊类型ID（门诊/住院/急诊/体检）
     RequestingDeptId INT NOT NULL,
     -- 申请科室ID
     RequestingDoctorId INT NOT NULL,
     -- 申请医生ID
     OrderStatus TINYINT NOT NULL DEFAULT 0 CHECK (OrderStatus IN (0,1,2,3,4,9)),
     -- 申请单状态：0=已开立 1=部分采集 2=全部采集 3=部分完成 4=全部完成 9=已取消
     Priority TINYINT NOT NULL DEFAULT 0 CHECK (Priority IN (0,1)),
     -- 优先级：0=常规(Normal) 1=紧急(STAT)
     ClinicalDiagnosis NVARCHAR(500) NULL,
     -- 临床诊断信息
     ReportComment NVARCHAR(500) NULL,
     -- 报告评价/总结：由审核医生填写的对本次检验的综合性结论或建议，显示在报告单上。
     OrderDateTime DATETIME NOT NULL DEFAULT GETDATE(),
     -- 医嘱开立时间
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录创建时间
     CreatedById INT NULL,
     -- 创建人ID
     CreatedByName NVARCHAR(100) NULL,
     -- 创建人姓名（冗余）
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录更新时间
     UpdatedById INT NULL,
     -- 最后更新人ID
     UpdatedByName NVARCHAR(100) NULL,
     -- 最后更新人姓名（冗余）
     CONSTRAINT FK_Order_VisitType FOREIGN KEY (VisitTypeId) REFERENCES dbo.DictVisitType(VisitTypeId),
     CONSTRAINT FK_Order_Dept FOREIGN KEY (RequestingDeptId) REFERENCES dbo.DictDepartment(DeptId),
     CONSTRAINT FK_Order_Doctor FOREIGN KEY (RequestingDoctorId) REFERENCES dbo.DictDoctor(DoctorId),
     CONSTRAINT FK_LabOrder_CreatedBy FOREIGN KEY (CreatedById) REFERENCES dbo.SysUser(UserId),
     CONSTRAINT FK_LabOrder_UpdatedBy FOREIGN KEY (UpdatedById) REFERENCES dbo.SysUser(UserId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验申请主表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'LabOrder';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS内部申请单ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'OrderId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'对应的HIS系统医嘱号/申请单号，用于系统间对接，手工单可为空',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'HisOrderCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'患者就诊ID（门诊号/住院号等）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'VisitId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'患者唯一标识（如患者ID/身份证号）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'PatientId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'患者姓名（冗余存储，便于查询显示）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'PatientName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'患者性别：M=男, F=女, U=未知。匹配参考范围的核心字段，应在创建时冗余。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'PatientSex';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'患者出生日期：用于精确计算年龄，匹配参考范围的核心字段，应在创建时冗余。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'PatientBirthDate';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'患者年龄（冗余字段便于查询）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'PatientAge';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'年龄单位：岁 月 天',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'PatientAgeUnit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'床号：住院患者的关键信息，用于标本采集和报告分发。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'BedNo';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'就诊类型ID（门诊/住院/急诊/体检）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'VisitTypeId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'申请科室ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'RequestingDeptId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'申请医生ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'RequestingDoctorId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'申请单状态：0=已开立 1=部分采集 2=全部采集 3=部分完成 4=全部完成 9=已取消',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'OrderStatus';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'优先级：0=常规(Normal) 1=紧急(STAT)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'Priority';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'临床诊断信息',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'ClinicalDiagnosis';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'报告评价/总结：由审核医生填写的对本次检验的综合性结论或建议，显示在报告单上。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'ReportComment';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'医嘱开立时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'OrderDateTime';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建人ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'CreatedById';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建人姓名（冗余）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'CreatedByName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'UpdatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最后更新人ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'UpdatedById';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最后更新人姓名（冗余）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrder',@level2type=N'COLUMN',@level2name=N'UpdatedByName';

CREATE INDEX IX_LabOrder_VisitId ON dbo.LabOrder(VisitId);
CREATE INDEX IX_LabOrder_Patient ON dbo.LabOrder(PatientId, PatientName);
CREATE INDEX IX_LabOrder_Status_Time ON dbo.LabOrder(OrderStatus, OrderDateTime);
GO

/*=============================================================
  16. LabSpecimen — 检验标本信息表
=============================================================*/
CREATE TABLE dbo.LabSpecimen
(
     SpecimenId BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- LIS内部标本ID，自增主键
     OrderId BIGINT NOT NULL,
     -- 关联的申请单ID
     Barcode NVARCHAR(30) NOT NULL UNIQUE,
     -- 标本唯一条码
     SeqPrefix NVARCHAR(20) NULL,
     -- 样本号前缀。可选字段。用于标识样本批次（如 'A' 批）、类型（如 'U' 代表尿液）或机架号。
     DailySeqNo INT NULL,
     -- 当日流水号。与 SpecimenDate 和可选的 SeqPrefix 组合，构成完整的样本号。
     SpecimenDate DATE NULL,
     -- 标本日期。与 SeqPrefix 和 DailySeqNo 配合使用，确保复合样本号在特定日期内的唯一性。
     SampleTypeId INT NOT NULL,
     -- 标本类型ID（血清/全血/尿液等）
     SpecimenStatus TINYINT NOT NULL DEFAULT 10 CHECK (SpecimenStatus IN (10,20,30,40,90,99)),
     -- 标本状态：10=待采集 20=已采集 30=已接收 40=检测中/完成 90=已废弃 99=已作废/拒收
     CollectedById INT NULL,
     -- 采集人ID
     CollectedByName NVARCHAR(100) NULL,
     -- 采集人姓名（冗余）
     CollectedAt DATETIME NULL,
     -- 采集时间
     ReceivedById INT NULL,
     -- 接收人ID
     ReceivedByName NVARCHAR(100) NULL,
     -- 接收人姓名（冗余）
     ReceivedAt DATETIME NULL,
     -- 接收时间
     SpecCondId INT NULL,
     -- 标本状态ID（溶血/脂血等）
     RejectReason NVARCHAR(200) NULL,
     -- 拒收原因（当SpecimenStatus=99时填写）
     Remarks NVARCHAR(500) NULL,
     -- 标本备注：用于记录非拒收原因的标本相关信息，如"轻微溶血"、"乳糜血"等。
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录创建时间
     CreatedById INT NULL,
     -- 创建人ID
     CreatedByName NVARCHAR(100) NULL,
     -- 创建人姓名（冗余）
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录更新时间
     UpdatedById INT NULL,
     -- 最后更新人ID
     UpdatedByName NVARCHAR(100) NULL,
     -- 最后更新人姓名（冗余）
     CONSTRAINT FK_Specimen_Order FOREIGN KEY (OrderId) REFERENCES dbo.LabOrder(OrderId),
     CONSTRAINT FK_Specimen_SampleType FOREIGN KEY (SampleTypeId) REFERENCES dbo.DictSampleType(SampleTypeId),
     CONSTRAINT FK_Specimen_Condition FOREIGN KEY (SpecCondId) REFERENCES dbo.DictSpecimenCondition(SpecCondId),
     CONSTRAINT FK_LabSpecimen_CollectedBy FOREIGN KEY (CollectedById) REFERENCES dbo.SysUser(UserId),
     CONSTRAINT FK_LabSpecimen_ReceivedBy FOREIGN KEY (ReceivedById) REFERENCES dbo.SysUser(UserId),
     CONSTRAINT FK_LabSpecimen_CreatedBy FOREIGN KEY (CreatedById) REFERENCES dbo.SysUser(UserId),
     CONSTRAINT FK_LabSpecimen_UpdatedBy FOREIGN KEY (UpdatedById) REFERENCES dbo.SysUser(UserId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验标本信息表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'LabSpecimen';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'LIS内部标本ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'SpecimenId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的申请单ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'OrderId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'标本唯一条码',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'Barcode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'样本号前缀。可选字段。用于标识样本批次（如 ''A'' 批）、类型（如 ''U'' 代表尿液）或机架号。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'SeqPrefix';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'当日流水号。与 SpecimenDate 和可选的 SeqPrefix 组合，构成完整的样本号。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'DailySeqNo';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'标本日期。与 SeqPrefix 和 DailySeqNo 配合使用，确保复合样本号在特定日期内的唯一性。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'SpecimenDate';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'标本类型ID（血清/全血/尿液等）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'SampleTypeId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'标本状态：10=待采集 20=已采集 30=已接收 40=检测中/完成 90=已废弃 99=已作废/拒收',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'SpecimenStatus';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'采集人ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'CollectedById';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'采集人姓名（冗余）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'CollectedByName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'采集时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'CollectedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'接收人ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'ReceivedById';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'接收人姓名（冗余）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'ReceivedByName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'接收时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'ReceivedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'标本状态ID（溶血/脂血等）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'SpecCondId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'拒收原因（当SpecimenStatus=99时填写）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'RejectReason';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'标本备注：用于记录非拒收原因的标本相关信息，如"轻微溶血"、"乳糜血"等。',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'Remarks';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建人ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'CreatedById';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建人姓名（冗余）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'CreatedByName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'UpdatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最后更新人ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'UpdatedById';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最后更新人姓名（冗余）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabSpecimen',@level2type=N'COLUMN',@level2name=N'UpdatedByName';

CREATE UNIQUE INDEX UQ_LabSpecimen_Barcode ON dbo.LabSpecimen(Barcode);
CREATE UNIQUE INDEX UQ_LabSpecimen_DailySeq ON dbo.LabSpecimen(SpecimenDate, SeqPrefix, DailySeqNo)
WHERE SpecimenDate IS NOT NULL AND DailySeqNo IS NOT NULL;
CREATE INDEX IX_LabSpecimen_OrderId ON dbo.LabSpecimen(OrderId);
CREATE INDEX IX_LabSpecimen_Status_Time ON dbo.LabSpecimen(SpecimenStatus, ReceivedAt);
GO

/*=============================================================
  17. LabOrderItem — 检验医嘱明细表
=============================================================*/
CREATE TABLE dbo.LabOrderItem
(
     OrderItemId BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 检验项目明细ID，自增主键
     SpecimenId BIGINT NOT NULL,
     -- 关联的标本ID
     LabItemId INT NOT NULL,
     -- 关联的LIS项目ID
     ProfileId INT NULL,
     -- 关联检验组合ID
     ItemStatus TINYINT NOT NULL DEFAULT 0 CHECK (ItemStatus IN (0,1,2,3,9)),
     -- 项目状态：0=已申请 1=已上机/检测中 2=已出结果 3=已审核 9=已取消
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录创建时间 (UTC)
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录更新时间 (UTC)
     CONSTRAINT FK_OrderItem_Specimen FOREIGN KEY (SpecimenId) REFERENCES dbo.LabSpecimen(SpecimenId),
     CONSTRAINT FK_OrderItem_LabItem FOREIGN KEY (LabItemId) REFERENCES dbo.DictLabItem(LabItemId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验医嘱明细表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'LabOrderItem';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验项目明细ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'OrderItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的标本ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'SpecimenId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的LIS项目ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'LabItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联检验组合ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'ProfileId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'项目状态：0=已申请 1=已上机/检测中 2=已出结果 3=已审核 9=已取消',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'ItemStatus';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间(UTC)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录更新时间(UTC)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabOrderItem',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE INDEX IX_LabOrderItem_Specimen ON dbo.LabOrderItem(SpecimenId);
CREATE INDEX IX_LabOrderItem_LabItem ON dbo.LabOrderItem(LabItemId);
CREATE INDEX IX_LabOrderItem_Profile ON dbo.LabOrderItem(ProfileId);
CREATE INDEX IX_LabOrderItem_Status ON dbo.LabOrderItem(ItemStatus);
GO

/*==============================================================
  18. LabResult  —— 结果主表（优化后的审核流程设计）
  作者： <Your Name>               创建：2025-05-19
  说明： 存放经过审核流程、可对外发布的最终检验结果。
==============================================================*/
CREATE TABLE dbo.LabResult
(
     ResultId BIGINT IDENTITY(1,1) NOT NULL -- 主键
          CONSTRAINT PK_LabResult PRIMARY KEY,

     OrderItemId BIGINT NOT NULL,
     -- FK → LabOrderItem
     InstrumentId INT NOT NULL
          CONSTRAINT FK_LabResult_Instrument
                   REFERENCES dbo.LabInstrument(InstrumentId),

     InstrItemCode NVARCHAR(30) NOT NULL,
     -- 仪器项目代码（冗余）
     ResultValue NVARCHAR(60) NULL,
     -- 结果值
     Unit NVARCHAR(20) NULL,
     -- 单位
     AbnormalFlag CHAR(1) NULL,
     -- H/L/A…
     IsSystemGenerated BIT NOT NULL DEFAULT 0,
     -- 是否系统生成
     MeasuredAt DATETIME NULL,
     -- 仪器测定时间

     /* —— 检验技师信息 —— */
     TestingTechnicianId INT NULL,
     -- 检验技师ID
     TestingTechnicianName NVARCHAR(100) NULL,
     -- 检验技师姓名（冗余）
     TestingCompletedAt DATETIME NULL,
     -- 检验完成时间

     /* —— 审核流程信息 —— */
     CurrentReviewLevel TINYINT NOT NULL DEFAULT 0,
     -- 当前审核级别：0=未审核 1=已初审 2=已复审 3=已三审...
     MaxReviewLevel TINYINT NOT NULL DEFAULT 2,
     -- 需要的最大审核级别（根据项目类型和业务规则确定）
     FinalSignerId INT NULL,
     -- 最终签发医师ID（承担医疗责任的医师）
     FinalSignerName NVARCHAR(100) NULL,
     -- 最终签发医师姓名（冗余存储）
     FinalSignedAt DATETIME NULL,
     -- 最终签发时间
     ReviewStatus TINYINT NOT NULL DEFAULT 0,
     -- 审核状态：0=待审 1=审核中 2=审核完成

     /* —— 外键约束 —— */
     CONSTRAINT FK_LabResult_OrderItem
          FOREIGN KEY (OrderItemId) REFERENCES dbo.LabOrderItem(OrderItemId),
     CONSTRAINT FK_LabResult_TestingTechnician
          FOREIGN KEY (TestingTechnicianId) REFERENCES dbo.DictDoctor(DoctorId),
     CONSTRAINT FK_LabResult_FinalSigner
          FOREIGN KEY (FinalSignerId) REFERENCES dbo.DictDoctor(DoctorId)
);
GO

/*—— 常用索引 ——*/
CREATE INDEX IX_LabResult_OrderItem ON dbo.LabResult(OrderItemId);
CREATE INDEX IX_LabResult_ReviewLevel ON dbo.LabResult(CurrentReviewLevel);
CREATE INDEX IX_LabResult_TestingTechnician ON dbo.LabResult(TestingTechnicianId);
CREATE INDEX IX_LabResult_FinalSigner ON dbo.LabResult(FinalSignerId);
CREATE INDEX IX_LabResult_MeasuredAt ON dbo.LabResult(MeasuredAt DESC);
CREATE INDEX IX_LabResult_ReviewWorkList ON dbo.LabResult(ReviewStatus, CurrentReviewLevel);
GO

/*==============================================================
  Extended Properties —— 为所有字段添加中文注释
==============================================================*/
/* 表级注释 */
EXEC sys.sp_addextendedproperty
 @name  = N'MS_Description',
 @value = N'检验结果主表：保存审核通过的最终结果记录',
 @level0type=N'SCHEMA',@level0name=N'dbo',
 @level1type=N'TABLE', @level1name=N'LabResult';

/* 列级注释 */
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'主键 ID（自动增长）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'ResultId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验医嘱明细 ID（LabOrderItem.OrderItemId）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'OrderItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器 ID（LabInstrument.InstrumentId）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'InstrumentId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器项目代码（冗余存储，便于追溯）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'InstrItemCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'结果值（字符或数字）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'ResultValue';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验结果单位',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'Unit';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'异常标志：H=高 L=低 A=阳性 …',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'AbnormalFlag';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'是否系统生成：0 否 1 是',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'IsSystemGenerated';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'仪器测定时间（DATETIME）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'MeasuredAt';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验技师ID（实际执行检验的人）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'TestingTechnicianId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验技师姓名（冗余存储）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'TestingTechnicianName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'检验完成时间',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'TestingCompletedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'当前审核级别：0=未审核 1=已初审 2=已复审 3=已三审',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'CurrentReviewLevel';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'需要的最大审核级别（根据项目类型和业务规则确定）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'MaxReviewLevel';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最终签发医师ID（承担医疗责任的医师）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'FinalSignerId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最终签发医师姓名（冗余存储）',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'FinalSignerName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最终签发时间',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'FinalSignedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'审核状态：0=待审 1=审核中 2=审核完成',
 @level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResult',@level2type=N'COLUMN',@level2name=N'ReviewStatus';
GO

/*=============================================================
  19. LabResultReview — 检验结果审核记录表
=============================================================*/
CREATE TABLE dbo.LabResultReview
(
     ReviewId BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 审核记录ID，自增主键
     ResultId BIGINT NOT NULL,
     -- 关联的检验结果ID
     ReviewLevel TINYINT NOT NULL,
     -- 审核级别：1=初审 2=复审 3=三审...
     ReviewAction TINYINT NOT NULL,
     -- 审核动作：1=通过 2=退回 3=修改
     OperatorId INT NOT NULL,
     -- 操作员ID（实际执行审核操作的人）
     OperatorName NVARCHAR(100) NOT NULL,
     -- 操作员姓名（冗余存储）
     ResponsibleDoctorId INT NOT NULL,
     -- 责任医师ID（承担医疗责任的医师）
     ResponsibleDoctorName NVARCHAR(100) NOT NULL,
     -- 责任医师姓名（冗余存储）
     ResponsibleDoctorTitle NVARCHAR(50) NULL,
     -- 责任医师职称（用于报告显示）
     ReviewComment NVARCHAR(500) NULL,
     -- 审核意见/备注
     ReviewedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 审核时间
     CONSTRAINT FK_LabResultReview_Result FOREIGN KEY (ResultId) REFERENCES dbo.LabResult(ResultId),
     CONSTRAINT FK_LabResultReview_Operator FOREIGN KEY (OperatorId) REFERENCES dbo.SysUser(UserId),
     CONSTRAINT FK_LabResultReview_ResponsibleDoctor FOREIGN KEY (ResponsibleDoctorId) REFERENCES dbo.DictDoctor(DoctorId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验结果审核记录表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'LabResultReview';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'审核记录ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ReviewId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的检验结果ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ResultId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'审核级别：1=初审 2=复审 3=三审',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ReviewLevel';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'审核动作：1=通过 2=退回 3=修改',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ReviewAction';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'操作员ID（实际执行审核操作的人）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'OperatorId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'操作员姓名（冗余存储）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'OperatorName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'责任医师ID（承担医疗责任的医师）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ResponsibleDoctorId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'责任医师姓名（冗余存储）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ResponsibleDoctorName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'责任医师职称（用于报告显示）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ResponsibleDoctorTitle';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'审核意见/备注',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ReviewComment';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'审核时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'LabResultReview',@level2type=N'COLUMN',@level2name=N'ReviewedAt';

CREATE INDEX IX_LabResultReview_ResultId ON dbo.LabResultReview(ResultId);
CREATE INDEX IX_LabResultReview_ReviewLevel ON dbo.LabResultReview(ReviewLevel);
CREATE INDEX IX_LabResultReview_Operator ON dbo.LabResultReview(OperatorId);
CREATE INDEX IX_LabResultReview_ResponsibleDoctor ON dbo.LabResultReview(ResponsibleDoctorId);
CREATE INDEX IX_LabResultReview_ReviewedAt ON dbo.LabResultReview(ReviewedAt DESC);
CREATE UNIQUE INDEX UQ_LabResultReview_ResultLevel ON dbo.LabResultReview(ResultId, ReviewLevel);
GO

/*=============================================================
  20. DictUnit — 检验结果单位字典表
=============================================================*/
CREATE TABLE dbo.DictUnit
(
     UnitId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 单位ID，自增主键，系统内部关联使用
     UnitName NVARCHAR(30) NOT NULL UNIQUE,
     -- 单位名称，作为业务唯一标识，如 g/L, U/L 等
     Status BIT NOT NULL DEFAULT 1,
     -- 状态：1 = Active (启用)，0 = Inactive (停用)
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 最近更新时间
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验结果单位字典表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictUnit';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'单位ID，自增主键，系统内部关联使用',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictUnit',@level2type=N'COLUMN',@level2name=N'UnitId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'单位名称，作为业务唯一标识，如 g/L, U/L 等',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictUnit',@level2type=N'COLUMN',@level2name=N'UnitName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态：1 = Active (启用)，0 = Inactive (停用)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictUnit',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictUnit',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictUnit',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE UNIQUE INDEX UQ_DictUnit_Name ON dbo.DictUnit(UnitName);
CREATE INDEX IX_DictUnit_Status ON dbo.DictUnit(Status);
GO



/*=============================================================
  20. DictLabItemCalculation — 检验项目计算公式表
=============================================================*/
CREATE TABLE dbo.DictLabItemCalculation
(
     CalculationId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 计算规则ID
     LabItemId INT NOT NULL UNIQUE,
     -- 关联的计算项目ID (一个项目只有一个公式)
     FormulaExpression NVARCHAR(500) NOT NULL,
     -- 公式表达式字符串，如: ([BIO_TP]-[BIO_ALB])/[BIO_ALB]
     Description NVARCHAR(200) NULL,
     -- 公式说明/备注
     Status BIT NOT NULL DEFAULT 1,
     -- 状态：1=启用 0=停用
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 最近更新时间
     CONSTRAINT FK_DictLabItemCalculation_LabItem FOREIGN KEY (LabItemId) REFERENCES dbo.DictLabItem(LabItemId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验项目计算公式表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictLabItemCalculation';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'计算规则ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'CalculationId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的计算项目ID (一个项目只有一个公式)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'LabItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'公式表达式字符串，如: ([BIO_TP]-[BIO_ALB])/[BIO_ALB]',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'FormulaExpression';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'公式说明/备注',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态：1=启用 0=停用',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabItemCalculation',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE UNIQUE INDEX UQ_DictLabItemCalculation_LabItem ON dbo.DictLabItemCalculation(LabItemId);
CREATE INDEX IX_DictLabItemCalculation_Status ON dbo.DictLabItemCalculation(Status);
GO

/*=============================================================
  新增表的示例数据插入
=============================================================*/

-- 插入单位字典示例数据
INSERT INTO dbo.DictUnit
     (UnitName, Status)
VALUES
     ('g/L', 1),
     ('U/L', 1),
     ('mmol/L', 1),
     ('umol/L', 1),
     ('10*9/L', 1),
     ('10*12/L', 1),
     ('%', 1),
     ('fL', 1),
     ('pg', 1),
     ('s', 1),
     ('mIU/L', 1),
     ('mg/dL', 1),
     ('ng/mL', 1),
     ('IU/L', 1),
     ('mg/L', 1);
GO

-- 插入检验申请示例数据
INSERT INTO dbo.LabOrder
     (HisOrderCode, VisitId, PatientId, PatientName, VisitTypeId,
     RequestingDeptId, RequestingDoctorId, ClinicalDiagnosis)
SELECT 'REQ20240521001', 'IP123456', 'PAT009876', N'王大锤', vt.VisitTypeId,
     d.DeptId, dr.DoctorId, N'疑似肝功能异常'
FROM dbo.DictVisitType vt, dbo.DictDepartment d, dbo.DictDoctor dr
WHERE vt.VisitTypeCode = 'IP' AND d.DeptCode = 'CARD' AND dr.DoctorCode = 'DOC003';
GO

-- 插入标本示例数据
-- 为上面的申请单创建一个血清标本
INSERT INTO dbo.LabSpecimen
     (OrderId, Barcode, SampleTypeId, SeqPrefix, DailySeqNo, SpecimenDate)
SELECT o.OrderId, 'S202405210001', st.SampleTypeId, 'S', 1, '2024-05-21'
FROM dbo.LabOrder o, dbo.DictSampleType st
WHERE o.HisOrderCode = 'REQ20240521001' AND st.SampleTypeCode = 'SR01';

-- 为上面的申请单再创建一个全血标本
INSERT INTO dbo.LabSpecimen
     (OrderId, Barcode, SampleTypeId, SeqPrefix, DailySeqNo, SpecimenDate)
SELECT o.OrderId, 'S202405210002', st.SampleTypeId, 'S', 2, '2024-05-21'
FROM dbo.LabOrder o, dbo.DictSampleType st
WHERE o.HisOrderCode = 'REQ20240521001' AND st.SampleTypeCode = 'WB01';
GO

-- 插入医嘱明细示例数据
-- 在血清标本(S202405210001)上添加'谷丙转氨酶'和'促甲状腺激素'两个项目
INSERT INTO dbo.LabOrderItem
     (SpecimenId, LabItemId)
SELECT sp.SpecimenId, li.LabItemId
FROM dbo.LabSpecimen sp, dbo.DictLabItem li
WHERE sp.Barcode = 'S202405210001' AND li.LabItemCode IN ('BIO_ALT', 'IMM_TSH');

-- 在全血标本(S202405210002)上添加'血红蛋白'项目
INSERT INTO dbo.LabOrderItem
     (SpecimenId, LabItemId)
SELECT sp.SpecimenId, li.LabItemId
FROM dbo.LabSpecimen sp, dbo.DictLabItem li
WHERE sp.Barcode = 'S202405210002' AND li.LabItemCode = 'HEM_HGB';
GO

/*=============================================================
  21. DictLabProfile — 检验组合字典表
=============================================================*/
CREATE TABLE dbo.DictLabProfile
(
     ProfileId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 组合ID，自增主键
     ProfileCode NVARCHAR(30) NOT NULL UNIQUE,
     -- 组合编码 (院内统一，如 CBC, LFP)
     ProfileName NVARCHAR(100) NOT NULL,
     -- 组合中文名称（如：血常规，肝功能全项）
     HisProfileCode NVARCHAR(200) NULL,
     -- HIS系统组合/医嘱编码，允许NULL，用于系统集成
     ReportTitle NVARCHAR(100) NULL,
     -- 报告单标题，如果为空则默认使用ProfileName
     EnglishName NVARCHAR(100) NULL,
     -- 英文名称
     PinYin NVARCHAR(100) NULL,
     -- 拼音/首字母
     Description NVARCHAR(200) NULL,
     -- 组合描述，如临床意义、注意事项等
     Status BIT NOT NULL DEFAULT 1,
     -- 状态：1 = Active (启用)，0 = Inactive (停用)
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE()
     -- 最近更新时间
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验组合字典表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictLabProfile';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'组合ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'ProfileId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'组合编码 (院内统一，如 CBC, LFP)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'ProfileCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'组合中文名称（如：血常规，肝功能全项）',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'ProfileName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'HIS系统组合/医嘱编码，允许NULL，用于系统集成',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'HisProfileCode';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'报告单标题，如果为空则默认使用ProfileName',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'ReportTitle';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'英文名称',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'EnglishName';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'拼音/首字母',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'PinYin';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'组合描述，如临床意义、注意事项等',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'Description';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'状态：1 = Active (启用)，0 = Inactive (停用)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'Status';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfile',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE UNIQUE INDEX UQ_DictLabProfile_Code ON dbo.DictLabProfile(ProfileCode);
CREATE INDEX IX_DictLabProfile_Status ON dbo.DictLabProfile(Status);
CREATE INDEX IX_DictLabProfile_PinYin ON dbo.DictLabProfile(PinYin);
GO

-- 添加LabOrderItem表的ProfileId外键约束
ALTER TABLE dbo.LabOrderItem 
ADD CONSTRAINT FK_OrderItem_Profile FOREIGN KEY (ProfileId) REFERENCES dbo.DictLabProfile(ProfileId);
GO

/*=============================================================
  22. DictLabProfileItemMap — 检验组合-项目映射表
=============================================================*/
CREATE TABLE dbo.DictLabProfileItemMap
(
     MapId BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
     -- 映射ID，自增主键
     ProfileId INT NOT NULL,
     -- 关联的组合ID
     LabItemId INT NOT NULL,
     -- 关联的检验项目ID
     SequenceNo INT NULL DEFAULT 0,
     -- 报告显示顺序，决定项目在报告单中的排列位置
     IsDefault BIT NOT NULL DEFAULT 1,
     -- 是否为默认项目：1=是(常规开立包含此项), 0=否(可选项目，需额外勾选)
     CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录创建时间
     UpdatedAt DATETIME NOT NULL DEFAULT GETDATE(),
     -- 记录更新时间
     CONSTRAINT FK_Map_Profile FOREIGN KEY (ProfileId) REFERENCES dbo.DictLabProfile(ProfileId),
     CONSTRAINT FK_Map_Item FOREIGN KEY (LabItemId) REFERENCES dbo.DictLabItem(LabItemId)
);
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description',
  @value=N'检验组合-项目映射表', @level0type=N'SCHEMA',@level0name=N'dbo',
  @level1type=N'TABLE',@level1name=N'DictLabProfileItemMap';

EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'映射ID，自增主键',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'MapId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的组合ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'ProfileId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'关联的检验项目ID',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'LabItemId';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'报告显示顺序，决定项目在报告单中的排列位置',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'SequenceNo';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'是否为默认项目：1=是(常规开立包含此项), 0=否(可选项目，需额外勾选)',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'IsDefault';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'记录创建时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'CreatedAt';
EXEC sys.sp_addextendedproperty @name=N'MS_Description',@value=N'最近更新时间',@level0type=N'SCHEMA',@level0name=N'dbo',@level1type=N'TABLE',@level1name=N'DictLabProfileItemMap',@level2type=N'COLUMN',@level2name=N'UpdatedAt';

CREATE UNIQUE INDEX UQ_DictLabProfileItemMap_Map ON dbo.DictLabProfileItemMap(ProfileId, LabItemId);
CREATE INDEX IX_DictLabProfileItemMap_Profile ON dbo.DictLabProfileItemMap(ProfileId);
CREATE INDEX IX_DictLabProfileItemMap_Item ON dbo.DictLabProfileItemMap(LabItemId);
GO

/*==============================================================
  为DictLabItemCalculation表添加示例数据
==============================================================*/

-- 首先添加计算项目到DictLabItem表
INSERT INTO dbo.DictLabItem
     (LabItemCode, LabItemName, EnglishName, PinYin,
     ItemCategoryId, SampleTypeId, DefaultUnit, Precision,
     DefaultMethodId, LoincCode, IsCalculated)
     SELECT 'BIO_AG_RATIO', N'A/G比值', 'A/G Ratio', 'AGBZ',
          c.ItemCategoryId, s.SampleTypeId, '', 2, NULL, '1751-7', 1
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s
     WHERE c.ItemCategoryCode = 'BIO' AND s.SampleTypeCode = 'SR01'
UNION ALL
     SELECT 'BIO_CCR', N'肌酐清除率', 'Creatinine Clearance', 'JGQCL',
          c.ItemCategoryId, s.SampleTypeId, 'ml/min', 2, NULL, '2164-2', 1
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s
     WHERE c.ItemCategoryCode = 'BIO' AND s.SampleTypeCode = 'SR01'
UNION ALL
     SELECT 'BIO_LDL_CALC', N'LDL胆固醇(计算)', 'LDL-C(Calculated)', 'LDLDGC',
          c.ItemCategoryId, s.SampleTypeId, 'mmol/L', 2, NULL, '13457-7', 1
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s
     WHERE c.ItemCategoryCode = 'BIO' AND s.SampleTypeCode = 'SR01'
UNION ALL
     SELECT 'BIO_OSMO_CALC', N'血清渗透压(计算)', 'Serum Osmolality(Calc)', 'XQSTY',
          c.ItemCategoryId, s.SampleTypeId, 'mOsm/kg', 1, NULL, '33747-0', 1
     FROM dbo.DictLabItemCategory c, dbo.DictSampleType s
     WHERE c.ItemCategoryCode = 'BIO' AND s.SampleTypeCode = 'SR01';
GO

-- 为计算项目添加计算公式
INSERT INTO dbo.DictLabItemCalculation
     (LabItemId, FormulaExpression, Description)
     SELECT li.LabItemId, '[BIO_ALB]/([BIO_TP]-[BIO_ALB])',
          N'白蛋白与球蛋白比值，用于评估肝功能和营养状态'
     FROM dbo.DictLabItem li
     WHERE li.LabItemCode = 'BIO_AG_RATIO'
UNION ALL
     SELECT li.LabItemId, '(140-[AGE])*[WEIGHT]*1.23/[BIO_CREA]',
          N'Cockcroft-Gault公式计算肌酐清除率，用于评估肾功能'
     FROM dbo.DictLabItem li
     WHERE li.LabItemCode = 'BIO_CCR'
UNION ALL
     SELECT li.LabItemId, '[BIO_TC]-[BIO_HDL]-[BIO_TG]/2.2',
          N'Friedewald公式计算LDL胆固醇（当TG<4.5mmol/L时适用）'
     FROM dbo.DictLabItem li
     WHERE li.LabItemCode = 'BIO_LDL_CALC'
UNION ALL
     SELECT li.LabItemId, '1.86*[BIO_NA]+[BIO_GLU]+[BIO_UREA]+9',
          N'血清渗透压计算公式，用于评估电解质平衡'
     FROM dbo.DictLabItem li
     WHERE li.LabItemCode = 'BIO_OSMO_CALC';
GO

/*==============================================================
  为DictLabProfile表添加示例数据
==============================================================*/

-- 插入检验组合示例数据
INSERT INTO dbo.DictLabProfile
     (ProfileCode, ProfileName, HisProfileCode, PinYin, Description)
VALUES
     ('CBC', N'血常规', 'HIS_CBC', 'XCG', N'全血细胞计数，用于筛查血液系统疾病'),
     ('LFP', N'肝功能全项', 'HIS_LFP', 'GGNQX', N'肝功能全面检测，包括转氨酶、胆红素、蛋白等指标'),
     ('RFP', N'肾功能三项', 'HIS_RFP', 'SGNJX', N'肾功能基础检测，包括肌酐、尿素、尿酸'),
     ('LIPID', N'血脂四项', 'HIS_LIPID', 'XZSX', N'血脂代谢检测，包括胆固醇、甘油三酯、HDL、LDL'),
     ('CHO', N'总胆固醇测定', 'HIS_CHO', 'ZDGC', N'单项胆固醇检测'); 
GO

/*==============================================================
  为DictLabProfileItemMap表添加示例数据
==============================================================*/

-- 肝功能全项包含的项目
INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 1, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_ALT';

INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 2, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_AST';

INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 3, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_TP';

INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 4, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_ALB';

-- 总胆固醇测定只包含它自己一个项目
INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 1, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'CHO' AND i.LabItemCode = 'BIO_CHO';

-- 肾功能三项包含的项目  
INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 1, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'RFP' AND i.LabItemCode = 'BIO_CREA';

INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 2, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'RFP' AND i.LabItemCode = 'BIO_UREA';

INSERT INTO dbo.DictLabProfileItemMap
     (ProfileId, LabItemId, SequenceNo, IsDefault)
SELECT p.ProfileId, i.LabItemId, 3, 1
FROM dbo.DictLabProfile p, dbo.DictLabItem i
WHERE p.ProfileCode = 'RFP' AND i.LabItemCode = 'BIO_UA';
GO