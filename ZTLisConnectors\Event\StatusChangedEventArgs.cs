﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器状态变化的事件参数
    /// </summary>
    public class StatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 仪器ID
        /// </summary>
        public int InstrumentId { get; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public ConnectorStatus OldStatus { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public ConnectorStatus NewStatus { get; }

        /// <summary>
        /// 变化时间
        /// </summary>
        public DateTime ChangedTime { get; }

        /// <summary>
        /// 状态变化原因说明
        /// </summary>
        public string Reason { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StatusChangedEventArgs(int instrumentId, ConnectorStatus oldStatus, ConnectorStatus newStatus, string reason = null)
        {
            InstrumentId = instrumentId;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            ChangedTime = DateTime.Now;
            Reason = reason;
        }
    }

}
