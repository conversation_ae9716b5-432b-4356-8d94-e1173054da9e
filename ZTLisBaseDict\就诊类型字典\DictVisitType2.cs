using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictVisitType2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictVisitType _bllDictVisitType = new BllDictVisitType();
        Model.MdlDictVisitType _mdlDictVisitType = new MdlDictVisitType();

        public DictVisitType2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtHisCode.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictVisitType2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictVisitType.MaxCode(3);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            TxtHisCode.Text = "";
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["VisitTypeCode"] + "";
            TxtName.Text = row["VisitTypeName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            TxtHisCode.Text = row["HisVisitTypeCode"] + "";
            TxtDescription.Text = row["Description"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写就诊类型名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写就诊类型编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("HIS编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["VisitTypeCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["VisitTypeName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && TxtHisCode.Text.Trim() != base.MyRow["HisVisitTypeCode"].ToString().Trim() && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("HIS编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictVisitType.GetRecordCount($"VisitTypeCode='{code}'");
            return cnt > 0;
        }

        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictVisitType.GetRecordCount($"VisitTypeName='{name}'");
            return cnt > 0;
        }

        private bool CheckHisCodeExists(string hisCode)
        {
            int cnt = _bllDictVisitType.GetRecordCount($"HisVisitTypeCode='{hisCode}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictVisitType.VisitTypeCode = TxtCode.Text.Trim();
            _mdlDictVisitType.VisitTypeName = TxtName.Text.Trim();
            _mdlDictVisitType.PinYin = TxtJc.Text.Trim();
            _mdlDictVisitType.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictVisitType.HisVisitTypeCode = TxtHisCode.Text.Trim();
            _mdlDictVisitType.Description = TxtDescription.Text.Trim();
            _mdlDictVisitType.Status = true;
            _mdlDictVisitType.CreatedAt = DateTime.Now;
            _mdlDictVisitType.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictVisitType, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["VisitTypeId"] = _bllDictVisitType.Add(_mdlDictVisitType);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictVisitType = _bllDictVisitType.GetModel(int.Parse(base.MyRow["VisitTypeId"].ToString()));
            _mdlDictVisitType.VisitTypeCode = TxtCode.Text.Trim();
            _mdlDictVisitType.VisitTypeName = TxtName.Text.Trim();
            _mdlDictVisitType.PinYin = TxtJc.Text.Trim();
            _mdlDictVisitType.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictVisitType.HisVisitTypeCode = TxtHisCode.Text.Trim();
            _mdlDictVisitType.Description = TxtDescription.Text.Trim();
            _mdlDictVisitType.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictVisitType, base.MyRow);

            //数据保存
            try
            {
                _bllDictVisitType.Update(_mdlDictVisitType);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 事件

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false) return;
            if (base.Insert == true)
                this.DataAdd();
            else
                this.DataEdit();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(TxtName.Text)) return;
            if (string.IsNullOrEmpty(TxtJc.Text))
                TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }

        private void TxtCode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                TxtName.Focus();
            }
        }

        private void TxtName_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                TxtEnglishName.Focus();
            }
        }

        private void TxtEnglishName_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                TxtHisCode.Focus();
            }
        }

        private void TxtHisCode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                TxtDescription.Focus();
            }
        }

        private void TxtDescription_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnSave.Focus();
            }
        }

        #endregion
    }
}