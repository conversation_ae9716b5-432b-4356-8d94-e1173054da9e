using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using CustomControl;
using ZTLisEnum;

namespace ZTLisControl
{
    public partial class SingleConnType : MySingleComobo
    {
        public SingleConnType()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        public void Init()
        {
            DataSource = typeof(ConnType);
            DisplayColumns[1].Visible = false;
            DroupDownWidth = Width - (int)CaptainWidth;
            SelectedIndex = 0;
            ItemHeight = 20;
        }
    }
}