﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Input;
using Microsoft.VisualBasic;
using System.Text.RegularExpressions;
namespace CustomControl
{
    public partial class DoubleDateEditH : UserControl
    {
        bool loadOk;
        public DoubleDateEditH()
        {
            InitializeComponent();
            loadOk = false;

            //myDateEdit1.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit1.EditFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit1.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit2.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit2.EditFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit2.DisplayFormat = "yyyy-MM-dd HH:mm:ss";


            myDateEdit1.CustomFormat = "yyyy-MM-dd HH:mm";
            myDateEdit1.EditFormat = "yyyy-MM-dd HH:mm";
            myDateEdit1.DisplayFormat = "yyyy-MM-dd HH:mm";
            myDateEdit2.CustomFormat = "yyyy-MM-dd HH:mm";
            myDateEdit2.EditFormat = "yyyy-MM-dd HH:mm";
            myDateEdit2.DisplayFormat = "yyyy-MM-dd HH:mm";
            loadOk = true;


        }

        #region 公开属性

        [Description("字体")]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                label2.Font = value;
                myDateEdit1.Font = value;
                myDateEdit2.Font = value;
                Invalidate();
            }
        }

        [Description("控件字体")]
        public new Font ControlFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                label2.Font = value;
                myDateEdit1.Font = value;
                myDateEdit2.Font = value;
                Invalidate();
            }
        }

        float _CaptainWidth = 60;
        [Description("标题宽度"),]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0)
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object StartValue
        {
            get { return myDateEdit1.Value; }
            set
            {
                myDateEdit1.Value = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object EndValue
        {
            get { return myDateEdit2.Value; }
            set
            {
                myDateEdit2.Value = value;
                Invalidate();
            }
        }

        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }


        [Description("自定义格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string CustomFormat
        {
            get { return myDateEdit1.CustomFormat; }
            set
            {
                myDateEdit1.CustomFormat = value;
                myDateEdit2.CustomFormat = value;
                Invalidate();
            }
        }

        [Description("显示格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string DisplayFormat
        {
            get { return myDateEdit1.DisplayFormat; }
            set
            {
                myDateEdit1.DisplayFormat = value;
                myDateEdit2.DisplayFormat = value;
                Invalidate();
            }
        }

        [Description("编辑格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string EditFormat
        {
            get { return myDateEdit1.EditFormat; }
            set
            {
                myDateEdit1.EditFormat = value;
                myDateEdit2.EditFormat = value;
                Invalidate();
            }
        }

        #endregion

        #region 公开方法

        public void SelectStart()
        {
            myDateEdit1.Select();
        }

        public void SelectEnd()
        {
            myDateEdit2.Select();
        }

        #endregion

        #region 公开事件

        [Description("当值改变时时触发"), Browsable(true)]
        public event EventHandler ValueChanged
        {
            add
            {
                myDateEdit1.ValueChanged += value;
                myDateEdit2.ValueChanged += value;
            }
            remove
            {
                myDateEdit1.ValueChanged -= value;
                myDateEdit2.ValueChanged -= value;
            }
        }

        #endregion

    }
}
