﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Common
{
    public class AES
    {
        #region aes实现
        // <summary>
        /// AES加密 (128-ECB加密模式)
        /// </summary>
        /// <param name="toEncrypt">内容</param>
        /// <param name="key">秘钥</param>
        /// <returns></returns>
        public static string AESEncrypt(string toEncrypt, string key)
        {
            //byte[] keyArray = Encoding.UTF8.GetBytes(key);
            //byte[] toEncryptArray = Encoding.UTF8.GetBytes(toEncrypt);

            //RijndaelManaged rDel = new RijndaelManaged();
            //rDel.Key = keyArray;
            //rDel.Mode = CipherMode.ECB;
            //rDel.Padding = PaddingMode.PKCS7;

            //ICryptoTransform cTransform = rDel.CreateEncryptor();
            //byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);

            //return Convert.ToBase64String(resultArray, 0, resultArray.Length);

            byte[] plainText = Encoding.UTF8.GetBytes(toEncrypt);
            RijndaelManaged rijndaelCipher = new RijndaelManaged();
            rijndaelCipher.Mode = CipherMode.ECB;
            rijndaelCipher.Padding = PaddingMode.PKCS7;
            rijndaelCipher.Key = Encoding.UTF8.GetBytes(key);

            ICryptoTransform transform = rijndaelCipher.CreateEncryptor();

            byte[] cipherBytes = transform.TransformFinalBlock(plainText, 0, plainText.Length);
            return Convert.ToBase64String(cipherBytes);
        }
        /// <summary>
        /// AES解密(128-ECB加密模式)
        /// </summary>
        /// <param name="toDecrypt">密文</param>
        /// <param name="key">秘钥(Base64String)</param>
        /// <returns></returns>
        public static string AESDecrypt(string toDecrypt, string key)
        {
            try
            {
                byte[] encryptedData = Convert.FromBase64String(toDecrypt);
                RijndaelManaged rijndaelCipher = new RijndaelManaged();
                rijndaelCipher.Mode = CipherMode.ECB;
                rijndaelCipher.Padding = PaddingMode.PKCS7;
                rijndaelCipher.Key = System.Text.Encoding.UTF8.GetBytes(key);

                ICryptoTransform transform = rijndaelCipher.CreateDecryptor();
                byte[] plainText = transform.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
                return Encoding.UTF8.GetString(plainText);
            }
            catch (Exception ex)
            {
                return "解密失败";
                throw ex;
            }
        }



        public static string Aes256CBCEncrypt(string toEncrypt, string key)
        {
            byte[] plainText = Encoding.UTF8.GetBytes(toEncrypt);
            RijndaelManaged rijndaelCipher = new RijndaelManaged();
            rijndaelCipher.Mode = CipherMode.CBC;
            rijndaelCipher.Padding = PaddingMode.PKCS7;
            rijndaelCipher.Key = Encoding.UTF8.GetBytes(key);
            rijndaelCipher.IV = (byte[]) (object) new sbyte[16];// Encoding.UTF8.GetBytes(key);

            ICryptoTransform transform = rijndaelCipher.CreateEncryptor();

            byte[] cipherBytes = transform.TransformFinalBlock(plainText, 0, plainText.Length);
            return Convert.ToBase64String(cipherBytes);
        }

        public static string AESCBCDecrypt(string toDecrypt, string key)
        {
            try
            {
                byte[] encryptedData = Convert.FromBase64String(toDecrypt);
                RijndaelManaged rijndaelCipher = new RijndaelManaged();
                rijndaelCipher.Mode = CipherMode.CBC;
                rijndaelCipher.Padding = PaddingMode.PKCS7;
                rijndaelCipher.Key = System.Text.Encoding.UTF8.GetBytes(key);

                ICryptoTransform transform = rijndaelCipher.CreateDecryptor();
                byte[] plainText = transform.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
                return Encoding.UTF8.GetString(plainText);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}
