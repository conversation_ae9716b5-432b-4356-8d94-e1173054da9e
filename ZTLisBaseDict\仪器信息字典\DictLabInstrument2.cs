using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictLabInstrument2 : Common.BaseForm.BaseDict2
    {
        BLL.BllLabInstrument _bllLabInstrument = new BllLabInstrument();
        Model.MdlLabInstrument _mdlLabInstrument = new MdlLabInstrument();

        public DictLabInstrument2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtInstrName.GotFocus += new System.EventHandler(base.InputCn);
            TxtVendor.GotFocus += new System.EventHandler(base.InputCn);
            TxtModel.GotFocus += new System.EventHandler(base.InputCn);
            TxtSerialNo.GotFocus += new System.EventHandler(base.InputEn);
            TxtLocation.GotFocus += new System.EventHandler(base.InputCn);
            TxtIpHost.GotFocus += new System.EventHandler(base.InputEn);
            TxtFilePath.GotFocus += new System.EventHandler(base.InputEn);
            TxtDbConnStr.GotFocus += new System.EventHandler(base.InputEn);
            TxtApiBaseUrl.GotFocus += new System.EventHandler(base.InputEn);
            TxtApiAuthToken.GotFocus += new System.EventHandler(base.InputEn);
            TxtMsgHeader.GotFocus += new System.EventHandler(base.InputEn);
            TxtMsgTail.GotFocus += new System.EventHandler(base.InputEn);
            TxtAckSuccess.GotFocus += new System.EventHandler(base.InputEn);
            TxtAckFailure.GotFocus += new System.EventHandler(base.InputEn);
            singleInstrumentDriverClass1.GotFocus += new System.EventHandler(base.InputEn);
            TxtFirmwareVersion.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictLabInstrument2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化枚举控件
            SingleConnType1.Init();
            SingleComPort1.Init();
            SingleBaudRate1.Init();
            SingleParity1.Init();
            SingleDataBits1.Init();
            SingleStopBits1.Init();
            SingleProtocolType1.Init();
            SingleMsgHeaderFormatType1.Init();
            singleMsgFormatType1.Init();
            singleDbType1.Init();
            singleInstrumentDriverClass1.Init();
            NumIpPort.CustomFormat = "#####";

            // 设置控件启用禁用逻辑
            UpdateControlsEnabled();
        }

        private void UpdateControlsEnabled()
        {
            int connType = SingleConnType1.SelectedIndex;

            // 串口相关控件
            SingleComPort1.Enabled = (connType == 0); // 串口
            SingleBaudRate1.Enabled = (connType == 0);
            SingleParity1.Enabled = (connType == 0);
            SingleDataBits1.Enabled = (connType == 0);
            SingleStopBits1.Enabled = (connType == 0);

            // TCP相关控件
            TxtIpHost.Enabled = (connType == 1); // TCP
            NumIpPort.Enabled = (connType == 1);
            NumKeepAliveSec.Enabled = (connType == 1);

            // 文件共享相关控件
            TxtFilePath.Enabled = (connType == 2); // 文件共享

            // 中间库相关控件
            singleDbType1.Enabled = (connType == 3); // 中间库
            TxtDbConnStr.Enabled = (connType == 3);

            // REST接口相关控件
            TxtApiBaseUrl.Enabled = (connType == 4); // REST接口
            TxtApiAuthToken.Enabled = (connType == 4);
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtInstrCode.Text = _bllLabInstrument.MaxCode(4);
            TxtInstrName.Text = "";
            TxtVendor.Text = "";
            TxtModel.Text = "";
            TxtSerialNo.Text = "";
            TxtLocation.Text = "";
            SingleConnType1.SelectedIndex = 0;
            singleMsgFormatType1.SelectedIndex = 0;
            SingleComPort1.SelectedIndex = 0;
            SingleBaudRate1.SelectedIndex = 3; // 默认9600
            SingleParity1.SelectedIndex = 0; // 默认无校验
            SingleDataBits1.SelectedIndex = 3; // 默认8位
            SingleStopBits1.SelectedIndex = 0; // 默认1位
            TxtIpHost.Text = "";
            NumIpPort.Value = null;
            NumKeepAliveSec.Value = null;
            TxtFilePath.Text = "";
            singleDbType1.SelectedIndex = -1;
            TxtDbConnStr.Text = "";
            TxtApiBaseUrl.Text = "";
            TxtApiAuthToken.Text = "";
            SingleProtocolType1.SelectedIndex = 0;
            SingleMsgHeaderFormatType1.SelectedIndex = 0;
            TxtMsgHeader.Text = "";
            TxtMsgTail.Text = "";
            TxtAckSuccess.Text = "";
            TxtAckFailure.Text = "";
            singleInstrumentDriverClass1.SelectedIndex = -1;
            TxtFirmwareVersion.Text = "";
            TxtDescription.Text = "";
            TxtInstrCode.Select();
            UpdateControlsEnabled();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtInstrCode.Text = row["InstrCode"] + "";
            TxtInstrName.Text = row["InstrName"] + "";
            TxtVendor.Text = row["Vendor"] + "";
            TxtModel.Text = row["Model"] + "";
            TxtSerialNo.Text = row["SerialNo"] + "";
            TxtLocation.Text = row["Location"] + "";
            SingleConnType1.SelectedIndex = row["ConnType"] == DBNull.Value ? 0 : int.Parse(row["ConnType"].ToString());
            singleMsgFormatType1.SelectedIndex = row["MsgFormatType"] == DBNull.Value ? 0 : int.Parse(row["MsgFormatType"].ToString());
            SingleComPort1.SelectedValue = row["ComPort"] + "";        // 设置串口号
            SingleBaudRate1.SelectedValue = row["BaudRate"] + "";    // 设置波特率
            SingleParity1.SelectedValue = row["Parity"] + "";    // 设置校验位
            SingleDataBits1.SelectedValue = row["DataBits"] + "";    // 设置数据位
            SingleStopBits1.SelectedValue = row["StopBits"] + "";    // 设置停止位
            TxtIpHost.Text = row["IpHost"] + "";
            NumIpPort.Value = row["IpPort"];
            NumKeepAliveSec.Value = row["KeepAliveSec"];
            TxtFilePath.Text = row["FilePath"] + "";
            singleDbType1.Text = row["DbType"] + "";
            TxtDbConnStr.Text = row["DbConnStr"] + "";
            TxtApiBaseUrl.Text = row["ApiBaseUrl"] + "";
            TxtApiAuthToken.Text = row["ApiAuthToken"] + "";
            SingleProtocolType1.SelectedIndex = row["ProtocolType"] == DBNull.Value ? 0 : int.Parse(row["ProtocolType"].ToString());
            SingleMsgHeaderFormatType1.SelectedIndex = row["MsgHeaderFormatType"] == DBNull.Value ? 0 : int.Parse(row["MsgHeaderFormatType"].ToString());
            TxtMsgHeader.Text = row["MsgHeader"] + "";
            TxtMsgTail.Text = row["MsgTail"] + "";
            TxtAckSuccess.Text = row["AckSuccess"] + "";
            TxtAckFailure.Text = row["AckFailure"] + "";
            singleInstrumentDriverClass1.SelectedValue = row["CustomDriverClass"] + "";
            TxtFirmwareVersion.Text = row["FirmwareVersion"] + "";
            TxtDescription.Text = row["Description"] + "";

            TxtInstrName.Select();
            UpdateControlsEnabled();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtInstrName.Text))
            {
                MessageBox.Show("请填写仪器名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtInstrName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtInstrCode.Text))
            {
                MessageBox.Show("请填写仪器编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtInstrCode.Select();
                return false;
            }
            switch (SingleConnType1.SelectedIndex)
            {
                case 0:
                    if (CustomControl.Func.NotAllowEmpty(SingleComPort1)) return false;
                    if (CustomControl.Func.NotAllowEmpty(SingleBaudRate1)) return false;
                    if (CustomControl.Func.NotAllowEmpty(SingleParity1)) return false;
                    if (CustomControl.Func.NotAllowEmpty(SingleDataBits1)) return false;
                    if (CustomControl.Func.NotAllowEmpty(SingleStopBits1)) return false;
                    break;
                case 1:
                    if (CustomControl.Func.NotAllowEmpty(TxtIpHost)) return false;
                    if (CustomControl.Func.NotAllowEmpty(NumIpPort)) return false;
                    if (CustomControl.Func.NotAllowEmpty(NumKeepAliveSec)) return false;
                    break;
                case 2:
                    if (CustomControl.Func.NotAllowEmpty(TxtFilePath)) return false;
                    break;
                case 3:
                    if (CustomControl.Func.NotAllowEmpty(singleDbType1)) return false;
                    if (CustomControl.Func.NotAllowEmpty(TxtDbConnStr)) return false;
                    break;
            }
            if (SingleMsgHeaderFormatType1.SelectedIndex == 1)
            {
                // 验证通讯控制字符字段的16进制格式
                if (!ValidateHexString(TxtMsgHeader.Text.Trim(), "消息头"))
                {
                    TxtMsgHeader.Select();
                    return false;
                }
                if (!ValidateHexString(TxtMsgTail.Text.Trim(), "消息尾"))
                {
                    TxtMsgTail.Select();
                    return false;
                }
                if (!ValidateHexString(TxtAckSuccess.Text.Trim(), "接收成功应答"))
                {
                    TxtAckSuccess.Select();
                    return false;
                }
                if (!ValidateHexString(TxtAckFailure.Text.Trim(), "接收失败应答"))
                {
                    TxtAckFailure.Select();
                    return false;
                }
            }
            if (!string.IsNullOrEmpty(TxtIpHost.Text.Trim()) && !Common.RegexHelper.IsIPv4(TxtIpHost.Text))
            {
                MessageBox.Show("IP地址格式错误！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtIpHost.Select();
                return false;
            }
            if (SingleProtocolType1.SelectedIndex == 4)
            {
                if (CustomControl.Func.NotAllowEmpty(singleInstrumentDriverClass1)) return false;
            }
            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtInstrCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtInstrName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtInstrCode.Text.Trim() != base.MyRow["InstrCode"].ToString().Trim() && CheckCodeExists(TxtInstrCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrCode.Select();
                    return false;
                }
                if (TxtInstrName.Text.Trim() != base.MyRow["InstrName"].ToString().Trim() && CheckNameExists(TxtInstrName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllLabInstrument.GetRecordCount($"InstrCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllLabInstrument.GetRecordCount($"InstrName='{name}'");
            return cnt > 0;
        }

        private bool ValidateHexString(string value, string fieldName)
        {
            if (string.IsNullOrEmpty(value))
            {
                return true; // 空值允许
            }

            // 正则表达式：匹配00到FF的16进制字符串，用英文空格分隔
            // ^([0-9A-Fa-f]{2})( ([0-9A-Fa-f]{2}))*$
            string pattern = @"^([0-9A-Fa-f]{2})( [0-9A-Fa-f]{2})*$";

            if (!System.Text.RegularExpressions.Regex.IsMatch(value, pattern))
            {
                MessageBox.Show($"{fieldName}格式错误！请输入00到FF的16进制字符串，多个值用英文空格分隔。", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 将界面数据映射到模型对象
        /// </summary>
        private void MapFormDataToModel()
        {
            _mdlLabInstrument.InstrCode = TxtInstrCode.Text.Trim();
            _mdlLabInstrument.InstrName = TxtInstrName.Text.Trim();
            _mdlLabInstrument.Vendor = TxtVendor.Text.Trim();
            _mdlLabInstrument.Model = TxtModel.Text.Trim();
            _mdlLabInstrument.SerialNo = TxtSerialNo.Text.Trim();
            _mdlLabInstrument.Location = TxtLocation.Text.Trim();
            _mdlLabInstrument.ConnType = SingleConnType1.SelectedIndex;
            _mdlLabInstrument.MsgFormatType = singleMsgFormatType1.SelectedIndex;
            _mdlLabInstrument.ComPort = SingleComPort1.SelectedIndex >= 0 ? SingleComPort1.Text : "";
            _mdlLabInstrument.BaudRate = Common.ConvertObject.ObjToInt(SingleBaudRate1.Text);
            _mdlLabInstrument.Parity = SingleParity1.SelectedValue + "";
            _mdlLabInstrument.DataBits = Common.ConvertObject.ObjToInt(SingleDataBits1.Text);
            _mdlLabInstrument.StopBits = SingleStopBits1.SelectedValue + "";
            _mdlLabInstrument.IpHost = TxtIpHost.Text.Trim();
            _mdlLabInstrument.IpPort = Common.ConvertObject.ObjToInt(NumIpPort.Value);
            _mdlLabInstrument.KeepAliveSec = Common.ConvertObject.ObjToInt(NumKeepAliveSec.Value);
            _mdlLabInstrument.FilePath = TxtFilePath.Text.Trim();
            _mdlLabInstrument.DbType = singleDbType1.Text.Trim();
            _mdlLabInstrument.DbConnStr = TxtDbConnStr.Text.Trim();
            _mdlLabInstrument.ApiBaseUrl = TxtApiBaseUrl.Text.Trim();
            _mdlLabInstrument.ApiAuthToken = TxtApiAuthToken.Text.Trim();
            _mdlLabInstrument.ProtocolType = SingleProtocolType1.SelectedIndex;
            _mdlLabInstrument.MsgHeaderFormatType = SingleMsgHeaderFormatType1.SelectedIndex;
            _mdlLabInstrument.MsgHeader = TxtMsgHeader.Text;
            _mdlLabInstrument.MsgTail = TxtMsgTail.Text.Trim();
            _mdlLabInstrument.AckSuccess = TxtAckSuccess.Text.Trim();
            _mdlLabInstrument.AckFailure = TxtAckFailure.Text.Trim();
            _mdlLabInstrument.CustomDriverClass = singleInstrumentDriverClass1.SelectedValue + "";
            _mdlLabInstrument.FirmwareVersion = TxtFirmwareVersion.Text.Trim();
            _mdlLabInstrument.Description = TxtDescription.Text.Trim();
            _mdlLabInstrument.UpdatedAt = DateTime.Now;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            // 映射界面数据到模型
            MapFormDataToModel();
            _mdlLabInstrument.Status = true;
            _mdlLabInstrument.CreatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlLabInstrument, base.MyRow);

            try
            {
                base.MyRow["InstrumentId"] = _bllLabInstrument.Add(_mdlLabInstrument);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtInstrName.Focus();
            }
            DataClear();
        }

        private void DataEdit()
        {
            _mdlLabInstrument = _bllLabInstrument.GetModel(int.Parse(base.MyRow["InstrumentId"].ToString()));

            // 映射界面数据到模型
            MapFormDataToModel();

            Common.DataTableToList.ToDataRow(_mdlLabInstrument, base.MyRow);

            try
            {
                _bllLabInstrument.Update(_mdlLabInstrument);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 控件动作
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert == true)
                {
                    this.DataAdd();
                }
                else
                {
                    this.DataEdit();
                }
            }

        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void SingleConnType1_SelectedValueChanged(object sender, EventArgs e)
        {
            UpdateControlsEnabled();
        }

        #endregion


    }
}