﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabInstrument.cs
*
* 功 能： N/A
* 类 名： MdlLabInstrument
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 仪器主档/连接参数表
	/// </summary>
	[Serializable]
	public partial class MdlLabInstrument
	{
		public MdlLabInstrument()
		{ }
		#region Model
		private int _instrumentid;
		private string _instrcode;
		private string _instrname;
		private string _vendor;
		private string _model;
		private string _serialno;
		private string _location;
		private int _conntype;
		private int _msgformattype = 0;
		private string _comport;
		private int? _baudrate;
		private string _parity;
		private int? _databits;
		private string _stopbits;
		private string _iphost;
		private int? _ipport;
		private int? _keepalivesec;
		private string _filepath;
		private string _dbtype;
		private string _dbconnstr;
		private string _apibaseurl;
		private string _apiauthtoken;
		private int _protocoltype;
		private int _msgheaderformattype = 0;
		private string _msgheader;
		private string _msgtail;
		private string _acksuccess;
		private string _ackfailure;
		private string _driverversion;
		private string _firmwareversion;
		private string _customdriverclass;
		private bool _status = true;
		private string _description;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 
		/// </summary>
		public int InstrumentId
		{
			set { _instrumentid = value; }
			get { return _instrumentid; }
		}
		/// <summary>
		/// 设备唯一代号（英文/拼音）
		/// </summary>
		public string InstrCode
		{
			set { _instrcode = value; }
			get { return _instrcode; }
		}
		/// <summary>
		/// 设备中文全称
		/// </summary>
		public string InstrName
		{
			set { _instrname = value; }
			get { return _instrname; }
		}
		/// <summary>
		/// 厂商名称
		/// </summary>
		public string Vendor
		{
			set { _vendor = value; }
			get { return _vendor; }
		}
		/// <summary>
		/// 设备型号
		/// </summary>
		public string Model
		{
			set { _model = value; }
			get { return _model; }
		}
		/// <summary>
		/// 出厂序列号
		/// </summary>
		public string SerialNo
		{
			set { _serialno = value; }
			get { return _serialno; }
		}
		/// <summary>
		/// 设备放置地点
		/// </summary>
		public string Location
		{
			set { _location = value; }
			get { return _location; }
		}
		/// <summary>
		/// 通讯类型：0=串口 1=TCP 2=文件共享 3=中间库 4=REST 5=HL7
		/// </summary>
		public int ConnType
		{
			set { _conntype = value; }
			get { return _conntype; }
		}
		/// <summary>
		/// 消息格式类型：0=字符串 1=字节流
		/// </summary>
		public int MsgFormatType
		{
			set { _msgformattype = value; }
			get { return _msgformattype; }
		}
		/// <summary>
		/// 串口号
		/// </summary>
		public string ComPort
		{
			set { _comport = value; }
			get { return _comport; }
		}
		/// <summary>
		/// 波特率
		/// </summary>
		public int? BaudRate
		{
			set { _baudrate = value; }
			get { return _baudrate; }
		}
		/// <summary>
		/// 校验位
		/// </summary>
		public string Parity
		{
			set { _parity = value; }
			get { return _parity; }
		}
		/// <summary>
		/// 数据位
		/// </summary>
		public int? DataBits
		{
			set { _databits = value; }
			get { return _databits; }
		}
		/// <summary>
		/// 停止位
		/// </summary>
		public string StopBits
		{
			set { _stopbits = value; }
			get { return _stopbits; }
		}
		/// <summary>
		/// IP地址或域名
		/// </summary>
		public string IpHost
		{
			set { _iphost = value; }
			get { return _iphost; }
		}
		/// <summary>
		/// IP端口
		/// </summary>
		public int? IpPort
		{
			set { _ipport = value; }
			get { return _ipport; }
		}
		/// <summary>
		/// 心跳间隔(秒)
		/// </summary>
		public int? KeepAliveSec
		{
			set { _keepalivesec = value; }
			get { return _keepalivesec; }
		}
		/// <summary>
		/// 文件共享路径
		/// </summary>
		public string FilePath
		{
			set { _filepath = value; }
			get { return _filepath; }
		}
		/// <summary>
		/// 数据库类型
		/// </summary>
		public string DbType
		{
			set { _dbtype = value; }
			get { return _dbtype; }
		}
		/// <summary>
		/// 中间库连接字符串
		/// </summary>
		public string DbConnStr
		{
			set { _dbconnstr = value; }
			get { return _dbconnstr; }
		}
		/// <summary>
		/// API基础URL
		/// </summary>
		public string ApiBaseUrl
		{
			set { _apibaseurl = value; }
			get { return _apibaseurl; }
		}
		/// <summary>
		/// API认证令牌
		/// </summary>
		public string ApiAuthToken
		{
			set { _apiauthtoken = value; }
			get { return _apiauthtoken; }
		}
		/// <summary>
		/// 协议类型：0=ASTM 1=LIS2 2=POCT1-A 3=HL7 4=自定义
		/// </summary>
		public int ProtocolType
		{
			set { _protocoltype = value; }
			get { return _protocoltype; }
		}
		/// <summary>
		/// 消息头格式类型：0=字符串 1=字节流
		/// </summary>
		public int MsgHeaderFormatType
		{
			set { _msgheaderformattype = value; }
			get { return _msgheaderformattype; }
		}
		/// <summary>
		/// 消息头（ASCII码16进制，多个用空格分割）
		/// </summary>
		public string MsgHeader
		{
			set { _msgheader = value; }
			get { return _msgheader; }
		}
		/// <summary>
		/// 消息尾（ASCII码16进制，多个用空格分割）
		/// </summary>
		public string MsgTail
		{
			set { _msgtail = value; }
			get { return _msgtail; }
		}
		/// <summary>
		/// 接收成功应答（ASCII码16进制，多个用空格分割）
		/// </summary>
		public string AckSuccess
		{
			set { _acksuccess = value; }
			get { return _acksuccess; }
		}
		/// <summary>
		/// 接收失败应答（ASCII码16进制，多个用空格分割）
		/// </summary>
		public string AckFailure
		{
			set { _ackfailure = value; }
			get { return _ackfailure; }
		}
		/// <summary>
		/// 驱动版本
		/// </summary>
		public string DriverVersion
		{
			set { _driverversion = value; }
			get { return _driverversion; }
		}
		/// <summary>
		/// 固件版本
		/// </summary>
		public string FirmwareVersion
		{
			set { _firmwareversion = value; }
			get { return _firmwareversion; }
		}
		/// <summary>
		/// 自定义驱动类名
		/// </summary>
		public string CustomDriverClass
		{
			set { _customdriverclass = value; }
			get { return _customdriverclass; }
		}
		/// <summary>
		/// 状态：1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 备注信息
		/// </summary>
		public string Description
		{
			set { _description = value; }
			get { return _description; }
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

