﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysUser.cs
*
* 功 能： N/A
* 类 名： MdlSysUser
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:54   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 系统用户表
	/// </summary>
	[Serializable]
	public partial class MdlSysUser
	{
		public MdlSysUser()
		{}
		#region Model
		private int _userid;
		private string _loginname;
		private string _passwordhash;
		private string _username;
		private int? _doctorid;
		private string _rolecode;
		private bool _islockedout= false;
		private DateTime? _lastloginat;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		private int? _createdby;
		private int? _updatedby;
		/// <summary>
		/// 用户ID，自增主键
		/// </summary>
		public int UserId
		{
			set{ _userid=value;}
			get{return _userid;}
		}
		/// <summary>
		/// 登录账号，系统内唯一
		/// </summary>
		public string LoginName
		{
			set{ _loginname=value;}
			get{return _loginname;}
		}
		/// <summary>
		/// 密码哈希值（严禁明文存储密码，必须使用强哈希算法如Argon2, bcrypt）
		/// </summary>
		public string PasswordHash
		{
			set{ _passwordhash=value;}
			get{return _passwordhash;}
		}
		/// <summary>
		/// 用户真实姓名
		/// </summary>
		public string UserName
		{
			set{ _username=value;}
			get{return _username;}
		}
		/// <summary>
		/// 关联的医生/技师ID，便于获取职称、科室等信息，实现单点登录或信息同步
		/// </summary>
		public int? DoctorId
		{
			set{ _doctorid=value;}
			get{return _doctorid;}
		}
		/// <summary>
		/// 角色编码，关联角色权限系统
		/// </summary>
		public string RoleCode
		{
			set{ _rolecode=value;}
			get{return _rolecode;}
		}
		/// <summary>
		/// 是否被锁定，因密码错误次数过多等原因被锁定
		/// </summary>
		public bool IsLockedOut
		{
			set{ _islockedout=value;}
			get{return _islockedout;}
		}
		/// <summary>
		/// 最后登录时间
		/// </summary>
		public DateTime? LastLoginAt
		{
			set{ _lastloginat=value;}
			get{return _lastloginat;}
		}
		/// <summary>
		/// 状态：1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		/// <summary>
		/// 创建此用户的操作员ID
		/// </summary>
		public int? CreatedBy
		{
			set{ _createdby=value;}
			get{return _createdby;}
		}
		/// <summary>
		/// 最后更新此用户的操作员ID
		/// </summary>
		public int? UpdatedBy
		{
			set{ _updatedby=value;}
			get{return _updatedby;}
		}
		#endregion Model

	}
}

