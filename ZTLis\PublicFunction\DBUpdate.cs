﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace ZTLis
{
    public class DBUpdate
    {
        public static Dictionary<int, string> DicUpdateSql = new Dictionary<int, string>();

        public static void DbUpdate(string dbPwd, string dbIp, string dbName)
        {
            string m_Jg;
            foreach (var item in DicUpdateSql)
            {
                try
                {
                    m_Jg = Common.WinFormVar.Var.DbHelper.ExecuteSqlPrint(item.Value);
                    if (bool.Parse(m_Jg) == false)
                    {
                        string sqlQuery = $"osql.exe -U Sa -P {dbPwd} -S {dbIp} -d {dbName} -i 更新脚本\\{item.Key}.sql -o {item.Key}.txt";
                        string strRst = Common.Cmd.ExeCommand(sqlQuery);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        public static bool DBNeedUpdate()
        {
            if (DicUpdateSql.Count == 0) return false;
            return !bool.Parse(Common.WinFormVar.Var.DbHelper.ExecuteSqlPrint(DicUpdateSql.Values.Last()));
        }

        public static void AddSqlScript()
        {

        }
    }
}