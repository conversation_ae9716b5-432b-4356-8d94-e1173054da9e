﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="BtnExit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAB3RJTUUH2gQaAAAAtVWE0gAABi1JREFU
        SEtNlGlMVFcUx9+sDAzDDDAsA7IjouIajY0xTWOTl9rENE36QRu1UeMHY0j6rclTY7Ua/WBM0yY2qUtr
        TBs/1AXci+wgKCDLA4ZleGwyLAMIzwHE0jn9n6tQX/IL3HPvPf//OeeC5N+62j20Lusb/+o0ZQQMr0xV
        /CuWKUPZScrLTI8ymJ6gDKTEKf3JbqXXE6P44l1Kh9uptMU4lBaXXWl0RCh1dptSG25VqsMsSoXZpJQY
        jcoDg7TnL0mKlnqXJ//SExUx3xtmoUGbJeQPs4RGQMBqCU1azaFpizkUBLMWU2jObAoFwZTJGJoAo8Bv
        NIYGjIaQZjCEuoHXIIVaJYmeS9LcQ0k6L3UlRvu6ENDAABgCo2ACTIEgmAPzYOb9mn9Og0kwBobBIOgF
        PtABVFBiMDRLnXFOrROLHtAPXoIREACvgA444aTLRb4jR8i7bx+N2mxCfBywGTbF5thkN/ACFig3GlXJ
        644SAqzcB9iJH7AzroJF2O30oUO0QERvZmbIL8tij02wezbF5tgkd6MdtADMQ5XaYh0aK7IyO+CD7Igv
        cgJOxK2YO3MG6YlCYPzgwaXWsBl2v9geNtsGWKDSYlYlNTpSY8XFKvjg4ix6wsOpzeMRv89+IDB24ABp
        bjc1opLWmBhROZtj92yWh9wM8KpUqdkVqXGAN/gAl8kXOpOTaeDaNQq0t5O2bRsFT51aEhiBwPi5czS/
        sEB9BQX0YvlycZeHy+65/yxQY7OqUqPTrnGAN/gAt6oDroZv3hQJaXaWxnbsIP3kSbFkAf/+/TQBQq9f
        i5i/rIwa0tNF7zkXt4cFaiPCVOmFI0LjAG/wAa6k/9gxkejf6WmawMvpNxpp+vRpkYzjQ5gBt3J0zx5a
        GB0Vce3yZWq0WkVygUGiZ3abKjVEhguBRRFvRgbN+Hzi0vjRo+QzGETbXn0g8BIt4kq5LSOHDxO9fUtv
        pqaoGa38UKDOEaFKFRDAX93SRu+uXSLJnKpSNwbMw2eGd++m0NwcLQQC1I9E3E6utgN/HzNPnwpx7fhx
        0RrOUwNjlU67Kv1tt2n3EKgA9cAP1/yN37hBrWjNYts68KIGd+6k/u3byWs2i5nx4+CqJy5dEneGr1yh
        WqyLQSEEyqIjVelxRJh2GwGmEHSh//yNYMh1Fgs1odRmHF6skB0yTYg1gOcwMXr9urijXbxId7DHue5g
        rzw2SpUe2qxLArdA/d494vCrri4qWp5FxTYLldltVA4q3sPrElCEf5BPMtNpCk+Zv6b8fJFjUaDS7VSl
        +2GWJQFWL129iuYGB8QcXhw/RkVREVQe46AKdxRwip9wxuVTkdNO6g8n383MP0Sla9eIPIsC1fEuVbpn
        MQuBAqOB7lvN9BjO+n68IBwFx8aoau/XVBbnpNrEaHqWFEvPPDFUE++isoQYqv82X7we/rQL5+mBzUqF
        JuOSwNPEaFVCQOMFhOhRRBg9ibRRTd4Kmq6tEReDeDUNJ09Q6YY8qkxNoCpQ8dFGavv5J5oPBsWZV5Xl
        VJWTRY/DrcIkm+WctZ4YVSowGjVWxSzoiSMcpTuoKtZBLZ9+TMGGOpGAWzDe1Um+O7fId/smTfb0iDh/
        ek01NW7bQmUuOxWjnY/Cw+iuxSREniW7WcCg3XvfmhIcwmCoBm2oS46l9m2baeL6VVoYD7xP9//3T2CM
        Ald/pZYt60X7quJcVMpziQynBxh+odlEdcvi3lXAgSJ2j2FWJ0TT82X4T5meSGqWhzryMmjgq89p/OwJ
        0v/4jfQ/f6fA2e+p78vPqC03lZrSE6guJY5qIFKB4b+rwipa3pAar0p3zSbvQwR4gw+w+/rUeGrOSqL2
        3BTqXpNBvesyaXBjNg1tzqGXm3KoD2tfXhp5V6ZSS3YSNaTFo9+xovpivCxuE5tuzvLU8yv6DoshtEiH
        iA4RHdPXUZ7elJGot+Ys0ztXpek9azP1vg3Zei/oWZepd+dl6B2It61I0dXsZL0p06NDSNzDcHW4H8TZ
        fAlTt6KKjZiFzEBQRkUyeimXOO0y3ryM9yzDoYxWyC/SE2Q4k1tzkmVvbqrctTpd9q3NlJFMHtiUIw9t
        yZVHtq6SJz9Zu37hi62W/wA3T+NiE/AZ3gAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="BtnLogin.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAB3RJTUUH3AIRFhENDo6VAQAABtRJREFU
        SEuVlHtQU1cexw/iA2lru7Xq2ta6blt3u2WUR9jKAq5sgRkeYuuqM1VqgEpFAfGFDigvH5RAICiQAIJI
        MSBoSEFQJAhBwATkIe+AQMJDIEKJQBAD5H73gun+sdM/2M/Md86dufP9fs85v7mX/Ia1tTWxsbEhjo6O
        ZN++fX/08PA45efnJwsKCpqNjIxUx8XF1XM4nBCajfRKTExMiJeXl869COzt7YmTk9P8arF79+5yNzc3
        DV0AOhB0ONLT03Hjxg0tl8utOH/+PCMmJoZYWlrq3Itg27ZtxNzcfNWOHTuKd+7cCVdXV/j7+yOBy0V2
        dg4EubkQCATIzRWCx+OVMJnM1d7e3jr3ImAwGMTMzMzUwsJixM7ODu7u7ohmXUZizCVc54QhmxeOOyls
        JF9lISw0RE0XbGcy3XTuRUCHz8ueLtJYWVnB04OJ6z/5QSKIR1dVLkaaRBhvK8Mz8S0Enzj0ytHRwWaX
        i7POvQh0BQ60Zs0Y5ji41xmi5ED01+ThZedjTHVLoOmR4FVHGcQ3WR0ee+w/Pua2W+deBLoCK1oTDrbb
        EXh4D8pSAiErSsaLpmKMy8RQy8ow3lyEgcrMqsbcqLc773N17kWgK/jY3JzRxjq5H7+wD+Nh8hnU34lE
        T1k6lLX5GG24C2WNAM8r+AW9pdcN+svTde5FMF9gtMVYj55BRMAhF/BDv8Ndjjcq04PRJIzBs+IkyEvT
        0FOSis6ixMRGYbRex/97Agatz/+29RO3b23ESf4uyAzbj3tXjy2U1OVEoDE3Gk8FbNTfZvnSInXCqAUv
        cmhlv1lnM/XIZIo+gZiQMa7+wvv/Ykp/nRd+sCVB7rZf8U46jaT4O4NPl+RzjqIkyR/i1ACIUwKUZakB
        ZvQz6cgLICj/iqBgPUF/BKHsyZIpd6KP/h/JeOoKslqXu8CuCi0pbBoj+3yPE/uPyHofZ6PehOMOuHba
        CT+f34uccDcIozyp/OjDVwtjvZfdjT1KIPrijfL+sBoVVl6ossnSljKEGsGHwarkpZum+O8RsjV3gvzr
        wTSxKJwmR6q15Pt2vO/BrzxxwM5o8tS3xog58jUSTzog5ew3SI0+05VyT2RdmHaJsNtBkP8uHb5qLR7b
        5kDqqNXcM8HLzM86JnM+bZvKXPNoJH6ZMfkr/znZkqMkhPdajynRfu1TB1FYw/RsWEQYLrlbIfwHG3B8
        HPFz6EHcrqqfvdmL9mtd1Al2G7Uqs2+CoMToDKrsoRWZYzT1I3HXhSWbhrkf/ElTYFL2MmNDIVkf30LW
        pPYZ7hJNnPWUzL7wqaUQ3gbktI3gdjoHBfP3z/NHzZ1YVHT0oXgY4Mu1cxcbptOj81M20QXFqHKC9pEz
        xm4xrs9ft/IIPfA6zyR1vvUcIWeK3jK5KY/cI3o58++HahyrnUNEG4XcAUDUM4aup+VQNhZD0SJFTdcA
        asYAYb8WIbVqeBY0cufub5biCROoP4oZqefITMO5cHRduYj20OGJey4gb4eWHtl+S/HKWjAEh8IxeFdr
        wKIL+AoKJUpANoEFXrwYgbRzAJJfAUEfhaAnk/hG2N3bJdxeg0Y/oIP1Rp3RQHc8tDI2Rm/9Q0bWhYsl
        VllyfHGjGzZCJZjlaoQ2acF7RiFvgELxEPBaCyiVShS1PkfBIJDWTeGURI1zhQ/nhsq8Jmer3EC1hgC9
        qcBAJqieZIw/+G6sP/Z9N7IxqmL6y1QZNiS2g5HVD5ciFXzpU1xupcClS9J6KMingMHBIWQ0DCK5GwtX
        GC99ip7KcMy2xEBTYKpR89d1viqwHlXfd1D9msWoHeC8e0ARTJaSTSwxNsQ9xeorTdic1g1LgRJ7Sqfg
        80SLwEYKF1soiOjBdiv6ECUdxrlmIKleht6qSLxuSQEe78XMXaPh0YQlf5dfIF/Kw8hWRRhZO5i6hfSx
        6Il/eLEYa9g1MIyswzquDH/JGIBlngq7xDP4XkLhUDUFTged09aD449Gwa5uhaIyAtPNdHj1QUznm46r
        srbEKuMM3uuLMiDyn1aS55wVZCpDnwzErCDE8PSdmZWhpVgeLoUhpxkfJPXgz3wlTPKn8E+RFralWjCl
        FJIqOnFWVAfF40vQNHMB6QFM/WKqGs0wTpB5k5WDHAOi4v3Pv2cePe8Mb/3j2Q1LA4tmll2WwiC2He8k
        92MtX4WNwhl8XkDB+L4WrvkKhBQ9AFV/GqhwglpgohpKM42XOJHlkwU7iIJF7/b30NvPJvq+mRuX+Gb+
        qH9SkEMXyZZdrJxeHllPrbwiw1tcOfUOT675JKG1Kzgjqlkj3IzJ20YYuGaS8cSVGKoEdkQRYUD62b9X
        QMh/ALOeajlRyoA9AAAAAElFTkSuQmCC
</value>
  </data>
</root>