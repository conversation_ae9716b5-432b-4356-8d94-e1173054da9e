﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictLabProfile.cs
*
* 功 能： N/A
* 类 名： MdlDictLabProfile
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:46:34   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验组合字典表
	/// </summary>
	[Serializable]
	public partial class MdlDictLabProfile
	{
		public MdlDictLabProfile()
		{}
		#region Model
		private int _profileid;
		private string _profilecode;
		private string _profilename;
		private string _hisprofilecode;
		private string _reporttitle;
		private string _englishname;
		private string _pinyin;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 组合ID，自增主键
		/// </summary>
		public int ProfileId
		{
			set{ _profileid=value;}
			get{return _profileid;}
		}
		/// <summary>
		/// 组合编码 (院内统一，如 CBC, LFP)
		/// </summary>
		public string ProfileCode
		{
			set{ _profilecode=value;}
			get{return _profilecode;}
		}
		/// <summary>
		/// 组合中文名称（如：血常规，肝功能全项）
		/// </summary>
		public string ProfileName
		{
			set{ _profilename=value;}
			get{return _profilename;}
		}
		/// <summary>
		/// HIS系统组合/医嘱编码，允许NULL，用于系统集成
		/// </summary>
		public string HisProfileCode
		{
			set{ _hisprofilecode=value;}
			get{return _hisprofilecode;}
		}
		/// <summary>
		/// 报告单标题，如果为空则默认使用ProfileName
		/// </summary>
		public string ReportTitle
		{
			set{ _reporttitle=value;}
			get{return _reporttitle;}
		}
		/// <summary>
		/// 英文名称
		/// </summary>
		public string EnglishName
		{
			set{ _englishname=value;}
			get{return _englishname;}
		}
		/// <summary>
		/// 拼音/首字母
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 组合描述，如临床意义、注意事项等
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 状态：1 = Active (启用)，0 = Inactive (停用)
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 记录创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

