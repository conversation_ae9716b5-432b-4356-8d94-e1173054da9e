﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Common.Log
{
    public class Log
    {
        /// <summary>
        /// 写日志函数
        /// </summary>
        /// <param name="strPath"></param>
        /// <param name="strContent"></param>
        public static void WriteLog(string strPath, string strContent)
        {
            if (Directory.Exists(Path.GetDirectoryName(strPath)) == false)
            {
                Directory.CreateDirectory(Path.GetDirectoryName(strPath));
            }

            FileStream fs = new FileStream(strPath, FileMode.Append);
            StreamWriter sw = new StreamWriter(fs);
            sw.WriteLine(strContent);
            sw.Close();
        }
    }
}
