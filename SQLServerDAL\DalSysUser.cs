﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysUser.cs
*
* 功 能： N/A
* 类 名： DalSysUser
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:54   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysUser
	/// </summary>
	public partial class DalSysUser : IDalSysUser
	{
		public DalSysUser()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("UserId", "SysUser");
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int UserId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from SysUser");
			strSql.Append(" where UserId=@UserId");
			SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.Int,4)
			};
			parameters[0].Value = UserId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlSysUser model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into SysUser(");
			strSql.Append("LoginName,PasswordHash,UserName,DoctorId,RoleCode,IsLockedOut,LastLoginAt,Status,CreatedAt,UpdatedAt,CreatedBy,UpdatedBy)");
			strSql.Append(" values (");
			strSql.Append("@LoginName,@PasswordHash,@UserName,@DoctorId,@RoleCode,@IsLockedOut,@LastLoginAt,@Status,@CreatedAt,@UpdatedAt,@CreatedBy,@UpdatedBy)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@LoginName", SqlDbType.NVarChar,30),
					new SqlParameter("@PasswordHash", SqlDbType.NVarChar,255),
					new SqlParameter("@UserName", SqlDbType.NVarChar,100),
					new SqlParameter("@DoctorId", SqlDbType.Int,4),
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@IsLockedOut", SqlDbType.Bit,1),
					new SqlParameter("@LastLoginAt", SqlDbType.DateTime),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@CreatedBy", SqlDbType.Int,4),
					new SqlParameter("@UpdatedBy", SqlDbType.Int,4)};
			parameters[0].Value = model.LoginName;
			parameters[1].Value = model.PasswordHash;
			parameters[2].Value = model.UserName;
			parameters[3].Value = Common.Tools.IsValueNull(model.DoctorId);
			parameters[4].Value = model.RoleCode;
			parameters[5].Value = model.IsLockedOut;
			parameters[6].Value = Common.Tools.IsValueNull(model.LastLoginAt);
			parameters[7].Value = model.Status;
			parameters[8].Value = model.CreatedAt;
			parameters[9].Value = model.UpdatedAt;
			parameters[10].Value = model.CreatedBy;
			parameters[11].Value = model.UpdatedBy;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysUser model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update SysUser set ");
			strSql.Append("LoginName=@LoginName,");
			strSql.Append("PasswordHash=@PasswordHash,");
			strSql.Append("UserName=@UserName,");
			strSql.Append("DoctorId=@DoctorId,");
			strSql.Append("RoleCode=@RoleCode,");
			strSql.Append("IsLockedOut=@IsLockedOut,");
			strSql.Append("LastLoginAt=@LastLoginAt,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt,");
			strSql.Append("CreatedBy=@CreatedBy,");
			strSql.Append("UpdatedBy=@UpdatedBy");
			strSql.Append(" where UserId=@UserId");
			SqlParameter[] parameters = {
					new SqlParameter("@LoginName", SqlDbType.NVarChar,30),
					new SqlParameter("@PasswordHash", SqlDbType.NVarChar,255),
					new SqlParameter("@UserName", SqlDbType.NVarChar,100),
					new SqlParameter("@DoctorId", SqlDbType.Int,4),
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@IsLockedOut", SqlDbType.Bit,1),
					new SqlParameter("@LastLoginAt", SqlDbType.DateTime),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@CreatedBy", SqlDbType.Int,4),
					new SqlParameter("@UpdatedBy", SqlDbType.Int,4),
					new SqlParameter("@UserId", SqlDbType.Int,4)};
			parameters[0].Value = model.LoginName;
			parameters[1].Value = model.PasswordHash;
			parameters[2].Value = model.UserName;
			parameters[3].Value = Common.Tools.IsValueNull(model.DoctorId);
			parameters[4].Value = model.RoleCode;
			parameters[5].Value = model.IsLockedOut;
			parameters[6].Value = Common.Tools.IsValueNull(model.LastLoginAt);
			parameters[7].Value = model.Status;
			parameters[8].Value = model.CreatedAt;
			parameters[9].Value = model.UpdatedAt;
			parameters[10].Value = model.CreatedBy;
			parameters[11].Value = model.UpdatedBy;
			parameters[12].Value = model.UserId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int UserId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from SysUser ");
			strSql.Append(" where UserId=@UserId");
			SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.Int,4)
			};
			parameters[0].Value = UserId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string UserIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from SysUser ");
			strSql.Append(" where UserId in (" + UserIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysUser GetModel(int UserId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 UserId,LoginName,PasswordHash,UserName,DoctorId,RoleCode,IsLockedOut,LastLoginAt,Status,CreatedAt,UpdatedAt,CreatedBy,UpdatedBy from SysUser ");
			strSql.Append(" where UserId=@UserId");
			SqlParameter[] parameters = {
					new SqlParameter("@UserId", SqlDbType.Int,4)
			};
			parameters[0].Value = UserId;

			Model.MdlSysUser model = new Model.MdlSysUser();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysUser DataRowToModel(DataRow row)
		{
			Model.MdlSysUser model = new Model.MdlSysUser();
			if (row != null)
			{
				if (row["UserId"] != null && row["UserId"].ToString() != "")
				{
					model.UserId = int.Parse(row["UserId"].ToString());
				}
				if (row["LoginName"] != null)
				{
					model.LoginName = row["LoginName"].ToString();
				}
				if (row["PasswordHash"] != null)
				{
					model.PasswordHash = row["PasswordHash"].ToString();
				}
				if (row["UserName"] != null)
				{
					model.UserName = row["UserName"].ToString();
				}
				if (row["DoctorId"] != null && row["DoctorId"].ToString() != "")
				{
					model.DoctorId = int.Parse(row["DoctorId"].ToString());
				}
				if (row["RoleCode"] != null)
				{
					model.RoleCode = row["RoleCode"].ToString();
				}
				if (row["IsLockedOut"] != null && row["IsLockedOut"].ToString() != "")
				{
					if ((row["IsLockedOut"].ToString() == "1") || (row["IsLockedOut"].ToString().ToLower() == "true"))
					{
						model.IsLockedOut = true;
					}
					else
					{
						model.IsLockedOut = false;
					}
				}
				if (row["LastLoginAt"] != null && row["LastLoginAt"].ToString() != "")
				{
					model.LastLoginAt = DateTime.Parse(row["LastLoginAt"].ToString());
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
				if (row["CreatedBy"] != null && row["CreatedBy"].ToString() != "")
				{
					model.CreatedBy = int.Parse(row["CreatedBy"].ToString());
				}
				if (row["UpdatedBy"] != null && row["UpdatedBy"].ToString() != "")
				{
					model.UpdatedBy = int.Parse(row["UpdatedBy"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select SysUser.*,RoleName,DoctorName ");
			strSql.Append(" FROM SysUser ");
			strSql.Append(" Left join DictDoctor on SysUser.DoctorId = DictDoctor.DoctorId");
			strSql.Append(" Inner join SysRole on SysUser.RoleCode = SysRole.RoleCode");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" UserId,LoginName,PasswordHash,UserName,DoctorId,RoleCode,IsLockedOut,LastLoginAt,Status,CreatedAt,UpdatedAt,CreatedBy,UpdatedBy ");
			strSql.Append(" FROM SysUser ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM SysUser ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.UserId desc");
			}
			strSql.Append(")AS Row, T.*  from SysUser T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysUser";
			parameters[1].Value = "UserId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		public Model.MdlSysUser GetModelByLoginName(string LoginName)
		{
			string sql = "select * from SysUser where LoginName=@LoginName";
			SqlParameter[] parameters = {
				new SqlParameter("@LoginName", SqlDbType.NVarChar, 30)
					};
			parameters[0].Value = LoginName;
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(sql, parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}
		#endregion  ExtensionMethod
	}
}

