﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabOrder.cs
*
* 功 能： N/A
* 类 名： DalLabOrder
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:53:37   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabOrder
	/// </summary>
	public partial class DalLabOrder : IDalLabOrder
	{
		public DalLabOrder()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long OrderId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabOrder");
			strSql.Append(" where OrderId=@OrderId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderId", SqlDbType.BigInt)
			};
			parameters[0].Value = OrderId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabOrder model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabOrder(");
			strSql.Append("HisOrderCode,VisitId,PatientId,PatientName,PatientSex,PatientBirthDate,PatientAge,PatientAgeUnit,BedNo,VisitTypeId,RequestingDeptId,RequestingDoctorId,OrderStatus,Priority,ClinicalDiagnosis,ReportComment,OrderDateTime,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName)");
			strSql.Append(" values (");
			strSql.Append("@HisOrderCode,@VisitId,@PatientId,@PatientName,@PatientSex,@PatientBirthDate,@PatientAge,@PatientAgeUnit,@BedNo,@VisitTypeId,@RequestingDeptId,@RequestingDoctorId,@OrderStatus,@Priority,@ClinicalDiagnosis,@ReportComment,@OrderDateTime,@CreatedAt,@CreatedById,@CreatedByName,@UpdatedAt,@UpdatedById,@UpdatedByName)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@HisOrderCode", SqlDbType.NVarChar,50),
					new SqlParameter("@VisitId", SqlDbType.NVarChar,50),
					new SqlParameter("@PatientId", SqlDbType.NVarChar,50),
					new SqlParameter("@PatientName", SqlDbType.NVarChar,100),
					new SqlParameter("@PatientSex", SqlDbType.Char,2),
					new SqlParameter("@PatientBirthDate", SqlDbType.Date),
					new SqlParameter("@PatientAge", SqlDbType.Int,4),
					new SqlParameter("@PatientAgeUnit", SqlDbType.Char,1),
					new SqlParameter("@BedNo", SqlDbType.NVarChar,20),
					new SqlParameter("@VisitTypeId", SqlDbType.Int,4),
					new SqlParameter("@RequestingDeptId", SqlDbType.Int,4),
					new SqlParameter("@RequestingDoctorId", SqlDbType.Int,4),
					new SqlParameter("@OrderStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@Priority", SqlDbType.TinyInt,1),
					new SqlParameter("@ClinicalDiagnosis", SqlDbType.NVarChar,500),
					new SqlParameter("@ReportComment", SqlDbType.NVarChar,500),
					new SqlParameter("@OrderDateTime", SqlDbType.DateTime),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@CreatedById", SqlDbType.Int,4),
					new SqlParameter("@CreatedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedById", SqlDbType.Int,4),
					new SqlParameter("@UpdatedByName", SqlDbType.NVarChar,100)};
			parameters[0].Value = Common.Tools.IsValueNull(model.HisOrderCode);
			parameters[1].Value = model.VisitId;
			parameters[2].Value = model.PatientId;
			parameters[3].Value = model.PatientName;
			parameters[4].Value = model.PatientSex;
			parameters[5].Value = model.PatientBirthDate;
			parameters[6].Value = model.PatientAge;
			parameters[7].Value = model.PatientAgeUnit;
			parameters[8].Value = model.BedNo;
			parameters[9].Value = model.VisitTypeId;
			parameters[10].Value = model.RequestingDeptId;
			parameters[11].Value = model.RequestingDoctorId;
			parameters[12].Value = model.OrderStatus;
			parameters[13].Value = model.Priority;
			parameters[14].Value = Common.Tools.IsValueNull(model.ClinicalDiagnosis);
			parameters[15].Value = Common.Tools.IsValueNull(model.ReportComment);
			parameters[16].Value = model.OrderDateTime;
			parameters[17].Value = model.CreatedAt;
			parameters[18].Value = model.CreatedById;
			parameters[19].Value = model.CreatedByName;
			parameters[20].Value = model.UpdatedAt;
			parameters[21].Value = model.UpdatedById;
			parameters[22].Value = model.UpdatedByName;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabOrder model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabOrder set ");
			strSql.Append("HisOrderCode=@HisOrderCode,");
			strSql.Append("VisitId=@VisitId,");
			strSql.Append("PatientId=@PatientId,");
			strSql.Append("PatientName=@PatientName,");
			strSql.Append("PatientSex=@PatientSex,");
			strSql.Append("PatientBirthDate=@PatientBirthDate,");
			strSql.Append("PatientAge=@PatientAge,");
			strSql.Append("PatientAgeUnit=@PatientAgeUnit,");
			strSql.Append("BedNo=@BedNo,");
			strSql.Append("VisitTypeId=@VisitTypeId,");
			strSql.Append("RequestingDeptId=@RequestingDeptId,");
			strSql.Append("RequestingDoctorId=@RequestingDoctorId,");
			strSql.Append("OrderStatus=@OrderStatus,");
			strSql.Append("Priority=@Priority,");
			strSql.Append("ClinicalDiagnosis=@ClinicalDiagnosis,");
			strSql.Append("ReportComment=@ReportComment,");
			strSql.Append("OrderDateTime=@OrderDateTime,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("CreatedById=@CreatedById,");
			strSql.Append("CreatedByName=@CreatedByName,");
			strSql.Append("UpdatedAt=@UpdatedAt,");
			strSql.Append("UpdatedById=@UpdatedById,");
			strSql.Append("UpdatedByName=@UpdatedByName");
			strSql.Append(" where OrderId=@OrderId");
			SqlParameter[] parameters = {
					new SqlParameter("@HisOrderCode", SqlDbType.NVarChar,50),
					new SqlParameter("@VisitId", SqlDbType.NVarChar,50),
					new SqlParameter("@PatientId", SqlDbType.NVarChar,50),
					new SqlParameter("@PatientName", SqlDbType.NVarChar,100),
					new SqlParameter("@PatientSex", SqlDbType.Char,2),
					new SqlParameter("@PatientBirthDate", SqlDbType.Date),
					new SqlParameter("@PatientAge", SqlDbType.Int,4),
					new SqlParameter("@PatientAgeUnit", SqlDbType.Char,1),
					new SqlParameter("@BedNo", SqlDbType.NVarChar,20),
					new SqlParameter("@VisitTypeId", SqlDbType.Int,4),
					new SqlParameter("@RequestingDeptId", SqlDbType.Int,4),
					new SqlParameter("@RequestingDoctorId", SqlDbType.Int,4),
					new SqlParameter("@OrderStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@Priority", SqlDbType.TinyInt,1),
					new SqlParameter("@ClinicalDiagnosis", SqlDbType.NVarChar,500),
					new SqlParameter("@ReportComment", SqlDbType.NVarChar,500),
					new SqlParameter("@OrderDateTime", SqlDbType.DateTime),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@CreatedById", SqlDbType.Int,4),
					new SqlParameter("@CreatedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedById", SqlDbType.Int,4),
					new SqlParameter("@UpdatedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@OrderId", SqlDbType.BigInt,8)};
			parameters[0].Value = Common.Tools.IsValueNull(model.HisOrderCode);
			parameters[1].Value = model.VisitId;
			parameters[2].Value = model.PatientId;
			parameters[3].Value = model.PatientName;
			parameters[4].Value = model.PatientSex;
			parameters[5].Value = model.PatientBirthDate;
			parameters[6].Value = model.PatientAge;
			parameters[7].Value = model.PatientAgeUnit;
			parameters[8].Value = model.BedNo;
			parameters[9].Value = model.VisitTypeId;
			parameters[10].Value = model.RequestingDeptId;
			parameters[11].Value = model.RequestingDoctorId;
			parameters[12].Value = model.OrderStatus;
			parameters[13].Value = model.Priority;
			parameters[14].Value = Common.Tools.IsValueNull(model.ClinicalDiagnosis);
			parameters[15].Value = Common.Tools.IsValueNull(model.ReportComment);
			parameters[16].Value = model.OrderDateTime;
			parameters[17].Value = model.CreatedAt;
			parameters[18].Value = model.CreatedById;
			parameters[19].Value = model.CreatedByName;
			parameters[20].Value = model.UpdatedAt;
			parameters[21].Value = model.UpdatedById;
			parameters[22].Value = model.UpdatedByName;
			parameters[23].Value = model.OrderId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long OrderId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabOrder ");
			strSql.Append(" where OrderId=@OrderId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderId", SqlDbType.BigInt)
			};
			parameters[0].Value = OrderId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string OrderIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabOrder ");
			strSql.Append(" where OrderId in (" + OrderIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabOrder GetModel(long OrderId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 OrderId,HisOrderCode,VisitId,PatientId,PatientName,PatientSex,PatientBirthDate,PatientAge,PatientAgeUnit,BedNo,VisitTypeId,RequestingDeptId,RequestingDoctorId,OrderStatus,Priority,ClinicalDiagnosis,ReportComment,OrderDateTime,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName from LabOrder ");
			strSql.Append(" where OrderId=@OrderId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderId", SqlDbType.BigInt)
			};
			parameters[0].Value = OrderId;

			Model.MdlLabOrder model = new Model.MdlLabOrder();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabOrder DataRowToModel(DataRow row)
		{
			Model.MdlLabOrder model = new Model.MdlLabOrder();
			if (row != null)
			{
				if (row["OrderId"] != null && row["OrderId"].ToString() != "")
				{
					model.OrderId = long.Parse(row["OrderId"].ToString());
				}
				if (row["HisOrderCode"] != null)
				{
					model.HisOrderCode = row["HisOrderCode"].ToString();
				}
				if (row["VisitId"] != null)
				{
					model.VisitId = row["VisitId"].ToString();
				}
				if (row["PatientId"] != null)
				{
					model.PatientId = row["PatientId"].ToString();
				}
				if (row["PatientName"] != null)
				{
					model.PatientName = row["PatientName"].ToString();
				}
				if (row["PatientSex"] != null)
				{
					model.PatientSex = row["PatientSex"].ToString();
				}
				if (row["PatientBirthDate"] != null && row["PatientBirthDate"].ToString() != "")
				{
					model.PatientBirthDate = DateTime.Parse(row["PatientBirthDate"].ToString());
				}
				if (row["PatientAge"] != null && row["PatientAge"].ToString() != "")
				{
					model.PatientAge = int.Parse(row["PatientAge"].ToString());
				}
				if (row["PatientAgeUnit"] != null)
				{
					model.PatientAgeUnit = row["PatientAgeUnit"].ToString();
				}
				if (row["BedNo"] != null)
				{
					model.BedNo = row["BedNo"].ToString();
				}
				if (row["VisitTypeId"] != null && row["VisitTypeId"].ToString() != "")
				{
					model.VisitTypeId = int.Parse(row["VisitTypeId"].ToString());
				}
				if (row["RequestingDeptId"] != null && row["RequestingDeptId"].ToString() != "")
				{
					model.RequestingDeptId = int.Parse(row["RequestingDeptId"].ToString());
				}
				if (row["RequestingDoctorId"] != null && row["RequestingDoctorId"].ToString() != "")
				{
					model.RequestingDoctorId = int.Parse(row["RequestingDoctorId"].ToString());
				}
				if (row["OrderStatus"] != null && row["OrderStatus"].ToString() != "")
				{
					model.OrderStatus = int.Parse(row["OrderStatus"].ToString());
				}
				if (row["Priority"] != null && row["Priority"].ToString() != "")
				{
					model.Priority = int.Parse(row["Priority"].ToString());
				}
				if (row["ClinicalDiagnosis"] != null)
				{
					model.ClinicalDiagnosis = row["ClinicalDiagnosis"].ToString();
				}
				if (row["ReportComment"] != null)
				{
					model.ReportComment = row["ReportComment"].ToString();
				}
				if (row["OrderDateTime"] != null && row["OrderDateTime"].ToString() != "")
				{
					model.OrderDateTime = DateTime.Parse(row["OrderDateTime"].ToString());
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["CreatedById"] != null && row["CreatedById"].ToString() != "")
				{
					model.CreatedById = int.Parse(row["CreatedById"].ToString());
				}
				if (row["CreatedByName"] != null)
				{
					model.CreatedByName = row["CreatedByName"].ToString();
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
				if (row["UpdatedById"] != null && row["UpdatedById"].ToString() != "")
				{
					model.UpdatedById = int.Parse(row["UpdatedById"].ToString());
				}
				if (row["UpdatedByName"] != null)
				{
					model.UpdatedByName = row["UpdatedByName"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select OrderId,HisOrderCode,VisitId,PatientId,PatientName,PatientSex,PatientBirthDate,PatientAge,PatientAgeUnit,BedNo,VisitTypeId,RequestingDeptId,RequestingDoctorId,OrderStatus,Priority,ClinicalDiagnosis,ReportComment,OrderDateTime,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName ");
			strSql.Append(" FROM LabOrder ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" OrderId,HisOrderCode,VisitId,PatientId,PatientName,PatientSex,PatientBirthDate,PatientAge,PatientAgeUnit,BedNo,VisitTypeId,RequestingDeptId,RequestingDoctorId,OrderStatus,Priority,ClinicalDiagnosis,ReportComment,OrderDateTime,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName ");
			strSql.Append(" FROM LabOrder ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabOrder ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.OrderId desc");
			}
			strSql.Append(")AS Row, T.*  from LabOrder T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabOrder";
			parameters[1].Value = "OrderId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

