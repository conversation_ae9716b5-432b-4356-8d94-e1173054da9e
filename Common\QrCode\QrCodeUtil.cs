﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using ZXing;
using ZXing.QrCode;
using ZXing.QrCode.Internal;

namespace Common.QrCode
{
   public class QrCodeUtil
    {
        /// <summary>
        /// 将文本字符串转化为二维码图片
        /// </summary>
        /// <param name="codeContent">打印条码对应的字符串</param>
        /// <param name="width"></param>
        /// <param name="height"></param>
        /// <param name="barcodeFormat">条码格式：CODE_128代表一维条码，QR_CODE代表二维码</param>
        /// <param name="margin"></param>
        /// <returns></returns>
        public static Bitmap GeneateQrCode(string codeContent, int width, int height, BarcodeFormat barcodeFormat, string errorCorrectionLevel, int margin = 1)
        {
            // 1.设置QR二维码的规格
            QrCodeEncodingOptions qrEncodeOption = new QrCodeEncodingOptions();
            qrEncodeOption.ErrorCorrection = GetErrorCorrectionLevel(errorCorrectionLevel);
            qrEncodeOption.DisableECI = true;
            qrEncodeOption.CharacterSet = "UTF-8"; // 设置编码格式，否则读取'中文'乱码
            qrEncodeOption.Height = height;
            qrEncodeOption.Width = width;
            qrEncodeOption.Margin = margin; // 设置周围空白边距
            qrEncodeOption.PureBarcode = true;
            //System.Windows.Forms.MessageBox.Show("" + qrEncodeOption.ErrorCorrection.Name + "...." + qrEncodeOption.ErrorCorrection.Bits);
            // 2.生成条形码图片
            BarcodeWriter wr = new BarcodeWriter();
            wr.Format = barcodeFormat; // 二维码 BarcodeFormat.QR_CODE
            wr.Options = qrEncodeOption;
            Bitmap img = wr.Write(codeContent);
            return img;
        }


        /// <summary>
        /// 获取纠错级别
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private static ErrorCorrectionLevel GetErrorCorrectionLevel(string levelName)
        {
            switch (levelName)
            {
                case "L"://bits=1
                    return ErrorCorrectionLevel.L;
                case "M"://bits=0
                    return ErrorCorrectionLevel.M;
                case "Q"://bits=3
                    return ErrorCorrectionLevel.Q;
                case "H"://bits=2
                    return ErrorCorrectionLevel.H;
                default:
                    return ErrorCorrectionLevel.M;
            }
        }
    }
}
