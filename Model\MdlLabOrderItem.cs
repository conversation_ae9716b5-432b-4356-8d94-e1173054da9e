﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabOrderItem.cs
*
* 功 能： N/A
* 类 名： MdlLabOrderItem
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:53:37   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验医嘱明细表
	/// </summary>
	[Serializable]
	public partial class MdlLabOrderItem
	{
		public MdlLabOrderItem()
		{}
		#region Model
		private long _orderitemid;
		private long _specimenid;
		private int _labitemid;
		private int? _profileid;
		private int _itemstatus=0;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 检验项目明细ID，自增主键
		/// </summary>
		public long OrderItemId
		{
			set{ _orderitemid=value;}
			get{return _orderitemid;}
		}
		/// <summary>
		/// 关联的标本ID
		/// </summary>
		public long SpecimenId
		{
			set{ _specimenid=value;}
			get{return _specimenid;}
		}
		/// <summary>
		/// 关联的LIS项目ID
		/// </summary>
		public int LabItemId
		{
			set{ _labitemid=value;}
			get{return _labitemid;}
		}
		/// <summary>
		/// 关联检验组合ID
		/// </summary>
		public int? ProfileId
		{
			set{ _profileid=value;}
			get{return _profileid;}
		}
		/// <summary>
		/// 项目状态：0=已申请 1=已上机/检测中 2=已出结果 3=已审核 9=已取消
		/// </summary>
		public int ItemStatus
		{
			set{ _itemstatus=value;}
			get{return _itemstatus;}
		}
		/// <summary>
		/// 记录创建时间(UTC)
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 记录更新时间(UTC)
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

