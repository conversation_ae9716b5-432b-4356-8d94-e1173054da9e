﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Common
{
    public class MathFormula
    {
        /// <summary>
        /// 计算两个数相乘
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        /// <returns></returns>
        public static decimal Multiply(object value1, object value2)
        {
            if (value1 == null || value2 == null|| value1 == DBNull.Value || value2 == DBNull.Value) return 0;
            return Convert.ToDecimal(value1) * Convert.ToDecimal(value2);
        }


        public static decimal Multiply(string value1, string value2)
        {
            if (value1 == null || value2 == null) return 0;

            return Convert.ToDecimal(value1) * Convert.ToDecimal(value2);
        }

        /// <summary>
        /// 计算两个数相加
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        /// <returns></returns>
        public static decimal Plus(object value1, object value2)
        {
            if (value1 == null || value1 == DBNull.Value)
            {
                value1 = 0;
            }
            if (value2 == null || value2 == DBNull.Value)
            {
                value2 = 0;
            }
            return (decimal)value1 + (decimal)value2;
        }


        public static decimal Plus(string value1, string value2)
        {
            if (value1 == null)
            {
                value1 = "0";
            }
            if (value2 == null)
            {
                value2 = "0";
            }
            return Convert.ToDecimal(value1) + Convert.ToDecimal(value2);
        }
        /// <summary>
        /// 计算两个数相减
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        /// <returns></returns>
        public static decimal Minus(string value1, string value2)
        {
            if (value1 == null)
            {
                value1 = "0";
            }
            if (value2 == null)
            {
                value2 = "0";
            }
            return Convert.ToDecimal(value1) - Convert.ToDecimal(value2);
        }

        /// <summary>
        /// 两个数相除
        /// </summary>
        /// <param name="value1"></param>
        /// <param name="value2"></param>
        /// <returns></returns>
        public static decimal Divide(object value1, object value2)
        {
            if (value1 == null || value2 == null || value1 == DBNull.Value || value2 == DBNull.Value) return 0;
            if (Convert.ToDecimal(value2) == 0)
            {
                MessageBox.Show("被除数不能为0", "警告", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return 0;
            }
            return Convert.ToDecimal(value1) / Convert.ToDecimal(value2);
        }
    }
}
