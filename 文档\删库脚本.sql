-- #####################################################################
-- # 警告：此脚本将删除当前数据库中的所有表。                         #
-- # 在运行之前，请务必备份数据库，并确保您在正确的数据库上下文中。   #
-- #####################################################################

-- 确保在目标数据库下执行此脚本
 USE lis;
 GO

BEGIN TRANSACTION; -- 使用事务包裹，以便在确认无误前可以回滚

-- --------------------------------------------------------
-- 第一步：删除所有外键约束
-- --------------------------------------------------------
PRINT '--- 开始删除外键约束 ---';

DECLARE @sql_foreign_key NVARCHAR(MAX);

-- 声明一个游标来获取所有外键的删除语句
DECLARE cur_foreign_keys CURSOR FOR
SELECT 
    'ALTER TABLE [' + SCHEMA_NAME(t.schema_id) + '].[' + t.name + '] DROP CONSTRAINT [' + fk.name + ']'
FROM 
    sys.foreign_keys AS fk
INNER JOIN 
    sys.tables AS t ON fk.parent_object_id = t.object_id
WHERE 
    t.is_ms_shipped = 0;

OPEN cur_foreign_keys;
FETCH NEXT FROM cur_foreign_keys INTO @sql_foreign_key;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '正在执行: ' + @sql_foreign_key;
    EXEC sp_executesql @sql_foreign_key;
    FETCH NEXT FROM cur_foreign_keys INTO @sql_foreign_key;
END

CLOSE cur_foreign_keys;
DEALLOCATE cur_foreign_keys;

PRINT '--- 所有外键约束已删除 ---';
GO

-- --------------------------------------------------------
-- 第二步：删除所有用户表
-- --------------------------------------------------------
PRINT '--- 开始删除所有表 ---';

DECLARE @sql_table NVARCHAR(MAX);

-- 声明一个游标来获取所有用户表的删除语句
DECLARE cur_tables CURSOR FOR
SELECT 
    'DROP TABLE [' + SCHEMA_NAME(schema_id) + '].[' + name + ']'
FROM 
    sys.tables
WHERE 
    type = 'U' -- 'U' 代表用户表
    AND is_ms_shipped = 0;

OPEN cur_tables;
FETCH NEXT FROM cur_tables INTO @sql_table;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '正在执行: ' + @sql_table;
    EXEC sp_executesql @sql_table;
    FETCH NEXT FROM cur_tables INTO @sql_table;
END

CLOSE cur_tables;
DEALLOCATE cur_tables;

PRINT '--- 所有用户表已删除 ---';
GO


-- #####################################################################
-- # 检查点：                                                          #
-- # 如果到这里一切看起来都正确，并且你确认要永久删除这些表，          #
-- # 就把下面的 ROLLBACK 注释掉，然后执行 COMMIT。                     #
-- # 如果你不确定，或者只是想测试，直接执行 ROLLBACK 即可撤销所有操作。#
-- #####################################################################

COMMIT TRANSACTION; -- 确认无误后，执行此行以提交更改
-- ROLLBACK TRANSACTION; -- 如果不确定或只是测试，执行此行以撤销所有操作

PRINT '--- 操作完成 ---';