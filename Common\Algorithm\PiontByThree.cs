﻿/*********************************************************************************** 
*        文 件 名 :PiontByThree 
*        功能描述 :  
*        生成日期 :2017/3/24 12:14:00 
*        作   者 :董铮
*        机 器 名 :2013-20160817SN
*        All rights reserved 
************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common
{
    public class PiontByThree
    {
        /// <summary>  
        /// 三点绝对定位  
        /// </summary>  
        public static ThreePoint GetPiontByThree(ThreePoint p1, ThreePoint p2, ThreePoint p3)
        {
            /* Math.Pow(y1-Y)+Math.Pow(X-x1)=Math.Pow(D1) 
             * Math.Pow(y2-Y)+Math.Pow(X-x2)=Math.Pow(D2) 
             * Math.Pow(y3-Y)+Math.Pow(X-x3)=Math.Pow(D3) 
             * 1-3.2-3解得： 
             * 2 * (p1.X - p3.X)x + 2 * (p1.Y - p3.Y)y = Math.Pow(p1.X, 2) - Math.Pow(p3.X, 2) + Math.Pow(p1.Y, 2) - Math.Pow(p3.Y, 2) + Math.Pow(p3.Distance, 2) - Math.Pow(p1.Distance, 2); 
             * 2 * (p2.X - p3.X)x + 2 * (p2.Y - p3.Y)y = Math.Pow(p2.X, 2) - Math.Pow(p3.X, 2) + Math.Pow(p2.Y, 2) - Math.Pow(p3.Y, 2) + Math.Pow(p3.Distance, 2) - Math.Pow(p2.Distance, 2); 
             * 简化： 
             * 2Ax+2By=C 
             * 2Dx+2Ey=F 
             * 简化： 
             * x=(BF-EC)/(2BD-2AE) 
             * y=(AF-DC)/(2AE-2BD) 
             */
            var A = p1.X - p3.X;
            var B = p1.Y - p3.Y;
            var C = Math.Pow(p1.X, 2) - Math.Pow(p3.X, 2) + Math.Pow(p1.Y, 2) - Math.Pow(p3.Y, 2) + Math.Pow(p3.Distance, 2) - Math.Pow(p1.Distance, 2);
            var D = p2.X - p3.X;
            var E = p2.Y - p3.Y;
            var F = Math.Pow(p2.X, 2) - Math.Pow(p3.X, 2) + Math.Pow(p2.Y, 2) - Math.Pow(p3.Y, 2) + Math.Pow(p3.Distance, 2) - Math.Pow(p2.Distance, 2);

            var x = (B * F - E * C) / (2 * B * D - 2 * A * E);
            var y = (A * F - D * C) / (2 * A * E - 2 * B * D);

            ThreePoint P = new ThreePoint() { X = x, Y = y, Distance = 0 };
            return P;
        }
    }
}
