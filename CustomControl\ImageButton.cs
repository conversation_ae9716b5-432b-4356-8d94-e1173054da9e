﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public class ImageButton : Button
    {
        private Image _backgroundImage;
        private Image _mouseDownImage;
        private Image _mouseEnterImage;
        private Image _disabledImage;
        private bool isBackupBgImage = false;
        private bool isBgImageChange = false;

        public ImageButton()
        {
            this.BackgroundImageLayout = ImageLayout.Stretch;
            this.BackColor = Color.Transparent;
            this.FlatStyle = FlatStyle.Flat;
            this.FlatAppearance.BorderSize = 0;
            this.FlatAppearance.MouseDownBackColor = Color.Transparent;
            this.FlatAppearance.MouseOverBackColor = Color.Transparent;
            this.Cursor = Cursors.Hand;
        }

        #region Property
        [Category("Custom"), DefaultValue(null), Description("鼠标按下时显示图片")]
        public Image MouseDownImage
        {
            set { _mouseDownImage = value; }
            get { return _mouseDownImage; }
        }


        [Category("Custom"), DefaultValue(null), Description("鼠标移进时显示图片")]
        public Image MouseEnterImage
        {
            set { _mouseEnterImage = value; }
            get { return _mouseEnterImage; }
        }

        [Category("Custom"), DefaultValue(null), Description("按钮不可用时显示图片")]
        public Image DisabledImage
        {
            set { _disabledImage = value; }
            get { return _disabledImage; }
        }
        #endregion

        #region override events
        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);

            if (this.Enabled)
            {
                ResetBackgroundImage();
            }
            else
            {
                if (_disabledImage != null)
                {
                    ChangeBackgroundImage(_disabledImage);
                }
            }
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            base.OnMouseEnter(e);
            if (_mouseEnterImage != null)
            {
                ChangeBackgroundImage(_mouseEnterImage);
            }
        }


        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnLeave(e);
            ResetBackgroundImage();
        }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            base.OnMouseDown(mevent);
            if (mevent.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (_mouseDownImage != null)
                {
                    ChangeBackgroundImage(_mouseDownImage);
                }
            }
        }

        protected override void OnMouseUp(MouseEventArgs mevent)
        {
            base.OnMouseUp(mevent);
            if (mevent.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (_mouseEnterImage != null)
                {
                    this.BackgroundImage = _mouseEnterImage;
                }
                else
                {
                    ResetBackgroundImage();
                }
            }
        }

        protected override bool ShowFocusCues
        {
            get
            {
                return false;
            }
        }

        #endregion

        private void ChangeBackgroundImage(Image image)
        {
            if (!isBackupBgImage)
            {
                _backgroundImage = this.BackgroundImage;
            }
            isBackupBgImage = true;
            isBgImageChange = true;
            this.BackgroundImage = image;
        }

        private bool ResetBackgroundImage()
        {
            if (isBgImageChange)
            {
                this.BackgroundImage = _backgroundImage;
                isBgImageChange = false;
                return true;
            }
            return false;
        }

    }
}
