using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictLabInstrument1 : Common.BaseForm.BaseDict1
    {
        BLL.BllLabInstrument _bllLabInstrument = new BllLabInstrument();

        public DictLabInstrument1()
        {
            InitializeComponent();
        }

        private void DictLabInstrument1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "InstrCode", 120, "中", "", false);
            myGrid1.Init_Column("名称", "InstrName", 250, "左", "", false);
            myGrid1.Init_Column("厂商", "Vendor", 120, "左", "", false);
            myGrid1.Init_Column("型号", "Model", 120, "左", "", false);
            myGrid1.Init_Column("序列号", "SerialNo", 120, "左", "", false);
            myGrid1.Init_Column("位置", "Location", 200, "左", "", false);
            myGrid1.Init_Column("通讯类型", "ConnTypeName", 100, "中", "", false);
            myGrid1.Init_Column("协议类型", "ProtocolTypeName", 100, "中", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllLabInstrument.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["InstrumentId"] };
            base.MyTable.Columns["InstrumentId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictLabInstrument2 vform = new DictLabInstrument2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除仪器：" + this.myGrid1.Columns["InstrName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllLabInstrument.Delete(int.Parse(base.MyRow["InstrumentId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("InstrCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("InstrName", new Common.MdlExcel(15, "", "名称", true));
            colconfig.Add("Vendor", new Common.MdlExcel(12, "", "厂商", true));
            colconfig.Add("Model", new Common.MdlExcel(12, "", "型号", true));
            colconfig.Add("SerialNo", new Common.MdlExcel(12, "", "序列号", true));
            colconfig.Add("Location", new Common.MdlExcel(12, "", "位置", true));
            colconfig.Add("ConnType", new Common.MdlExcel(10, "", "通讯类型", true));
            colconfig.Add("ProtocolType", new Common.MdlExcel(10, "", "协议类型", true));
            Common.ExcelHelper.Export(base.MyTable, "仪器信息字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlLabInstrument mdlLabInstrument = new MdlLabInstrument();
                    if (row["名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlLabInstrument.InstrCode = row["编码"] + "";
                    mdlLabInstrument.InstrName = row["名称"] + "";
                    mdlLabInstrument.Vendor = row["厂商"] + "";
                    mdlLabInstrument.Model = row["型号"] + "";
                    mdlLabInstrument.SerialNo = row["序列号"] + "";
                    mdlLabInstrument.Location = row["位置"] + "";
                    if (row["通讯类型"] != null && row["通讯类型"].ToString().Trim() != "")
                    {
                        int connType;
                        if (int.TryParse(row["通讯类型"].ToString(), out connType))
                        {
                            mdlLabInstrument.ConnType = connType;
                        }
                    }
                    if (row["协议类型"] != null && row["协议类型"].ToString().Trim() != "")
                    {
                        int protocolType;
                        if (int.TryParse(row["协议类型"].ToString(), out protocolType))
                        {
                            mdlLabInstrument.ProtocolType = protocolType;
                        }
                    }
                    mdlLabInstrument.Status = true;
                    mdlLabInstrument.CreatedAt = DateTime.Now;
                    mdlLabInstrument.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlLabInstrument).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlLabInstrument, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["InstrumentId"] = _bllLabInstrument.Add(mdlLabInstrument);
                    base.MyTable.Rows.Add(newRow);
                    base.MyTable.AcceptChanges();

                    UpdateData[i - 1] = "";
                    i++;
                }
                Common.ExcelHelper.UpdateExcel(fileName, 0, UpdateData, tableColCnt, 2);
                MessageBox.Show("成功导入【" + successCnt + "】条记录!失败【" + failCnt + "】条\r\n如存在未导入数据，请查看导入Excel文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
            }

        }
        private void DataEnable(bool Status)
        {
            if (myGrid1.RowCount == 0) return;
            Model.MdlLabInstrument model1 = new Model.MdlLabInstrument();
            model1 = _bllLabInstrument.GetModel(int.Parse(base.MyRow["InstrumentId"].ToString()));
            model1.Status = Status;
            base.MyRow["Status"] = Status;
            base.MyRow.AcceptChanges();
            _bllLabInstrument.Update(model1);
        }

        #endregion

        #region 控件动作

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEnable(false);
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("InstrCode+InstrName+Vendor+Model", TxtFilter.Text.Trim());
        }

        #endregion
    }
}