﻿using System;
using System.CodeDom;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Input;

namespace CustomControl
{
    public partial class MyNumericEdit : UserControl
    {
        public MyNumericEdit()
        {
            InitializeComponent();
            InitNumericEdit();
        }

        #region 私有函数

        private void InitNumericEdit()
        {
            c1NumericEdit1.Value = 0;//DBNull.Value;
            c1NumericEdit1.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat;
            c1NumericEdit1.CustomFormat = "###,###,###,###,##0.##################";
            c1NumericEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            c1NumericEdit1.TextAlign = HorizontalAlignment.Right;
            c1NumericEdit1.ShowContextMenu = false;
            c1NumericEdit1.EmptyAsNull = true;
            c1NumericEdit1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
        }

        private void c1NumericEdit1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (EnterToTab == true)
            {
                if (e.KeyChar != (char)(Keys.Return))
                    return;
                e.Handled = true;
                System.Windows.Forms.SendKeys.Send("{Tab}");
            }
        }

        #endregion

        #region 公开属性
        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        float _CaptainWidth = 70;
        [Description("标题宽度")]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0 || label1.Text == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        [Description("字体")]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                CaptainFont = value;
                NumFont = value;
                Invalidate();
            }
        }

        [Description("标题字体")]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }

        [Description("数值字体")]
        public Font NumFont
        {
            get { return c1NumericEdit1.Font; }
            set
            {
                c1NumericEdit1.Font = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1Input.VisualStyle VisualStyle
        {
            get { return c1NumericEdit1.VisualStyle; }
            set
            {
                c1NumericEdit1.VisualStyle = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public new Color ForeColor
        {
            get { return c1NumericEdit1.ForeColor; }
            set
            {
                c1NumericEdit1.ForeColor = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Color DisabledForeColor
        {
            get { return c1NumericEdit1.DisabledForeColor; }
            set
            {
                c1NumericEdit1.DisabledForeColor = value;
                Invalidate();
            }
        }

        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }

        [Description("控件值"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object Value
        {
            get { return c1NumericEdit1.Value; }
            set
            {
                c1NumericEdit1.Value = value;
                c1NumericEdit1.Refresh();
                Invalidate();
            }
        }

        [Description("控件文本"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public new string Text
        {
            get { return c1NumericEdit1.Text; }
            set
            {
                c1NumericEdit1.Text = value;
                c1NumericEdit1.Refresh();
                Invalidate();
            }
        }

        [Description("控件是否启用")]
        public new bool Enabled
        {
            get { return c1NumericEdit1.Enabled; }
            set
            {
                if (value == true)
                {
                    c1NumericEdit1.TabStop = true;
                    this.TabStop = true;
                    c1NumericEdit1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
                }
                else
                {
                    c1NumericEdit1.TabStop = false;
                    this.TabStop = false;
                    c1NumericEdit1.BackColor = SystemColors.Info;
                    c1NumericEdit1.DisabledForeColor = Color.Black;
                }
                c1NumericEdit1.Enabled = value;
                Invalidate();
            }
        }

        private bool _entertotab = true;
        [Description("回车键走Tab键"), Browsable(true), DefaultValue(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public bool EnterToTab
        {
            get { return _entertotab; }
            set
            {
                _entertotab = value;
            }
        }

        public bool ReadOnly
        {
            get { return c1NumericEdit1.ReadOnly; }
            set
            {
                if (value == true)
                {
                    c1NumericEdit1.BackColor = Color.MistyRose;
                    c1NumericEdit1.DisabledForeColor = Color.Black;
                }
                else
                {
                    c1NumericEdit1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;

                }
                c1NumericEdit1.ReadOnly = value;
                Invalidate();
            }
        }

        [Description("自定义格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string CustomFormat
        {
            get { return c1NumericEdit1.CustomFormat; }
            set
            {
                c1NumericEdit1.CustomFormat = value;
                Invalidate();
            }
        }


        public bool ValueIsDbNull
        {
            get { return c1NumericEdit1.ValueIsDbNull; }
            set
            {
                if (value == false) this.Value = 0;
                c1NumericEdit1.ValueIsDbNull = value;
                
                Invalidate();
            }
        }

        [Description("编辑掩码"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string EditMask
        {
            get { return c1NumericEdit1.EditMask; }
            set
            {
                c1NumericEdit1.EditMask = value;
                Invalidate();
            }
        }

        public NumericInputKeyFlags NumericInputKeys
        {
            get { return c1NumericEdit1.NumericInputKeys; }
            set
            {
                c1NumericEdit1.NumericInputKeys = value;
                Invalidate();
            }
        }

        #endregion

        #region 公开方法
        public new void Select()
        {
            c1NumericEdit1.Select();
        }
        public void SelectAll()
        {
            c1NumericEdit1.SelectAll();
        }
        public void ResetText()
        {
            c1NumericEdit1.ResetText();
        }
        public void ValidateText()
        {
            c1NumericEdit1.ValidateText();
        }

        public void SetToolTip(ToolTip toolTip,string captain)
        {
            toolTip.SetToolTip(c1NumericEdit1,captain);
            toolTip.SetToolTip(label1,captain);
        }
        #endregion

        #region 公开事件
        [Description("当控件获得光标焦点时"), Browsable(true)]
        public new event EventHandler GotFocus
        {
            add
            {
                c1NumericEdit1.GotFocus += value;
            }
            remove
            {
                c1NumericEdit1.GotFocus -= value;
            }
        }
        [Description("当值改变时时触发"), Browsable(true)]
        public event EventHandler ValueChanged
        {
            add
            {
                c1NumericEdit1.ValueChanged += value;
            }
            remove
            {
                c1NumericEdit1.ValueChanged -= value;
            }
        }

        [Description("更改Text值时触发"), Browsable(true)]
        public new event EventHandler TextChanged
        {
            add
            {
                c1NumericEdit1.TextChanged += value;
            }
            remove
            {
                c1NumericEdit1.TextChanged -= value;
            }
        }

        public new event KeyPressEventHandler KeyPress
        {
            add
            {
                c1NumericEdit1.KeyPress += value;
            }
            remove
            {
                c1NumericEdit1.KeyPress -= value;
            }
        }

        public new event EventHandler DoubleClick
        {
            add
            {
                c1NumericEdit1.DoubleClick += value;
            }
            remove
            {
                c1NumericEdit1.DoubleClick -= value;
            }
        }
        public new event EventHandler EnabledChanged
        {
            add
            {
                c1NumericEdit1.EnabledChanged += value;
            }
            remove
            {
                c1NumericEdit1.EnabledChanged -= value;
            }
        }
        #endregion
    }
}
