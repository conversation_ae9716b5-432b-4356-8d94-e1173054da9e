---
description: 
globs: 
alwaysApply: false
---
---

# 数据库最佳实践

## Prisma 设置
- 使用适当的模式设计
- 实施适当的迁移
- 使用适当的关系定义
- 配置适当的连接
- 实施适当的数据填充 (seeding)
- 使用适当的客户端设置

## Prisma 模型
- 使用适当的模型命名
- 实现适当的关系
- 使用适当的字段类型
- 定义适当的索引
- 实现适当的约束
- 使用适当的枚举 (enums)

## Prisma 查询
- 使用适当的查询优化
- 实现适当的筛选
- 使用适当的关系加载
- 恰当地处理事务
- 实现适当的分页
- 使用适当的聚合

## Supabase 设置
- 配置适当的项目设置
- 实现适当的身份验证
- 使用适当的数据库设置
- 配置适当的存储
- 实施适当的策略 (policies)
- 使用适当的客户端设置

## Supabase 安全性
- 实施适当的行级安全 (RLS) 策略
- 使用适当的身份验证
- 配置适当的权限
- 恰当地处理敏感数据
- 实施适当的备份
- 使用适当的加密

## Supabase 查询
- 使用适当的查询优化
- 实现适当的筛选
- 使用适当的连接 (joins)
- 恰当地处理实时数据/功能
- 实现适当的分页
- 使用适当的函数

## 数据库设计
- 使用适当的规范化 (normalization)
- 实施适当的索引
- 使用适当的约束
- 定义适当的关系
- 实施适当的级联操作 (cascades)
- 使用适当的数据类型

## 性能
- 使用适当的连接池
- 实施适当的缓存
- 使用适当的查询优化
- 恰当地处理 N+1 查询问题
- 实施适当的批处理
- 监控性能指标

## 安全性
- 使用适当的身份验证
- 实施适当的授权
- 恰当地处理敏感数据
- 使用适当的加密
- 实施适当的备份
- 监控安全问题

## 最佳实践 (通用)
- 遵循数据库约定
- 使用适当的迁移
- 实施适当的版本控制
- 恰当地处理错误
- 恰当地为模式 (schema) 编写文档
- 监控数据库健康状况