﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security;
using System.Text;
using System.Windows.Forms;

namespace Common
{
    /// <summary>
    /// pos机相关类
    /// </summary>
    public class POSHelper
    {
        #region API声明
        [DllImport("winspool.Drv", EntryPoint = "OpenPrinterA", SetLastError = true, CharSet = CharSet.Ansi, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        public static extern bool OpenPrinter([MarshalAs(UnmanagedType.LPStr)] string szPrinter, out IntPtr hPrinter, IntPtr pd);

        [DllImport("winspool.Drv", EntryPoint = "StartDocPrinterA", SetLastError = true, CharSet = CharSet.Ansi, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        public static extern bool StartDocPrinter(IntPtr hPrinter, Int32 level, [In, MarshalAs(UnmanagedType.LPStruct)] DOCINFOA di);
        [DllImport("winspool.Drv", EntryPoint = "EndDocPrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        public static extern bool EndDocPrinter(IntPtr hPrinter);
        [DllImport("winspool.Drv", EntryPoint = "StartPagePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        public static extern bool StartPagePrinter(IntPtr hPrinter);
        [DllImport("winspool.Drv", EntryPoint = "EndPagePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        public static extern bool EndPagePrinter(IntPtr hPrinter);
        [DllImport("winspool.Drv", EntryPoint = "WritePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        public static extern bool WritePrinter(IntPtr hPrinter, IntPtr pBytes, Int32 dwCount, out Int32 dwWritten);
        [DllImport("winspool.Drv", EntryPoint = "ClosePrinter", SetLastError = true, CharSet = CharSet.Unicode, ExactSpelling = false, CallingConvention = CallingConvention.StdCall), SuppressUnmanagedCodeSecurity()]
        internal static extern bool ClosePrinter(IntPtr phPrinter);

        #endregion

        /// <summary>
        /// 开始弹出钱箱
        /// </summary>
        /// <param name="PrinterName"> 打印机名称</param>
        /// <returns></returns>
        public static bool StartQianXiang(string PrinterName = "POS58")
        {
            //不同的打印机需要不同的参数，这个参数应该可以在打印机的编程文档中找到            
            string str = "" + (char)(27) + (char)(112) + (char)(0) + (char)(60) + (char)(255);
            byte[] bytes = System.Text.Encoding.Default.GetBytes(str);
            Int32 dwCount = bytes.Length;
            IntPtr pBytes = Marshal.AllocHGlobal(dwCount);
            Marshal.Copy(bytes, 0, pBytes, dwCount);

            string szPrinterName = PrinterName;
            Int32 dwError = 0, dwWritten = 0;
            IntPtr hPrinter = new IntPtr(0);
            DOCINFOA di = new DOCINFOA();
            bool bSuccess = false;
            di.pDocName = "My C#.NET RAW Document";
            di.pDataType = "RAW";
            try
            {
                // 打开打印机                
                if (OpenPrinter(szPrinterName.Normalize(), out hPrinter, IntPtr.Zero))
                {
                    // 启动文档打印                    
                    if (StartDocPrinter(hPrinter, 1, di))
                    {
                        // 开始打印                        
                        if (StartPagePrinter(hPrinter))
                        {
                            // 向打印机输出字节                              
                            bSuccess = WritePrinter(hPrinter, pBytes, dwCount, out dwWritten);
                            EndPagePrinter(hPrinter);
                        }
                        EndDocPrinter(hPrinter);
                    }
                    ClosePrinter(hPrinter);
                }
                if (bSuccess == false)
                {
                    dwError = Marshal.GetLastWin32Error();
                }
            }
            catch (Win32Exception ex)
            {
                MessageBox.Show(ex.ToString());
                bSuccess = false;
            }
            Marshal.FreeCoTaskMem(pBytes);
            return bSuccess;
        }

        /// <summary>
        /// led显示金额
        /// </summary>
        /// <param name="money"> 要显示的金额</param>
        /// <param name="moneytype"> 金额的类型 单价，总计， 收款，找零，清屏</param>
        public static void LEDText(string moneyType, string comPort, string money)
        {
            string moneystr = money + "\r";
          
            //金额字节数组
            byte[] monsyBytes = Encoding.ASCII.GetBytes(moneystr);
            //com口输入字节数组
            byte[] inputBytes = new byte[22];

            switch (moneyType)
            {
                case "单价":
                    //类型控制字节数组
                    byte[] djBytes ={ 0x1B, 0x73, 0x31, 0x1B, 0x51, 0x41 };                                       
                    //将控制写入
                    Array.Copy(djBytes, 0, inputBytes, 0, djBytes.Length);
                    break;
                case "总计":
                    byte[] zjBytes = { 0x1B, 0x73, 0x32, 0x1B, 0x51, 0x41 };                              
                    Array.Copy(zjBytes, 0, inputBytes, 0, zjBytes.Length);
                    break;
                case "收款":
                    byte[] skBytes = { 0x1B, 0x73, 0x33, 0x1B, 0x51, 0x41 };
                    Array.Copy(skBytes, 0, inputBytes, 0, skBytes.Length);
                    break;
                case "找零":
                    byte[] zlBytes = { 0x1B, 0x73, 0x34, 0x1B, 0x51, 0x41 };
                    Array.Copy(zlBytes, 0, inputBytes, 0, zlBytes.Length);
                    break;
                default:
                    byte[] qpBytes = { 0x1B, 0x73, 0x30, 0x1B, 0x51, 0x41 };
                    Array.Copy(qpBytes, 0, inputBytes, 0, qpBytes.Length);
                    monsyBytes = Encoding.ASCII.GetBytes("0.00\r");
                    break;
            }
            Array.Copy(monsyBytes, 0, inputBytes, 6, monsyBytes.Length);
            try
            {
                System.IO.Ports.SerialPort com = new System.IO.Ports.SerialPort(comPort);
                com.BaudRate = 2400;
                com.Open();
                com.Write(inputBytes, 0, inputBytes.Length);
                com.Close();
            }
            catch (Exception e)
            {

            }
       
        }
    }
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
    public class DOCINFOA
    {
        [MarshalAs(UnmanagedType.LPStr)]
        public string pDocName;
        [MarshalAs(UnmanagedType.LPStr)]
        public string pOutputFile;
        [MarshalAs(UnmanagedType.LPStr)]
        public string pDataType;
    }
}
