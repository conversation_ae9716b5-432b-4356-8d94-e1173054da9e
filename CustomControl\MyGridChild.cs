﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1TrueDBGrid;
namespace CustomControl
{
    public partial class MyGridChild : C1TrueDBGrid
    {
        public MyGridChild()
        {
            InitializeComponent();
        }

        public MyGridChild(IContainer container)
        {
            container.Add(this);

            InitializeComponent();
        }


        #region 公共函数
        /// <summary>
        /// 
        /// </summary>
        /// <param name="V_标题"></param>
        /// <param name="V_字段"></param>
        /// <param name="V_长度"></param>
        /// <param name="V_水平"></param>
        /// <param name="V_格式"></param>
        /// <param name="V_可编辑"></param>
        /// <param name="frozen">冻结列</param>
        public void Init_Column(string V_标题, string V_字段, int V_长度, string V_水平, string V_格式, bool V_可编辑, bool frozen = false)
        {
            //字段付值
            C1.Win.C1TrueDBGrid.C1DataColumn My_C = new C1.Win.C1TrueDBGrid.C1DataColumn();
            if (V_格式.ToUpper() == "CHECK")
            {
                My_C.ValueItems.Translate = true;
                My_C.ValueItems.CycleOnClick = true;
                My_C.ValueItems.Validate = true;
                My_C.ValueItems.Presentation = PresentationEnum.CheckBox;
                My_C.ValueItems.Values.Clear();
                My_C.ValueItems.Values.Add(new C1.Win.C1TrueDBGrid.ValueItem("False", false));
                My_C.ValueItems.Values.Add(new C1.Win.C1TrueDBGrid.ValueItem("True", true));
            }
            else if (V_格式.ToUpper() == "COMBOBOX")
            {
                My_C.ValueItems.Translate = true;
                My_C.ValueItems.Validate = true;

                My_C.ValueItems.Presentation = PresentationEnum.ComboBox;
                My_C.ValueItems.Values.Clear();
            }
            else
            {
                if (!string.IsNullOrEmpty(V_格式))
                    My_C.NumberFormat = V_格式;
            }
            My_C.Caption = V_标题.Trim();
            My_C.DataField = V_字段.Trim();
            this.Columns.Add(My_C);

            C1.Win.C1TrueDBGrid.C1DisplayColumn My_D;
            My_D = this.Splits[0].DisplayColumns[V_字段];

            if (V_格式.ToUpper() == "BUTTON")
            {
                My_D.ButtonAlways = true;
                My_D.Button = true;
                My_D.ButtonText = true;
            }
            if (V_格式.ToUpper() == "COMBOBOX")
            {
                My_D.DropDownList = true;
            }
            //字段显示
            if (V_长度 == 0)
            {
                My_D.Visible = false;
            }
            else
            {
                My_D.Visible = true;
            }
            My_D.Width = V_长度;
            //My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.General;
            My_D.Style.Trimming = StringTrimming.EllipsisCharacter;
            My_D.Style.Wrap = TextWrapping.NoWrap;


            if (V_可编辑 == true)
            {
                My_D.Locked = false;
                My_D.Style.BackColor = EditColor;
            }
            else
            {
                My_D.Locked = true;
                My_D.Style.BackColor = LockedColor;
            }

            My_D.Frozen = frozen;
            switch (V_水平)
            {
                case "左":
                    My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near;
                    My_D.FooterStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near;
                    break;
                case "中":
                    My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center;
                    My_D.FooterStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center;
                    break;
                case "右":
                    My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far;
                    My_D.FooterStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far;
                    break;
            }
            //Header
            My_D.HeadingStyle.Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.Raised;
            My_D.HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center;

            My_D.HeadingStyle.Wrap = TextWrapping.WrapWithOverflow;

            if (V_长度 == 0)
            {
                My_D.Visible = false;
            }


        }

        public void Init_Grid()
        {
            this.BorderStyle = BorderStyle.FixedSingle;
            //清空
            this.Columns.Clear();
            this.ClearFields();
            //属性
            this.AllowUpdate = true;
            this.AllowDelete = true;
            this.AllowAddNew = false;
            this.AllowSort = true;
            this.AllowColSelect = false;
            this.AllowColMove = false;
            this.ScrollTips = true;
            this.ExtendRightColumn = true;

            this.BorderStyle = BorderStyle.Fixed3D;
            this.FlatStyle = C1.Win.C1TrueDBGrid.FlatModeEnum.Standard;

            //滚动条()
            this.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic;
            this.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic;

            //选择行
            this.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow;
            this.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.None;

            //行状态
            this.RowHeight = 20;
            this.RowDivider.Style = C1.Win.C1TrueDBGrid.LineStyleEnum.Single;
            this.Splits[0].ColumnCaptionHeight = 22;
            this.Splits[0].BorderStyle = Border3DStyle.SunkenInner;
            this.Splits[0].Style.ForeColor = Color.Black;
            this.Splits[0].Style.BackColor = Color.White;
            this.Splits[0].HighLightRowStyle.ForeColor = Color.Black;
            this.Splits[0].HighLightRowStyle.BackColor = Color.FromArgb(175, 238, 238);

            this.Splits[0].EditorStyle.ForeColor = Color.Black;
            this.Splits[0].EditorStyle.BackColor = Color.FromArgb(175, 238, 238);


            this.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2010Blue;

            this.FooterStyle.BackColor = Color.FromArgb(218, 231, 245);
            this.FooterStyle.Borders.BorderType = BorderTypeEnum.Raised;

            this.FooterStyle.Borders.Top = 1;
            this.FooterStyle.Borders.Bottom = 1;
            this.FooterStyle.Borders.Left = 1;
            this.FooterStyle.Borders.Right = 1;

            //Record
            this.RecordSelectorWidth = 10;
            this.RecordSelectorStyle.Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.InsetBevel;

            this.Font = new Font("宋体", (float)10.5, FontStyle.Regular);
            this.Style.VerticalAlignment = AlignVertEnum.Center;
            this.Style.HorizontalAlignment = AlignHorzEnum.Center;
        }

        #endregion

        #region 公共属性

        public Color EditColor
        {
            get { return SystemColors.Info; }
        }

        public Color LockedColor
        {
            get { return Color.White; }
        }

        public Object DataTable
        {
            set
            {
                this.SetDataBinding(value, "", true);
            }
        }

        #endregion
    }
}
