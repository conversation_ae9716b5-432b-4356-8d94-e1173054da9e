using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisControl;

namespace ZTLisBaseDict
{
    public partial class DictSpecimenParam2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictSpecimenParam _bllDictSpecimenParam = new BllDictSpecimenParam();
        Model.MdlDictSpecimenParam _mdlDictSpecimenParam = new MdlDictSpecimenParam();

        public DictSpecimenParam2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtUnit.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictSpecimenParam2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化参数类型枚举控件
            NumParamType.Init();
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictSpecimenParam.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            NumParamType.SelectedIndex = 0;
            TxtUnit.Text = "";
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["ParamCode"] + "";
            TxtName.Text = row["ParamName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            NumParamType.SelectedIndex = row["ParamType"] == DBNull.Value ? 0 : int.Parse(row["ParamType"].ToString());
            TxtUnit.Text = row["Unit"] + "";
            TxtDescription.Text = row["Description"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写参数名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写参数编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["ParamCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["ParamName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictSpecimenParam.GetRecordCount($"ParamCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictSpecimenParam.GetRecordCount($"ParamName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictSpecimenParam.ParamCode = TxtCode.Text.Trim();
            _mdlDictSpecimenParam.ParamName = TxtName.Text.Trim();
            _mdlDictSpecimenParam.PinYin = TxtJc.Text.Trim();
            _mdlDictSpecimenParam.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictSpecimenParam.ParamType = NumParamType.SelectedIndex;
            _mdlDictSpecimenParam.Unit = TxtUnit.Text.Trim();
            _mdlDictSpecimenParam.Description = TxtDescription.Text.Trim();
            _mdlDictSpecimenParam.Status = true;
            _mdlDictSpecimenParam.CreatedAt = DateTime.Now;
            _mdlDictSpecimenParam.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictSpecimenParam, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["ParamId"] = _bllDictSpecimenParam.Add(_mdlDictSpecimenParam);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictSpecimenParam = _bllDictSpecimenParam.GetModel(int.Parse(base.MyRow["ParamId"].ToString()));
            _mdlDictSpecimenParam.ParamCode = TxtCode.Text.Trim();
            _mdlDictSpecimenParam.ParamName = TxtName.Text.Trim();
            _mdlDictSpecimenParam.PinYin = TxtJc.Text.Trim();
            _mdlDictSpecimenParam.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictSpecimenParam.ParamType = NumParamType.SelectedIndex;
            _mdlDictSpecimenParam.Unit = TxtUnit.Text.Trim();
            _mdlDictSpecimenParam.Description = TxtDescription.Text.Trim();
            _mdlDictSpecimenParam.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictSpecimenParam, base.MyRow);

            //数据保存
            try
            {
                _bllDictSpecimenParam.Update(_mdlDictSpecimenParam);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }
        #endregion

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                this.DataAdd();
            }
            else
            {
                this.DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }

    }
}