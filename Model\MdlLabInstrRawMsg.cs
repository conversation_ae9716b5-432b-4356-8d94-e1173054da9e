﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabInstrRawMsg.cs
*
* 功 能： N/A
* 类 名： MdlLabInstrRawMsg
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 仪器原始报文缓冲表
	/// </summary>
	[Serializable]
	public partial class MdlLabInstrRawMsg
	{
		public MdlLabInstrRawMsg()
		{}
		#region Model
		private long _rawid;
		private int _instrumentid;
		private string _rawtext;
		private DateTime _acquiredat= DateTime.Now;
		private bool _parsedflag= false;
		private string _parseerror;
		/// <summary>
		/// 主键 ID
		/// </summary>
		public long RawId
		{
			set{ _rawid=value;}
			get{return _rawid;}
		}
		/// <summary>
		/// 关联仪器 ID (LabInstrument)
		/// </summary>
		public int InstrumentId
		{
			set{ _instrumentid=value;}
			get{return _instrumentid;}
		}
		/// <summary>
		/// 完整原始报文文本
		/// </summary>
		public string RawText
		{
			set{ _rawtext=value;}
			get{return _rawtext;}
		}
		/// <summary>
		/// 接收时间（UTC）
		/// </summary>
		public DateTime AcquiredAt
		{
			set{ _acquiredat=value;}
			get{return _acquiredat;}
		}
		/// <summary>
		/// 解析标志：0 未解析 1 已解析
		/// </summary>
		public bool ParsedFlag
		{
			set{ _parsedflag=value;}
			get{return _parsedflag;}
		}
		/// <summary>
		/// 解析失败原因
		/// </summary>
		public string ParseError
		{
			set{ _parseerror=value;}
			get{return _parseerror;}
		}
		#endregion Model

	}
}

