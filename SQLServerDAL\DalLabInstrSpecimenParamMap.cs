﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabInstrSpecimenParamMap.cs
*
* 功 能： N/A
* 类 名： DalLabInstrSpecimenParamMap
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/24 10:36:31   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabInstrSpecimenParamMap
	/// </summary>
	public partial class DalLabInstrSpecimenParamMap : IDalLabInstrSpecimenParamMap
	{
		public DalLabInstrSpecimenParamMap()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long MapId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabInstrSpecimenParamMap");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabInstrSpecimenParamMap model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabInstrSpecimenParamMap(");
			strSql.Append("InstrumentId,InstrParamCode,InstrParamName,ParamId,ConversionFactor,ConversionOffset,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@InstrumentId,@InstrParamCode,@InstrParamName,@ParamId,@ConversionFactor,@ConversionOffset,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@InstrParamCode", SqlDbType.NVarChar,30),
					new SqlParameter("@InstrParamName", SqlDbType.NVarChar,100),
					new SqlParameter("@ParamId", SqlDbType.Int,4),
					new SqlParameter("@ConversionFactor", SqlDbType.Decimal,9),
					new SqlParameter("@ConversionOffset", SqlDbType.Decimal,9),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.InstrumentId;
			parameters[1].Value = model.InstrParamCode;
			parameters[2].Value = model.InstrParamName;
			parameters[3].Value = model.ParamId;
			parameters[4].Value = model.ConversionFactor;
			parameters[5].Value = model.ConversionOffset;
			parameters[6].Value = model.Status;
			parameters[7].Value = model.CreatedAt;
			parameters[8].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabInstrSpecimenParamMap model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabInstrSpecimenParamMap set ");
			strSql.Append("InstrumentId=@InstrumentId,");
			strSql.Append("InstrParamCode=@InstrParamCode,");
			strSql.Append("InstrParamName=@InstrParamName,");
			strSql.Append("ParamId=@ParamId,");
			strSql.Append("ConversionFactor=@ConversionFactor,");
			strSql.Append("ConversionOffset=@ConversionOffset,");
			strSql.Append("Status=@Status,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@InstrParamCode", SqlDbType.NVarChar,30),
					new SqlParameter("@InstrParamName", SqlDbType.NVarChar,100),
					new SqlParameter("@ParamId", SqlDbType.Int,4),
					new SqlParameter("@ConversionFactor", SqlDbType.Decimal,9),
					new SqlParameter("@ConversionOffset", SqlDbType.Decimal,9),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@MapId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.InstrumentId;
			parameters[1].Value = model.InstrParamCode;
			parameters[2].Value = model.InstrParamName;
			parameters[3].Value = model.ParamId;
			parameters[4].Value = model.ConversionFactor;
			parameters[5].Value = model.ConversionOffset;
			parameters[6].Value = model.Status;
			parameters[7].Value = model.UpdatedAt;
			parameters[8].Value = model.MapId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long MapId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrSpecimenParamMap ");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string MapIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrSpecimenParamMap ");
			strSql.Append(" where MapId in (" + MapIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrSpecimenParamMap GetModel(long MapId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 MapId,InstrumentId,InstrParamCode,InstrParamName,ParamId,ConversionFactor,ConversionOffset,Status,CreatedAt,UpdatedAt from LabInstrSpecimenParamMap ");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			Model.MdlLabInstrSpecimenParamMap model = new Model.MdlLabInstrSpecimenParamMap();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrSpecimenParamMap DataRowToModel(DataRow row)
		{
			Model.MdlLabInstrSpecimenParamMap model = new Model.MdlLabInstrSpecimenParamMap();
			if (row != null)
			{
				if (row["MapId"] != null && row["MapId"].ToString() != "")
				{
					model.MapId = long.Parse(row["MapId"].ToString());
				}
				if (row["InstrumentId"] != null && row["InstrumentId"].ToString() != "")
				{
					model.InstrumentId = int.Parse(row["InstrumentId"].ToString());
				}
				if (row["InstrParamCode"] != null)
				{
					model.InstrParamCode = row["InstrParamCode"].ToString();
				}
				if (row["InstrParamName"] != null)
				{
					model.InstrParamName = row["InstrParamName"].ToString();
				}
				if (row["ParamId"] != null && row["ParamId"].ToString() != "")
				{
					model.ParamId = int.Parse(row["ParamId"].ToString());
				}
				if (row["ConversionFactor"] != null && row["ConversionFactor"].ToString() != "")
				{
					model.ConversionFactor = decimal.Parse(row["ConversionFactor"].ToString());
				}
				if (row["ConversionOffset"] != null && row["ConversionOffset"].ToString() != "")
				{
					model.ConversionOffset = decimal.Parse(row["ConversionOffset"].ToString());
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select MapId,InstrumentId,InstrParamCode,InstrParamName,ParamId,ConversionFactor,ConversionOffset,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM LabInstrSpecimenParamMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" MapId,InstrumentId,InstrParamCode,InstrParamName,ParamId,ConversionFactor,ConversionOffset,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM LabInstrSpecimenParamMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabInstrSpecimenParamMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.MapId desc");
			}
			strSql.Append(")AS Row, T.*  from LabInstrSpecimenParamMap T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabInstrSpecimenParamMap";
			parameters[1].Value = "MapId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 获取包含标本参数详情的映射列表
		/// </summary>
		public DataSet GetListWithParams(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select LabInstrSpecimenParamMap.*,");
			strSql.Append("DictSpecimenParam.ParamCode,");
			strSql.Append("DictSpecimenParam.ParamName,");
			strSql.Append("DictSpecimenParam.EnglishName,");
			strSql.Append("DictSpecimenParam.ParamType,");
			strSql.Append("DictSpecimenParam.Unit,");
			strSql.Append("DictSpecimenParam.PinYin ");
			strSql.Append(" FROM LabInstrSpecimenParamMap ");
			strSql.Append(" INNER JOIN DictSpecimenParam ON LabInstrSpecimenParamMap.ParamId = DictSpecimenParam.ParamId ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" ORDER BY LabInstrSpecimenParamMap.InstrParamCode");
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		#endregion  ExtensionMethod
	}
}

