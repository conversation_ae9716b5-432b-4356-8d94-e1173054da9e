﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common.BaseForm;
using Common;
using Common.Delegate;
using CustomControl;



namespace Common.BaseForm
{
    public partial class BaseDict1 : BaseFather
    {

        #region 基础变量

        private Common.Delegate.TransmitTxt _transmitTxt = new TransmitTxt();
        private Common.Delegate.TransmitDataRow _transmitDataRow = new TransmitDataRow();
        private Chs2Spell _myChs2Spell = new Chs2Spell();
        private CustomControl.MyGrid _baseMyGrid;
        protected DataTable MyTable { get; set; }
        protected DataView MyView { get; set; }
        protected CurrencyManager MyCm { get; set; }
        protected DataRow MyRow { get; set; }
        protected DataRow ExchangedRow { get; set; }
        protected Chs2Spell MyChs2Spell
        {
            get { return _myChs2Spell; }
            set { _myChs2Spell = value; }
        }

        protected bool Insert { get; set; }

        protected Label BaseLblTotal { get; set; }


        protected CustomControl.MyGrid BaseMyGrid
        {
            get { return _baseMyGrid; }
            set
            {
                _baseMyGrid = value;
                this.BaseMyGrid.MouseUp += myGrid1_MouseUp;
                this.BaseMyGrid.RowColChange += myGrid1_RowColChange;
                this.BaseMyGrid.KeyDown += myGrid1_KeyDown;

            }
        }

        protected Common.Delegate.TransmitTxt MyTransmitTxt
        {
            get { return _transmitTxt; }
            set
            {
                _transmitTxt = value;
            }
        }

        protected Common.Delegate.TransmitDataRow MyTransmitDataRow
        {
            get { return _transmitDataRow; }
            set
            {
                _transmitDataRow = value;
            }
        }
        #endregion

        public BaseDict1()
        {
            InitializeComponent();
        }
        #region 控件动作

        private void BaseDict1_Load(object sender, EventArgs e)
        {
            if (!base.IsDesignMode())
            {
                this.MyTransmitTxt.SetText += GridMove;

            }
        }

        private void BaseDict1_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.MyTransmitTxt.SetText -= GridMove;
        }
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Return:
                    DataEdit(false);
                    break;
                case Keys.Insert:
                    DataEdit(true);
                    break;
                case Keys.Delete:
                    DataDelete();
                    break;
            }
        }
        private void myGrid1_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
                DataEdit(false);
        }
        private void myGrid1_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            if ((BaseMyGrid.Row + 1) > BaseMyGrid.RowCount)
            {

            }
            else
            {
                MyRow = ((DataRowView)MyCm.List[BaseMyGrid.Row]).Row;
                this.MyTransmitDataRow.OnSetDataRow(MyRow);
            }
        }

        #endregion

        #region 公共函数


        protected bool ExchangeRow(string sort, DataRow myRow, DataRow exchangeRow)
        {
            if (sort.Replace("[", "").Replace("]", "").ToUpper() != "SORTNO" & sort.Replace("[", "").Replace("]", "").ToUpper() != "SORTNO ASC")
            {
                MessageBox.Show("按序号正序排列才能移动!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }

            int sortNo;
            sortNo = (int)myRow["SortNo"];
            myRow["SortNo"] = exchangeRow["SortNo"];
            exchangeRow["SortNo"] = sortNo;
            myRow.AcceptChanges();
            exchangeRow.AcceptChanges();
            return true;
        }

        private void GridMove(string moveType)
        {
            if (BaseMyGrid.RowCount == 0)
                return;
            switch (moveType)
            {
                case "最前":
                    BaseMyGrid.MoveFirst();
                    break;
                case "上移":
                    BaseMyGrid.MovePrevious();
                    break;
                case "下移":
                    BaseMyGrid.MoveNext();
                    break;
                case "最后":
                    BaseMyGrid.MoveLast();
                    BaseLblTotal.Text = "∑=" + BaseMyGrid.Splits[0].Rows.Count.ToString();
                    break;
                default:
                    int index;
                    if (int.TryParse(moveType, out index))
                    {
                        BaseMyGrid.Row = index;
                    }
                    break;
            }
        }

        protected virtual void DataEdit(bool insert)
        {

        }

        protected virtual void DataDelete()
        {

        }
        protected virtual void DataFilter(string column, string filter)
        {
            if (string.IsNullOrEmpty(filter))
            {
                MyView.RowFilter = filter;
            }
            else
            {
                MyView.RowFilter = column + " like '%" + filter + "%'";
            }
            this.BaseLblTotal.Text = "∑=" + this.BaseMyGrid.Splits[0].Rows.Count.ToString();
        }
        #endregion


    }
}
