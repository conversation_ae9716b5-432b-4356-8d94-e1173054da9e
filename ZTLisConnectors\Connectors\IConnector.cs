﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZTLisEnum;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器核心接口，定义与仪器通讯连接器的标准功能
    /// </summary>
    public interface IConnector : IDisposable
    {
        /// <summary>
        /// 仪器ID，对应数据库LabInstrument表的InstrumentId
        /// </summary>
        int InstrumentId { get; }

        /// <summary>
        /// 仪器名称，用于日志和调试
        /// </summary>
        string InstrumentName { get; }

        /// <summary>
        /// 连接器当前状态
        /// </summary>
        ConnectorStatus Status { get; }

        /// <summary>
        /// 接收到数据时触发的事件
        /// </summary>
        event EventHandler<DataReceivedEventArgs> DataReceived;

        /// <summary>
        /// 连接器状态变化时触发的事件
        /// </summary>
        event EventHandler<StatusChangedEventArgs> StatusChanged;

        /// <summary>
        /// 发生错误时触发的事件
        /// </summary>
        event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        /// <summary>
        /// 加载仪器配置参数
        /// </summary>
        /// <param name="instrumentConfig">仪器配置对象，包含连接参数等</param>
        void Configure(LabInstrument instrumentConfig);

        /// <summary>
        /// 启动连接器，开始与仪器通讯
        /// </summary>
        void Start();

        /// <summary>
        /// 停止连接器，终止与仪器通讯
        /// </summary>
        void Stop();

        /// <summary>
        /// 发送命令到仪器
        /// </summary>
        /// <param name="command">要发送的命令文本或二进制数据</param>
        /// <returns>操作是否成功</returns>
        bool SendCommand(string command);

        /// <summary>
        /// 获取连接器当前统计信息
        /// </summary>
        /// <returns>包含接收消息数、错误次数等统计数据的对象</returns>
        ConnectorStatistics GetStatistics();
    }
}
