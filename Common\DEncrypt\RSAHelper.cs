﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Xml;
using Org.BouncyCastle.Asn1;
using Org.BouncyCastle.Asn1.Pkcs;
using Org.BouncyCastle.Asn1.X509;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Crypto.Prng;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.Pkcs;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.X509;

namespace Common
{
    //#########################
    //本类中的秘钥生成函数生成的秘钥为PEM字符串格式，非xml格式，
    //需引用BouncyCastle包进行加密解密操作
    //#########################
    public class RSAHelper
    {
        const int MAX_ENCRYPT_BLOCK = 117;//最大加密明文

        const int MAX_DECRYPT_BLOCK = 128;//最大解密密文
        public RSAHelper()
        {
        }

        public void GetRSAKey(out string PrivateKey, out string PublicKey)
        {
            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();           
            PrivateKey = rsa.ToXmlString(true);
            PrivateKey = RSAXMLPrivateKeyToPEM(PrivateKey);
            PublicKey = rsa.ToXmlString(false);
            PublicKey = RSAXMLPublicKeyToPEM(PublicKey);
        }
        /// <summary>
        /// RSA私钥格式转换，.net->java
        /// 将xml格式的私钥转换成PEM格式
        /// </summary>
        /// <param name="privateKey">.net生成的私钥</param>
        /// <returns></returns>
        public static string RSAXMLPrivateKeyToPEM(string privateKey)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(privateKey);
            BigInteger m = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("Modulus")[0].InnerText));
            BigInteger exp = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("Exponent")[0].InnerText));
            BigInteger d = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("D")[0].InnerText));
            BigInteger p = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("P")[0].InnerText));
            BigInteger q = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("Q")[0].InnerText));
            BigInteger dp = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("DP")[0].InnerText));
            BigInteger dq = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("DQ")[0].InnerText));
            BigInteger qinv = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("InverseQ")[0].InnerText));

            RsaPrivateCrtKeyParameters privateKeyParam = new RsaPrivateCrtKeyParameters(m, exp, d, p, q, dp, dq, qinv);

            PrivateKeyInfo privateKeyInfo = PrivateKeyInfoFactory.CreatePrivateKeyInfo(privateKeyParam);
            byte[] serializedPrivateBytes = privateKeyInfo.ToAsn1Object().GetEncoded();
            return Convert.ToBase64String(serializedPrivateBytes);
        }

        /// <summary>
        /// RSA公钥格式转换，.net->java
        /// 将xml格式的公钥转换成PEM格式
        /// </summary>
        /// <param name="publicKey">.net生成的公钥</param>
        /// <returns></returns>
        public  string RSAXMLPublicKeyToPEM(string publicKey)
        {
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(publicKey);
            BigInteger m = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("Modulus")[0].InnerText));
            BigInteger p = new BigInteger(1, Convert.FromBase64String(doc.DocumentElement.GetElementsByTagName("Exponent")[0].InnerText));
            RsaKeyParameters pub = new RsaKeyParameters(false, m, p);

            SubjectPublicKeyInfo publicKeyInfo = SubjectPublicKeyInfoFactory.CreateSubjectPublicKeyInfo(pub);
            byte[] serializedPublicBytes = publicKeyInfo.ToAsn1Object().GetDerEncoded();
            return Convert.ToBase64String(serializedPublicBytes);
        }


        #region 公钥加密，私钥解密
        /// <summary>
        /// 公钥加密
        /// </summary>
        /// <param name="publicKey"></param>
        /// <param name="contentData"></param>
        /// <param name="algorithm"></param>
        /// <returns></returns>
        public string EncryptWithPublicKey(string publicKey, string contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            return Convert.ToBase64String(EncryptWithPublicKey(Convert.FromBase64String(publicKey), Encoding.UTF8.GetBytes(contentData), algorithm));
        }
        /// <summary>
        /// 公钥加密
        /// </summary>
        /// <param name="publicKey">RSA公钥 base64格式</param>
        /// <param name="contentData">待加密的数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public string EncryptWithPublicKey(string publicKey, byte[] contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            return Convert.ToBase64String(EncryptWithPublicKey(Convert.FromBase64String(publicKey), contentData, algorithm));
        }
        /// <summary>
        /// 公钥加密
        /// </summary>
        /// <param name="publicKey">RSA公钥</param>
        /// <param name="contentData">待加密的数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public byte[] EncryptWithPublicKey(byte[] publicKey, byte[] contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            RsaKeyParameters publicKeyParam = (RsaKeyParameters)PublicKeyFactory.CreateKey(publicKey);
            return Transform(publicKeyParam, contentData, algorithm, true);
        }
        private byte[] Transform(AsymmetricKeyParameter key, byte[] contentData, string algorithm, bool forEncryption)
        {
            var c = CipherUtilities.GetCipher(algorithm);
            c.Init(forEncryption, new ParametersWithRandom(key));
            MemoryStream ms = new MemoryStream();
            int offSet = 0,i=0;
            byte[] cache;
            if (forEncryption==true)
            {
                while (contentData.Length - offSet > 0)
                {
                    if (contentData.Length - offSet > MAX_ENCRYPT_BLOCK)
                    {
                        cache = c.DoFinal(contentData, offSet, MAX_ENCRYPT_BLOCK);
                    }
                    else
                    {
                        cache = c.DoFinal(contentData, offSet, contentData.Length - offSet);
                    }
                    ms.Write(cache, 0, cache.Length);
                    i++;
                    offSet = i * MAX_ENCRYPT_BLOCK;
                }
            }
            else
            {
                while (contentData.Length - offSet > 0)
                {
                    if (contentData.Length - offSet > MAX_DECRYPT_BLOCK)
                    {
                        cache = c.DoFinal(contentData, offSet, MAX_DECRYPT_BLOCK);
                    }
                    else
                    {
                        cache = c.DoFinal(contentData, offSet, contentData.Length - offSet);
                    }
                    ms.Write(cache, 0, cache.Length);
                    i++;
                    offSet = i * MAX_DECRYPT_BLOCK;
                }
            }            
            byte[] finalBytes = ms.ToArray();
            return finalBytes;
        }

        /// <summary>
        /// 私钥解密
        /// </summary>
        /// <param name="privateKey">RSA私钥  base64格式</param>
        /// <param name="content">待解密数据 base64格式</param>
        /// <param name="encoding">解密出来的数据编码格式，默认UTF-8</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public  string DecryptWithPrivateKey(string privateKey, string content, string encoding = "UTF-8", string algorithm = "RSA/ECB/PKCS1Padding")
        {
            return Encoding.GetEncoding(encoding).GetString(DecryptWithPrivateKey(Convert.FromBase64String(privateKey), Convert.FromBase64String(content), algorithm));
        }
        /// <summary>
        /// 私钥解密
        /// </summary>
        /// <param name="privateKey">RSA私钥</param>
        /// <param name="contentData">待解密数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public  byte[] DecryptWithPrivateKey(byte[] privateKey, byte[] contentData, string algorithm)
        {
            RsaPrivateCrtKeyParameters privateKeyParam = (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(privateKey);
            return Transform(privateKeyParam, contentData, algorithm, false);
        }


        #endregion

        #region 私钥加密，公钥解密
        /// <summary>
        /// 私钥加密
        /// </summary>
        /// <param name="privateKey">RSA私钥 base64格式</param>
        /// <param name="contentData">待加密的数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public string EncryptWithPrivateKey(string privateKey, string contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            return Convert.ToBase64String(EncryptWithPrivateKey(Convert.FromBase64String(privateKey), Encoding.UTF8.GetBytes(contentData), algorithm));
        }
        /// <summary>
        /// 私钥加密
        /// </summary>
        /// <param name="privateKey">RSA私钥 base64格式</param>
        /// <param name="contentData">待加密的数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public string EncryptWithPrivateKey(string privateKey, byte[] contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            return Convert.ToBase64String(EncryptWithPrivateKey(Convert.FromBase64String(privateKey), contentData, algorithm));
        }
        /// <summary>
        /// 私钥加密
        /// </summary>
        /// <param name="privateKey">RSA私钥</param>
        /// <param name="contentData">待加密的数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public byte[] EncryptWithPrivateKey(byte[] privateKey, byte[] contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            RsaPrivateCrtKeyParameters privateKeyParam = (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(privateKey);
            return Transform(privateKeyParam, contentData, algorithm, true);
        }
        /// <summary>
        /// 公钥解密
        /// </summary>
        /// <param name="publicKey">RSA公钥  base64格式</param>
        /// <param name="content">待解密数据 base64格式</param>
        /// <param name="encoding">解密出来的数据编码格式，默认UTF-8</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public  string DecryptWithPublicKey(string publicKey, string content, string encoding = "UTF-8", string algorithm = "RSA/ECB/PKCS1Padding")
        {
            return Encoding.GetEncoding(encoding).GetString(DecryptWithPublicKey(Convert.FromBase64String(publicKey), Convert.FromBase64String(content), algorithm));
        }
        /// <summary>
        /// 公钥解密
        /// </summary>
        /// <param name="publicKey">RSA公钥</param>
        /// <param name="contentData">待解密数据</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns></returns>
        public  byte[] DecryptWithPublicKey(byte[] publicKey, byte[] contentData, string algorithm = "RSA/ECB/PKCS1Padding")
        {
            RsaKeyParameters publicKeyParam = (RsaKeyParameters)PublicKeyFactory.CreateKey(publicKey);
            return Transform(publicKeyParam, contentData, algorithm, false);
        }
        #endregion

        /// <summary>
        /// SHA1WithRSA 私钥签名
        /// </summary>
        /// <param name="data">待签名字符串</param>
        /// <param name="key">私钥</param>
        /// <returns></returns>
        public static string SignByPrivateKey(string data, string key)
        {
            AsymmetricKeyParameter priKey = (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(Convert.FromBase64String(key));
            byte[] byteData = System.Text.Encoding.UTF8.GetBytes(data);

            ISigner normalSig = SignerUtilities.GetSigner("SHA1WithRSA");
            normalSig.Init(true, priKey);
            normalSig.BlockUpdate(byteData, 0, data.Length);
            byte[] normalResult = normalSig.GenerateSignature();
            return Convert.ToBase64String(normalResult);//签名结果
        }
        /// <summary>
        /// SHA1WithRSA 公钥验签
        /// </summary>
        /// <param name="plainData">待签名字符串</param>
        /// <param name="sign">签名</param>
        /// <param name="key">公钥</param>
        /// <returns></returns>
        public static bool ValidationPublicKey(string plainData, string sign, string key)
        {
            AsymmetricKeyParameter pubKey = (RsaKeyParameters)PublicKeyFactory.CreateKey(Convert.FromBase64String(key));

            byte[] signBytes = Convert.FromBase64String(sign);
            byte[] plainBytes = Encoding.UTF8.GetBytes(plainData);

            ISigner verifier = SignerUtilities.GetSigner("SHA1WithRSA");
            verifier.Init(false, pubKey);
            verifier.BlockUpdate(plainBytes, 0, plainBytes.Length);

            return verifier.VerifySignature(signBytes); //验签结果
        }

        #region 生成pfx
        /// <summary>
        /// 根据crt以及RSA私钥生成pfx证书
        /// </summary>
        /// <param name="passWord">证书密码</param>
        /// <param name="x509CertData">crt证书</param>
        /// <param name="privateKey">Rsa私钥</param>
        /// <param name="x509CertChainData">证书链</param>
        /// <param name="alias">默认别名</param>
        /// <returns></returns>
        public  MemoryStream GeneratePFX(string passWord, byte[] x509CertData, byte[] privateKey, byte[] x509CertChainData = null, string alias = "PrimaryCertificate")
        {
            RsaPrivateCrtKeyParameters privateKeyParam = (RsaPrivateCrtKeyParameters)PrivateKeyFactory.CreateKey(privateKey);
            var certEntry = GetX509CertificateEntry(x509CertData);
            Pkcs12Store store = new Pkcs12StoreBuilder().Build();
            store.SetCertificateEntry(alias, certEntry);
            X509CertificateEntry[] chain = new X509CertificateEntry[1];
            if (x509CertChainData != null)
            {
                chain = new X509CertificateEntry[2];
                chain[1] = GetX509CertificateEntry(x509CertChainData);
            }
            chain[0] = certEntry;
            store.SetKeyEntry(alias, new AsymmetricKeyEntry(privateKeyParam), chain);   //设置私钥  
            var ms = new MemoryStream();
            store.Save(ms, passWord.ToCharArray(), new SecureRandom());
            ms.Position = 0;         
            return ms;
        }
        private  X509CertificateEntry GetX509CertificateEntry(byte[] certData)
        {
            var certificate = new X509CertificateParser().ReadCertificate(certData);
            X509CertificateEntry certEntry = new X509CertificateEntry(certificate);
            return certEntry;
        }
        #endregion

        #region 生成证书


        public  byte[] GenerateCrt(AsymmetricKeyParameter pubkey, AsymmetricKeyParameter privatekey,UserInfo userInfo)
        {
            string signatureAlgorithm = "SHA1WithRSA";

            // Generate certificate
            var attributes = new Hashtable();
            attributes[X509Name.E] = userInfo.PublishEmail;
            attributes[X509Name.CN] = userInfo.Publish;
            attributes[X509Name.O] = userInfo.Publish2;
            attributes[X509Name.C] = userInfo.Language;
            

            //这里是证书颁发者的信息
            var ordering = new ArrayList();
            ordering.Add(X509Name.E);
            ordering.Add(X509Name.CN);
            ordering.Add(X509Name.O);
            ordering.Add(X509Name.C);

            var certificateGenerator = new X509V3CertificateGenerator();
            //设置证书序列化号
            certificateGenerator.SetSerialNumber(BigInteger.ProbablePrime(120, new Random()));


            //设置颁发者dn信息
            certificateGenerator.SetIssuerDN(new X509Name(ordering, attributes));


            //设置证书生效时间
            certificateGenerator.SetNotBefore(DateTime.Today.Subtract(new TimeSpan(0, 0, 1, 0)));
            //设置证书失效时间
            certificateGenerator.SetNotAfter(DateTime.Today.AddDays(365*100));
            //设置接受者dn信息
            Hashtable attributes2=new Hashtable();
            attributes2[X509Name.E] = userInfo.ReceiveEmail;
            attributes2[X509Name.CN] = userInfo.Receive;           
            attributes2[X509Name.C] = userInfo.Language;
            var ordering2 = new ArrayList();
            ordering2.Add(X509Name.E);
            ordering2.Add(X509Name.CN);
            ordering2.Add(X509Name.C);

            certificateGenerator.SetSubjectDN(new X509Name(ordering2, attributes2));
            //设置证书的公钥
            certificateGenerator.SetPublicKey(pubkey);

            //设置证书的加密算法
            certificateGenerator.SetSignatureAlgorithm(signatureAlgorithm);
            certificateGenerator.AddExtension(X509Extensions.BasicConstraints, true, new BasicConstraints(false));
            certificateGenerator.AddExtension(X509Extensions.AuthorityKeyIdentifier, true, new AuthorityKeyIdentifier(SubjectPublicKeyInfoFactory.CreateSubjectPublicKeyInfo(pubkey)));


            certificateGenerator.AddExtension(X509Extensions.ExtendedKeyUsage.Id, false, new ExtendedKeyUsage(new ArrayList() { new DerObjectIdentifier("1.3.6.1.5.5.7.3.2") }));

            //创建证书
            var x509Certificate = certificateGenerator.Generate(privatekey);

            byte[] pkcs12Bytes = DotNetUtilities.ToX509Certificate(x509Certificate).Export(X509ContentType.Cert);

            return pkcs12Bytes;
        }

        #endregion





    }
}
