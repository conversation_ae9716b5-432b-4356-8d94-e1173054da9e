﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysRoleModule.cs
*
* 功 能： N/A
* 类 名： MdlSysRoleModule
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:35   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlSysRoleModule:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlSysRoleModule
	{
		public MdlSysRoleModule()
		{}
		#region Model
		private string _rolecode;
		private string _modulecode;
		/// <summary>
		/// 
		/// </summary>
		public string RoleCode
		{
			set{ _rolecode=value;}
			get{return _rolecode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ModuleCode
		{
			set{ _modulecode=value;}
			get{return _modulecode;}
		}
		#endregion Model

	}
}

