namespace ZTLisBaseDict
{
    partial class DictLabProfile1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DictLabProfile1));
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.c1SplitContainer1 = new C1.Win.C1SplitContainer.C1SplitContainer();
            this.c1SplitterPanel2 = new C1.Win.C1SplitContainer.C1SplitterPanel();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.c1ToolBar2 = new C1.Win.C1Command.C1ToolBar();
            this.c1CommandLink9 = new C1.Win.C1Command.C1CommandLink();
            this.CmdItemAdd = new C1.Win.C1Command.C1Command();
            this.c1CommandLink10 = new C1.Win.C1Command.C1CommandLink();
            this.CmdItemDelete = new C1.Win.C1Command.C1Command();
            this.c1CommandLink13 = new C1.Win.C1Command.C1CommandLink();
            this.CmdItemRefresh = new C1.Win.C1Command.C1Command();
            this.myGrid2 = new CustomControl.MyGrid();
            this.c1SplitterPanel1 = new C1.Win.C1SplitContainer.C1SplitterPanel();
            this.myGrid1 = new CustomControl.MyGrid();
            this.C1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.C1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.CmdAdd = new C1.Win.C1Command.C1Command();
            this.C1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.CmdDelete = new C1.Win.C1Command.C1Command();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.CmdImport = new C1.Win.C1Command.C1Command();
            this.c1CommandLink5 = new C1.Win.C1Command.C1CommandLink();
            this.CmdExport = new C1.Win.C1Command.C1Command();
            this.c1CommandLink6 = new C1.Win.C1Command.C1CommandLink();
            this.CmdEnable = new C1.Win.C1Command.C1Command();
            this.c1CommandLink7 = new C1.Win.C1Command.C1CommandLink();
            this.CmdDisable = new C1.Win.C1Command.C1Command();
            this.C1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.CmdRefresh = new C1.Win.C1Command.C1Command();
            this.LblTotal = new System.Windows.Forms.Label();
            this.TxtFilter = new CustomControl.MyTextBox();
            this.CmdMoveUp = new C1.Win.C1Command.C1Command();
            this.CmdMoveDown = new C1.Win.C1Command.C1Command();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1SplitContainer1)).BeginInit();
            this.c1SplitContainer1.SuspendLayout();
            this.c1SplitterPanel2.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.c1SplitterPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.SuspendLayout();
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.Images.SetKeyName(0, "增加.png");
            this.imageList1.Images.SetKeyName(1, "删除.png");
            this.imageList1.Images.SetKeyName(2, "打印.png");
            this.imageList1.Images.SetKeyName(3, "导入.png");
            this.imageList1.Images.SetKeyName(4, "导出.png");
            this.imageList1.Images.SetKeyName(5, "上移.png");
            this.imageList1.Images.SetKeyName(6, "下移.png");
            this.imageList1.Images.SetKeyName(7, "启用.png");
            this.imageList1.Images.SetKeyName(8, "停用.png");
            this.imageList1.Images.SetKeyName(9, "刷新.png");
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.Controls.Add(this.c1SplitContainer1, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.C1ToolBar1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.LblTotal, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.TxtFilter, 1, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(858, 515);
            this.tableLayoutPanel1.TabIndex = 6;
            // 
            // c1SplitContainer1
            // 
            this.c1SplitContainer1.AutoSizeElement = C1.Framework.AutoSizeElement.Both;
            this.c1SplitContainer1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.c1SplitContainer1.CollapsingAreaColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(232)))), ((int)(((byte)(237)))));
            this.c1SplitContainer1.CollapsingCueColor = System.Drawing.Color.LightCoral;
            this.tableLayoutPanel1.SetColumnSpan(this.c1SplitContainer1, 3);
            this.c1SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.c1SplitContainer1.FixedLineColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(156)))), ((int)(((byte)(184)))));
            this.c1SplitContainer1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.c1SplitContainer1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(57)))), ((int)(((byte)(91)))));
            this.c1SplitContainer1.Location = new System.Drawing.Point(0, 50);
            this.c1SplitContainer1.Margin = new System.Windows.Forms.Padding(0);
            this.c1SplitContainer1.Name = "c1SplitContainer1";
            this.c1SplitContainer1.Panels.Add(this.c1SplitterPanel2);
            this.c1SplitContainer1.Panels.Add(this.c1SplitterPanel1);
            this.c1SplitContainer1.Size = new System.Drawing.Size(858, 465);
            this.c1SplitContainer1.SplitterColor = System.Drawing.Color.SteelBlue;
            this.c1SplitContainer1.SplitterWidth = 8;
            this.c1SplitContainer1.TabIndex = 2;
            this.c1SplitContainer1.UseParentVisualStyle = false;
            // 
            // c1SplitterPanel2
            // 
            this.c1SplitterPanel2.Collapsible = true;
            this.c1SplitterPanel2.Controls.Add(this.tableLayoutPanel2);
            this.c1SplitterPanel2.Dock = C1.Win.C1SplitContainer.PanelDockStyle.Bottom;
            this.c1SplitterPanel2.Height = 188;
            this.c1SplitterPanel2.Location = new System.Drawing.Point(0, 284);
            this.c1SplitterPanel2.Name = "c1SplitterPanel2";
            this.c1SplitterPanel2.Size = new System.Drawing.Size(858, 181);
            this.c1SplitterPanel2.SizeRatio = 41.138D;
            this.c1SplitterPanel2.TabIndex = 0;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.c1ToolBar2, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.myGrid2, 0, 1);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 2;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(858, 181);
            this.tableLayoutPanel2.TabIndex = 0;
            // 
            // c1ToolBar2
            // 
            this.c1ToolBar2.AccessibleName = "Tool Bar";
            this.c1ToolBar2.BackColor = System.Drawing.Color.Transparent;
            this.c1ToolBar2.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.c1ToolBar2.CommandHolder = null;
            this.c1ToolBar2.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink9,
            this.c1CommandLink10,
            this.c1CommandLink13});
            this.c1ToolBar2.ForeColor = System.Drawing.SystemColors.ControlText;
            this.c1ToolBar2.Location = new System.Drawing.Point(3, 3);
            this.c1ToolBar2.Name = "c1ToolBar2";
            this.c1ToolBar2.Size = new System.Drawing.Size(117, 44);
            this.c1ToolBar2.Text = "C1ToolBar2";
            this.c1ToolBar2.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.c1ToolBar2.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // c1CommandLink9
            // 
            this.c1CommandLink9.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink9.Command = this.CmdItemAdd;
            // 
            // CmdItemAdd
            // 
            this.CmdItemAdd.ImageIndex = 0;
            this.CmdItemAdd.Name = "CmdItemAdd";
            this.CmdItemAdd.ShortcutText = "";
            this.CmdItemAdd.Text = "增加";
            this.CmdItemAdd.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdItemAdd_Click);
            // 
            // c1CommandLink10
            // 
            this.c1CommandLink10.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink10.Command = this.CmdItemDelete;
            // 
            // CmdItemDelete
            // 
            this.CmdItemDelete.ImageIndex = 1;
            this.CmdItemDelete.Name = "CmdItemDelete";
            this.CmdItemDelete.ShortcutText = "";
            this.CmdItemDelete.Text = "删除";
            this.CmdItemDelete.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdItemDelete_Click);
            // 
            // c1CommandLink13
            // 
            this.c1CommandLink13.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink13.Command = this.CmdItemRefresh;
            // 
            // CmdItemRefresh
            // 
            this.CmdItemRefresh.ImageIndex = 9;
            this.CmdItemRefresh.Name = "CmdItemRefresh";
            this.CmdItemRefresh.ShortcutText = "";
            this.CmdItemRefresh.Text = "刷新";
            this.CmdItemRefresh.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdItemRefresh_Click);
            // 
            // myGrid2
            // 
            this.myGrid2.AllowColMove = true;
            this.myGrid2.AllowFilter = true;
            this.myGrid2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.myGrid2.CanCustomCol = false;
            this.myGrid2.Caption = "组合项目明细";
            this.myGrid2.ChildGrid = null;
            this.myGrid2.Col = 0;
            this.myGrid2.ColumnFooters = false;
            this.myGrid2.ColumnHeaders = true;
            this.myGrid2.DataMember = "";
            this.myGrid2.DataSource = null;
            this.myGrid2.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid2.FetchRowStyles = false;
            this.myGrid2.FilterBar = false;
            this.myGrid2.Font = new System.Drawing.Font("宋体", 10.5F);
            this.myGrid2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(57)))), ((int)(((byte)(91)))));
            this.myGrid2.GroupByAreaVisible = true;
            this.myGrid2.Location = new System.Drawing.Point(0, 50);
            this.myGrid2.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid2.Name = "myGrid2";
            this.myGrid2.Size = new System.Drawing.Size(858, 131);
            this.myGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid2.TabIndex = 3;
            this.myGrid2.Xmlpath = null;
            this.myGrid2.RowColChange += new C1.Win.C1TrueDBGrid.RowColChangeEventHandler(this.myGrid2_RowColChange);
            this.myGrid2.MouseUp += new System.Windows.Forms.MouseEventHandler(this.myGrid2_MouseUp);
            // 
            // c1SplitterPanel1
            // 
            this.c1SplitterPanel1.Controls.Add(this.myGrid1);
            this.c1SplitterPanel1.Height = 269;
            this.c1SplitterPanel1.Location = new System.Drawing.Point(0, 0);
            this.c1SplitterPanel1.Name = "c1SplitterPanel1";
            this.c1SplitterPanel1.Size = new System.Drawing.Size(858, 269);
            this.c1SplitterPanel1.SizeRatio = 58.862D;
            this.c1SplitterPanel1.TabIndex = 1;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "检验组合字典";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.myGrid1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(57)))), ((int)(((byte)(91)))));
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 0);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(858, 269);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 3;
            this.myGrid1.Xmlpath = null;
            this.myGrid1.RowColChange += new C1.Win.C1TrueDBGrid.RowColChangeEventHandler(this.myGrid1_RowColChange);
            // 
            // C1ToolBar1
            // 
            this.C1ToolBar1.AccessibleName = "Tool Bar";
            this.C1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.C1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.C1ToolBar1.CommandHolder = null;
            this.C1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.C1CommandLink1,
            this.C1CommandLink2,
            this.c1CommandLink4,
            this.c1CommandLink5,
            this.c1CommandLink6,
            this.c1CommandLink7,
            this.C1CommandLink3});
            this.C1ToolBar1.ForeColor = System.Drawing.SystemColors.ControlText;
            this.C1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.C1ToolBar1.Name = "C1ToolBar1";
            this.C1ToolBar1.Size = new System.Drawing.Size(273, 44);
            this.C1ToolBar1.Text = "C1ToolBar1";
            this.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // C1CommandLink1
            // 
            this.C1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink1.Command = this.CmdAdd;
            // 
            // CmdAdd
            // 
            this.CmdAdd.ImageIndex = 0;
            this.CmdAdd.Name = "CmdAdd";
            this.CmdAdd.ShortcutText = "";
            this.CmdAdd.Text = "增加";
            this.CmdAdd.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdAdd_Click);
            // 
            // C1CommandLink2
            // 
            this.C1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink2.Command = this.CmdDelete;
            // 
            // CmdDelete
            // 
            this.CmdDelete.ImageIndex = 1;
            this.CmdDelete.Name = "CmdDelete";
            this.CmdDelete.ShortcutText = "";
            this.CmdDelete.Text = "删除";
            this.CmdDelete.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDelete_Click);
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink4.Command = this.CmdImport;
            // 
            // CmdImport
            // 
            this.CmdImport.ImageIndex = 3;
            this.CmdImport.Name = "CmdImport";
            this.CmdImport.ShortcutText = "";
            this.CmdImport.Text = "导入";
            this.CmdImport.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdImport_Click);
            // 
            // c1CommandLink5
            // 
            this.c1CommandLink5.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink5.Command = this.CmdExport;
            // 
            // CmdExport
            // 
            this.CmdExport.ImageIndex = 4;
            this.CmdExport.Name = "CmdExport";
            this.CmdExport.ShortcutText = "";
            this.CmdExport.Text = "导出";
            this.CmdExport.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdExport_Click);
            // 
            // c1CommandLink6
            // 
            this.c1CommandLink6.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink6.Command = this.CmdEnable;
            // 
            // CmdEnable
            // 
            this.CmdEnable.ImageIndex = 7;
            this.CmdEnable.Name = "CmdEnable";
            this.CmdEnable.ShortcutText = "";
            this.CmdEnable.Text = "启用";
            this.CmdEnable.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdEnable_Click);
            // 
            // c1CommandLink7
            // 
            this.c1CommandLink7.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink7.Command = this.CmdDisable;
            // 
            // CmdDisable
            // 
            this.CmdDisable.ImageIndex = 8;
            this.CmdDisable.Name = "CmdDisable";
            this.CmdDisable.ShortcutText = "";
            this.CmdDisable.Text = "停用";
            this.CmdDisable.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdDisable_Click);
            // 
            // C1CommandLink3
            // 
            this.C1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.C1CommandLink3.Command = this.CmdRefresh;
            // 
            // CmdRefresh
            // 
            this.CmdRefresh.ImageIndex = 9;
            this.CmdRefresh.Name = "CmdRefresh";
            this.CmdRefresh.ShortcutText = "";
            this.CmdRefresh.Text = "刷新";
            this.CmdRefresh.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdRefresh_Click);
            // 
            // LblTotal
            // 
            this.LblTotal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.LblTotal.AutoSize = true;
            this.LblTotal.BackColor = System.Drawing.Color.Transparent;
            this.LblTotal.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LblTotal.Location = new System.Drawing.Point(541, 18);
            this.LblTotal.Name = "LblTotal";
            this.LblTotal.Size = new System.Drawing.Size(314, 14);
            this.LblTotal.TabIndex = 5;
            this.LblTotal.Text = "∑=999999";
            // 
            // TxtFilter
            // 
            this.TxtFilter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtFilter.Captain = "过滤框";
            this.TxtFilter.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFilter.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFilter.CaptainWidth = 70F;
            this.TxtFilter.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFilter.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFilter.EditMask = null;
            this.TxtFilter.Location = new System.Drawing.Point(282, 13);
            this.TxtFilter.Multiline = false;
            this.TxtFilter.Name = "TxtFilter";
            this.TxtFilter.PasswordChar = '\0';
            this.TxtFilter.ReadOnly = false;
            this.TxtFilter.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFilter.SelectionStart = 0;
            this.TxtFilter.SelectStart = 0;
            this.TxtFilter.Size = new System.Drawing.Size(253, 23);
            this.TxtFilter.TabIndex = 3;
            this.TxtFilter.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFilter.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilter.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFilter.Watermark = null;
            this.TxtFilter.TextChanged += new System.EventHandler(this.TxtFilter_TextChanged);
            // 
            // CmdMoveUp
            // 
            this.CmdMoveUp.ImageIndex = 5;
            this.CmdMoveUp.Name = "CmdMoveUp";
            this.CmdMoveUp.ShortcutText = "";
            this.CmdMoveUp.Text = "上移";
            // 
            // CmdMoveDown
            // 
            this.CmdMoveDown.ImageIndex = 6;
            this.CmdMoveDown.Name = "CmdMoveDown";
            this.CmdMoveDown.ShortcutText = "";
            this.CmdMoveDown.Text = "下移";
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmdAdd);
            this.c1CommandHolder1.Commands.Add(this.CmdDelete);
            this.c1CommandHolder1.Commands.Add(this.CmdRefresh);
            this.c1CommandHolder1.Commands.Add(this.CmdImport);
            this.c1CommandHolder1.Commands.Add(this.CmdExport);
            this.c1CommandHolder1.Commands.Add(this.CmdEnable);
            this.c1CommandHolder1.Commands.Add(this.CmdDisable);
            this.c1CommandHolder1.Commands.Add(this.CmdMoveUp);
            this.c1CommandHolder1.Commands.Add(this.CmdMoveDown);
            this.c1CommandHolder1.Commands.Add(this.CmdItemAdd);
            this.c1CommandHolder1.Commands.Add(this.CmdItemDelete);
            this.c1CommandHolder1.Commands.Add(this.CmdItemRefresh);
            this.c1CommandHolder1.ImageList = this.imageList1;
            this.c1CommandHolder1.Owner = this;
            // 
            // DictLabProfile1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(858, 515);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "DictLabProfile1";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "检验组合字典";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.DictLabProfile1_FormClosed);
            this.Load += new System.EventHandler(this.DictLabProfile1_Load);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1SplitContainer1)).EndInit();
            this.c1SplitContainer1.ResumeLayout(false);
            this.c1SplitterPanel2.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.c1SplitterPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        internal C1.Win.C1Command.C1ToolBar C1ToolBar1;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink1;
        private C1.Win.C1Command.C1Command CmdAdd;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink2;
        private C1.Win.C1Command.C1Command CmdDelete;
        internal C1.Win.C1Command.C1CommandLink C1CommandLink3;
        private C1.Win.C1Command.C1Command CmdRefresh;
        private System.Windows.Forms.Label LblTotal;
        internal CustomControl.MyTextBox TxtFilter;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1Command CmdImport;
        private C1.Win.C1Command.C1Command CmdMoveDown;
        private C1.Win.C1Command.C1Command CmdExport;
        private C1.Win.C1Command.C1Command CmdEnable;
        private C1.Win.C1Command.C1Command CmdMoveUp;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1CommandLink c1CommandLink5;
        private C1.Win.C1Command.C1Command CmdDisable;
        private C1.Win.C1Command.C1CommandLink c1CommandLink6;
        private C1.Win.C1Command.C1CommandLink c1CommandLink7;
        private C1.Win.C1SplitContainer.C1SplitContainer c1SplitContainer1;
        private C1.Win.C1SplitContainer.C1SplitterPanel c1SplitterPanel2;
        private C1.Win.C1SplitContainer.C1SplitterPanel c1SplitterPanel1;
        private CustomControl.MyGrid myGrid1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        internal C1.Win.C1Command.C1ToolBar c1ToolBar2;
        internal C1.Win.C1Command.C1CommandLink c1CommandLink9;
        internal C1.Win.C1Command.C1CommandLink c1CommandLink10;
        private C1.Win.C1Command.C1CommandLink c1CommandLink13;
        private C1.Win.C1Command.C1Command CmdItemAdd;
        private C1.Win.C1Command.C1Command CmdItemDelete;
        private C1.Win.C1Command.C1Command CmdItemRefresh;
        private CustomControl.MyGrid myGrid2;
    }
}