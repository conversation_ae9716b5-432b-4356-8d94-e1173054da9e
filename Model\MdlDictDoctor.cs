﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictDoctor.cs
*
* 功 能： N/A
* 类 名： MdlDictDoctor
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:12   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 医生/检验人员字典表
	/// </summary>
	[Serializable]
	public partial class MdlDictDoctor
	{
		public MdlDictDoctor()
		{}
		#region Model
		private int _doctorid;
		private string _doctorcode;
		private string _doctorname;
		private string _hisdoctorcode;
		private string _pinyin;
		private int _deptid;
		private string _title;
		private string _licenseno;
		private string _mobile;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int DoctorId
		{
			set{ _doctorid=value;}
			get{return _doctorid;}
		}
		/// <summary>
		/// LIS 医生编码/工号
		/// </summary>
		public string DoctorCode
		{
			set{ _doctorcode=value;}
			get{return _doctorcode;}
		}
		/// <summary>
		/// 中文姓名
		/// </summary>
		public string DoctorName
		{
			set{ _doctorname=value;}
			get{return _doctorname;}
		}
		/// <summary>
		/// HIS 医生编码（nvarchar(200)）
		/// </summary>
		public string HisDoctorCode
		{
			set{ _hisdoctorcode=value;}
			get{return _hisdoctorcode;}
		}
		/// <summary>
		/// 拼音/首字母
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 所属科室ID (FK)
		/// </summary>
		public int DeptId
		{
			set{ _deptid=value;}
			get{return _deptid;}
		}
		/// <summary>
		/// 职称
		/// </summary>
		public string Title
		{
			set{ _title=value;}
			get{return _title;}
		}
		/// <summary>
		/// 执业证书号
		/// </summary>
		public string LicenseNo
		{
			set{ _licenseno=value;}
			get{return _licenseno;}
		}
		/// <summary>
		/// 联系方式
		/// </summary>
		public string Mobile
		{
			set{ _mobile=value;}
			get{return _mobile;}
		}
		/// <summary>
		/// 状态 1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

