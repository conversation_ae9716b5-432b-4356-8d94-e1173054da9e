﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace Common
{
    /// <summary>
    /// 消息提示类
    /// </summary>
    public class Alert
    {
        [DllImport("user32.dll")] //闪烁窗体
        public static extern bool FlashWindowEx(ref FLASHWINFO pwfi);

        [StructLayout(LayoutKind.Sequential)]
        public struct FLASHWINFO
        {
            public UInt32 cbSize;//该结构的字节大小
            public IntPtr hwnd;//要闪烁的窗口的句柄，该窗口可以是打开的或最小化的
            public UInt32 dwFlags;//闪烁的状态
            public UInt32 uCount;//闪烁窗口的次数
            public UInt32 dwTimeout;//窗口闪烁的频度，毫秒为单位；若该值为0，则为默认图标的闪烁频度
        }
        //闪烁窗体参数
        public const UInt32 FLASHW_STOP = 0;//停止闪动.系统将窗体恢复到初始状态.
        public const UInt32 FLASHW_CAPTION = 1;//闪动窗体的标题.
        public const UInt32 FLASHW_TRAY = 2;//闪动任务栏按钮
        public const UInt32 FLASHW_ALL = 3;//闪动窗体标题和任务栏按钮
        public const UInt32 FLASHW_TIMER = 4;//连续不停的闪动,直到此参数被设置为:FLASHW_STOP
        public const UInt32 FLASHW_TIMERNOFG = 12;//连续不停的闪动,直到窗体用户被激活.通常用法将参数设置为: FLASHW_ALL | FLASHW_TIMERNOFG

        /// <summary>
        /// 窗口闪烁
        /// </summary>
        /// <param name="handle">窗口句柄</param>
        public static void FlashWindow(IntPtr handle)
        {
            FLASHWINFO fInfo = new FLASHWINFO();
            fInfo.cbSize = Convert.ToUInt32(System.Runtime.InteropServices.Marshal.SizeOf(fInfo));
            fInfo.hwnd = handle;
            fInfo.dwFlags = FLASHW_TRAY | FLASHW_TIMERNOFG;//这里是闪动窗标题和任务栏按钮,直到用户激活窗体
            fInfo.uCount = 3;//UInt32.MaxValue;
            fInfo.dwTimeout = 1000;
            FlashWindowEx(ref fInfo);
        }
    }
}
