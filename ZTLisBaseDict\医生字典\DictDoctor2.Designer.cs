namespace ZTLisBaseDict
{
    partial class DictDoctor2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.TableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.TxtCode = new CustomControl.MyTextBox();
            this.TxtName = new CustomControl.MyTextBox();
            this.TxtJc = new CustomControl.MyTextBox();
            this.TxtHisCode = new CustomControl.MyTextBox();
            this.ComboDept = new ZTLisControl.ComboDept();
            this.TxtTitle = new CustomControl.MyTextBox();
            this.TxtLicenseNo = new CustomControl.MyTextBox();
            this.TxtMobile = new CustomControl.MyTextBox();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.BtnSave = new CustomControl.MyButton();
            this.BtnCancel = new CustomControl.MyButton();
            this.TableLayoutPanel1.SuspendLayout();
            this.Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // TableLayoutPanel1
            // 
            this.TableLayoutPanel1.ColumnCount = 4;
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.Controls.Add(this.TxtCode, 1, 0);
            this.TableLayoutPanel1.Controls.Add(this.TxtName, 1, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtJc, 2, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtHisCode, 2, 0);
            this.TableLayoutPanel1.Controls.Add(this.ComboDept, 1, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtTitle, 2, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtLicenseNo, 1, 3);
            this.TableLayoutPanel1.Controls.Add(this.TxtMobile, 2, 3);
            this.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.TableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.TableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.TableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.TableLayoutPanel1.Name = "TableLayoutPanel1";
            this.TableLayoutPanel1.RowCount = 4;
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.Size = new System.Drawing.Size(520, 116);
            this.TableLayoutPanel1.TabIndex = 8;
            // 
            // TxtCode
            // 
            this.TxtCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtCode.Captain = "编    码";
            this.TxtCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtCode.CaptainWidth = 70F;
            this.TxtCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtCode.EditMask = null;
            this.TxtCode.Location = new System.Drawing.Point(13, 3);
            this.TxtCode.Multiline = false;
            this.TxtCode.Name = "TxtCode";
            this.TxtCode.PasswordChar = '\0';
            this.TxtCode.ReadOnly = false;
            this.TxtCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtCode.SelectionStart = 0;
            this.TxtCode.SelectStart = 0;
            this.TxtCode.Size = new System.Drawing.Size(244, 23);
            this.TxtCode.TabIndex = 0;
            this.TxtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtCode.Watermark = null;
            // 
            // TxtName
            // 
            this.TxtName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtName.Captain = "姓    名";
            this.TxtName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtName.CaptainWidth = 70F;
            this.TxtName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtName.EditMask = null;
            this.TxtName.Location = new System.Drawing.Point(13, 32);
            this.TxtName.Multiline = false;
            this.TxtName.Name = "TxtName";
            this.TxtName.PasswordChar = '\0';
            this.TxtName.ReadOnly = false;
            this.TxtName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtName.SelectionStart = 0;
            this.TxtName.SelectStart = 0;
            this.TxtName.Size = new System.Drawing.Size(244, 23);
            this.TxtName.TabIndex = 2;
            this.TxtName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtName.Watermark = null;
            this.TxtName.Validated += new System.EventHandler(this.TxtName_Validated);
            // 
            // TxtJc
            // 
            this.TxtJc.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtJc.Captain = "简    称";
            this.TxtJc.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtJc.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtJc.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtJc.CaptainWidth = 70F;
            this.TxtJc.ContentForeColor = System.Drawing.Color.Black;
            this.TxtJc.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtJc.EditMask = null;
            this.TxtJc.Location = new System.Drawing.Point(263, 32);
            this.TxtJc.Multiline = false;
            this.TxtJc.Name = "TxtJc";
            this.TxtJc.PasswordChar = '\0';
            this.TxtJc.ReadOnly = false;
            this.TxtJc.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtJc.SelectionStart = 0;
            this.TxtJc.SelectStart = 0;
            this.TxtJc.Size = new System.Drawing.Size(244, 23);
            this.TxtJc.TabIndex = 3;
            this.TxtJc.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtJc.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtJc.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtJc.Watermark = null;
            // 
            // TxtHisCode
            // 
            this.TxtHisCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtHisCode.Captain = "HIS编码";
            this.TxtHisCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtHisCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtHisCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtHisCode.CaptainWidth = 70F;
            this.TxtHisCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtHisCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtHisCode.EditMask = null;
            this.TxtHisCode.Location = new System.Drawing.Point(263, 3);
            this.TxtHisCode.Multiline = false;
            this.TxtHisCode.Name = "TxtHisCode";
            this.TxtHisCode.PasswordChar = '\0';
            this.TxtHisCode.ReadOnly = false;
            this.TxtHisCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtHisCode.SelectionStart = 0;
            this.TxtHisCode.SelectStart = 0;
            this.TxtHisCode.Size = new System.Drawing.Size(244, 23);
            this.TxtHisCode.TabIndex = 1;
            this.TxtHisCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtHisCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtHisCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtHisCode.Watermark = null;
            // 
            // ComboDept
            // 
            this.ComboDept.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboDept.Bookmark = -1;
            this.ComboDept.Captain = "科    室";
            this.ComboDept.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboDept.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboDept.CaptainWidth = 70F;
            this.ComboDept.ColumnCaptionHeight = 20;
            this.ComboDept.DataSource = null;
            this.ComboDept.DataView = null;
            this.ComboDept.ItemHeight = 18;
            this.ComboDept.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboDept.Location = new System.Drawing.Point(13, 61);
            this.ComboDept.MaximumSize = new System.Drawing.Size(10000, 23);
            this.ComboDept.MinimumSize = new System.Drawing.Size(0, 20);
            this.ComboDept.Name = "ComboDept";
            this.ComboDept.ReadOnly = false;
            this.ComboDept.Row = 0;
            this.ComboDept.Size = new System.Drawing.Size(244, 23);
            this.ComboDept.TabIndex = 4;
            this.ComboDept.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // TxtTitle
            // 
            this.TxtTitle.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtTitle.Captain = "职    称";
            this.TxtTitle.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtTitle.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtTitle.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtTitle.CaptainWidth = 70F;
            this.TxtTitle.ContentForeColor = System.Drawing.Color.Black;
            this.TxtTitle.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtTitle.EditMask = null;
            this.TxtTitle.Location = new System.Drawing.Point(263, 61);
            this.TxtTitle.Multiline = false;
            this.TxtTitle.Name = "TxtTitle";
            this.TxtTitle.PasswordChar = '\0';
            this.TxtTitle.ReadOnly = false;
            this.TxtTitle.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtTitle.SelectionStart = 0;
            this.TxtTitle.SelectStart = 0;
            this.TxtTitle.Size = new System.Drawing.Size(244, 23);
            this.TxtTitle.TabIndex = 5;
            this.TxtTitle.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtTitle.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtTitle.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtTitle.Watermark = null;
            // 
            // TxtLicenseNo
            // 
            this.TxtLicenseNo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtLicenseNo.Captain = "执业证书号";
            this.TxtLicenseNo.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtLicenseNo.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLicenseNo.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtLicenseNo.CaptainWidth = 70F;
            this.TxtLicenseNo.ContentForeColor = System.Drawing.Color.Black;
            this.TxtLicenseNo.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtLicenseNo.EditMask = null;
            this.TxtLicenseNo.Location = new System.Drawing.Point(13, 90);
            this.TxtLicenseNo.Multiline = false;
            this.TxtLicenseNo.Name = "TxtLicenseNo";
            this.TxtLicenseNo.PasswordChar = '\0';
            this.TxtLicenseNo.ReadOnly = false;
            this.TxtLicenseNo.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtLicenseNo.SelectionStart = 0;
            this.TxtLicenseNo.SelectStart = 0;
            this.TxtLicenseNo.Size = new System.Drawing.Size(244, 23);
            this.TxtLicenseNo.TabIndex = 6;
            this.TxtLicenseNo.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtLicenseNo.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLicenseNo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtLicenseNo.Watermark = null;
            // 
            // TxtMobile
            // 
            this.TxtMobile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtMobile.Captain = "联系方式";
            this.TxtMobile.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtMobile.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtMobile.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtMobile.CaptainWidth = 70F;
            this.TxtMobile.ContentForeColor = System.Drawing.Color.Black;
            this.TxtMobile.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtMobile.EditMask = null;
            this.TxtMobile.Location = new System.Drawing.Point(263, 90);
            this.TxtMobile.Multiline = false;
            this.TxtMobile.Name = "TxtMobile";
            this.TxtMobile.PasswordChar = '\0';
            this.TxtMobile.ReadOnly = false;
            this.TxtMobile.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtMobile.SelectionStart = 0;
            this.TxtMobile.SelectStart = 0;
            this.TxtMobile.Size = new System.Drawing.Size(244, 23);
            this.TxtMobile.TabIndex = 7;
            this.TxtMobile.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtMobile.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtMobile.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtMobile.Watermark = null;
            // 
            // Panel1
            // 
            this.Panel1.Controls.Add(this.BtnSave);
            this.Panel1.Controls.Add(this.BtnCancel);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel1.Location = new System.Drawing.Point(0, 116);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(520, 38);
            this.Panel1.TabIndex = 9;
            // 
            // BtnSave
            // 
            this.BtnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(360, 6);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnCancel.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnCancel.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnCancel.Location = new System.Drawing.Point(441, 6);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(70, 35);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // DictDoctor2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(520, 154);
            this.Controls.Add(this.TableLayoutPanel1);
            this.Controls.Add(this.Panel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Name = "DictDoctor2";
            this.Text = "医生信息";
            this.Load += new System.EventHandler(this.DictDoctor2_Load);
            this.TableLayoutPanel1.ResumeLayout(false);
            this.Panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.TableLayoutPanel TableLayoutPanel1;
        internal CustomControl.MyTextBox TxtCode;
        internal CustomControl.MyTextBox TxtName;
        internal CustomControl.MyTextBox TxtJc;
        internal CustomControl.MyTextBox TxtHisCode;
        internal ZTLisControl.ComboDept ComboDept;
        internal CustomControl.MyTextBox TxtTitle;
        internal CustomControl.MyTextBox TxtLicenseNo;
        internal CustomControl.MyTextBox TxtMobile;
        internal System.Windows.Forms.Panel Panel1;
        internal CustomControl.MyButton BtnSave;
        internal CustomControl.MyButton BtnCancel;
    }
}