﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabInstrument.cs
*
* 功 能： N/A
* 类 名： DalLabInstrument
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabInstrument
	/// </summary>
	public partial class DalLabInstrument : IDalLabInstrument
	{
		public DalLabInstrument()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("InstrumentId", "LabInstrument");
		}

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(InstrCode) FROM LabInstrument where InstrCode NOT LIKE '%[^0-9]%' And LEN(InstrCode)=" + length, length));
			return max;
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int InstrumentId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabInstrument");
			strSql.Append(" where InstrumentId=@InstrumentId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4)
			};
			parameters[0].Value = InstrumentId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlLabInstrument model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabInstrument(");
			strSql.Append("InstrCode,InstrName,Vendor,Model,SerialNo,Location,ConnType,MsgFormatType,ComPort,BaudRate,Parity,DataBits,StopBits,IpHost,IpPort,KeepAliveSec,FilePath,DbType,DbConnStr,ApiBaseUrl,ApiAuthToken,ProtocolType,MsgHeaderFormatType,MsgHeader,MsgTail,AckSuccess,AckFailure,DriverVersion,FirmwareVersion,CustomDriverClass,Status,Description,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@InstrCode,@InstrName,@Vendor,@Model,@SerialNo,@Location,@ConnType,@MsgFormatType,@ComPort,@BaudRate,@Parity,@DataBits,@StopBits,@IpHost,@IpPort,@KeepAliveSec,@FilePath,@DbType,@DbConnStr,@ApiBaseUrl,@ApiAuthToken,@ProtocolType,@MsgHeaderFormatType,@MsgHeader,@MsgTail,@AckSuccess,@AckFailure,@DriverVersion,@FirmwareVersion,@CustomDriverClass,@Status,@Description,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrCode", SqlDbType.NVarChar,20),
					new SqlParameter("@InstrName", SqlDbType.NVarChar,100),
					new SqlParameter("@Vendor", SqlDbType.NVarChar,60),
					new SqlParameter("@Model", SqlDbType.NVarChar,60),
					new SqlParameter("@SerialNo", SqlDbType.NVarChar,60),
					new SqlParameter("@Location", SqlDbType.NVarChar,60),
					new SqlParameter("@ConnType", SqlDbType.TinyInt,1),
					new SqlParameter("@MsgFormatType", SqlDbType.TinyInt,1),
					new SqlParameter("@ComPort", SqlDbType.NVarChar,10),
					new SqlParameter("@BaudRate", SqlDbType.Int,4),
					new SqlParameter("@Parity", SqlDbType.Char,1),
					new SqlParameter("@DataBits", SqlDbType.TinyInt,1),
					new SqlParameter("@StopBits", SqlDbType.VarChar,10),
					new SqlParameter("@IpHost", SqlDbType.VarChar,255),
					new SqlParameter("@IpPort", SqlDbType.Int,4),
					new SqlParameter("@KeepAliveSec", SqlDbType.SmallInt,2),
					new SqlParameter("@FilePath", SqlDbType.NVarChar,260),
					new SqlParameter("@DbType", SqlDbType.NVarChar,50),
					new SqlParameter("@DbConnStr", SqlDbType.NVarChar,400),
					new SqlParameter("@ApiBaseUrl", SqlDbType.NVarChar,200),
					new SqlParameter("@ApiAuthToken", SqlDbType.NVarChar,400),
					new SqlParameter("@ProtocolType", SqlDbType.TinyInt,1),
					new SqlParameter("@MsgHeaderFormatType", SqlDbType.TinyInt,1),
					new SqlParameter("@MsgHeader", SqlDbType.NVarChar,100),
					new SqlParameter("@MsgTail", SqlDbType.NVarChar,100),
					new SqlParameter("@AckSuccess", SqlDbType.NVarChar,100),
					new SqlParameter("@AckFailure", SqlDbType.NVarChar,100),
					new SqlParameter("@DriverVersion", SqlDbType.NVarChar,20),
					new SqlParameter("@FirmwareVersion", SqlDbType.NVarChar,40),
					new SqlParameter("@CustomDriverClass", SqlDbType.NVarChar,255),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.InstrCode;
			parameters[1].Value = model.InstrName;
			parameters[2].Value = Common.Tools.IsValueNull(model.Vendor);
			parameters[3].Value = Common.Tools.IsValueNull(model.Model);
			parameters[4].Value = Common.Tools.IsValueNull(model.SerialNo);
			parameters[5].Value = Common.Tools.IsValueNull(model.Location);
			parameters[6].Value = model.ConnType;
			parameters[7].Value = model.MsgFormatType;
			parameters[8].Value = Common.Tools.IsValueNull(model.ComPort);
			parameters[9].Value = Common.Tools.IsValueNull(model.BaudRate);
			parameters[10].Value = Common.Tools.IsValueNull(model.Parity);
			parameters[11].Value = Common.Tools.IsValueNull(model.DataBits);
			parameters[12].Value = Common.Tools.IsValueNull(model.StopBits);
			parameters[13].Value = Common.Tools.IsValueNull(model.IpHost);
			parameters[14].Value = Common.Tools.IsValueNull(model.IpPort);
			parameters[15].Value = Common.Tools.IsValueNull(model.KeepAliveSec);
			parameters[16].Value = Common.Tools.IsValueNull(model.FilePath);
			parameters[17].Value = Common.Tools.IsValueNull(model.DbType);
			parameters[18].Value = Common.Tools.IsValueNull(model.DbConnStr);
			parameters[19].Value = Common.Tools.IsValueNull(model.ApiBaseUrl);
			parameters[20].Value = Common.Tools.IsValueNull(model.ApiAuthToken);
			parameters[21].Value = model.ProtocolType;
			parameters[22].Value = model.MsgHeaderFormatType;
			parameters[23].Value = model.MsgHeader;
			parameters[24].Value = model.MsgTail;
			parameters[25].Value = model.AckSuccess;
			parameters[26].Value = model.AckFailure;
			parameters[27].Value = model.DriverVersion;
			parameters[28].Value = model.FirmwareVersion;
			parameters[29].Value = Common.Tools.IsValueNull(model.CustomDriverClass);
			parameters[30].Value = model.Status;
			parameters[31].Value = model.Description;
			parameters[32].Value = model.CreatedAt;
			parameters[33].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabInstrument model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabInstrument set ");
			strSql.Append("InstrCode=@InstrCode,");
			strSql.Append("InstrName=@InstrName,");
			strSql.Append("Vendor=@Vendor,");
			strSql.Append("Model=@Model,");
			strSql.Append("SerialNo=@SerialNo,");
			strSql.Append("Location=@Location,");
			strSql.Append("ConnType=@ConnType,");
			strSql.Append("MsgFormatType=@MsgFormatType,");
			strSql.Append("ComPort=@ComPort,");
			strSql.Append("BaudRate=@BaudRate,");
			strSql.Append("Parity=@Parity,");
			strSql.Append("DataBits=@DataBits,");
			strSql.Append("StopBits=@StopBits,");
			strSql.Append("IpHost=@IpHost,");
			strSql.Append("IpPort=@IpPort,");
			strSql.Append("KeepAliveSec=@KeepAliveSec,");
			strSql.Append("FilePath=@FilePath,");
			strSql.Append("DbType=@DbType,");
			strSql.Append("DbConnStr=@DbConnStr,");
			strSql.Append("ApiBaseUrl=@ApiBaseUrl,");
			strSql.Append("ApiAuthToken=@ApiAuthToken,");
			strSql.Append("ProtocolType=@ProtocolType,");
			strSql.Append("MsgHeaderFormatType=@MsgHeaderFormatType,");
			strSql.Append("MsgHeader=@MsgHeader,");
			strSql.Append("MsgTail=@MsgTail,");
			strSql.Append("AckSuccess=@AckSuccess,");
			strSql.Append("AckFailure=@AckFailure,");
			strSql.Append("DriverVersion=@DriverVersion,");
			strSql.Append("FirmwareVersion=@FirmwareVersion,");
			strSql.Append("CustomDriverClass=@CustomDriverClass,");
			strSql.Append("Status=@Status,");
			strSql.Append("Description=@Description,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where InstrumentId=@InstrumentId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrCode", SqlDbType.NVarChar,20),
					new SqlParameter("@InstrName", SqlDbType.NVarChar,100),
					new SqlParameter("@Vendor", SqlDbType.NVarChar,60),
					new SqlParameter("@Model", SqlDbType.NVarChar,60),
					new SqlParameter("@SerialNo", SqlDbType.NVarChar,60),
					new SqlParameter("@Location", SqlDbType.NVarChar,60),
					new SqlParameter("@ConnType", SqlDbType.TinyInt,1),
					new SqlParameter("@MsgFormatType", SqlDbType.TinyInt,1),
					new SqlParameter("@ComPort", SqlDbType.NVarChar,10),
					new SqlParameter("@BaudRate", SqlDbType.Int,4),
					new SqlParameter("@Parity", SqlDbType.Char,1),
					new SqlParameter("@DataBits", SqlDbType.TinyInt,1),
					new SqlParameter("@StopBits", SqlDbType.VarChar,10),
					new SqlParameter("@IpHost", SqlDbType.VarChar,255),
					new SqlParameter("@IpPort", SqlDbType.Int,4),
					new SqlParameter("@KeepAliveSec", SqlDbType.SmallInt,2),
					new SqlParameter("@FilePath", SqlDbType.NVarChar,260),
					new SqlParameter("@DbType", SqlDbType.NVarChar,50),
					new SqlParameter("@DbConnStr", SqlDbType.NVarChar,400),
					new SqlParameter("@ApiBaseUrl", SqlDbType.NVarChar,200),
					new SqlParameter("@ApiAuthToken", SqlDbType.NVarChar,400),
					new SqlParameter("@ProtocolType", SqlDbType.TinyInt,1),
					new SqlParameter("@MsgHeaderFormatType", SqlDbType.TinyInt,1),
					new SqlParameter("@MsgHeader", SqlDbType.NVarChar,100),
					new SqlParameter("@MsgTail", SqlDbType.NVarChar,100),
					new SqlParameter("@AckSuccess", SqlDbType.NVarChar,100),
					new SqlParameter("@AckFailure", SqlDbType.NVarChar,100),
					new SqlParameter("@DriverVersion", SqlDbType.NVarChar,20),
					new SqlParameter("@FirmwareVersion", SqlDbType.NVarChar,40),
					new SqlParameter("@CustomDriverClass", SqlDbType.NVarChar,255),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@InstrumentId", SqlDbType.Int,4)};
			parameters[0].Value = model.InstrCode;
			parameters[1].Value = model.InstrName;
			parameters[2].Value = Common.Tools.IsValueNull(model.Vendor);
			parameters[3].Value = Common.Tools.IsValueNull(model.Model);
			parameters[4].Value = Common.Tools.IsValueNull(model.SerialNo);
			parameters[5].Value = Common.Tools.IsValueNull(model.Location);
			parameters[6].Value = model.ConnType;
			parameters[7].Value = model.MsgFormatType;
			parameters[8].Value = Common.Tools.IsValueNull(model.ComPort);
			parameters[9].Value = Common.Tools.IsValueNull(model.BaudRate);
			parameters[10].Value = Common.Tools.IsValueNull(model.Parity);
			parameters[11].Value = Common.Tools.IsValueNull(model.DataBits);
			parameters[12].Value = Common.Tools.IsValueNull(model.StopBits);
			parameters[13].Value = Common.Tools.IsValueNull(model.IpHost);
			parameters[14].Value = Common.Tools.IsValueNull(model.IpPort);
			parameters[15].Value = Common.Tools.IsValueNull(model.KeepAliveSec);
			parameters[16].Value = Common.Tools.IsValueNull(model.FilePath);
			parameters[17].Value = Common.Tools.IsValueNull(model.DbType);
			parameters[18].Value = Common.Tools.IsValueNull(model.DbConnStr);
			parameters[19].Value = Common.Tools.IsValueNull(model.ApiBaseUrl);
			parameters[20].Value = model.ApiAuthToken;
			parameters[21].Value = model.ProtocolType;
			parameters[22].Value = model.MsgHeaderFormatType;
			parameters[23].Value = model.MsgHeader;
			parameters[24].Value = model.MsgTail;
			parameters[25].Value = model.AckSuccess;
			parameters[26].Value = model.AckFailure;
			parameters[27].Value = model.DriverVersion;
			parameters[28].Value = model.FirmwareVersion;
			parameters[29].Value = Common.Tools.IsValueNull(model.CustomDriverClass);
			parameters[30].Value = model.Status;
			parameters[31].Value = model.Description;
			parameters[32].Value = model.CreatedAt;
			parameters[33].Value = model.UpdatedAt;
			parameters[34].Value = model.InstrumentId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int InstrumentId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrument ");
			strSql.Append(" where InstrumentId=@InstrumentId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4)
			};
			parameters[0].Value = InstrumentId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string InstrumentIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrument ");
			strSql.Append(" where InstrumentId in (" + InstrumentIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrument GetModel(int InstrumentId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 InstrumentId,InstrCode,InstrName,Vendor,Model,SerialNo,Location,ConnType,MsgFormatType,ComPort,BaudRate,Parity,DataBits,StopBits,IpHost,IpPort,KeepAliveSec,FilePath,DbType,DbConnStr,ApiBaseUrl,ApiAuthToken,ProtocolType,MsgHeaderFormatType,MsgHeader,MsgTail,AckSuccess,AckFailure,DriverVersion,FirmwareVersion,CustomDriverClass,Status,Description,CreatedAt,UpdatedAt from LabInstrument ");
			strSql.Append(" where InstrumentId=@InstrumentId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4)
			};
			parameters[0].Value = InstrumentId;

			Model.MdlLabInstrument model = new Model.MdlLabInstrument();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrument DataRowToModel(DataRow row)
		{
			Model.MdlLabInstrument model = new Model.MdlLabInstrument();
			if (row != null)
			{
				if (row["InstrumentId"] != null && row["InstrumentId"].ToString() != "")
				{
					model.InstrumentId = int.Parse(row["InstrumentId"].ToString());
				}
				if (row["InstrCode"] != null)
				{
					model.InstrCode = row["InstrCode"].ToString();
				}
				if (row["InstrName"] != null)
				{
					model.InstrName = row["InstrName"].ToString();
				}
				if (row["Vendor"] != null)
				{
					model.Vendor = row["Vendor"].ToString();
				}
				if (row["Model"] != null)
				{
					model.Model = row["Model"].ToString();
				}
				if (row["SerialNo"] != null)
				{
					model.SerialNo = row["SerialNo"].ToString();
				}
				if (row["Location"] != null)
				{
					model.Location = row["Location"].ToString();
				}
				if (row["ConnType"] != null && row["ConnType"].ToString() != "")
				{
					model.ConnType = int.Parse(row["ConnType"].ToString());
				}
				if (row["MsgFormatType"] != null && row["MsgFormatType"].ToString() != "")
				{
					model.MsgFormatType = int.Parse(row["MsgFormatType"].ToString());
				}
				if (row["ComPort"] != null)
				{
					model.ComPort = row["ComPort"].ToString();
				}
				if (row["BaudRate"] != null && row["BaudRate"].ToString() != "")
				{
					model.BaudRate = int.Parse(row["BaudRate"].ToString());
				}
				if (row["Parity"] != null)
				{
					model.Parity = row["Parity"].ToString();
				}
				if (row["DataBits"] != null && row["DataBits"].ToString() != "")
				{
					model.DataBits = int.Parse(row["DataBits"].ToString());
				}
				if (row["StopBits"] != null && row["StopBits"].ToString() != "")
				{
					model.StopBits = row["StopBits"].ToString();
				}
				if (row["IpHost"] != null)
				{
					model.IpHost = row["IpHost"].ToString();
				}
				if (row["IpPort"] != null && row["IpPort"].ToString() != "")
				{
					model.IpPort = int.Parse(row["IpPort"].ToString());
				}
				if (row["KeepAliveSec"] != null && row["KeepAliveSec"].ToString() != "")
				{
					model.KeepAliveSec = int.Parse(row["KeepAliveSec"].ToString());
				}
				if (row["FilePath"] != null)
				{
					model.FilePath = row["FilePath"].ToString();
				}
				if (row["DbType"] != null)
				{
					model.DbType = row["DbType"].ToString();
				}
				if (row["DbConnStr"] != null)
				{
					model.DbConnStr = row["DbConnStr"].ToString();
				}
				if (row["ApiBaseUrl"] != null)
				{
					model.ApiBaseUrl = row["ApiBaseUrl"].ToString();
				}
				if (row["ApiAuthToken"] != null)
				{
					model.ApiAuthToken = row["ApiAuthToken"].ToString();
				}
				if (row["ProtocolType"] != null && row["ProtocolType"].ToString() != "")
				{
					model.ProtocolType = int.Parse(row["ProtocolType"].ToString());
				}
				if (row["MsgHeaderFormatType"] != null && row["MsgHeaderFormatType"].ToString() != "")
				{
					model.MsgHeaderFormatType = int.Parse(row["MsgHeaderFormatType"].ToString());
				}
				if (row["MsgHeader"] != null)
				{
					model.MsgHeader = row["MsgHeader"].ToString();
				}
				if (row["MsgTail"] != null)
				{
					model.MsgTail = row["MsgTail"].ToString();
				}
				if (row["AckSuccess"] != null)
				{
					model.AckSuccess = row["AckSuccess"].ToString();
				}
				if (row["AckFailure"] != null)
				{
					model.AckFailure = row["AckFailure"].ToString();
				}
				if (row["DriverVersion"] != null)
				{
					model.DriverVersion = row["DriverVersion"].ToString();
				}
				if (row["FirmwareVersion"] != null)
				{
					model.FirmwareVersion = row["FirmwareVersion"].ToString();
				}
				if (row["CustomDriverClass"] != null)
				{
					model.CustomDriverClass = row["CustomDriverClass"].ToString();
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["Description"] != null)
				{
					model.Description = row["Description"].ToString();
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select InstrumentId,InstrCode,InstrName,Vendor,Model,SerialNo,Location,ConnType,MsgFormatType,ComPort,BaudRate,Parity,DataBits,StopBits,IpHost,IpPort,KeepAliveSec,FilePath,DbType,DbConnStr,ApiBaseUrl,ApiAuthToken,ProtocolType,MsgHeaderFormatType,MsgHeader,MsgTail,AckSuccess,AckFailure,DriverVersion,FirmwareVersion,CustomDriverClass,Status,Description,CreatedAt,UpdatedAt ");
			// 串口_字符串,
			// 串口_字节流,
			// TCP,
			// 文件共享,
			// 中间库,
			// REST接口,
			// HL7_MLLP
			strSql.Append(",case when ConnType=0 then '串口' when ConnType=1 then 'TCP' when ConnType=2 then '文件共享' when ConnType=3 then '中间库' when ConnType=4 then 'REST接口' when ConnType=5 then 'HL7_MLLP' end ConnTypeName ");

			// ASTM = 0,
			// LIS2 = 1,
			// POCT1_A = 2,
			// HL7 = 3,
			// 自定义 = 4
			strSql.Append(",case when ProtocolType=0 then 'ASTM' when ProtocolType=1 then 'LIS2' when ProtocolType=2 then 'POCT1_A' when ProtocolType=3 then 'HL7' when ProtocolType=4 then '自定义' end ProtocolTypeName ");
			strSql.Append(",case when MsgFormatType=0 then '字符串' when MsgFormatType=1 then '字节流' end MsgFormatTypeName ");
			strSql.Append(",case when MsgHeaderFormatType=0 then '字符串' when MsgHeaderFormatType=1 then '字节流' end MsgHeaderFormatTypeName ");
			strSql.Append(" FROM LabInstrument ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" InstrumentId,InstrCode,InstrName,Vendor,Model,SerialNo,Location,ConnType,MsgFormatType,ComPort,BaudRate,Parity,DataBits,StopBits,IpHost,IpPort,KeepAliveSec,FilePath,DbType,DbConnStr,ApiBaseUrl,ApiAuthToken,ProtocolType,MsgHeaderFormatType,MsgHeader,MsgTail,AckSuccess,AckFailure,DriverVersion,FirmwareVersion,CustomDriverClass,Status,Description,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM LabInstrument ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabInstrument ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.InstrumentId desc");
			}
			strSql.Append(")AS Row, T.*  from LabInstrument T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabInstrument";
			parameters[1].Value = "InstrumentId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

