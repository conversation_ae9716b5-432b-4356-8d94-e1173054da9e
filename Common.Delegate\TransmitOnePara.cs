﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common.Delegate
{
    public class TransmitOnePara<T>
    {
        public delegate bool SetOneParaDelegate(T value);

        public event SetOneParaDelegate SetOnePara;
        public bool OnSetOnePara(T value)
        {
            if (SetOnePara != null)
            {
              return  SetOnePara(value);
            }
            else
            {
                return false;
            }
        }
    }
}
