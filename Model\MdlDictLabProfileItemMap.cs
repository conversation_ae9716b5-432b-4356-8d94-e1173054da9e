﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictLabProfileItemMap.cs
*
* 功 能： N/A
* 类 名： MdlDictLabProfileItemMap
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:46:35   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验组合-项目映射表
	/// </summary>
	[Serializable]
	public partial class MdlDictLabProfileItemMap
	{
		public MdlDictLabProfileItemMap()
		{ }
		#region Model
		private long _mapid;
		private int _profileid;
		private int _labitemid;
		private int? _sequenceno = 0;
		private bool _isdefault = true;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 映射ID，自增主键
		/// </summary>
		public long MapId
		{
			set { _mapid = value; }
			get { return _mapid; }
		}
		/// <summary>
		/// 关联的组合ID
		/// </summary>
		public int ProfileId
		{
			set { _profileid = value; }
			get { return _profileid; }
		}
		/// <summary>
		/// 关联的检验项目ID
		/// </summary>
		public int LabItemId
		{
			set { _labitemid = value; }
			get { return _labitemid; }
		}
		/// <summary>
		/// 报告显示顺序，决定项目在报告单中的排列位置
		/// </summary>
		public int? SequenceNo
		{
			set { _sequenceno = value; }
			get { return _sequenceno; }
		}
		/// <summary>
		/// 是否为默认项目：1=是(常规开立包含此项), 0=否(可选项目，需额外勾选)
		/// </summary>
		public bool IsDefault
		{
			set { _isdefault = value; }
			get { return _isdefault; }
		}
		/// <summary>
		/// 记录创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 记录更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

