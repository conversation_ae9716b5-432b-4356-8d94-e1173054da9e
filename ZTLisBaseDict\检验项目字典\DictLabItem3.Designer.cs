namespace ZTLisBaseDict
{
    partial class DictLabItem3
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.TableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.ComboMethod = new ZTLisControl.ComboMethod();
            this.ComboSex = new ZTLisControl.SingleSex();
            this.NumAgeLower = new CustomControl.MyNumericEdit();
            this.NumAgeUpper = new CustomControl.MyNumericEdit();
            this.ComboAgeUnit = new ZTLisControl.SingleAgeUnit();
            this.TxtLowerLimit = new CustomControl.MyTextBox();
            this.TxtUpperLimit = new CustomControl.MyTextBox();
            this.TxtTextualRefValue = new CustomControl.MyTextBox();
            this.TxtAlertLow = new CustomControl.MyTextBox();
            this.TxtAlertHigh = new CustomControl.MyTextBox();
            this.TxtCriticalLow = new CustomControl.MyTextBox();
            this.TxtCriticalHigh = new CustomControl.MyTextBox();
            this.DateEffectiveFrom = new CustomControl.MyDateEdit();
            this.DateEffectiveTo = new CustomControl.MyDateEdit();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.BtnSave = new CustomControl.MyButton();
            this.BtnCancel = new CustomControl.MyButton();
            this.comboUnit1 = new ZTLisControl.ComboUnit();
            this.TableLayoutPanel1.SuspendLayout();
            this.Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // TableLayoutPanel1
            // 
            this.TableLayoutPanel1.ColumnCount = 4;
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.TableLayoutPanel1.Controls.Add(this.ComboMethod, 1, 0);
            this.TableLayoutPanel1.Controls.Add(this.ComboSex, 2, 0);
            this.TableLayoutPanel1.Controls.Add(this.NumAgeLower, 1, 1);
            this.TableLayoutPanel1.Controls.Add(this.NumAgeUpper, 2, 1);
            this.TableLayoutPanel1.Controls.Add(this.ComboAgeUnit, 1, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtLowerLimit, 1, 3);
            this.TableLayoutPanel1.Controls.Add(this.TxtUpperLimit, 2, 3);
            this.TableLayoutPanel1.Controls.Add(this.TxtTextualRefValue, 1, 4);
            this.TableLayoutPanel1.Controls.Add(this.TxtAlertLow, 1, 5);
            this.TableLayoutPanel1.Controls.Add(this.TxtAlertHigh, 2, 5);
            this.TableLayoutPanel1.Controls.Add(this.TxtCriticalLow, 1, 6);
            this.TableLayoutPanel1.Controls.Add(this.TxtCriticalHigh, 2, 6);
            this.TableLayoutPanel1.Controls.Add(this.DateEffectiveFrom, 1, 7);
            this.TableLayoutPanel1.Controls.Add(this.DateEffectiveTo, 2, 7);
            this.TableLayoutPanel1.Controls.Add(this.comboUnit1, 2, 4);
            this.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.TableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.TableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.TableLayoutPanel1.Name = "TableLayoutPanel1";
            this.TableLayoutPanel1.RowCount = 8;
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.Size = new System.Drawing.Size(600, 232);
            this.TableLayoutPanel1.TabIndex = 0;
            // 
            // ComboMethod
            // 
            this.ComboMethod.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboMethod.Bookmark = -1;
            this.ComboMethod.Captain = "检验方法";
            this.ComboMethod.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboMethod.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboMethod.CaptainWidth = 70F;
            this.ComboMethod.ColumnCaptionHeight = 20;
            this.ComboMethod.DataSource = null;
            this.ComboMethod.DataView = null;
            this.ComboMethod.ItemHeight = 18;
            this.ComboMethod.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboMethod.Location = new System.Drawing.Point(43, 3);
            this.ComboMethod.MaximumSize = new System.Drawing.Size(10000, 23);
            this.ComboMethod.MinimumSize = new System.Drawing.Size(0, 20);
            this.ComboMethod.Name = "ComboMethod";
            this.ComboMethod.ReadOnly = false;
            this.ComboMethod.Row = 0;
            this.ComboMethod.Size = new System.Drawing.Size(254, 23);
            this.ComboMethod.TabIndex = 0;
            this.ComboMethod.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // ComboSex
            // 
            this.ComboSex.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboSex.Captain = "性    别";
            this.ComboSex.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboSex.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboSex.CaptainWidth = 69F;
            this.ComboSex.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.ComboSex.ItemHeight = 18;
            this.ComboSex.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.ComboSex.Location = new System.Drawing.Point(303, 3);
            this.ComboSex.MaximumSize = new System.Drawing.Size(10000, 23);
            this.ComboSex.MinimumSize = new System.Drawing.Size(0, 20);
            this.ComboSex.Name = "ComboSex";
            this.ComboSex.ReadOnly = false;
            this.ComboSex.Size = new System.Drawing.Size(254, 23);
            this.ComboSex.TabIndex = 1;
            this.ComboSex.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // NumAgeLower
            // 
            this.NumAgeLower.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumAgeLower.Captain = "年龄下限";
            this.NumAgeLower.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumAgeLower.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumAgeLower.CaptainWidth = 70F;
            this.NumAgeLower.Location = new System.Drawing.Point(43, 32);
            this.NumAgeLower.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumAgeLower.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumAgeLower.Name = "NumAgeLower";
            this.NumAgeLower.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumAgeLower.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumAgeLower.ReadOnly = false;
            this.NumAgeLower.Size = new System.Drawing.Size(254, 23);
            this.NumAgeLower.TabIndex = 2;
            this.NumAgeLower.ValueIsDbNull = false;
            // 
            // NumAgeUpper
            // 
            this.NumAgeUpper.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumAgeUpper.Captain = "年龄上限";
            this.NumAgeUpper.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumAgeUpper.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumAgeUpper.CaptainWidth = 70F;
            this.NumAgeUpper.Location = new System.Drawing.Point(303, 32);
            this.NumAgeUpper.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumAgeUpper.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumAgeUpper.Name = "NumAgeUpper";
            this.NumAgeUpper.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumAgeUpper.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumAgeUpper.ReadOnly = false;
            this.NumAgeUpper.Size = new System.Drawing.Size(254, 23);
            this.NumAgeUpper.TabIndex = 3;
            this.NumAgeUpper.ValueIsDbNull = false;
            // 
            // ComboAgeUnit
            // 
            this.ComboAgeUnit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboAgeUnit.Captain = "年龄单位";
            this.ComboAgeUnit.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboAgeUnit.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboAgeUnit.CaptainWidth = 69F;
            this.ComboAgeUnit.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.ComboAgeUnit.ItemHeight = 18;
            this.ComboAgeUnit.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.ComboAgeUnit.Location = new System.Drawing.Point(43, 61);
            this.ComboAgeUnit.MaximumSize = new System.Drawing.Size(10000, 23);
            this.ComboAgeUnit.MinimumSize = new System.Drawing.Size(0, 20);
            this.ComboAgeUnit.Name = "ComboAgeUnit";
            this.ComboAgeUnit.ReadOnly = false;
            this.ComboAgeUnit.Size = new System.Drawing.Size(254, 23);
            this.ComboAgeUnit.TabIndex = 4;
            this.ComboAgeUnit.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // TxtLowerLimit
            // 
            this.TxtLowerLimit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtLowerLimit.Captain = "参考下限";
            this.TxtLowerLimit.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtLowerLimit.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLowerLimit.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtLowerLimit.CaptainWidth = 70F;
            this.TxtLowerLimit.ContentForeColor = System.Drawing.Color.Black;
            this.TxtLowerLimit.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtLowerLimit.EditMask = null;
            this.TxtLowerLimit.Location = new System.Drawing.Point(43, 90);
            this.TxtLowerLimit.Multiline = false;
            this.TxtLowerLimit.Name = "TxtLowerLimit";
            this.TxtLowerLimit.PasswordChar = '\0';
            this.TxtLowerLimit.ReadOnly = false;
            this.TxtLowerLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtLowerLimit.SelectionStart = 0;
            this.TxtLowerLimit.SelectStart = 0;
            this.TxtLowerLimit.Size = new System.Drawing.Size(254, 23);
            this.TxtLowerLimit.TabIndex = 6;
            this.TxtLowerLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtLowerLimit.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLowerLimit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtLowerLimit.Watermark = null;
            // 
            // TxtUpperLimit
            // 
            this.TxtUpperLimit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtUpperLimit.Captain = "参考上限";
            this.TxtUpperLimit.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtUpperLimit.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtUpperLimit.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtUpperLimit.CaptainWidth = 70F;
            this.TxtUpperLimit.ContentForeColor = System.Drawing.Color.Black;
            this.TxtUpperLimit.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtUpperLimit.EditMask = null;
            this.TxtUpperLimit.Location = new System.Drawing.Point(303, 90);
            this.TxtUpperLimit.Multiline = false;
            this.TxtUpperLimit.Name = "TxtUpperLimit";
            this.TxtUpperLimit.PasswordChar = '\0';
            this.TxtUpperLimit.ReadOnly = false;
            this.TxtUpperLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtUpperLimit.SelectionStart = 0;
            this.TxtUpperLimit.SelectStart = 0;
            this.TxtUpperLimit.Size = new System.Drawing.Size(254, 23);
            this.TxtUpperLimit.TabIndex = 7;
            this.TxtUpperLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtUpperLimit.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtUpperLimit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtUpperLimit.Watermark = null;
            // 
            // TxtTextualRefValue
            // 
            this.TxtTextualRefValue.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtTextualRefValue.Captain = "文本参考值";
            this.TxtTextualRefValue.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtTextualRefValue.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtTextualRefValue.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtTextualRefValue.CaptainWidth = 70F;
            this.TxtTextualRefValue.ContentForeColor = System.Drawing.Color.Black;
            this.TxtTextualRefValue.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtTextualRefValue.EditMask = null;
            this.TxtTextualRefValue.Location = new System.Drawing.Point(43, 119);
            this.TxtTextualRefValue.Multiline = false;
            this.TxtTextualRefValue.Name = "TxtTextualRefValue";
            this.TxtTextualRefValue.PasswordChar = '\0';
            this.TxtTextualRefValue.ReadOnly = false;
            this.TxtTextualRefValue.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtTextualRefValue.SelectionStart = 0;
            this.TxtTextualRefValue.SelectStart = 0;
            this.TxtTextualRefValue.Size = new System.Drawing.Size(254, 23);
            this.TxtTextualRefValue.TabIndex = 8;
            this.TxtTextualRefValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtTextualRefValue.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtTextualRefValue.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtTextualRefValue.Watermark = null;
            // 
            // TxtAlertLow
            // 
            this.TxtAlertLow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtAlertLow.Captain = "警告下限";
            this.TxtAlertLow.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtAlertLow.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAlertLow.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtAlertLow.CaptainWidth = 70F;
            this.TxtAlertLow.ContentForeColor = System.Drawing.Color.Black;
            this.TxtAlertLow.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtAlertLow.EditMask = null;
            this.TxtAlertLow.Location = new System.Drawing.Point(43, 148);
            this.TxtAlertLow.Multiline = false;
            this.TxtAlertLow.Name = "TxtAlertLow";
            this.TxtAlertLow.PasswordChar = '\0';
            this.TxtAlertLow.ReadOnly = false;
            this.TxtAlertLow.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtAlertLow.SelectionStart = 0;
            this.TxtAlertLow.SelectStart = 0;
            this.TxtAlertLow.Size = new System.Drawing.Size(254, 23);
            this.TxtAlertLow.TabIndex = 10;
            this.TxtAlertLow.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtAlertLow.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAlertLow.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtAlertLow.Watermark = null;
            // 
            // TxtAlertHigh
            // 
            this.TxtAlertHigh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtAlertHigh.Captain = "警告上限";
            this.TxtAlertHigh.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtAlertHigh.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAlertHigh.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtAlertHigh.CaptainWidth = 70F;
            this.TxtAlertHigh.ContentForeColor = System.Drawing.Color.Black;
            this.TxtAlertHigh.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtAlertHigh.EditMask = null;
            this.TxtAlertHigh.Location = new System.Drawing.Point(303, 148);
            this.TxtAlertHigh.Multiline = false;
            this.TxtAlertHigh.Name = "TxtAlertHigh";
            this.TxtAlertHigh.PasswordChar = '\0';
            this.TxtAlertHigh.ReadOnly = false;
            this.TxtAlertHigh.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtAlertHigh.SelectionStart = 0;
            this.TxtAlertHigh.SelectStart = 0;
            this.TxtAlertHigh.Size = new System.Drawing.Size(254, 23);
            this.TxtAlertHigh.TabIndex = 11;
            this.TxtAlertHigh.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtAlertHigh.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAlertHigh.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtAlertHigh.Watermark = null;
            // 
            // TxtCriticalLow
            // 
            this.TxtCriticalLow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtCriticalLow.Captain = "危急下限";
            this.TxtCriticalLow.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtCriticalLow.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCriticalLow.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtCriticalLow.CaptainWidth = 70F;
            this.TxtCriticalLow.ContentForeColor = System.Drawing.Color.Black;
            this.TxtCriticalLow.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtCriticalLow.EditMask = null;
            this.TxtCriticalLow.Location = new System.Drawing.Point(43, 177);
            this.TxtCriticalLow.Multiline = false;
            this.TxtCriticalLow.Name = "TxtCriticalLow";
            this.TxtCriticalLow.PasswordChar = '\0';
            this.TxtCriticalLow.ReadOnly = false;
            this.TxtCriticalLow.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtCriticalLow.SelectionStart = 0;
            this.TxtCriticalLow.SelectStart = 0;
            this.TxtCriticalLow.Size = new System.Drawing.Size(254, 23);
            this.TxtCriticalLow.TabIndex = 12;
            this.TxtCriticalLow.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtCriticalLow.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCriticalLow.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtCriticalLow.Watermark = null;
            // 
            // TxtCriticalHigh
            // 
            this.TxtCriticalHigh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtCriticalHigh.Captain = "危急上限";
            this.TxtCriticalHigh.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtCriticalHigh.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCriticalHigh.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtCriticalHigh.CaptainWidth = 70F;
            this.TxtCriticalHigh.ContentForeColor = System.Drawing.Color.Black;
            this.TxtCriticalHigh.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtCriticalHigh.EditMask = null;
            this.TxtCriticalHigh.Location = new System.Drawing.Point(303, 177);
            this.TxtCriticalHigh.Multiline = false;
            this.TxtCriticalHigh.Name = "TxtCriticalHigh";
            this.TxtCriticalHigh.PasswordChar = '\0';
            this.TxtCriticalHigh.ReadOnly = false;
            this.TxtCriticalHigh.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtCriticalHigh.SelectionStart = 0;
            this.TxtCriticalHigh.SelectStart = 0;
            this.TxtCriticalHigh.Size = new System.Drawing.Size(254, 23);
            this.TxtCriticalHigh.TabIndex = 13;
            this.TxtCriticalHigh.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtCriticalHigh.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCriticalHigh.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtCriticalHigh.Watermark = null;
            // 
            // DateEffectiveFrom
            // 
            this.DateEffectiveFrom.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.DateEffectiveFrom.Captain = "生效时间";
            this.DateEffectiveFrom.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEffectiveFrom.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.DateEffectiveFrom.CaptainWidth = 70F;
            this.DateEffectiveFrom.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart;
            this.DateEffectiveFrom.Location = new System.Drawing.Point(43, 206);
            this.DateEffectiveFrom.MaximumSize = new System.Drawing.Size(100000000, 23);
            this.DateEffectiveFrom.MinimumSize = new System.Drawing.Size(0, 20);
            this.DateEffectiveFrom.Name = "DateEffectiveFrom";
            this.DateEffectiveFrom.ReadOnly = false;
            this.DateEffectiveFrom.Size = new System.Drawing.Size(254, 23);
            this.DateEffectiveFrom.TabIndex = 14;
            this.DateEffectiveFrom.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEffectiveFrom.ValueIsDbNull = false;
            this.DateEffectiveFrom.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.DateEffectiveFrom.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            // 
            // DateEffectiveTo
            // 
            this.DateEffectiveTo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.DateEffectiveTo.Captain = "失效时间";
            this.DateEffectiveTo.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEffectiveTo.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.DateEffectiveTo.CaptainWidth = 70F;
            this.DateEffectiveTo.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart;
            this.DateEffectiveTo.Location = new System.Drawing.Point(303, 206);
            this.DateEffectiveTo.MaximumSize = new System.Drawing.Size(100000000, 23);
            this.DateEffectiveTo.MinimumSize = new System.Drawing.Size(0, 20);
            this.DateEffectiveTo.Name = "DateEffectiveTo";
            this.DateEffectiveTo.ReadOnly = false;
            this.DateEffectiveTo.Size = new System.Drawing.Size(254, 23);
            this.DateEffectiveTo.TabIndex = 15;
            this.DateEffectiveTo.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEffectiveTo.ValueIsDbNull = false;
            this.DateEffectiveTo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.DateEffectiveTo.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            // 
            // Panel1
            // 
            this.Panel1.Controls.Add(this.BtnSave);
            this.Panel1.Controls.Add(this.BtnCancel);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel1.Location = new System.Drawing.Point(0, 232);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(600, 38);
            this.Panel1.TabIndex = 1;
            // 
            // BtnSave
            // 
            this.BtnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(440, 0);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.BtnCancel.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnCancel.Location = new System.Drawing.Point(516, 0);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(70, 35);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // comboUnit1
            // 
            this.comboUnit1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.comboUnit1.Bookmark = -1;
            this.comboUnit1.Captain = "项目单位";
            this.comboUnit1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboUnit1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.comboUnit1.CaptainWidth = 70F;
            this.comboUnit1.ColumnCaptionHeight = 20;
            this.comboUnit1.DataSource = null;
            this.comboUnit1.DataView = null;
            this.comboUnit1.ItemHeight = 18;
            this.comboUnit1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboUnit1.Location = new System.Drawing.Point(303, 119);
            this.comboUnit1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.comboUnit1.MinimumSize = new System.Drawing.Size(0, 20);
            this.comboUnit1.Name = "comboUnit1";
            this.comboUnit1.ReadOnly = false;
            this.comboUnit1.Row = 0;
            this.comboUnit1.Size = new System.Drawing.Size(254, 23);
            this.comboUnit1.TabIndex = 9;
            this.comboUnit1.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // DictLabItem3
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(600, 270);
            this.Controls.Add(this.TableLayoutPanel1);
            this.Controls.Add(this.Panel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Name = "DictLabItem3";
            this.Text = "检验项目参考区间";
            this.Load += new System.EventHandler(this.DictLabItem3_Load);
            this.TableLayoutPanel1.ResumeLayout(false);
            this.Panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.TableLayoutPanel TableLayoutPanel1;
        internal ZTLisControl.ComboMethod ComboMethod;
        internal ZTLisControl.SingleSex ComboSex;
        internal CustomControl.MyNumericEdit NumAgeLower;
        internal CustomControl.MyNumericEdit NumAgeUpper;
        internal ZTLisControl.SingleAgeUnit ComboAgeUnit;
        internal CustomControl.MyTextBox TxtLowerLimit;
        internal CustomControl.MyTextBox TxtUpperLimit;
        internal CustomControl.MyTextBox TxtTextualRefValue;
        internal CustomControl.MyTextBox TxtAlertLow;
        internal CustomControl.MyTextBox TxtAlertHigh;
        internal CustomControl.MyTextBox TxtCriticalLow;
        internal CustomControl.MyTextBox TxtCriticalHigh;
        internal CustomControl.MyDateEdit DateEffectiveFrom;
        internal CustomControl.MyDateEdit DateEffectiveTo;
        internal System.Windows.Forms.Panel Panel1;
        internal CustomControl.MyButton BtnSave;
        internal CustomControl.MyButton BtnCancel;
        private ZTLisControl.ComboUnit comboUnit1;
    }
}