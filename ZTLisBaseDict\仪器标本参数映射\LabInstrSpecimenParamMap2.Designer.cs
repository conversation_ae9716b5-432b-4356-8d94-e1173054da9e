namespace ZTLisBaseDict
{
    partial class LabInstrSpecimenParamMap2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.TableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.TxtInstrParamCode = new CustomControl.MyTextBox();
            this.TxtInstrParamName = new CustomControl.MyTextBox();
            this.ComboSpecimenParam = new ZTLisControl.ComboSpecimenParam();
            this.NumConversionFactor = new CustomControl.MyNumericEdit();
            this.NumConversionOffset = new CustomControl.MyNumericEdit();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.BtnSave = new CustomControl.MyButton();
            this.BtnCancel = new CustomControl.MyButton();
            this.TableLayoutPanel1.SuspendLayout();
            this.Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // TableLayoutPanel1
            // 
            this.TableLayoutPanel1.ColumnCount = 4;
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.Controls.Add(this.TxtInstrParamCode, 1, 0);
            this.TableLayoutPanel1.Controls.Add(this.TxtInstrParamName, 1, 1);
            this.TableLayoutPanel1.Controls.Add(this.ComboSpecimenParam, 1, 2);
            this.TableLayoutPanel1.Controls.Add(this.NumConversionFactor, 1, 3);
            this.TableLayoutPanel1.Controls.Add(this.NumConversionOffset, 2, 3);
            this.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.TableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.TableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.TableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.TableLayoutPanel1.Name = "TableLayoutPanel1";
            this.TableLayoutPanel1.RowCount = 5;
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.Size = new System.Drawing.Size(520, 121);
            this.TableLayoutPanel1.TabIndex = 8;
            // 
            // TxtInstrParamCode
            // 
            this.TxtInstrParamCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtInstrParamCode.Captain = "仪器参数代码";
            this.TxtInstrParamCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtInstrParamCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrParamCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtInstrParamCode.CaptainWidth = 100F;
            this.TxtInstrParamCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtInstrParamCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtInstrParamCode.EditMask = null;
            this.TxtInstrParamCode.Location = new System.Drawing.Point(13, 3);
            this.TxtInstrParamCode.Multiline = false;
            this.TxtInstrParamCode.Name = "TxtInstrParamCode";
            this.TxtInstrParamCode.PasswordChar = '\0';
            this.TxtInstrParamCode.ReadOnly = false;
            this.TxtInstrParamCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtInstrParamCode.SelectionStart = 0;
            this.TxtInstrParamCode.SelectStart = 0;
            this.TxtInstrParamCode.Size = new System.Drawing.Size(244, 23);
            this.TxtInstrParamCode.TabIndex = 0;
            this.TxtInstrParamCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtInstrParamCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrParamCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtInstrParamCode.Watermark = null;
            // 
            // TxtInstrParamName
            // 
            this.TxtInstrParamName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtInstrParamName.Captain = "仪器参数名称";
            this.TxtInstrParamName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtInstrParamName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrParamName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtInstrParamName.CaptainWidth = 100F;
            this.TxtInstrParamName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtInstrParamName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtInstrParamName.EditMask = null;
            this.TxtInstrParamName.Location = new System.Drawing.Point(13, 32);
            this.TxtInstrParamName.Multiline = false;
            this.TxtInstrParamName.Name = "TxtInstrParamName";
            this.TxtInstrParamName.PasswordChar = '\0';
            this.TxtInstrParamName.ReadOnly = false;
            this.TxtInstrParamName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtInstrParamName.SelectionStart = 0;
            this.TxtInstrParamName.SelectStart = 0;
            this.TxtInstrParamName.Size = new System.Drawing.Size(244, 23);
            this.TxtInstrParamName.TabIndex = 1;
            this.TxtInstrParamName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtInstrParamName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrParamName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtInstrParamName.Watermark = null;
            // 
            // ComboSpecimenParam
            // 
            this.ComboSpecimenParam.Bookmark = -1;
            this.ComboSpecimenParam.Captain = "LIS标本参数";
            this.ComboSpecimenParam.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboSpecimenParam.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboSpecimenParam.CaptainWidth = 100F;
            this.ComboSpecimenParam.ColumnCaptionHeight = 20;
            this.TableLayoutPanel1.SetColumnSpan(this.ComboSpecimenParam, 2);
            this.ComboSpecimenParam.DataSource = null;
            this.ComboSpecimenParam.DataView = null;
            this.ComboSpecimenParam.ItemHeight = 18;
            this.ComboSpecimenParam.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboSpecimenParam.Location = new System.Drawing.Point(13, 61);
            this.ComboSpecimenParam.MaximumSize = new System.Drawing.Size(10000, 23);
            this.ComboSpecimenParam.MinimumSize = new System.Drawing.Size(0, 20);
            this.ComboSpecimenParam.Name = "ComboSpecimenParam";
            this.ComboSpecimenParam.ReadOnly = false;
            this.ComboSpecimenParam.Row = 0;
            this.ComboSpecimenParam.Size = new System.Drawing.Size(494, 23);
            this.ComboSpecimenParam.TabIndex = 2;
            this.ComboSpecimenParam.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // NumConversionFactor
            // 
            this.NumConversionFactor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumConversionFactor.Captain = "转换系数";
            this.NumConversionFactor.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumConversionFactor.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumConversionFactor.CaptainWidth = 69F;
            this.NumConversionFactor.Location = new System.Drawing.Point(13, 90);
            this.NumConversionFactor.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumConversionFactor.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumConversionFactor.Name = "NumConversionFactor";
            this.NumConversionFactor.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumConversionFactor.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumConversionFactor.ReadOnly = false;
            this.NumConversionFactor.Size = new System.Drawing.Size(244, 23);
            this.NumConversionFactor.TabIndex = 3;
            this.NumConversionFactor.ValueIsDbNull = false;
            // 
            // NumConversionOffset
            // 
            this.NumConversionOffset.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumConversionOffset.Captain = "转换偏移";
            this.NumConversionOffset.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumConversionOffset.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumConversionOffset.CaptainWidth = 69F;
            this.NumConversionOffset.Location = new System.Drawing.Point(263, 90);
            this.NumConversionOffset.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumConversionOffset.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumConversionOffset.Name = "NumConversionOffset";
            this.NumConversionOffset.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumConversionOffset.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumConversionOffset.ReadOnly = false;
            this.NumConversionOffset.Size = new System.Drawing.Size(244, 23);
            this.NumConversionOffset.TabIndex = 4;
            this.NumConversionOffset.ValueIsDbNull = false;
            // 
            // Panel1
            // 
            this.Panel1.Controls.Add(this.BtnSave);
            this.Panel1.Controls.Add(this.BtnCancel);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.Panel1.Location = new System.Drawing.Point(0, 123);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(520, 38);
            this.Panel1.TabIndex = 9;
            // 
            // BtnSave
            // 
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(350, 6);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.BtnCancel.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnCancel.Location = new System.Drawing.Point(431, 6);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(70, 35);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // LabInstrSpecimenParamMap2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(520, 161);
            this.Controls.Add(this.TableLayoutPanel1);
            this.Controls.Add(this.Panel1);
            this.Name = "LabInstrSpecimenParamMap2";
            this.Text = "仪器标本参数映射编辑";
            this.Load += new System.EventHandler(this.LabInstrSpecimenParamMap2_Load);
            this.TableLayoutPanel1.ResumeLayout(false);
            this.Panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.Panel Panel1;
        internal CustomControl.MyButton BtnSave;
        internal CustomControl.MyButton BtnCancel;
        internal System.Windows.Forms.TableLayoutPanel TableLayoutPanel1;
        internal CustomControl.MyTextBox TxtInstrParamCode;
        internal CustomControl.MyTextBox TxtInstrParamName;
        internal ZTLisControl.ComboSpecimenParam ComboSpecimenParam;
        private CustomControl.MyNumericEdit NumConversionFactor;
        private CustomControl.MyNumericEdit NumConversionOffset;
    }
}