﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictUnit.cs
*
* 功 能： N/A
* 类 名： MdlDictUnit
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/1 16:22:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验结果单位字典表
	/// </summary>
	[Serializable]
	public partial class MdlDictUnit
	{
		public MdlDictUnit()
		{}
		#region Model
		private int _unitid;
		private string _unitname;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 单位ID，自增主键，系统内部关联使用
		/// </summary>
		public int UnitId
		{
			set{ _unitid=value;}
			get{return _unitid;}
		}
		/// <summary>
		/// 单位名称，作为业务唯一标识，如 g/L, U/L 等
		/// </summary>
		public string UnitName
		{
			set{ _unitname=value;}
			get{return _unitname;}
		}
		/// <summary>
		/// 状态：1 = Active (启用)，0 = Inactive (停用)
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 记录创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 最近更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

