﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZTLisEnum;

namespace ZTLisPublicFunction
{
    public static class Utilities
    {
        /// <summary>
        /// 根据出生日期计算年龄
        /// </summary>
        public static void CalculateAge(DateTime birthDate, out decimal age, out AgeUnit ageUnit)
        {
            age = 0;
            ageUnit = AgeUnit.天;

            if (birthDate == DateTime.MinValue)
            {
                return;
            }

            DateTime now = DateTime.Now;

            // 如果出生日期大于当前日期，无效
            if (birthDate > now)
            {
                return;
            }

            // 计算年数
            int years = now.Year - birthDate.Year;
            if (now.Month < birthDate.Month || (now.Month == birthDate.Month && now.Day < birthDate.Day))
            {
                years--;
            }

            // 判断年龄单位和值
            if (years >= 3)
            {
                // 3岁及以上按岁计算
                age = years;
                ageUnit = AgeUnit.岁;
            }
            else if (years >= 1)
            {
                // 1-2岁按月计算
                DateTime ageDate = birthDate.AddYears(years);
                int months = years * 12;

                while (ageDate.AddMonths(1) <= now)
                {
                    months++;
                    ageDate = ageDate.AddMonths(1);
                }

                age = months;
                ageUnit = AgeUnit.月;
            }
            else
            {
                // 不足1岁按天计算
                TimeSpan diff = now - birthDate;
                age = diff.Days;
                ageUnit = AgeUnit.天;
            }
        }

    }
}
