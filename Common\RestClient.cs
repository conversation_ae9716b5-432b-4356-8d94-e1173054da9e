﻿using System;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Web;
using System.Windows.Forms;

public enum HttpVerb
{
    GET,
    POST,
    PUT,
    DELETE
}

namespace Common
{
    public class RestClient
    {
        public int Timeout { get; set; } = 30000;
        public string EndPoint { get; set; }
        public HttpVerb Method { get; set; }
        public string ContentType { get; set; }
        public string PostData { get; set; }

        public WebProxy Proxy { get; set; }

        public HttpStatusCode StatusCode { get; set; }

        private WebHeaderCollection _headers = new WebHeaderCollection();
        public WebHeaderCollection Headers
        {
            set { _headers = value; }
            get { return _headers; }
        }

        public RestClient()
        {
            EndPoint = "";
            Method = HttpVerb.GET;
            ContentType = "text/xml";
            PostData = "";
        }
        public RestClient(string endpoint)
        {
            EndPoint = endpoint;
            Method = HttpVerb.GET;
            ContentType = "text/xml";
            PostData = "";
        }
        public RestClient(string endpoint, HttpVerb method)
        {
            EndPoint = endpoint;
            Method = method;
            ContentType = "text/xml";
            PostData = "";
        }

        public RestClient(string endpoint, HttpVerb method, string postData)
        {
            EndPoint = endpoint;
            Method = method;
            ContentType = "text/xml";
            PostData = postData;
        }

        // 添加https
        private static readonly string DefaultUserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; SV1; .NET CLR 1.1.4322; .NET CLR 2.0.50727)";


        private static bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        {
            return true; //总是接受     
        }
        // end添加https

        public string MakeRequest()
        {
            return MakeRequest("");
        }

        public string MakeRequest(string parameters)
        {


            // 添加https
            if (EndPoint.Substring(0, 8) == "https://")
            {
                System.Net.ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
            }
            // end添加https

            var request = (HttpWebRequest)WebRequest.Create(EndPoint + parameters);
            if (Proxy != null)
            {
                request.Proxy = Proxy;
            }

            request.Timeout = Timeout;
            request.AllowAutoRedirect = true;
            request.Method = Method.ToString();
            request.ContentLength = 0;
            request.ContentType = ContentType;
            request.ServicePoint.Expect100Continue = false;
            if (Headers.Count != 0)
            {
                request.Headers.Add(Headers);
            }
            if (!string.IsNullOrEmpty(PostData) && (Method == HttpVerb.POST || Method == HttpVerb.PUT || Method == HttpVerb.DELETE))
            {
                Encoding encoding = Encoding.Default;
                byte[] bytes = Encoding.UTF8.GetBytes(PostData);
                request.ContentLength = bytes.Length;

                try
                {
                    using (var writeStream = request.GetRequestStream())
                    {
                        writeStream.Write(bytes, 0, bytes.Length);
                    }
                }
                catch (WebException e)
                {
                    return e.Message;
                }
            }

            try
            {
                using (var response = (HttpWebResponse)request.GetResponse())
                {
                    var responseValue = string.Empty;
                    StatusCode = response.StatusCode;
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        var message = String.Format("Request failed. Received HTTP {0}", response.StatusCode);
                        throw new ApplicationException(message);
                    }

                    // grab the response
                    using (var responseStream = response.GetResponseStream())
                    {
                        if (responseStream != null)
                        {
                            using (var reader = new StreamReader(responseStream))
                            {
                                responseValue = reader.ReadToEnd();
                            }
                        }
                    }

                    return responseValue;
                }
            }
            catch (WebException ex)
            {
                var rsp = ex.Response as HttpWebResponse;
                if (rsp != null) StatusCode = rsp.StatusCode;
                return ex.ToString();
            }
            catch (System.Exception ex)
            {
                return ex.ToString();
            }

        }

        public string MakeRequestFile(string parameters, string path, bool checkContentType = true)
        {


            // 添加https
            if (EndPoint.Substring(0, 8) == "https://")
            {
                System.Net.ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(CheckValidationResult);
            }
            // end添加https

            var request = (HttpWebRequest)WebRequest.Create(EndPoint + parameters);
            if (Proxy != null)
            {
                request.Proxy = Proxy;
            }

            request.Timeout = Timeout;
            request.AllowAutoRedirect = true;
            request.Method = Method.ToString();
            request.ContentLength = 0;
            request.ContentType = ContentType;
            request.ServicePoint.Expect100Continue = false;
            if (Headers.Count != 0)
            {
                request.Headers.Add(Headers);
            }
            if (!string.IsNullOrEmpty(PostData) && (Method == HttpVerb.POST || Method == HttpVerb.PUT || Method == HttpVerb.DELETE))
            {
                Encoding encoding = Encoding.Default;
                byte[] bytes = Encoding.UTF8.GetBytes(PostData);
                request.ContentLength = bytes.Length;

                try
                {
                    using (var writeStream = request.GetRequestStream())
                    {
                        writeStream.Write(bytes, 0, bytes.Length);
                    }
                }
                catch (WebException e)
                {
                    return e.Message;
                }
            }

            try
            {
                using (var response = (HttpWebResponse)request.GetResponse())
                {
                    var responseValue = string.Empty;
                    StatusCode = response.StatusCode;
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        var message = String.Format("Request failed. Received HTTP {0}", response.StatusCode);
                        throw new ApplicationException(message);
                    }

                    // grab the response
                    using (var responseStream = response.GetResponseStream())
                    {
                        if (responseStream != null)
                        {
                            if (checkContentType && response.ContentType.Contains("json"))
                            {
                                using (var reader = new StreamReader(responseStream))
                                {
                                    responseValue = reader.ReadToEnd();
                                }
                                return responseValue;
                            }

                            if (File.Exists(path))
                            {
                                File.Delete(path);
                            }
                            System.IO.FileStream so = new System.IO.FileStream(path, FileMode.Create, FileAccess.Write, FileShare.ReadWrite);
                            long totalDownloadedByte = 0;
                            byte[] by = new byte[1024];
                            int osize = responseStream.Read(by, 0, (int)by.Length);
                            while (osize > 0)
                            {
                                totalDownloadedByte = osize + totalDownloadedByte;
                                so.Write(by, 0, osize);
                                osize = responseStream.Read(by, 0, (int)by.Length);
                            }
                            so.Close();
                        }
                    }

                    return "下载成功！";
                }
            }
            catch (WebException ex)
            {
                var rsp = ex.Response as HttpWebResponse;
                if (rsp != null) StatusCode = rsp.StatusCode;
                return ex.ToString();
            }
            catch (System.Exception ex)
            {
                return ex.ToString();
            }

        }

        public bool CheckUrl(string parameters)
        {
            bool bResult = true;
            HttpWebRequest myRequest = (HttpWebRequest)WebRequest.Create(EndPoint + parameters);
            myRequest.Method = Method.ToString();             //设置提交方式可以为＂ｇｅｔ＂，＂ｈｅａｄ＂等
            myRequest.Timeout = 10000;　             //设置网页响应时间长度
            myRequest.AllowAutoRedirect = false;//是否允许自动重定向
            HttpWebResponse myResponse = (HttpWebResponse)myRequest.GetResponse();
            bResult = (myResponse.StatusCode == HttpStatusCode.OK);//返回响应的状态
            return bResult;
        }

        public string ModelToUriParam<T>(T obj)
        {
            PropertyInfo[] propertis = obj.GetType().GetProperties();
            StringBuilder sb = new StringBuilder();
            foreach (var p in propertis)
            {
                var v = p.GetValue(obj, null);
                if (v == null)
                    continue;
                sb.Append(p.Name);
                sb.Append("=");
                sb.Append(HttpUtility.UrlEncode(v.ToString()));
                //sb.Append(v.ToString());
                sb.Append("&");
            }
            sb.Remove(sb.Length - 1, 1);
            return sb.ToString();
        }

    } // class

}