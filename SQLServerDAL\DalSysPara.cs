﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysPara.cs
*
* 功 能： N/A
* 类 名： DalSysPara
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-16 11:46:54   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysPara
	/// </summary>
	public partial class DalSysPara:IDalSysPara
	{
		public DalSysPara()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string ParaCode)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysPara");
			strSql.Append(" where ParaCode=@ParaCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ParaCode", SqlDbType.Char,4)			};
			parameters[0].Value = ParaCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysPara model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysPara(");
			strSql.Append("ParaCode,ParaType,ParaName,ParaValue)");
			strSql.Append(" values (");
			strSql.Append("@ParaCode,@ParaType,@ParaName,@ParaValue)");
			SqlParameter[] parameters = {
					new SqlParameter("@ParaCode", SqlDbType.Char,4),
					new SqlParameter("@ParaType", SqlDbType.VarChar,50),
					new SqlParameter("@ParaName", SqlDbType.VarChar,50),
					new SqlParameter("@ParaValue", SqlDbType.VarChar,50)};
			parameters[0].Value = model.ParaCode;
			parameters[1].Value = model.ParaType;
			parameters[2].Value = model.ParaName;
			parameters[3].Value = model.ParaValue;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysPara model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysPara set ");
			strSql.Append("ParaType=@ParaType,");
			strSql.Append("ParaName=@ParaName,");
			strSql.Append("ParaValue=@ParaValue");
			strSql.Append(" where ParaCode=@ParaCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ParaType", SqlDbType.VarChar,50),
					new SqlParameter("@ParaName", SqlDbType.VarChar,50),
					new SqlParameter("@ParaValue", SqlDbType.VarChar,50),
					new SqlParameter("@ParaCode", SqlDbType.Char,4)};
			parameters[0].Value = model.ParaType;
			parameters[1].Value = model.ParaName;
			parameters[2].Value = model.ParaValue;
			parameters[3].Value = model.ParaCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string ParaCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysPara ");
			strSql.Append(" where ParaCode=@ParaCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ParaCode", SqlDbType.Char,4)			};
			parameters[0].Value = ParaCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string ParaCodelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysPara ");
			strSql.Append(" where ParaCode in ("+ParaCodelist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysPara GetModel(string ParaCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ParaCode,ParaType,ParaName,ParaValue from SysPara ");
			strSql.Append(" where ParaCode=@ParaCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ParaCode", SqlDbType.Char,4)			};
			parameters[0].Value = ParaCode;

			Model.MdlSysPara model=new Model.MdlSysPara();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysPara DataRowToModel(DataRow row)
		{
			Model.MdlSysPara model=new Model.MdlSysPara();
			if (row != null)
			{
				if(row["ParaCode"]!=null)
				{
					model.ParaCode=row["ParaCode"].ToString();
				}
				if(row["ParaType"]!=null)
				{
					model.ParaType=row["ParaType"].ToString();
				}
				if(row["ParaName"]!=null)
				{
					model.ParaName=row["ParaName"].ToString();
				}
				if(row["ParaValue"]!=null)
				{
					model.ParaValue=row["ParaValue"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ParaCode,ParaType,ParaName,ParaMemo,ParaValue ");
			strSql.Append(" FROM SysPara ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ParaCode,ParaType,ParaName,ParaValue ");
			strSql.Append(" FROM SysPara ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysPara ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ParaCode desc");
			}
			strSql.Append(")AS Row, T.*  from SysPara T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysPara";
			parameters[1].Value = "ParaCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

