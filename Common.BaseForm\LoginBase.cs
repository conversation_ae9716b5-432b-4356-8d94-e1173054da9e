﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Common;

namespace Common.BaseForm
{

    public partial class LoginBase : Form
    {
        private string _realPassword = "";
       // protected Common.INIFileHelper iniHelper { get; set; }

        /// <summary>
        /// 真实密码
        /// </summary>
        protected string realPassword
        {
            get
            {
                return _realPassword;
            }
            set
            {
                _realPassword = value;
            }
        }

        public LoginBase()
        {
            InitializeComponent();
        }

        #region 实现窗体可以移动

        [DllImport("user32.dll")]
        public static extern bool ReleaseCapture();

        [DllImport("user32.dll")]
        public static extern bool SendMessage(IntPtr hwnd, int wMsg, int wParam, int lParam);

        public const int WM_SYSCOMMAND = 0x112;
        public const int SC_MOVE = 0xf010;

        public const int HTCAPTION = 0x2;

        /// <summary>
        /// 鼠标按住时候能移动窗体
        /// </summary>
        /// <param name="e"></param>
        protected override void OnMouseDown(MouseEventArgs e)
        {
            base.OnMouseDown(e);
            ReleaseCapture();
            SendMessage(this.Handle, WM_SYSCOMMAND, SC_MOVE + HTCAPTION, 0);
        }

        #endregion

        #region 控件动作

        private void CheckBox1_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkLoginName.Checked)
            {
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "记住用户", ChkLoginName.Checked.ToString());

            }
            else
            {
                ChkPassWord.Checked = false;
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "记住用户", ChkLoginName.Checked.ToString());
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "登录用户", "");
            }
        }

        private void CheckBox2_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkPassWord.Checked)
            {
                ChkLoginName.Checked = true; //记住密码的时候，登录名也要保存
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "记住密码", ChkPassWord.Checked.ToString());
            }
            else
            {
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "记住密码", ChkPassWord.Checked.ToString());
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "登录密码", "");
            }
        }

        #endregion

        #region 函数

        protected void SetText(string text)
        {
            if (TxtState.InvokeRequired == true)
            {
                TxtState.Invoke(new Action<string>(SetText), text);
            }
            else
            {
                this.TxtState.Text = text;
            }
        }

        /// <summary>
        /// 控制控件是否可用
        /// </summary>
        /// <param name="enable">是否可用，true可用，false为不可用</param>
        protected void SetCtlEnable(bool enable)
        {
            if (TxtLoginName.InvokeRequired == true)
            {
                TxtLoginName.Invoke(new Action<bool> (SetCtlEnable), enable);
            }
            else
            {
                TxtName.Enabled = false;
                TxtLoginName.Enabled = enable;
                TxtPassWord.Enabled = enable;
                ChkLoginName.Enabled = enable;
                ChkPassWord.Enabled = enable;
                BtnLogin.Enabled = enable;
                LlblPara.Enabled = enable;
            }
        }

        /// <summary>
        /// 保存登录信息
        /// </summary>
        protected void SaveLoginInfo()
        {
            if (ChkLoginName.Checked) Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "登录用户", TxtLoginName.Text.Trim());
            if (realPassword == "" || Common.DESEncryptMD5.Encrypt(realPassword) != TxtPassWord.Text.Trim())
            {
                realPassword = TxtPassWord.Text.Trim();
            }
            if (ChkPassWord.Checked && TxtPassWord.Text.Trim() != "")
                Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "登录密码", Common.DESEncryptMD5.Encrypt(realPassword));
        }

        /// <summary>
        /// 显示登录信息
        /// </summary>
        protected void ShowLoginInfo()
        {
            if (ChkLoginName.InvokeRequired == true)
            {
                ChkLoginName.Invoke(new Action(ShowLoginInfo));
            }
            else
            {
                ChkLoginName.Checked = Boolean.Parse(Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "记住用户"));
                ChkPassWord.Checked = Boolean.Parse(Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "记住密码"));
                TxtLoginName.Focus();
                if (ChkLoginName.Checked)
                {
                    TxtLoginName.Text = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "登录用户");
                    TxtPassWord.Focus();
                }
                if (ChkPassWord.Checked)
                {
                    TxtPassWord.Text = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "登录密码");
                    if (TxtPassWord.Text.Trim() != "") realPassword = Common.DESEncryptMD5.Decrypt(TxtPassWord.Text.Trim());
                    BtnLogin.Focus();
                }
            }

        }

        #endregion

    }

}
