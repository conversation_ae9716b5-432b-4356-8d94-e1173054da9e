﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{896CD677-F8C6-4B85-8BD1-8CB657E18D62}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ZTLisBaseDict</RootNamespace>
    <AssemblyName>ZTLisBaseDict</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1SplitContainer.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL" />
    <Reference Include="SunnyUI, Version=3.1.9.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.3.1.9\lib\net40\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.1.2.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.Common.3.1.2\lib\net40\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="仪器信息字典\DictLabInstrument1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="仪器信息字典\DictLabInstrument1.Designer.cs">
      <DependentUpon>DictLabInstrument1.cs</DependentUpon>
    </Compile>
    <Compile Include="仪器信息字典\DictLabInstrument2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="仪器信息字典\DictLabInstrument2.Designer.cs">
      <DependentUpon>DictLabInstrument2.cs</DependentUpon>
    </Compile>
    <Compile Include="仪器标本参数映射\LabInstrSpecimenParamMap1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="仪器标本参数映射\LabInstrSpecimenParamMap1.Designer.cs">
      <DependentUpon>LabInstrSpecimenParamMap1.cs</DependentUpon>
    </Compile>
    <Compile Include="仪器标本参数映射\LabInstrSpecimenParamMap2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="仪器标本参数映射\LabInstrSpecimenParamMap2.Designer.cs">
      <DependentUpon>LabInstrSpecimenParamMap2.cs</DependentUpon>
    </Compile>
    <Compile Include="仪器项目映射\LabInstrItemMap1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="仪器项目映射\LabInstrItemMap1.Designer.cs">
      <DependentUpon>LabInstrItemMap1.cs</DependentUpon>
    </Compile>
    <Compile Include="仪器项目映射\LabInstrItemMap2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="仪器项目映射\LabInstrItemMap2.Designer.cs">
      <DependentUpon>LabInstrItemMap2.cs</DependentUpon>
    </Compile>
    <Compile Include="医生字典\DictDoctor1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="医生字典\DictDoctor1.Designer.cs">
      <DependentUpon>DictDoctor1.cs</DependentUpon>
    </Compile>
    <Compile Include="医生字典\DictDoctor2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="医生字典\DictDoctor2.Designer.cs">
      <DependentUpon>DictDoctor2.cs</DependentUpon>
    </Compile>
    <Compile Include="单位字典\DictUnit1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="单位字典\DictUnit1.Designer.cs">
      <DependentUpon>DictUnit1.cs</DependentUpon>
    </Compile>
    <Compile Include="单位字典\DictUnit2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="单位字典\DictUnit2.Designer.cs">
      <DependentUpon>DictUnit2.cs</DependentUpon>
    </Compile>
    <Compile Include="标本参数字典\DictSpecimenParam1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="标本参数字典\DictSpecimenParam1.Designer.cs">
      <DependentUpon>DictSpecimenParam1.cs</DependentUpon>
    </Compile>
    <Compile Include="标本参数字典\DictSpecimenParam2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="标本参数字典\DictSpecimenParam2.Designer.cs">
      <DependentUpon>DictSpecimenParam2.cs</DependentUpon>
    </Compile>
    <Compile Include="检验组合字典\DictLabProfile1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验组合字典\DictLabProfile1.Designer.cs">
      <DependentUpon>DictLabProfile1.cs</DependentUpon>
    </Compile>
    <Compile Include="检验组合字典\DictLabProfile2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验组合字典\DictLabProfile2.Designer.cs">
      <DependentUpon>DictLabProfile2.cs</DependentUpon>
    </Compile>
    <Compile Include="检验组合字典\DictLabProfile3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验组合字典\DictLabProfile3.Designer.cs">
      <DependentUpon>DictLabProfile3.cs</DependentUpon>
    </Compile>
    <Compile Include="检验项目字典\DictLabItem1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验项目字典\DictLabItem1.Designer.cs">
      <DependentUpon>DictLabItem1.cs</DependentUpon>
    </Compile>
    <Compile Include="检验项目字典\DictLabItem2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验项目字典\DictLabItem2.Designer.cs">
      <DependentUpon>DictLabItem2.cs</DependentUpon>
    </Compile>
    <Compile Include="检验项目字典\DictLabItem3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验项目字典\DictLabItem3.Designer.cs">
      <DependentUpon>DictLabItem3.cs</DependentUpon>
    </Compile>
    <Compile Include="科室字典\DictDepartment1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="科室字典\DictDepartment1.Designer.cs">
      <DependentUpon>DictDepartment1.cs</DependentUpon>
    </Compile>
    <Compile Include="科室字典\DictDepartment2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="科室字典\DictDepartment2.Designer.cs">
      <DependentUpon>DictDepartment2.cs</DependentUpon>
    </Compile>
    <Compile Include="方法字典\DictMethod1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="方法字典\DictMethod1.Designer.cs">
      <DependentUpon>DictMethod1.cs</DependentUpon>
    </Compile>
    <Compile Include="方法字典\DictMethod2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="方法字典\DictMethod2.Designer.cs">
      <DependentUpon>DictMethod2.cs</DependentUpon>
    </Compile>
    <Compile Include="方法学类别字典\DictMethodCategory1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="方法学类别字典\DictMethodCategory1.Designer.cs">
      <DependentUpon>DictMethodCategory1.cs</DependentUpon>
    </Compile>
    <Compile Include="方法学类别字典\DictMethodCategory2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="方法学类别字典\DictMethodCategory2.Designer.cs">
      <DependentUpon>DictMethodCategory2.cs</DependentUpon>
    </Compile>
    <Compile Include="标本类别字典\DictSampleType1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="标本类别字典\DictSampleType1.Designer.cs">
      <DependentUpon>DictSampleType1.cs</DependentUpon>
    </Compile>
    <Compile Include="标本类别字典\DictSampleType2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="标本类别字典\DictSampleType2.Designer.cs">
      <DependentUpon>DictSampleType2.cs</DependentUpon>
    </Compile>
    <Compile Include="就诊类型字典\DictVisitType1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="就诊类型字典\DictVisitType1.Designer.cs">
      <DependentUpon>DictVisitType1.cs</DependentUpon>
    </Compile>
    <Compile Include="就诊类型字典\DictVisitType2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="就诊类型字典\DictVisitType2.Designer.cs">
      <DependentUpon>DictVisitType2.cs</DependentUpon>
    </Compile>
    <Compile Include="标本状态字典\DictSpecimenCondition1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="标本状态字典\DictSpecimenCondition1.Designer.cs">
      <DependentUpon>DictSpecimenCondition1.cs</DependentUpon>
    </Compile>
    <Compile Include="标本状态字典\DictSpecimenCondition2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="标本状态字典\DictSpecimenCondition2.Designer.cs">
      <DependentUpon>DictSpecimenCondition2.cs</DependentUpon>
    </Compile>
    <Compile Include="检验项目类别字典\DictLabItemCategory1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验项目类别字典\DictLabItemCategory1.Designer.cs">
      <DependentUpon>DictLabItemCategory1.cs</DependentUpon>
    </Compile>
    <Compile Include="检验项目类别字典\DictLabItemCategory2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验项目类别字典\DictLabItemCategory2.Designer.cs">
      <DependentUpon>DictLabItemCategory2.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Enum\Common.Enum.csproj">
      <Project>{eca72bf5-a6c2-4ecb-a80a-9723ef1098a1}</Project>
      <Name>Common.Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTLisControl\ZTLisControl.csproj">
      <Project>{186009e9-7a04-4519-a696-79e57bb80b4e}</Project>
      <Name>ZTLisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTLisEnum\ZTLisEnum.csproj">
      <Project>{2658ea9e-035b-43e4-b40f-6cebe092f702}</Project>
      <Name>ZTLisEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTLisPublicFunction\ZTLisPublicFunction.csproj">
      <Project>{dfd1bd9d-45ff-4998-99bf-2a661d038f7c}</Project>
      <Name>ZTLisPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTLisResources\ZTLisResources.csproj">
      <Project>{fd356f66-2186-4b9d-b45b-e8e7b6040a8a}</Project>
      <Name>ZTLisResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTLisVar\ZTLisVar.csproj">
      <Project>{4596a1b7-93c2-4ff7-9412-a9b49e7beb6a}</Project>
      <Name>ZTLisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="仪器信息字典\DictLabInstrument1.resx">
      <DependentUpon>DictLabInstrument1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="仪器信息字典\DictLabInstrument2.resx">
      <DependentUpon>DictLabInstrument2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="仪器标本参数映射\LabInstrSpecimenParamMap1.resx">
      <DependentUpon>LabInstrSpecimenParamMap1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="仪器标本参数映射\LabInstrSpecimenParamMap2.resx">
      <DependentUpon>LabInstrSpecimenParamMap2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="仪器项目映射\LabInstrItemMap1.resx">
      <DependentUpon>LabInstrItemMap1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="仪器项目映射\LabInstrItemMap2.resx">
      <DependentUpon>LabInstrItemMap2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="医生字典\DictDoctor1.resx">
      <DependentUpon>DictDoctor1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="医生字典\DictDoctor2.resx">
      <DependentUpon>DictDoctor2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="单位字典\DictUnit1.resx">
      <DependentUpon>DictUnit1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="单位字典\DictUnit2.resx">
      <DependentUpon>DictUnit2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="方法字典\DictMethod1.resx">
      <DependentUpon>DictMethod1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="方法字典\DictMethod2.resx">
      <DependentUpon>DictMethod2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="方法学类别字典\DictMethodCategory1.resx">
      <DependentUpon>DictMethodCategory1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="方法学类别字典\DictMethodCategory2.resx">
      <DependentUpon>DictMethodCategory2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="标本参数字典\DictSpecimenParam1.resx">
      <DependentUpon>DictSpecimenParam1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="标本参数字典\DictSpecimenParam2.resx">
      <DependentUpon>DictSpecimenParam2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="标本类别字典\DictSampleType1.resx">
      <DependentUpon>DictSampleType1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="标本类别字典\DictSampleType2.resx">
      <DependentUpon>DictSampleType2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="就诊类型字典\DictVisitType1.resx">
      <DependentUpon>DictVisitType1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="就诊类型字典\DictVisitType2.resx">
      <DependentUpon>DictVisitType2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="标本状态字典\DictSpecimenCondition1.resx">
      <DependentUpon>DictSpecimenCondition1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="标本状态字典\DictSpecimenCondition2.resx">
      <DependentUpon>DictSpecimenCondition2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验组合字典\DictLabProfile1.resx">
      <DependentUpon>DictLabProfile1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验组合字典\DictLabProfile2.resx">
      <DependentUpon>DictLabProfile2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验组合字典\DictLabProfile3.resx">
      <DependentUpon>DictLabProfile3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验项目字典\DictLabItem1.resx">
      <DependentUpon>DictLabItem1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验项目字典\DictLabItem2.resx">
      <DependentUpon>DictLabItem2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验项目字典\DictLabItem3.resx">
      <DependentUpon>DictLabItem3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验项目类别字典\DictLabItemCategory1.resx">
      <DependentUpon>DictLabItemCategory1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="检验项目类别字典\DictLabItemCategory2.resx">
      <DependentUpon>DictLabItemCategory2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="科室字典\DictDepartment1.resx">
      <DependentUpon>DictDepartment1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="科室字典\DictDepartment2.resx">
      <DependentUpon>DictDepartment2.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>