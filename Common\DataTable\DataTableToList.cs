﻿/*********************************************************************************** 
*        Filename :DataTableToList 
*        Description :  
*        Date:2017/07/13 10:41:42 
 *       Machinename:DESKTOP-PJMK973 
*        Created by: <T> 董铮
*        All rights reserved 
* 
*       ChangeLog: 
*       2017/07/13 10:41:42: 
 *          Created! 
************************************************************************************/
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
namespace Common
{
    public class DataTableToList
    {
        /// <summary>
        /// 把DataTable转换成List
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static List<T> ToList<T>(DataTable dt) where T : class, new()
        {
            //创建一个属性的列表
            List<PropertyInfo> prlist = new List<PropertyInfo>();
            //获取T的类型实例  反射的入口
            Type t = typeof(T);
            //获得T 的所有的Public 属性 并找出T属性和DataTable的列名称相同的属性(PropertyInfo) 并加入到属性列表 
            Array.ForEach<PropertyInfo>(t.GetProperties(), p => { if (dt.Columns.IndexOf(p.Name) != -1) prlist.Add(p); });
            //创建返回的集合
            List<T> oblist = new List<T>();

            foreach (DataRow row in dt.Rows)
            {
                //创建T的实例
                T ob = new T();
                //找到对应的数据  并赋值
                prlist.ForEach(p =>
                {
                    switch (p.PropertyType.Name)
                    {
                        case "DateTime":
                            if (row[p.Name] != DBNull.Value) p.SetValue(ob, DateTime.Parse(row[p.Name].ToString()), null);
                            else p.SetValue(ob, null, null);
                            break;
                        case "Boolean":
                            if (row[p.Name] != DBNull.Value) p.SetValue(ob, Convert.ToBoolean(row[p.Name]), null);
                            else p.SetValue(ob, null, null);
                            break;
                        case "String":
                            if (row[p.Name] != DBNull.Value) p.SetValue(ob, row[p.Name].ToString(), null);
                            else p.SetValue(ob, null, null);
                            break;
                        case "Int32":
                            if (row[p.Name] != DBNull.Value) p.SetValue(ob, (int)Decimal.Parse(row[p.Name].ToString()), null);
                            else p.SetValue(ob, null, null);
                            break;
                        case "Nullable`1":
                            if (p.PropertyType.FullName.Contains("Decimal"))
                            {
                                if (row[p.Name] != DBNull.Value) p.SetValue(ob, decimal.Parse(row[p.Name].ToString()), null);
                                else p.SetValue(ob, null, null);
                            }
                            else if (p.PropertyType.FullName.Contains("Int32"))
                            {
                                if (row[p.Name] != DBNull.Value) p.SetValue(ob, int.Parse(row[p.Name].ToString()), null);
                                else p.SetValue(ob, null, null);
                            }
                            else if (p.PropertyType.FullName.Contains("DateTime"))
                            {
                                if (row[p.Name] != DBNull.Value) p.SetValue(ob, DateTime.Parse(row[p.Name].ToString()), null);
                                else p.SetValue(ob, null, null);
                            }
                            else
                            {
                                if (row[p.Name] != DBNull.Value) p.SetValue(ob, row[p.Name], null);
                                else p.SetValue(ob, null, null);
                            }

                            break;
                        default:
                            if (row[p.Name] != DBNull.Value) p.SetValue(ob, row[p.Name], null);
                            else p.SetValue(ob, null, null);
                            break;
                    }
                });
                //放入到返回的集合中.
                oblist.Add(ob);
            }

            return oblist;
        }


        /// <summary>
        /// 把DataRow转换成Model
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dr"></param>
        /// <returns></returns>
        public static T ToModel<T>(DataRow dr) where T : class, new()
        {
            //创建一个属性的列表
            List<PropertyInfo> prlist = new List<PropertyInfo>();
            //获取T的类型实例  反射的入口
            Type t = typeof(T);
            //获得T 的所有的Public 属性 并找出T属性和DataTable的列名称相同的属性(PropertyInfo) 并加入到属性列表 
            Array.ForEach<PropertyInfo>(t.GetProperties(), p => { if (dr.Table.Columns.IndexOf(p.Name) != -1) prlist.Add(p); });
            //创建T的实例
            T ob = new T();
            //找到对应的数据  并赋值
            prlist.ForEach(p =>
            {
                switch (p.PropertyType.Name)
                {
                    case "DateTime":
                        if (dr[p.Name] != DBNull.Value) p.SetValue(ob, DateTime.Parse(dr[p.Name].ToString()), null);
                        else p.SetValue(ob, null, null);
                        break;
                    case "Nullable`1":
                        if (p.PropertyType.FullName.Contains("Decimal"))
                        {
                            if (dr[p.Name] != DBNull.Value) p.SetValue(ob, decimal.Parse(dr[p.Name].ToString()), null);
                            else p.SetValue(ob, null, null);
                        }
                        else if (p.PropertyType.FullName.Contains("DateTime"))
                        {
                            if (dr[p.Name] != DBNull.Value) p.SetValue(ob, DateTime.Parse(dr[p.Name].ToString()), null);
                            else p.SetValue(ob, null, null);
                        }
                        else
                        {
                            if (dr[p.Name] != DBNull.Value) p.SetValue(ob, dr[p.Name], null);
                            else p.SetValue(ob, null, null);
                        }
                        break;
                    case "String":
                        if (dr[p.Name] != DBNull.Value) p.SetValue(ob, dr[p.Name].ToString(), null);
                        else p.SetValue(ob, null, null);
                        break;
                    case "Int32":
                        if (dr[p.Name] != DBNull.Value) p.SetValue(ob, int.Parse(dr[p.Name].ToString()), null);
                        else p.SetValue(ob, null, null);
                        break;
                    default:
                        if (dr[p.Name] != DBNull.Value) p.SetValue(ob, dr[p.Name], null);
                        else p.SetValue(ob, null, null);
                        break;
                }

            });
            return ob;
        }

        /// <summary>
        /// 把DataRow转换成Model
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dr"></param>
        /// <returns></returns>
        public static T ToModel<T>(DataRow dr, T ob) where T : class, new()
        {
            //创建一个属性的列表
            List<PropertyInfo> prlist = new List<PropertyInfo>();
            //获取T的类型实例  反射的入口
            Type t = typeof(T);
            //获得T 的所有的Public 属性 并找出T属性和DataTable的列名称相同的属性(PropertyInfo) 并加入到属性列表 
            Array.ForEach<PropertyInfo>(t.GetProperties(), p => { if (dr.Table.Columns.IndexOf(p.Name) != -1) prlist.Add(p); });
            //找到对应的数据  并赋值
            prlist.ForEach(p =>
            {
                switch (p.PropertyType.Name)
                {
                    case "DateTime":
                        if (dr[p.Name] != DBNull.Value) p.SetValue(ob, DateTime.Parse(dr[p.Name].ToString()), null);
                        else p.SetValue(ob, null, null);
                        break;
                    default:
                        if (dr[p.Name] != DBNull.Value) p.SetValue(ob, dr[p.Name], null);
                        else p.SetValue(ob, null, null);
                        break;
                }

            });
            return ob;
        }

        /// <summary>
        /// List转换成DataTable
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static DataTable ToDataTable<T>(IEnumerable<T> value) where T : class, new()
        {
            //创建属性的集合
            List<PropertyInfo> pList = new List<PropertyInfo>();
            //获得反射的入口
            Type type = typeof(T);
            DataTable dt = new DataTable();
            //把所有的public属性加入到集合 并添加DataTable的列
            Array.ForEach<PropertyInfo>(type.GetProperties(), p =>
            {
                pList.Add(p);
                if ((p.PropertyType.IsGenericType) && (p.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>)))
                {
                    var v = p.PropertyType.GetGenericArguments()[0];
                    dt.Columns.Add(p.Name, v);
                }
                else
                {
                    dt.Columns.Add(p.Name, p.PropertyType);
                }
            });

            foreach (var item in value)
            {
                //创建一个DataRow实例
                DataRow row = dt.NewRow();
                //给row 赋值
                pList.ForEach(p =>
                {
                    if (p.GetValue(item, null) == null)
                    {
                        row[p.Name] = DBNull.Value;
                    }
                    else
                    {
                        row[p.Name] = p.GetValue(item, null);
                    }
                });
                //加入到DataTable
                dt.Rows.Add(row);
            }
            return dt;
        }

        /// <summary>
        /// 将DataTable转换成属性是数组的Model
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static T ToModelArray<T>(DataTable dt) where T : class, new()
        {
            //创建一个属性的列表
            List<PropertyInfo> prlist = new List<PropertyInfo>();

            //获取T的类型实例  反射的入口
            Type t = typeof(T);
            Array.ForEach<PropertyInfo>(t.GetProperties(), p => { if (dt.Columns.Contains(p.Name)) prlist.Add(p); });

            //创建T的实例
            T ob = new T();

            foreach (PropertyInfo p in prlist)
            {

                switch (p.PropertyType.Name)
                {
                    case "DateTime[]":
                        p.SetValue(ob, dt.AsEnumerable().Select(d => d.Field<DateTime>(p.Name)).ToArray(), null);
                        break;
                    case "Int32[]":
                        p.SetValue(ob, dt.AsEnumerable().Select(d => d.Field<int>(p.Name)).ToArray(), null);
                        break;
                    case "Nullable`1[]":
                        p.SetValue(ob, dt.AsEnumerable().Select(d => d.Field<int?>(p.Name)).ToArray(), null);
                        break;
                    default:
                        p.SetValue(ob, dt.AsEnumerable().Select(d => d.Field<string>(p.Name)).ToArray(), null);
                        break;
                }

            }


            return ob;
        }


        /// <summary>
        /// 实体类转换为DataRow
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        public static void ToDataRow<T>(T value, DataRow dr) where T : class, new()
        {
            //创建属性的集合
            List<PropertyInfo> pList = new List<PropertyInfo>();
            //获得反射的入口
            Type type = typeof(T);
            Array.ForEach<PropertyInfo>(type.GetProperties(), p =>
            {
                pList.Add(p);
            });

            //给row 赋值
            pList.ForEach(p =>
            {
                if (dr.Table.Columns.Contains(p.Name))
                {
                    if (p.GetValue(value, null) == null)
                    {
                        dr[p.Name] = DBNull.Value;
                    }
                    else
                    {
                        dr[p.Name] = p.GetValue(value, null);
                    }
                }
            });



        }
    }
}
