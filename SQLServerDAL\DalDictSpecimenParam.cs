﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictSpecimenParam.cs
*
* 功 能： N/A
* 类 名： DalDictSpecimenParam
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/24 10:36:31   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictSpecimenParam
	/// </summary>
	public partial class DalDictSpecimenParam : IDalDictSpecimenParam
	{
		public DalDictSpecimenParam()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("ParamId", "DictSpecimenParam");
		}
		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(ParamCode) FROM DictSpecimenParam where ParamCode NOT LIKE '%[^0-9]%' And LEN(ParamCode)=" + length, length));
			return max;
		}
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int ParamId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictSpecimenParam");
			strSql.Append(" where ParamId=@ParamId");
			SqlParameter[] parameters = {
					new SqlParameter("@ParamId", SqlDbType.Int,4)
			};
			parameters[0].Value = ParamId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictSpecimenParam model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictSpecimenParam(");
			strSql.Append("ParamCode,ParamName,EnglishName,PinYin,ParamType,Unit,Description,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@ParamCode,@ParamName,@EnglishName,@PinYin,@ParamType,@Unit,@Description,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@ParamCode", SqlDbType.NVarChar,20),
					new SqlParameter("@ParamName", SqlDbType.NVarChar,100),
					new SqlParameter("@EnglishName", SqlDbType.NVarChar,100),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@ParamType", SqlDbType.TinyInt,1),
					new SqlParameter("@Unit", SqlDbType.NVarChar,20),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.ParamCode;
			parameters[1].Value = model.ParamName;
			parameters[2].Value = model.EnglishName;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = model.ParamType;
			parameters[5].Value = model.Unit;
			parameters[6].Value = model.Description;
			parameters[7].Value = model.Status;
			parameters[8].Value = model.CreatedAt;
			parameters[9].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictSpecimenParam model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictSpecimenParam set ");
			strSql.Append("ParamCode=@ParamCode,");
			strSql.Append("ParamName=@ParamName,");
			strSql.Append("EnglishName=@EnglishName,");
			strSql.Append("PinYin=@PinYin,");
			strSql.Append("ParamType=@ParamType,");
			strSql.Append("Unit=@Unit,");
			strSql.Append("Description=@Description,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where ParamId=@ParamId");
			SqlParameter[] parameters = {
					new SqlParameter("@ParamCode", SqlDbType.NVarChar,20),
					new SqlParameter("@ParamName", SqlDbType.NVarChar,100),
					new SqlParameter("@EnglishName", SqlDbType.NVarChar,100),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@ParamType", SqlDbType.TinyInt,1),
					new SqlParameter("@Unit", SqlDbType.NVarChar,20),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@ParamId", SqlDbType.Int,4)};
			parameters[0].Value = model.ParamCode;
			parameters[1].Value = model.ParamName;
			parameters[2].Value = model.EnglishName;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = model.ParamType;
			parameters[5].Value = model.Unit;
			parameters[6].Value = model.Description;
			parameters[7].Value = model.Status;
			parameters[8].Value = model.CreatedAt;
			parameters[9].Value = model.UpdatedAt;
			parameters[10].Value = model.ParamId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int ParamId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictSpecimenParam ");
			strSql.Append(" where ParamId=@ParamId");
			SqlParameter[] parameters = {
					new SqlParameter("@ParamId", SqlDbType.Int,4)
			};
			parameters[0].Value = ParamId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string ParamIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictSpecimenParam ");
			strSql.Append(" where ParamId in (" + ParamIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictSpecimenParam GetModel(int ParamId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 ParamId,ParamCode,ParamName,EnglishName,PinYin,ParamType,Unit,Description,Status,CreatedAt,UpdatedAt from DictSpecimenParam ");
			strSql.Append(" where ParamId=@ParamId");
			SqlParameter[] parameters = {
					new SqlParameter("@ParamId", SqlDbType.Int,4)
			};
			parameters[0].Value = ParamId;

			Model.MdlDictSpecimenParam model = new Model.MdlDictSpecimenParam();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictSpecimenParam DataRowToModel(DataRow row)
		{
			Model.MdlDictSpecimenParam model = new Model.MdlDictSpecimenParam();
			if (row != null)
			{
				if (row["ParamId"] != null && row["ParamId"].ToString() != "")
				{
					model.ParamId = int.Parse(row["ParamId"].ToString());
				}
				if (row["ParamCode"] != null)
				{
					model.ParamCode = row["ParamCode"].ToString();
				}
				if (row["ParamName"] != null)
				{
					model.ParamName = row["ParamName"].ToString();
				}
				if (row["EnglishName"] != null)
				{
					model.EnglishName = row["EnglishName"].ToString();
				}
				if (row["PinYin"] != null)
				{
					model.PinYin = row["PinYin"].ToString();
				}
				if (row["ParamType"] != null && row["ParamType"].ToString() != "")
				{
					model.ParamType = int.Parse(row["ParamType"].ToString());
				}
				if (row["Unit"] != null)
				{
					model.Unit = row["Unit"].ToString();
				}
				if (row["Description"] != null)
				{
					model.Description = row["Description"].ToString();
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ParamId,ParamCode,ParamName,EnglishName,PinYin,ParamType,Unit,Description,Status,CreatedAt,UpdatedAt ");
			strSql.Append(",case when ParamType=0 then '常规参数' when ParamType=1 then '质量指数' when ParamType=2 then '其他' end as ParamTypeName ");
			strSql.Append(" FROM DictSpecimenParam ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" ParamId,ParamCode,ParamName,EnglishName,PinYin,ParamType,Unit,Description,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictSpecimenParam ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictSpecimenParam ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.ParamId desc");
			}
			strSql.Append(")AS Row, T.*  from DictSpecimenParam T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictSpecimenParam";
			parameters[1].Value = "ParamId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

