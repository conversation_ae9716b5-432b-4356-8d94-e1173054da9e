﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysRole.cs
*
* 功 能： N/A
* 类 名： MdlSysRole
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:35   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// MdlSysRole:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlSysRole
	{
		public MdlSysRole()
		{}
		#region Model
		private string _rolecode;
		private string _rolename;
		private string _rolepinyin;
		private string _rolewubi;
		private string _rolememo;
		/// <summary>
		/// 
		/// </summary>
		public string RoleCode
		{
			set{ _rolecode=value;}
			get{return _rolecode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RoleName
		{
			set{ _rolename=value;}
			get{return _rolename;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RolePinYin
		{
			set{ _rolepinyin=value;}
			get{return _rolepinyin;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RoleWuBi
		{
			set{ _rolewubi=value;}
			get{return _rolewubi;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string RoleMemo
		{
			set{ _rolememo=value;}
			get{return _rolememo;}
		}
		#endregion Model

	}
}

