﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictLabItem.cs
*
* 功 能： N/A
* 类 名： DalDictLabItem
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:12   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;
using System.Collections.Generic;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictLabItem
	/// </summary>
	public partial class DalDictLabItem : IDalDictLabItem
	{
		public DalDictLabItem()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("LabItemId", "DictLabItem");
		}

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(LabItemCode) FROM DictLabItem where LabItemCode NOT LIKE '%[^0-9]%' And LEN(LabItemCode)=" + length, length));
			return max;
		}
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int LabItemId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictLabItem");
			strSql.Append(" where LabItemId=@LabItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4)
			};
			parameters[0].Value = LabItemId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictLabItem model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictLabItem(");
			strSql.Append("LabItemCode,LabItemName,EnglishName,PinYin,ReportName,ItemCategoryId,SampleTypeId,DefaultUnit,Precision,DefaultMethodId,LoincCode,IsCalculated,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@LabItemCode,@LabItemName,@EnglishName,@PinYin,@ReportName,@ItemCategoryId,@SampleTypeId,@DefaultUnit,@Precision,@DefaultMethodId,@LoincCode,@IsCalculated,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@LabItemName", SqlDbType.NVarChar,100),
					new SqlParameter("@EnglishName", SqlDbType.NVarChar,100),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@ReportName", SqlDbType.NVarChar,100),
					new SqlParameter("@ItemCategoryId", SqlDbType.Int,4),
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4),
					new SqlParameter("@DefaultUnit", SqlDbType.NVarChar,20),
					new SqlParameter("@Precision", SqlDbType.Int,4),
					new SqlParameter("@DefaultMethodId", SqlDbType.Int,4),
					new SqlParameter("@LoincCode", SqlDbType.NVarChar,20),
					new SqlParameter("@IsCalculated", SqlDbType.Bit,1),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.LabItemCode;
			parameters[1].Value = model.LabItemName;
			parameters[2].Value = model.EnglishName;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = model.ReportName;
			parameters[5].Value = Common.Tools.IsValueNull(model.ItemCategoryId);
			parameters[6].Value = Common.Tools.IsValueNull(model.SampleTypeId);
			parameters[7].Value = model.DefaultUnit;
			parameters[8].Value = model.Precision;
			parameters[9].Value = Common.Tools.IsValueNull(model.DefaultMethodId);
			parameters[10].Value = model.LoincCode;
			parameters[11].Value = Common.Tools.IsValueNull(model.IsCalculated);
			parameters[12].Value = model.Status;
			parameters[13].Value = model.CreatedAt;
			parameters[14].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictLabItem model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictLabItem set ");
			strSql.Append("LabItemCode=@LabItemCode,");
			strSql.Append("LabItemName=@LabItemName,");
			strSql.Append("EnglishName=@EnglishName,");
			strSql.Append("PinYin=@PinYin,");
			strSql.Append("ReportName=@ReportName,");
			strSql.Append("ItemCategoryId=@ItemCategoryId,");
			strSql.Append("SampleTypeId=@SampleTypeId,");
			strSql.Append("DefaultUnit=@DefaultUnit,");
			strSql.Append("Precision=@Precision,");
			strSql.Append("DefaultMethodId=@DefaultMethodId,");
			strSql.Append("LoincCode=@LoincCode,");
			strSql.Append("IsCalculated=@IsCalculated,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where LabItemId=@LabItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@LabItemName", SqlDbType.NVarChar,100),
					new SqlParameter("@EnglishName", SqlDbType.NVarChar,100),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@ReportName", SqlDbType.NVarChar,100),
					new SqlParameter("@ItemCategoryId", SqlDbType.Int,4),
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4),
					new SqlParameter("@DefaultUnit", SqlDbType.NVarChar,20),
					new SqlParameter("@Precision", SqlDbType.Int,4),
					new SqlParameter("@DefaultMethodId", SqlDbType.Int,4),
					new SqlParameter("@LoincCode", SqlDbType.NVarChar,20),
					new SqlParameter("@IsCalculated", SqlDbType.Bit,1),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@LabItemId", SqlDbType.Int,4)};
			parameters[0].Value = model.LabItemCode;
			parameters[1].Value = model.LabItemName;
			parameters[2].Value = model.EnglishName;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = model.ReportName;
			parameters[5].Value = Common.Tools.IsValueNull(model.ItemCategoryId);
			parameters[6].Value = Common.Tools.IsValueNull(model.SampleTypeId);
			parameters[7].Value = model.DefaultUnit;
			parameters[8].Value = model.Precision;
			parameters[9].Value = Common.Tools.IsValueNull(model.DefaultMethodId);
			parameters[10].Value = model.LoincCode;
			parameters[11].Value = Common.Tools.IsValueNull(model.IsCalculated);
			parameters[12].Value = model.Status;
			parameters[13].Value = model.CreatedAt;
			parameters[14].Value = model.UpdatedAt;
			parameters[15].Value = model.LabItemId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int LabItemId)
		{
			List<string> strList = new List<string>();
			List<IDataParameter[]> parametersList = new List<IDataParameter[]>();

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictLabItem ");
			strSql.Append(" where LabItemId=@LabItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4)
			};
			parameters[0].Value = LabItemId;
			strList.Add(strSql.ToString());
			parametersList.Add(parameters);

			StringBuilder strSql1 = new StringBuilder();
			strSql1.Append("delete from DictLabItemRefRange ");
			strSql1.Append(" where LabItemId=@LabItemId");
			SqlParameter[] parameters1 = {
				new SqlParameter("@LabItemId", SqlDbType.Int, 4)
			};
			parameters1[0].Value = LabItemId;
			strList.Add(strSql1.ToString());
			parametersList.Add(parameters1);

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strList, parametersList);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string LabItemIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictLabItem ");
			strSql.Append(" where LabItemId in (" + LabItemIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabItem GetModel(int LabItemId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 * from DictLabItem ");
			strSql.Append(" where LabItemId=@LabItemId");
			SqlParameter[] parameters = {
					new SqlParameter("@LabItemId", SqlDbType.Int,4)
			};
			parameters[0].Value = LabItemId;

			Model.MdlDictLabItem model = new Model.MdlDictLabItem();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabItem DataRowToModel(DataRow row)
		{
			Model.MdlDictLabItem model = new Model.MdlDictLabItem();
			if (row != null)
			{
				if (row["LabItemId"] != null && row["LabItemId"].ToString() != "")
				{
					model.LabItemId = int.Parse(row["LabItemId"].ToString());
				}
				if (row["LabItemCode"] != null)
				{
					model.LabItemCode = row["LabItemCode"].ToString();
				}
				if (row["LabItemName"] != null)
				{
					model.LabItemName = row["LabItemName"].ToString();
				}
				if (row["EnglishName"] != null)
				{
					model.EnglishName = row["EnglishName"].ToString();
				}
				if (row["PinYin"] != null)
				{
					model.PinYin = row["PinYin"].ToString();
				}
				if (row["ReportName"] != null)
				{
					model.ReportName = row["ReportName"].ToString();
				}
				if (row["ItemCategoryId"] != null && row["ItemCategoryId"].ToString() != "")
				{
					model.ItemCategoryId = int.Parse(row["ItemCategoryId"].ToString());
				}
				if (row["SampleTypeId"] != null && row["SampleTypeId"].ToString() != "")
				{
					model.SampleTypeId = int.Parse(row["SampleTypeId"].ToString());
				}
				if (row["DefaultUnit"] != null)
				{
					model.DefaultUnit = row["DefaultUnit"].ToString();
				}
				if (row["Precision"] != null && row["Precision"].ToString() != "")
				{
					model.Precision = int.Parse(row["Precision"].ToString());
				}
				if (row["DefaultMethodId"] != null && row["DefaultMethodId"].ToString() != "")
				{
					model.DefaultMethodId = int.Parse(row["DefaultMethodId"].ToString());
				}
				if (row["LoincCode"] != null)
				{
					model.LoincCode = row["LoincCode"].ToString();
				}
				if (row["IsCalculated"] != null && row["IsCalculated"].ToString() != "")
				{
					if ((row["IsCalculated"].ToString() == "1") || (row["IsCalculated"].ToString().ToLower() == "true"))
					{
						model.IsCalculated = true;
					}
					else
					{
						model.IsCalculated = false;
					}
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DictLabItem.* ");
			strSql.Append(" ,DictLabItemCategory.ItemCategoryName,DictSampleType.SampleTypeName,DictMethod.MethodName ");
			strSql.Append(" FROM DictLabItem ");
			strSql.Append(" left join DictLabItemCategory on DictLabItem.ItemCategoryId = DictLabItemCategory.ItemCategoryId ");
			strSql.Append(" left join DictSampleType on DictLabItem.SampleTypeId = DictSampleType.SampleTypeId ");
			strSql.Append(" left join DictMethod on DictLabItem.DefaultMethodId = DictMethod.MethodId ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" LabItemId,LabItemCode,LabItemName,EnglishName,PinYin,ItemCategoryId,SampleTypeId,DefaultUnit,Precision,DefaultMethodId,LoincCode,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictLabItem ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictLabItem ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.LabItemId desc");
			}
			strSql.Append(")AS Row, T.*  from DictLabItem T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictLabItem";
			parameters[1].Value = "LabItemId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

