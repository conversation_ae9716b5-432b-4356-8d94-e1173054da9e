﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Common
{
    public class EnumConvert
    {
        /// <summary>
        /// 枚举类型转化为DataTable
        /// </summary>
        /// <param name="enumType">枚举类型</param>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <returns></returns>
        public static DataTable EnumToDataTable(Type enumType, string key = "key", string val = "value")
        {
            string[] Names = System.Enum.GetNames(enumType);

            Array Values = System.Enum.GetValues(enumType);

            DataTable table = new DataTable();
            table.Columns.Add(key, System.Type.GetType("System.Int32"));
            table.Columns.Add(val, System.Type.GetType("System.String"));
            table.Columns[key].Unique = true;
            for (int i = 0; i < Values.Length; i++)
            {
                DataRow DR = table.NewRow();
                DR[key] = (int)Values.GetValue(i);
                DR[val] = GetDescription(enumType, Names[i].ToString());
                table.Rows.Add(DR);
            }
            return table;
        }

        /// <summary>  
        /// 获取枚举描述
        /// </summary>  
        /// <param name="en">枚举</param>  
        /// <returns>返回枚举的描述 </returns>  
        public static string GetDescription(Enum en)
        {
            Type type = en.GetType();   //获取类型  
            MemberInfo[] memberInfos = type.GetMember(en.ToString());   //获取成员  
            if (memberInfos != null && memberInfos.Length > 0)
            {
                DescriptionAttribute[] attrs = memberInfos[0].GetCustomAttributes(typeof(DescriptionAttribute), false) as DescriptionAttribute[];   //获取描述特性  
                if (attrs != null && attrs.Length > 0)
                {
                    return attrs[0].Description;    //返回当前描述
                }
            }
            return en.ToString();
        }

        public static string GetDescription(Type type, string en)
        {
            MemberInfo[] memberInfos = type.GetMember(en);   //获取成员  
            if (memberInfos != null && memberInfos.Length > 0)
            {
                DescriptionAttribute[] attrs = memberInfos[0].GetCustomAttributes(typeof(DescriptionAttribute), false) as DescriptionAttribute[];   //获取描述特性  
                if (attrs != null && attrs.Length > 0)
                {
                    return attrs[0].Description;    //返回当前描述
                }
            }
            return en.ToString();
        }

        public static int ConvertEnumToInt<T>(string name)
        {
            return (int)Enum.Parse(typeof(T), name);
        }
        public static T GetEnumFromString<T>(string enumString) where T : struct, Enum
        {
            if (Enum.TryParse<T>(enumString, out var result))
            {
                return result;
            }
            else
            {
                throw new ArgumentException($"Invalid enum string: {enumString}", nameof(enumString));
            }
        }


        /// <summary>
        /// 将指定的枚举类型转换为一个包含 Name, Value, Description 的 DataTable。
        /// </summary>
        /// <typeparam name="T">要转换的枚举类型 (必须是 Enum)。</typeparam>
        /// <returns>一个包含枚举信息的 DataTable。</returns>
        public static DataTable ToDataTable<T>() where T : Enum
        {
            // 获取枚举类型
            var enumType = typeof(T);

            // 创建 DataTable 实例并设置表名
            var dataTable = new DataTable();
            dataTable.TableName = enumType.Name;

            // 添加列定义
            dataTable.Columns.Add("Value", typeof(string));
            dataTable.Columns.Add("Name", typeof(string));

            // 遍历枚举的所有成员
            foreach (T value in Enum.GetValues(enumType))
            {
                // 为每个枚举成员创建新行
                DataRow row = dataTable.NewRow();

                // 填充行数据
                row["Name"] = GetDescription(enumType, value.ToString());
                row["Value"] = value.ToString();
 

                // 将行添加到 DataTable
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }
    }
}
