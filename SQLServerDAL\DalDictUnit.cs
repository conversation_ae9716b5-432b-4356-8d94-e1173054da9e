﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictUnit.cs
*
* 功 能： N/A
* 类 名： DalDictUnit
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/1 16:22:19   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictUnit
	/// </summary>
	public partial class DalDictUnit:IDalDictUnit
	{
		public DalDictUnit()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return Common.WinFormVar.Var.DbHelper.GetMaxID("UnitId", "DictUnit"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int UnitId)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from DictUnit");
			strSql.Append(" where UnitId=@UnitId");
			SqlParameter[] parameters = {
					new SqlParameter("@UnitId", SqlDbType.Int,4)
			};
			parameters[0].Value = UnitId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictUnit model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into DictUnit(");
			strSql.Append("UnitName,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@UnitName,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@UnitName", SqlDbType.NVarChar,30),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.UnitName;
			parameters[1].Value = model.Status;
			parameters[2].Value = model.CreatedAt;
			parameters[3].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictUnit model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update DictUnit set ");
			strSql.Append("UnitName=@UnitName,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where UnitId=@UnitId");
			SqlParameter[] parameters = {
					new SqlParameter("@UnitName", SqlDbType.NVarChar,30),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@UnitId", SqlDbType.Int,4)};
			parameters[0].Value = model.UnitName;
			parameters[1].Value = model.Status;
			parameters[2].Value = model.CreatedAt;
			parameters[3].Value = model.UpdatedAt;
			parameters[4].Value = model.UnitId;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int UnitId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DictUnit ");
			strSql.Append(" where UnitId=@UnitId");
			SqlParameter[] parameters = {
					new SqlParameter("@UnitId", SqlDbType.Int,4)
			};
			parameters[0].Value = UnitId;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string UnitIdlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DictUnit ");
			strSql.Append(" where UnitId in ("+UnitIdlist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictUnit GetModel(int UnitId)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 UnitId,UnitName,Status,CreatedAt,UpdatedAt from DictUnit ");
			strSql.Append(" where UnitId=@UnitId");
			SqlParameter[] parameters = {
					new SqlParameter("@UnitId", SqlDbType.Int,4)
			};
			parameters[0].Value = UnitId;

			Model.MdlDictUnit model=new Model.MdlDictUnit();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictUnit DataRowToModel(DataRow row)
		{
			Model.MdlDictUnit model=new Model.MdlDictUnit();
			if (row != null)
			{
				if(row["UnitId"]!=null && row["UnitId"].ToString()!="")
				{
					model.UnitId=int.Parse(row["UnitId"].ToString());
				}
				if(row["UnitName"]!=null)
				{
					model.UnitName=row["UnitName"].ToString();
				}
				if(row["Status"]!=null && row["Status"].ToString()!="")
				{
					if((row["Status"].ToString()=="1")||(row["Status"].ToString().ToLower()=="true"))
					{
						model.Status=true;
					}
					else
					{
						model.Status=false;
					}
				}
				if(row["CreatedAt"]!=null && row["CreatedAt"].ToString()!="")
				{
					model.CreatedAt=DateTime.Parse(row["CreatedAt"].ToString());
				}
				if(row["UpdatedAt"]!=null && row["UpdatedAt"].ToString()!="")
				{
					model.UpdatedAt=DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select UnitId,UnitName,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictUnit ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" UnitId,UnitName,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictUnit ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM DictUnit ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.UnitId desc");
			}
			strSql.Append(")AS Row, T.*  from DictUnit T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictUnit";
			parameters[1].Value = "UnitId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

