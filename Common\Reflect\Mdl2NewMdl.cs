﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using NPOI.SS.Formula.Functions;

namespace Common
{
    public class ReflectSetValue
    {
        public static NewT Mdl2NewMdl<T, NewT>(T mdl) where T : class, new() where NewT : class, new()
        {
            NewT newMdl = new NewT();
            PropertyInfo[] newPis = newMdl.GetType().GetProperties();

            PropertyInfo[] pis = typeof(T).GetProperties();
            foreach (PropertyInfo pi in pis)
            {

                PropertyInfo newPi = newPis.SingleOrDefault(x => x.Name == pi.Name);
                if (newPi != null)
                {
                    //得到属性的值
                    object value1 = pi.GetValue(mdl, null);
                    switch (newPi.PropertyType.Name)
                    {
                        case "Nullable`1":
                            if (newPi.PropertyType.FullName.Contains("Decimal"))
                            {
                                if (value1 != DBNull.Value) newPi.SetValue(newMdl, decimal.Parse(value1.ToString()), null);
                                else newPi.SetValue(newMdl, null, null);
                            }
                            else if (newPi.PropertyType.FullName.Contains("DateTime"))
                            {
                                if (value1 != DBNull.Value) newPi.SetValue(newMdl, DateTime.Parse(value1.ToString()), null);
                                else newPi.SetValue(newMdl, null, null);
                            }
                            else
                            {
                                if (value1 != DBNull.Value) newPi.SetValue(newMdl, value1, null);
                                else newPi.SetValue(newMdl, null, null);
                            }
                            break;
                        default:
                            if (value1 != DBNull.Value) newPi.SetValue(newMdl, value1, null);
                            else newPi.SetValue(newMdl, null, null);
                            break;
                    }

                }
            }

            return newMdl;
        }

        /// <summary>
        /// 通过反射获取属性值
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static string GetPropertyValue(object obj, string propertyName)
        {
            // 检查对象是否为 null
            if (obj == null)
            {
                throw new ArgumentNullException(nameof(obj), "对象不能为 null。");
            }

            // 获取对象的类型
            Type objType = obj.GetType();

            // 获取指定名称的属性
            PropertyInfo propertyInfo = objType.GetProperty(propertyName);

            // 如果属性不存在，则抛出异常
            if (propertyInfo == null)
            {
                throw new ArgumentException($"在类型 '{objType.FullName}' 中找不到属性 '{propertyName}'。", nameof(propertyName));
            }

            // 获取属性值
            var value = propertyInfo.GetValue(obj, null);  // 获取属性的值，第二个参数在非索引属性中为 null

            // 将属性值转换为字符串，并处理 null 值
            return value?.ToString() ?? string.Empty;  // 如果值为 null，返回空字符串
        }

    }
}
