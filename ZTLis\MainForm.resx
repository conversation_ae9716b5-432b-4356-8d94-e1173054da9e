﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ContextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>184, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="ToolStripMenuItem1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAACXBIWXMAAAsMAAALDAE/QCLIAAAC1UlE
        QVQ4T62S30uaYRTHz0aMsd0HXYSEA8UgwgrSsNRQC/V9/ZGaZVmabyUkBFnRbTfBLvYXLDbG2G7WGlGS
        P2tW/sh+rV+aztZNt/0D4z17HnPBdrt94bzPC+d8vs95zvPAf9Pr+3hE4jH9/1vVPM3Rmj8VammBNyS5
        Nzho2vf7X8ZUKslxMAif6+srcTQzA1GlUkJzpMb8ltSGpNJ7OCKTVeBdm815MTd3W15cxMPx8US4o0OS
        9Xgg6/VCWC6XHE5MJG6WljC/sHC7Q2opQ1lIsizEdTrRyeRk6Wp0FAtWKxYDATzkuPimTCamQeB4ORjE
        stuN11NTeBoIlOJarShpMgEktFqIqlR1aZaNXDidWOjrwyuHA0vT0/yR379Lozw7y5eJ+TWJos+HWas1
        ElEq6ygLoaYmSBqNtM3GtMGQuBwcrBgUBwbwx/w83iwsYAX2eLDIcZg1mxO0dod0TtmK1ltbYaenB+i5
        00ZjLD88zBdJNyVi8t3lwvLICJY4js9aLDE6jz2DAdaam+/h31oVCiFlNkNYJtMf2+131KDY318xoR18
        c7nuaC5ttcKKQFClqvpQUwNptxsiCsWLnMezQY7B5xkGCyyLVxYLFm02vPJ6+YOxsQ1akx4agndVFqIK
        BWzr9RDp7BTmfL7QhdeLZ2o1nms0mCcDLdjteKnX46nBgOd+P+Y4biNKarfJMShb+ayJRM+yo6Pvjwi8
        r1TiCTE4s1r5DMNsZVh269zh4A96e5HMCQ8mJnDf41n+0tDw9MFgXSp9njKZPubIG8iQ3U5Iy+RGQpty
        uZBGmmFCR2QWu2Yz5pxOnszh1apY/OTBIN7VBRG5XJwxGrcO7XZ+T69fJzci/KrRQFKno1csTDHMOjH5
        mTIaV8Lt7XXxzs7qEIg22trgwGKBLbVast3dzRFTwQ4BVxob4ZNYDLu9vXQjAcm5YmRNkge0XFtbpf9J
        AL8AlKhybzWNKmMAAAAASUVORK5CYII=
</value>
  </data>
  <data name="ToolStripMenuItem5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA3QAAAN0AEQbD0HAAAAInRF
        WHRBdXRob3IASm9uaWFuIEd1dmVsaSwgT2xpYmlhIFRzYXRpG3GlPgAAAFJ0RVh0Q29weXJpZ2h0AEND
        IEF0dHJpYnV0aW9uLVNoYXJlQWxpa2UgaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbGljZW5zZXMv
        Ynktc2EvMy4wL16DWrwAAAAWdEVYdFRpdGxlAENhbmR5IEljb24gVGhlbWXr/knSAAACLElEQVQ4T6WO
        30uTYRzFX2+yf6CLINCLBZFKWrMfi9QuNiFNkBKhVWw1E5lgWGbZCjfBiRtso7woKEgeiSS8CCIqkMxs
        iThENo1Zcz9yay6x/oLjc57kvQyhFw7vec/3cw6v9t9P5v4dLdpuOxPv7hxN9N0UKfddkfG6RdY/KHKB
        YSV6ZryRIcsOuxoALeZ0PEu6epHx3EPW60He70Uh6MPGw4ASPTPeyJBlh101sNhuF4nebqRct/Cjvw9Z
        CeWHBrDuG1SiZ8YbGbLs6AMRu1WsXHditacLCQnGR0JIu13ISU+lPS6V8UaGLDv6wKy1RXztuIqVzmtI
        yd/dXFtDcuIlErdvKNEzSwV8iiHLjj7w6XyTiNovYNlxGXFnG3Ljz/FnPY9v8k3RM+ONDFl29IHJRotY
        aG3GorUF0UutWLpyEemxUfze+KVEz4w3MmTZ0QfemmvFXFM9Is0NWDh3FvMSiD15jM1CQYmeGW9kyLKj
        D7yuNYkvljrM1p9GuNGCyIMgfiZX8f3RiBI9M97IkGVHH3h1slrM1J3A55pjmOtwILMUw3LQj7D5lBI9
        M97IkGVHH5g4WimmTUZMm44g3GDGfJsNMzXH1TdFz4y3v5kR7KgB+RSNV5WNTVVX4CNlLMfU4TL53v5W
        WcV2Vq6+ybLDLgeKnx7c/2Ly0AF82KHIssMuB3b1lO6zhQylb0KGkvdBQ8m7f4kMWXbY5QB/Y7fUHqm9
        OxRZ2dGKtgAqcCfny+QMQAAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="toolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>351, 17</value>
  </metadata>
  <data name="ToolBarExit.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIDSURBVDhPpZLrS5NhGMb3j4SWh0oRQVExD4gonkDpg4hG
        YKxG6WBogkMZKgPNCEVJFBGdGETEvgwyO9DJE5syZw3PIlPEE9pgBCLZ5XvdMB8Ew8gXbl54nuf63dd9
        0OGSnwCahxbPRNPAPMw9Xpg6ZmF46kZZ0xSKzJPIrhpDWsVnpBhGkKx3nAX8Pv7z1zg8OoY/cITdn4fw
        bf/C0kYAN3Ma/w3gWfZL5kzTKBxjWyK2DftwI9tyMYCZKXbNHaD91bLYJrDXsYbrWfUKwJrPE9M2M1Oc
        VzOOpHI7Jr376Hi9ogHqFIANO0/MmmmbmSmm9a8ze+I4MrNWAdjtoJgWcx+PSzg166yZZ8xM8XvXDix9
        c4jIqFYAjoriBV9AhEPv1mH/sonogha0afbZMMZz+yreTGyhpusHwtNNCsA5U1zS4BLxzJIfg299qO32
        Ir7UJtZfftyATqeT+8o2D8JSjQrAJblrncYL7ZJ2+bfaFnC/1S1NjL3diRat7qrO7wLRP3HjWsojBeCo
        mDEo5mNjuweFGvjWg2EBhCbpkW78htSHHwRyNdmgAFzPEee2iFkzayy2OLXzT4gr6UdUnlXrullsxxQ+
        kx0g8BTA3aZlButjSTyjODq/WcQcW/B/Je4OQhLvKQDnzN1mp0nnkvAhR8VuMzNrpm1mpjgkoVwB/v8D
        TgDQASA1MVpwzwAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="menuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>461, 17</value>
  </metadata>
</root>