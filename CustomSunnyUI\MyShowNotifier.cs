﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Sunny.UI;

namespace CustomSunnyUI
{
    public class MyShowNotifier
    {
        public static void ShowSuccessNotifier(string desc, bool isDialog = false, int timeout = 2000)
        {
            UINotifierHelper.ShowNotifier(desc, UINotifierType.OK, "成功", isDialog, timeout);
        }
        public static void ShowWarningNotifier(string desc, bool isDialog = false, int timeout = 2000)
        {
            UINotifierHelper.ShowNotifier(desc, UINotifierType.WARNING, "警告", isDialog, timeout);
        }
        public static void ShowErrorNotifier(string desc, bool isDialog = false, int timeout = 2000, string title = "错误")
        {
            UINotifierHelper.ShowNotifier(desc, UINotifierType.ERROR, title, isDialog, timeout);
        }
        public static void ShowInfoNotifier(string desc, bool isDialog = false, int timeout = 2000)
        {
            UINotifierHelper.ShowNotifier(desc, UINotifierType.INFO, "提示", isDialog, timeout);
        }
    }
}
