using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictDepartment2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictDepartment _bllDictDepartment = new BllDictDepartment();
        Model.MdlDictDepartment _mdlDictDepartment = new MdlDictDepartment();

        public DictDepartment2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtHisCode.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictDepartment2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化科室类型控件
            singleDeptType1.Init();
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictDepartment.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtHisCode.Text = "";
            singleDeptType1.SelectedIndex = 0;
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["DeptCode"] + "";
            TxtName.Text = row["DeptName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtHisCode.Text = row["HisDeptCode"] + "";
            singleDeptType1.Text = row["DeptType"] + "";
            TxtDescription.Text = row["Description"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写科室名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写科室编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("His编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["DeptCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["DeptName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && TxtHisCode.Text.Trim() != base.MyRow["HisDeptCode"].ToString().Trim() && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("His编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictDepartment.GetRecordCount($"DeptCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictDepartment.GetRecordCount($"DeptName='{name}'");
            return cnt > 0;
        }
        private bool CheckHisCodeExists(string hisCode)
        {
            int cnt = _bllDictDepartment.GetRecordCount($"HisDeptCode='{hisCode}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictDepartment.DeptCode = TxtCode.Text.Trim();
            _mdlDictDepartment.DeptName = TxtName.Text.Trim();
            _mdlDictDepartment.HisDeptCode = TxtHisCode.Text.Trim();
            _mdlDictDepartment.PinYin = TxtJc.Text.Trim();
            _mdlDictDepartment.DeptType = singleDeptType1.Text;
            _mdlDictDepartment.Description = TxtDescription.Text.Trim();
            _mdlDictDepartment.ParentDeptId = null;
            _mdlDictDepartment.Status = true;
            _mdlDictDepartment.CreatedAt = DateTime.Now;
            _mdlDictDepartment.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictDepartment, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["DeptId"] = _bllDictDepartment.Add(_mdlDictDepartment);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictDepartment = _bllDictDepartment.GetModel(int.Parse(base.MyRow["DeptId"].ToString()));
            _mdlDictDepartment.DeptCode = TxtCode.Text.Trim();
            _mdlDictDepartment.DeptName = TxtName.Text.Trim();
            _mdlDictDepartment.HisDeptCode = TxtHisCode.Text.Trim();
            _mdlDictDepartment.PinYin = TxtJc.Text.Trim();
            _mdlDictDepartment.DeptType = singleDeptType1.SelectedText;
            _mdlDictDepartment.Description = TxtDescription.Text.Trim();
            _mdlDictDepartment.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictDepartment, base.MyRow);

            //数据保存
            try
            {
                _bllDictDepartment.Update(_mdlDictDepartment);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }
    }
}