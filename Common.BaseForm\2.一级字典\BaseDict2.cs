﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Common;
using Common.Delegate;

namespace Common.BaseForm
{
    public partial class BaseDict2 : BaseChild
    {
        #region 字段和属性

        private Chs2Spell _myChs2Spell = new Chs2Spell();
        private Common.Delegate.TransmitDataRow _transmitDataRow;
        private Common.Delegate.TransmitTxt _transmitTxt;
        protected DataTable MyTable { get; set; }
        protected DataRow MyRow { get; set; }
        protected Chs2Spell MyChs2Spell
        {
            get { return _myChs2Spell; }
            set
            {
                _myChs2Spell = value;
            }
        }
        protected bool Insert { get; set; }
        public Common.Delegate.TransmitTxt MyTransmitTxt
        {
            get { return _transmitTxt; }
            set
            {
                _transmitTxt = value;
            }

        }
        public Common.Delegate.TransmitDataRow MyTransmitDataRow
        {
            get { return _transmitDataRow; }
            set
            {
                _transmitDataRow = value;

            }

        }

        #endregion

        public BaseDict2()
        {
            InitializeComponent();
        }

        private void BaseDict2_Load(object sender, EventArgs e)
        {
            if (!base.IsDesignMode())
            {
                if (MyTransmitDataRow != null) this.MyTransmitDataRow.SetDataRow += DataShow;
            }

        }
        private void BaseDict2_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (MyTransmitDataRow != null) this.MyTransmitDataRow.SetDataRow -= DataShow;
        }

        #region 自定义函数

        protected virtual void DataShow(DataRow row)
        {

        }

        #endregion


    }
}
