﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public class Func
    {
        /// <summary>
        /// 返回true是控件为空，并弹出提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool NotAllowEmpty(MyNumericEdit input)
        {
            if (string.IsNullOrWhiteSpace(input.Text))
            {
                MessageBox.Show($"{input.Captain.Replace(" ", "")}不允许为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                input.Select();
                return true;
            }

            return false;
        }

        /// <summary>
        /// 返回true是控件为空，并弹出提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool NotAllowEmpty(MyTextBox input)
        {
            if (string.IsNullOrWhiteSpace(input.Text))
            {
                MessageBox.Show($"{input.Captain.Replace(" ", "")}不允许为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                input.Select();
                return true;
            }

            return false;
        }
        /// <summary>
        /// 返回true是控件为空，并弹出提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool NotAllowEmpty(MyDtComobo input)
        {
            if (input.SelectedValue == null)
            {
                MessageBox.Show($"{input.Captain.Replace(" ", "")}不允许为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                input.Select();
                return true;
            }

            return false;
        }
        /// <summary>
        /// 返回true是控件为空，并弹出提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool NotAllowEmpty(MySingleComobo input)
        {
            if (input.SelectedValue == null)
            {
                MessageBox.Show($"{input.Captain.Replace(" ", "")}不允许为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                input.Select();
                return true;
            }

            return false;
        }
        /// <summary>
        /// 返回true是控件为空，并弹出提示
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static bool NotAllowEmpty(MyDateEdit input)
        {
            if (input.Value == DBNull.Value)
            {
                MessageBox.Show($"{input.Captain.Replace(" ", "")}不允许为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                input.Select();
                return true;
            }

            return false;
        }
    }
}
