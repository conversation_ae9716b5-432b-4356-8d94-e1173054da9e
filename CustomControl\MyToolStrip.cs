﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public partial class MyToolStrip : ToolStrip
    {
        public MyToolStrip()
        {
            InitializeComponent();
        }

        public MyToolStrip(IContainer container)
        {
            container.Add(this);

            InitializeComponent();
        }

        private void MyToolStrip_Paint(object sender, PaintEventArgs e)
        {
            if ((sender as ToolStrip).RenderMode == ToolStripRenderMode.ManagerRenderMode)
            {
                Rectangle rect = new Rectangle(0, 0, (sender as ToolStrip).Width - 1, (sender as ToolStrip).Height - 1);
                e.Graphics.SetClip(rect);
            }
        }
    }
}
