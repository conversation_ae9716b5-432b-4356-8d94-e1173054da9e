﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Text;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CustomControl
{
    public partial class ColorLabel : Label
    {
        public ColorLabel()
        {
            InitializeComponent();
        }

        public ColorLabel(IContainer container)
        {
            container.Add(this);

            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            string info = Text;
            string[] datas = info.Split('\n');
            int line = 0;
            Graphics g = e.Graphics;

            foreach (string data in datas)
            {
                int xoff = 0;
                if (data.IndexOf('|') >= 0)
                {
                    string[] text = data.Split('|');
                    string lineText = "";
                    for (int i = 0; i < text.Length; i += 2)
                    {
                        lineText += text[i + 1];
                    }

                    for (int i = 0; i < text.Length; i += 2)
                    {
                        Color color = ForeColor;
                        if (text[i] != "")
                        {
                            color = System.Drawing.ColorTranslator.FromHtml(text[i]);
                        }

                        if (xoff == 0)
                        {
                            SizeF textSize = g.MeasureString(lineText, this.Font);
                            switch (TextAlign)
                            {
                                case ContentAlignment.BottomCenter:
                                case ContentAlignment.MiddleCenter:
                                case ContentAlignment.TopCenter:
                                    if (this.Width > textSize.Width)
                                    {
                                        xoff = (this.Width - (int)textSize.Width) / 2;
                                    }
                                    break;
                            }
                        }

                        Brush brush = new SolidBrush(color);
                        e.Graphics.DrawString(text[i + 1], Font, brush, xoff, Font.Height * line);
                        brush.Dispose();
                        xoff += (int)e.Graphics.MeasureString(text[i + 1], Font).Width- (int)(e.Graphics.MeasureString("1", Font).Width*0.4);
                    }
                }
                else
                {
                    SizeF textSize = g.MeasureString(data, this.Font);
                    switch (TextAlign)
                    {
                        case ContentAlignment.BottomCenter:
                        case ContentAlignment.MiddleCenter:
                        case ContentAlignment.TopCenter:
                            if (e.ClipRectangle.Width > textSize.Width)
                            {
                                xoff = (e.ClipRectangle.Width - (int)textSize.Width) / 2;
                            }
                            break;
                    }

                    Brush brush = new SolidBrush(ForeColor);
                    e.Graphics.DrawString(data, Font, brush, xoff, Font.Height * line);
                    brush.Dispose();
                }
                line++;
            }
        }
    


    }
}
