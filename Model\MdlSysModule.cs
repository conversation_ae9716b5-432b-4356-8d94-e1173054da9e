﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysModule.cs
*
* 功 能： N/A
* 类 名： MdlSysModule
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:34   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Collections.Generic;

namespace Model
{
	/// <summary>
	/// MdlSysModule:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MdlSysModule
	{
		public MdlSysModule()
		{}
		#region Model
		private string _scndcode;
		private string _modulecode;
		private string _modulename;
		private int? _sortno;
		/// <summary>
		/// 
		/// </summary>
		public string ScndCode
		{
			set{ _scndcode=value;}
			get{return _scndcode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ModuleCode
		{
			set{ _modulecode=value;}
			get{return _modulecode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ModuleName
		{
			set{ _modulename=value;}
			get{return _modulename;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? SortNo
		{
			set{ _sortno=value;}
			get{return _sortno;}
		}
	    public List<MdlSysModuleAuth> ModuleAuthLists { get; set; }
        #endregion Model

    }
}

