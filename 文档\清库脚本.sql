/*==============================================================
  LIS系统清库脚本
  说明：按照主外键关系的正确顺序删除表
  作者：Kiro AI
  创建日期：2025/7/22
==============================================================*/

USE lis
GO

PRINT '开始执行清库脚本...'
GO

/*==============================================================
  第一部分：删除业务表（先删除有外键依赖的表）
==============================================================*/
PRINT '1. 删除业务表...'
GO

-- 1. 删除结果审核记录表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabResultReview]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabResultReview'
    DROP TABLE [dbo].[LabResultReview]
END
GO

-- 2. 删除检验结果表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabResult]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabResult'
    DROP TABLE [dbo].[LabResult]
END
GO

-- 3. 删除仪器结果队列表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabInstrResultQ]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabInstrResultQ'
    DROP TABLE [dbo].[LabInstrResultQ]
END
GO

-- 4. 删除仪器原始报文缓冲表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabInstrRawMsg]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabInstrRawMsg'
    DROP TABLE [dbo].[LabInstrRawMsg]
END
GO

-- 5. 删除检验医嘱明细表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabOrderItem]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabOrderItem'
    DROP TABLE [dbo].[LabOrderItem]
END
GO

-- 6. 删除检验标本信息表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabSpecimen]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabSpecimen'
    DROP TABLE [dbo].[LabSpecimen]
END
GO

-- 7. 删除检验申请主表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabOrder]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabOrder'
    DROP TABLE [dbo].[LabOrder]
END
GO

-- 8. 删除仪器标本参数映射表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabInstrSpecimenParamMap]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabInstrSpecimenParamMap'
    DROP TABLE [dbo].[LabInstrSpecimenParamMap]
END
GO

-- 9. 删除仪器项目映射表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabInstrItemMap]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabInstrItemMap'
    DROP TABLE [dbo].[LabInstrItemMap]
END
GO

/*==============================================================
  第二部分：删除字典表和配置表（先删除有外键依赖的表）
==============================================================*/
PRINT '2. 删除字典表和配置表...'
GO

-- 1. 删除检验组合-项目映射表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictLabProfileItemMap]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictLabProfileItemMap'
    DROP TABLE [dbo].[DictLabProfileItemMap]
END
GO

-- 2. 删除检验项目计算公式表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictLabItemCalculation]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictLabItemCalculation'
    DROP TABLE [dbo].[DictLabItemCalculation]
END
GO

-- 3. 删除检验项目参考区间表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictLabItemRefRange]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictLabItemRefRange'
    DROP TABLE [dbo].[DictLabItemRefRange]
END
GO

-- 4. 删除检验组合字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictLabProfile]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictLabProfile'
    DROP TABLE [dbo].[DictLabProfile]
END
GO

-- 5. 删除检验项目字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictLabItem]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictLabItem'
    DROP TABLE [dbo].[DictLabItem]
END
GO

-- 6. 删除检验方法学字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictMethod]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictMethod'
    DROP TABLE [dbo].[DictMethod]
END
GO

-- 7. 删除方法学类别字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictMethodCategory]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictMethodCategory'
    DROP TABLE [dbo].[DictMethodCategory]
END
GO

-- 8. 删除检验项目类别字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictLabItemCategory]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictLabItemCategory'
    DROP TABLE [dbo].[DictLabItemCategory]
END
GO

-- 9. 删除标本参数字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictSpecimenParam]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictSpecimenParam'
    DROP TABLE [dbo].[DictSpecimenParam]
END
GO

-- 10. 删除标本状态字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictSpecimenCondition]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictSpecimenCondition'
    DROP TABLE [dbo].[DictSpecimenCondition]
END
GO

-- 11. 删除标本类型字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictSampleType]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictSampleType'
    DROP TABLE [dbo].[DictSampleType]
END
GO

-- 12. 删除检验结果单位字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictUnit]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictUnit'
    DROP TABLE [dbo].[DictUnit]
END
GO

-- 13. 删除系统用户表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysUser]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysUser'
    DROP TABLE [dbo].[SysUser]
END
GO

-- 14. 删除医生字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictDoctor]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictDoctor'
    DROP TABLE [dbo].[DictDoctor]
END
GO

-- 15. 删除科室字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictDepartment]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictDepartment'
    DROP TABLE [dbo].[DictDepartment]
END
GO

-- 16. 删除就诊类型字典表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[DictVisitType]') AND type in (N'U'))
BEGIN
    PRINT '删除表: DictVisitType'
    DROP TABLE [dbo].[DictVisitType]
END
GO

-- 17. 删除仪器主档表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[LabInstrument]') AND type in (N'U'))
BEGIN
    PRINT '删除表: LabInstrument'
    DROP TABLE [dbo].[LabInstrument]
END
GO

/*==============================================================
  第三部分：删除系统菜单和权限表
==============================================================*/
PRINT '3. 删除系统菜单和权限表...'
GO

-- 1. 删除角色模块表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysRoleModule]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysRoleModule'
    DROP TABLE [dbo].[SysRoleModule]
END
GO

-- 2. 删除角色权限表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysRoleAuth]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysRoleAuth'
    DROP TABLE [dbo].[SysRoleAuth]
END
GO

-- 3. 删除系统报表表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysRpt]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysRpt'
    DROP TABLE [dbo].[SysRpt]
END
GO

-- 4. 删除报表类别规则表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysRpt_Class_Level]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysRpt_Class_Level'
    DROP TABLE [dbo].[SysRpt_Class_Level]
END
GO

-- 5. 删除报表类别表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysRpt_Class]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysRpt_Class'
    DROP TABLE [dbo].[SysRpt_Class]
END
GO

-- 6. 删除系统角色表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysRole]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysRole'
    DROP TABLE [dbo].[SysRole]
END
GO

-- 7. 删除系统参数表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysPara]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysPara'
    DROP TABLE [dbo].[SysPara]
END
GO

-- 8. 删除模块权限表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysModuleAuth]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysModuleAuth'
    DROP TABLE [dbo].[SysModuleAuth]
END
GO

-- 9. 删除系统模块表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysModule]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysModule'
    DROP TABLE [dbo].[SysModule]
END
GO

-- 10. 删除二级菜单表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysMenu2]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysMenu2'
    DROP TABLE [dbo].[SysMenu2]
END
GO

-- 11. 删除一级菜单表
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SysMenu1]') AND type in (N'U'))
BEGIN
    PRINT '删除表: SysMenu1'
    DROP TABLE [dbo].[SysMenu1]
END
GO

PRINT '清库脚本执行完成！'
GO