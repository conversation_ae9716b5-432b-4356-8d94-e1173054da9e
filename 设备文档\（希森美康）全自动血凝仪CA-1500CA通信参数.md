好的，我已经详细分析了您提供的这份 Sysmex CA-6000 操作手册的附录C（共40页），并为您整理了一份全面的中文Markdown格式摘要。

这份附录是关于仪器的技术信息，主要涵盖了与外部系统（如主机电脑）进行数据交换的细节，以及条形码和测试协议的详细规格。对于需要将CA-6000与实验室信息系统（LIS）集成或进行高级设置的用户来说，这份文档至关重要。

------



# Sysmex CA-6000 操作手册 - 附录C: 技术信息 摘要

## 概述

本附录提供了Sysmex CA-6000分析仪的详细技术信息，主要分为四个核心部分：

1. 
2. **串行接口 (Serial Interface)**: 定义了与主机电脑（Host Computer）进行通信的物理连接、协议和软件规范。
3. **文本格式 (Text Format)**: 详细描述了分析数据、查询数据和指令信息的数据结构和格式。
4. **ID条形码 (ID Bar Code)**: 规定了仪器支持的条形码类型、尺寸、光学要求及校验位计算方法。
5. **测试协议列表 (Test Protocol List)**: 列出了各种检测项目的出厂默认设置和推荐设置。

------



## 1. 主机串行接口 (Serial Interface for Host Computer)

这部分详细说明了仪器如何通过RS-232C接口与主机电脑进行通信。

### 1.1 物理连接与信号

- 
- **接口类型**: EIA RS-232C V.24 标准，使用 **9针D-SUB母头 (DB-9S)** 连接器。
- **引脚分配 (Pin Assignment)**:
  | 引脚 (PIN) | 信号名称 | 信号流向 | 描述 |
  | :--- | :--- | :--- | :--- |
  | 2 | RxD | IN | 从主机接收数据 |
  | 3 | TxD | OUT | 向主机发送数据 |
  | 4 | DTR | OUT | 数据终端就绪 (CA->Host) |
  | 5 | SG | - | 信号地 |
  | 6 | DSR | IN | 数据设备就绪 (Host->CA) |
  | 7 | RTS | OUT | 请求发送 (CA->Host) |
  | 8 | CTS | IN | 清除发送 (Host->CA) |

### 1.2 通信格式与设置

- 
- **模式**: 异步半双工 (Asynchronous Half Duplex)。
- **通信参数**:
  - 
  - **波特率 (Baud Rate)**: 600, 1200, 2400, 4800, **9600** (下划线为出厂默认值)。
  - **字符长度**: 7-Bit / **8-Bit**。
  - **停止位**: **1-Bit** / 2-Bit。
  - **奇偶校验**: **None** (无) / Even (偶) / Odd (奇)。
- **通信协议 (Class)**:
  - 
  - **Class A (默认)**: 单向传输。CA-6000发送数据后**不等待**主机的ACK/NAK（确认/否认）信号。适用于简单的数据输出。
  - **Class B**: 双向安全传输。CA-6000发送数据后**必须等待**主机的ACK或NAK信号，收到ACK后才认为传输成功。适用于需要高可靠性的查询和指令交互。
- **数据帧**: 数据被包裹在 **STX** (Start of Text, 02H) 和 **ETX** (End of Text, 03H) 字符之间，最大长度255字节。

### 1.3 传输错误处理

文档列出了多种通信错误及其原因，是故障排查的关键。

- 
- **HC CTS Time Out**: 仪器请求发送（RTS=ON）后5秒内未收到主机的允许发送信号（CTS=ON）。
- **HC ACK Time Out**: (Class B模式下) 仪器发送数据后15秒内未收到主机的ACK/NAK响应。
- **HC ACK Code Error**: 主机返回了非ACK/NAK的错误响应码。
- **Instruction Not Found...**: 主机对样本查询返回"999"，表示无该样本的测试指令，仪器将停止对该样本架后续样本的查询。

------



## 2. 文本格式 (Text Format)

这部分定义了仪器与主机之间传输数据的具体内容和结构。

### 核心标识符

- 
- **文本区分码I (Text Distinction Code I)**:
  - 
  - "D": 分析数据 (Analysis Data)
  - "R": 查询数据 (Inquiry Data)
  - "S": 指令信息数据 (Order Information Data)

### 2.1 分析数据格式 (D)

这是仪器向主机发送样本检测结果的格式。

- 
- **结构**: 固定字段 + 多个数据块（Data n）。
- **关键字段**:
  - 
  - Sample ID Number (13位)
  - Patient Name (11位)
  - Rack Number (4位)
  - Tube Position Number (2位)
  - Data n: 每个检测结果包含Parameter Code (3位), Data (5位), Flag (1位)。
- **参数代码 (Parameter Code)**: 每个检测项目都有一个唯一的3位数字代码，如 041代表PT凝血时间，061代表Fbg凝血时间。
- **数据 (Data)**: 5位数字字符串，**不含小数点**。例如，PT结果12.3秒会被格式化为0123。主机需根据项目自行添加小数点。
- **标志 (Flag)**: 1位字符，表示数据状态，如：  (正常), + (高于病人参考范围), * (分析错误), > (高于报告上限)。

### 2.2 查询数据格式 (R)

这是仪器向主机查询样本测试指令的格式。

- 
- **查询类型 (Text Distinction Code II)**:
  - 
  - "1": 按**样本架号+位置号**查询。
  - "2": 按**样本ID号**查询。
- **结构**: 类似于分析数据格式，但数据部分只包含需要查询的参数代码。

### 2.3 指令信息数据格式 (S)

这是主机响应仪器的查询，向仪器下发测试指令的格式。

- 
- **结构**: 主机返回样本ID、病人信息以及需要执行的测试项目列表。
- **特殊指令代码**:
  - 
  - 000: 不分析此项目。
  - 999: (仅在按样本架查询时) 停止对该样本架后续所有样本的查询。

------



## 3. ID条形码 (ID Bar Code)

这部分详细规定了仪器条码阅读器支持的条码规格。

### 3.1 适用条码类型及校验

- 
- **支持的条码**:
  - 
  - NW-7 (CODABAR)
  - CODE-39
  - CODE-128
  - ITF (Interleaved 2 of 5)
  - JAN-8, JAN-13
- **校验位 (Check-Digit)**: 强烈建议使用校验位以防读码错误。文档详细描述了多种校验算法的计算过程，并附有示例：
  - 
  - **Modulus 10/Weight 3**: 用于JAN, ITF等。
  - **Modulus 11**: 用于NW-7, CODE-11。
  - **Weighted Modulus 11**: 用于NW-7, CODABAR。
  - **Modulus 16**: 用于NW-7, CODABAR的特殊场景。
  - **Modulus 43**: 用于CODE-39。
  - **Modulus 103**: 用于CODE-128。

### 3.2 条码物理与光学要求

- 
- **尺寸**:
  - 
  - 窄条宽度 ≥ 150 µm。
  - 宽/窄比在2.2到1.4之间有严格规定。
  - 样本管条码高度 ≥ 20 mm。
  - 前后空白区 ≥ 2.5 mm。
- **光学特性**:
  - 
  - **印刷对比度 (PCS)**: ≥ 0.6。
  - **表面反射**: 必须是**漫反射 (Diffused Reflection)**，避免使用覆膜等易产生镜面反射的标签。

### 3.3 特殊条码

- 
- **质控条码**: 定义了用于自动识别质控品的特殊条码格式。
  - 
  - **NW-7**: c + 相同数字 + c (例如 c11111111c 代表质控文件1)。
  - **CODE-128**: QC- + 8位批号。

------



## 4. 测试协议列表 (Test Protocol List)

这部分是纯粹的技术参数表，为各种检测试剂提供了详细的仪器设置参数。

### 4.1 出厂默认设置 (Factory Default Settings)

- 
- 提供了主流测试项目的默认参数表，如：
  - 
  - **PT (Thromboplastin-C Plus)**: 样本50µL, 试剂100µL, 孵育180秒, 最大检测时间300秒。
  - **APTT (Actin Activated Cephaloplastin)**: 样本50µL, 试剂50µL, CaCl2 50µL, 最大检测时间600秒。
  - **Fbg (Fibrinogen Determination)**: 样本10µL, 缓冲液90µL, 试剂50µL, 最大检测时间180秒。
  - **AT III, Extrinsic/Intrinsic Factors** 等。

### 4.2 其他试剂的推荐设置

- 
- 提供了更多非默认试剂或测试组合的推荐参数设置。

**注意**: 表格中包含了样本体积、稀释液体积、试剂体积、孵育时间、检测器类型（凝固法/显色法）、灵敏度、最大检测时间、分析范围等极其详细的参数。当更换试剂或自定义测试方法时，必须参考此列表进行正确设置。

------



## 总结

该技术附录是Sysmex CA-6000仪器的“高级用户指南”。它为实验室自动化工程师、LIS/HIS系统开发人员和资深检验技师提供了与外部系统进行可靠数据交换所需的所有协议和格式细节。同时，它也明确了条形码标签的制作标准，并为各种检测项目提供了详尽的参数设置参考，是确保仪器正确、高效运行的重要技术文档。