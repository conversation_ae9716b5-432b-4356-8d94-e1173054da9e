using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictLabItem1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictLabItem _bllDictLabItem = new BllDictLabItem();
        BLL.BllDictLabItemRefRange _bllDictLabItemRefRange = new BllDictLabItemRefRange();

        // 参考区间相关变量
        private int _labItemId;
        private DataTable _refRangeTable;
        private CurrencyManager _refRangeCm;
        private Common.Delegate.TransmitTxt _refRangeTransmitTxt = new Common.Delegate.TransmitTxt();
        private Common.Delegate.TransmitDataRow _refRangeTransmitDataRow = new Common.Delegate.TransmitDataRow();

        public DictLabItem1()
        {
            InitializeComponent();
        }

        private void DictLabItem1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();
            this.RefRangeFormInit();
            this.RefRangeDataInit(_labItemId);

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        private void DictLabItem1_FormClosed(object sender, FormClosedEventArgs e)
        {
            this._refRangeTransmitTxt.SetText -= GridMove;
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "LabItemCode", 120, "中", "", false);
            myGrid1.Init_Column("名称", "LabItemName", 160, "左", "", false);
            myGrid1.Init_Column("简称", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("英文名称", "EnglishName", 150, "左", "", false);
            myGrid1.Init_Column("报告名称", "ReportName", 150, "左", "", false);
            myGrid1.Init_Column("项目类别", "ItemCategoryName", 120, "左", "", false);
            myGrid1.Init_Column("标本类型", "SampleTypeName", 120, "左", "", false);
            myGrid1.Init_Column("默认单位", "DefaultUnit", 100, "左", "", false);
            myGrid1.Init_Column("精度", "Precision", 80, "右", "", false);
            myGrid1.Init_Column("默认方法", "MethodName", 120, "左", "", false);
            myGrid1.Init_Column("LOINC码", "LoincCode", 120, "左", "", false);
            myGrid1.Init_Column("计算项目", "IsCalculated", 80, "中", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllDictLabItem.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["LabItemCode"] };
            base.MyTable.Columns["LabItemId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
            if (base.MyTable.Rows.Count > 0)
            {
                _labItemId = int.Parse(base.MyTable.Rows[0]["LabItemId"].ToString());
            }
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictLabItem2 vform = new DictLabItem2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除检验项目：" + this.myGrid1.Columns["LabItemName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictLabItem.Delete(int.Parse(base.MyRow["LabItemId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("LabItemCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("LabItemName", new Common.MdlExcel(15, "", "名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "简称", true));
            colconfig.Add("EnglishName", new Common.MdlExcel(15, "", "英文名称", true));
            colconfig.Add("ReportName", new Common.MdlExcel(15, "", "报告名称", true));
            colconfig.Add("DefaultUnit", new Common.MdlExcel(10, "", "默认单位", true));
            colconfig.Add("Precision", new Common.MdlExcel(8, "", "精度", true));
            colconfig.Add("LoincCode", new Common.MdlExcel(12, "", "LOINC码", true));
            Common.ExcelHelper.Export(base.MyTable, "检验项目字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictLabItem mdlDictLabItem = new MdlDictLabItem();
                    if (row["名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictLabItem.LabItemCode = row["编码"] + "";
                    mdlDictLabItem.LabItemName = row["名称"] + "";
                    mdlDictLabItem.PinYin = base.MyChs2Spell.GetPy(row["名称"].ToString());
                    mdlDictLabItem.EnglishName = row["英文名称"] + "";
                    mdlDictLabItem.ReportName = row["报告名称"] + "";
                    mdlDictLabItem.DefaultUnit = row["默认单位"] + "";
                    if (row["精度"] != null && row["精度"].ToString().Trim() != "")
                    {
                        int precision;
                        if (int.TryParse(row["精度"].ToString(), out precision))
                        {
                            mdlDictLabItem.Precision = precision;
                        }
                    }
                    mdlDictLabItem.LoincCode = row["LOINC码"] + "";
                    mdlDictLabItem.IsCalculated = false;
                    mdlDictLabItem.Status = true;
                    mdlDictLabItem.CreatedAt = DateTime.Now;
                    mdlDictLabItem.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictLabItem).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictLabItem, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["LabItemId"] = _bllDictLabItem.Add(mdlDictLabItem);
                    base.MyTable.Rows.Add(newRow);

                    UpdateData[i - 1] = "导入成功";
                    i = i + 1;
                }
                base.MyTable.AcceptChanges();
                MessageBox.Show("导入成功：" + successCnt + "条，失败：" + failCnt + "条", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            }
        }

        private void DataEnable(bool Status)
        {
            if (myGrid1.RowCount == 0) return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            Model.MdlDictLabItem model1 = new Model.MdlDictLabItem();
            model1 = _bllDictLabItem.GetModel(int.Parse(base.MyRow["LabItemId"].ToString()));
            model1.Status = Status;
            base.MyRow["Status"] = Status;
            base.MyRow.AcceptChanges();
            _bllDictLabItem.Update(model1);
        }

        #endregion

        #region 事件
        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }
        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("LabItemCode+isnull(LabItemName,'')+isnull(PinYin,'')+isnull(EnglishName,'')", TxtFilter.Text.Trim());
        }
        #endregion

        #region 参考区间管理功能

        /// <summary>
        /// 初始化参考区间表格
        /// </summary>
        private void RefRangeFormInit()
        {
            myGrid2.Init_Grid();
            myGrid2.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid2.Init_Column("方法学", "MethodName", 120, "左", "", false);
            myGrid2.Init_Column("性别", "Sex", 80, "中", "", false);
            myGrid2.Init_Column("年龄下限", "AgeLower", 80, "右", "", false);
            myGrid2.Init_Column("年龄上限", "AgeUpper", 80, "右", "", false);
            myGrid2.Init_Column("年龄单位", "AgeUnit", 80, "中", "", false);
            myGrid2.Init_Column("参考下限", "LowerLimit", 100, "右", "", false);
            myGrid2.Init_Column("参考上限", "UpperLimit", 100, "右", "", false);
            myGrid2.Init_Column("文本参考值", "TextualRefValue", 120, "左", "", false);
            myGrid2.Init_Column("警告下限", "AlertLow", 100, "右", "", false);
            myGrid2.Init_Column("警告上限", "AlertHigh", 100, "右", "", false);
            myGrid2.Init_Column("危急下限", "CriticalLow", 100, "右", "", false);
            myGrid2.Init_Column("危急上限", "CriticalHigh", 100, "右", "", false);
            myGrid2.Init_Column("单位", "Unit", 80, "左", "", false);
            myGrid2.Init_Column("生效时间", "EffectiveFrom", 120, "中", "yyyy-MM-dd", false);
            myGrid2.Init_Column("失效时间", "EffectiveTo", 120, "中", "yyyy-MM-dd", false);
            myGrid2.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid2.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid2.AllowSort = true;

            this._refRangeTransmitTxt.SetText += GridMove;
        }

        /// <summary>
        /// 初始化参考区间数据
        /// </summary>
        private void RefRangeDataInit(int labItemId)
        {
            _refRangeTable = new DataTable();
            _refRangeTable = _bllDictLabItemRefRange.GetList($"DictLabItemRefRange.LabItemId={labItemId}").Tables[0]; // 先获取空表结构
            _refRangeTable.PrimaryKey = new DataColumn[] { _refRangeTable.Columns["RefRangeId"] };
            _refRangeTable.Columns["RefRangeId"].ReadOnly = false;
            _refRangeCm = (CurrencyManager)BindingContext[_refRangeTable, ""];
            myGrid2.DataTable = _refRangeTable;
        }
        private void GridMove(string moveType)
        {
            if (myGrid2.RowCount == 0)
                return;
            switch (moveType)
            {
                case "最前":
                    myGrid2.MoveFirst();
                    break;
                case "上移":
                    myGrid2.MovePrevious();
                    break;
                case "下移":
                    myGrid2.MoveNext();
                    break;
                case "最后":
                    myGrid2.MoveLast();
                    break;
                default:
                    int index;
                    if (int.TryParse(moveType, out index))
                    {
                        myGrid2.Row = index;
                    }
                    break;
            }
        }
        /// <summary>
        /// 参考区间编辑
        /// </summary>
        private void RefRangeEdit(bool insert)
        {
            if (myGrid1.RowCount == 0)
            {
                MessageBox.Show("请先选择检验项目！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            MdlDictLabItem currentLabItem = _bllDictLabItem.GetModel(int.Parse(base.MyRow["LabItemId"].ToString()));

            DataRow refRangeRow;
            if (insert)
            {
                refRangeRow = _refRangeTable.NewRow();
            }
            else
            {
                if (myGrid2.RowCount == 0)
                {
                    return;
                }
                refRangeRow = ((DataRowView)_refRangeCm.List[myGrid2.Row]).Row;
            }

            DictLabItem3 vform = new DictLabItem3(currentLabItem, insert, refRangeRow, _refRangeTable);
            vform.MyTransmitTxt = _refRangeTransmitTxt;
            vform.MyTransmitDataRow = _refRangeTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        /// <summary>
        /// 参考区间删除
        /// </summary>
        private void RefRangeDelete()
        {
            if (myGrid2.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除选中的参考区间？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.Cancel)
                return;

            DataRow refRangeRow = ((DataRowView)_refRangeCm.List[myGrid2.Row]).Row;
            long refRangeId = long.Parse(refRangeRow["RefRangeId"].ToString());

            try
            {
                _bllDictLabItemRefRange.Delete(refRangeId);
                myGrid2.Delete();
                _refRangeTable.AcceptChanges();
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("删除成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show("删除失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 参考区间启用/停用
        /// </summary>
        private void RefRangeEnable(bool status)
        {
            if (myGrid2.RowCount == 0) return;

            DataRow refRangeRow = ((DataRowView)_refRangeCm.List[myGrid2.Row]).Row;
            long refRangeId = long.Parse(refRangeRow["RefRangeId"].ToString());

            try
            {
                MdlDictLabItemRefRange model = _bllDictLabItemRefRange.GetModel(refRangeId);
                model.Status = status;
                model.UpdatedAt = DateTime.Now;

                _bllDictLabItemRefRange.Update(model);
                refRangeRow["Status"] = status;
                refRangeRow.AcceptChanges();

                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier(status ? "启用成功" : "停用成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show((status ? "启用" : "停用") + "失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 刷新参考区间数据
        /// </summary>
        private void RefRangeRefresh()
        {
            this.RefRangeDataInit(_labItemId);
        }

        #endregion

        #region 参考区间按钮事件
        private void myGrid1_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            if (myGrid1.RowCount == 0) return;
            try
            {
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                int labItemId = int.Parse(base.MyRow["LabItemId"].ToString());
                this.RefRangeDataInit(labItemId);
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载参考区间数据失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void myGrid2_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
                this.RefRangeEdit(false);
        }
        private void myGrid2_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            if ((myGrid2.Row + 1) > myGrid2.RowCount)
            {

            }
            else
            {
                MyRow = ((DataRowView)_refRangeCm.List[myGrid2.Row]).Row;
                this._refRangeTransmitDataRow.OnSetDataRow(MyRow);
            }
        }
        private void CmdItemAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.RefRangeEdit(true);
        }

        private void CmdItemDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.RefRangeDelete();
        }

        private void CmdItemEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.RefRangeEnable(true);
        }

        private void CmdItemDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.RefRangeEnable(false);
        }

        private void CmdItemRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.RefRangeRefresh();
        }




        #endregion


    }
}