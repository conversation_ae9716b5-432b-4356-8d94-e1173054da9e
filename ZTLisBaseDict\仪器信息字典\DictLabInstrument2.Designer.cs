namespace ZTLisBaseDict
{
    partial class DictLabInstrument2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.TableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.TxtInstrCode = new CustomControl.MyTextBox();
            this.TxtInstrName = new CustomControl.MyTextBox();
            this.TxtFilePath = new CustomControl.MyTextBox();
            this.TxtApiBaseUrl = new CustomControl.MyTextBox();
            this.SingleProtocolType1 = new ZTLisControl.SingleProtocolType();
            this.SingleMsgHeaderFormatType1 = new ZTLisControl.SingleMsgFormatType();
            this.TxtMsgHeader = new CustomControl.MyTextBox();
            this.TxtMsgTail = new CustomControl.MyTextBox();
            this.TxtAckSuccess = new CustomControl.MyTextBox();
            this.TxtAckFailure = new CustomControl.MyTextBox();
            this.TxtDescription = new CustomControl.MyTextBox();
            this.TxtModel = new CustomControl.MyTextBox();
            this.TxtVendor = new CustomControl.MyTextBox();
            this.TxtSerialNo = new CustomControl.MyTextBox();
            this.TxtLocation = new CustomControl.MyTextBox();
            this.NumIpPort = new CustomControl.MyNumericEdit();
            this.TxtIpHost = new CustomControl.MyTextBox();
            this.NumKeepAliveSec = new CustomControl.MyNumericEdit();
            this.TxtDbConnStr = new CustomControl.MyTextBox();
            this.TxtApiAuthToken = new CustomControl.MyTextBox();
            this.SingleComPort1 = new ZTLisControl.SingleComPort();
            this.SingleBaudRate1 = new ZTLisControl.SingleBaudRate();
            this.SingleConnType1 = new ZTLisControl.SingleConnType();
            this.singleDbType1 = new ZTLisControl.SingleDbType();
            this.SingleParity1 = new ZTLisControl.SingleParity();
            this.SingleDataBits1 = new ZTLisControl.SingleDataBits();
            this.SingleStopBits1 = new ZTLisControl.SingleStopBits();
            this.TxtFirmwareVersion = new CustomControl.MyTextBox();
            this.singleInstrumentDriverClass1 = new ZTLisControl.SingleInstrumentDriverClass();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.BtnSave = new CustomControl.MyButton();
            this.BtnCancel = new CustomControl.MyButton();
            this.singleMsgFormatType1 = new ZTLisControl.SingleMsgFormatType();
            this.TableLayoutPanel1.SuspendLayout();
            this.Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // TableLayoutPanel1
            // 
            this.TableLayoutPanel1.ColumnCount = 5;
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.Controls.Add(this.TxtInstrCode, 1, 0);
            this.TableLayoutPanel1.Controls.Add(this.TxtInstrName, 1, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtFilePath, 1, 8);
            this.TableLayoutPanel1.Controls.Add(this.TxtApiBaseUrl, 1, 10);
            this.TableLayoutPanel1.Controls.Add(this.SingleProtocolType1, 1, 11);
            this.TableLayoutPanel1.Controls.Add(this.SingleMsgHeaderFormatType1, 2, 11);
            this.TableLayoutPanel1.Controls.Add(this.TxtMsgHeader, 3, 11);
            this.TableLayoutPanel1.Controls.Add(this.TxtMsgTail, 1, 12);
            this.TableLayoutPanel1.Controls.Add(this.TxtAckSuccess, 2, 12);
            this.TableLayoutPanel1.Controls.Add(this.TxtAckFailure, 3, 12);
            this.TableLayoutPanel1.Controls.Add(this.TxtDescription, 1, 14);
            this.TableLayoutPanel1.Controls.Add(this.TxtModel, 3, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtVendor, 1, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtSerialNo, 1, 3);
            this.TableLayoutPanel1.Controls.Add(this.TxtLocation, 2, 3);
            this.TableLayoutPanel1.Controls.Add(this.NumIpPort, 2, 7);
            this.TableLayoutPanel1.Controls.Add(this.TxtIpHost, 1, 7);
            this.TableLayoutPanel1.Controls.Add(this.NumKeepAliveSec, 3, 7);
            this.TableLayoutPanel1.Controls.Add(this.TxtDbConnStr, 2, 9);
            this.TableLayoutPanel1.Controls.Add(this.TxtApiAuthToken, 3, 10);
            this.TableLayoutPanel1.Controls.Add(this.SingleComPort1, 1, 5);
            this.TableLayoutPanel1.Controls.Add(this.SingleBaudRate1, 2, 5);
            this.TableLayoutPanel1.Controls.Add(this.SingleConnType1, 1, 4);
            this.TableLayoutPanel1.Controls.Add(this.singleDbType1, 1, 9);
            this.TableLayoutPanel1.Controls.Add(this.SingleParity1, 3, 5);
            this.TableLayoutPanel1.Controls.Add(this.SingleDataBits1, 1, 6);
            this.TableLayoutPanel1.Controls.Add(this.SingleStopBits1, 2, 6);
            this.TableLayoutPanel1.Controls.Add(this.TxtFirmwareVersion, 3, 13);
            this.TableLayoutPanel1.Controls.Add(this.singleInstrumentDriverClass1, 1, 13);
            this.TableLayoutPanel1.Controls.Add(this.singleMsgFormatType1, 2, 4);
            this.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.TableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.TableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.TableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.TableLayoutPanel1.Name = "TableLayoutPanel1";
            this.TableLayoutPanel1.RowCount = 15;
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.TableLayoutPanel1.Size = new System.Drawing.Size(767, 458);
            this.TableLayoutPanel1.TabIndex = 8;
            // 
            // TxtInstrCode
            // 
            this.TxtInstrCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtInstrCode.Captain = "设备编码";
            this.TxtInstrCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtInstrCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtInstrCode.CaptainWidth = 70F;
            this.TxtInstrCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtInstrCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtInstrCode.EditMask = null;
            this.TxtInstrCode.Location = new System.Drawing.Point(11, 3);
            this.TxtInstrCode.Multiline = false;
            this.TxtInstrCode.Name = "TxtInstrCode";
            this.TxtInstrCode.PasswordChar = '\0';
            this.TxtInstrCode.ReadOnly = false;
            this.TxtInstrCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtInstrCode.SelectionStart = 0;
            this.TxtInstrCode.SelectStart = 0;
            this.TxtInstrCode.Size = new System.Drawing.Size(244, 23);
            this.TxtInstrCode.TabIndex = 0;
            this.TxtInstrCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtInstrCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtInstrCode.Watermark = null;
            // 
            // TxtInstrName
            // 
            this.TxtInstrName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtInstrName.Captain = "设备名称";
            this.TxtInstrName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtInstrName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtInstrName.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtInstrName, 2);
            this.TxtInstrName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtInstrName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtInstrName.EditMask = null;
            this.TxtInstrName.Location = new System.Drawing.Point(11, 32);
            this.TxtInstrName.Multiline = false;
            this.TxtInstrName.Name = "TxtInstrName";
            this.TxtInstrName.PasswordChar = '\0';
            this.TxtInstrName.ReadOnly = false;
            this.TxtInstrName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtInstrName.SelectionStart = 0;
            this.TxtInstrName.SelectStart = 0;
            this.TxtInstrName.Size = new System.Drawing.Size(494, 23);
            this.TxtInstrName.TabIndex = 1;
            this.TxtInstrName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtInstrName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtInstrName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtInstrName.Watermark = null;
            // 
            // TxtFilePath
            // 
            this.TxtFilePath.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtFilePath.Captain = "文件路径";
            this.TxtFilePath.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFilePath.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilePath.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFilePath.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtFilePath, 3);
            this.TxtFilePath.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFilePath.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFilePath.EditMask = null;
            this.TxtFilePath.Location = new System.Drawing.Point(11, 235);
            this.TxtFilePath.Multiline = false;
            this.TxtFilePath.Name = "TxtFilePath";
            this.TxtFilePath.PasswordChar = '\0';
            this.TxtFilePath.ReadOnly = false;
            this.TxtFilePath.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFilePath.SelectionStart = 0;
            this.TxtFilePath.SelectStart = 0;
            this.TxtFilePath.Size = new System.Drawing.Size(744, 23);
            this.TxtFilePath.TabIndex = 16;
            this.TxtFilePath.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFilePath.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFilePath.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFilePath.Watermark = null;
            // 
            // TxtApiBaseUrl
            // 
            this.TxtApiBaseUrl.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtApiBaseUrl.Captain = "API地址";
            this.TxtApiBaseUrl.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtApiBaseUrl.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtApiBaseUrl.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtApiBaseUrl.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtApiBaseUrl, 2);
            this.TxtApiBaseUrl.ContentForeColor = System.Drawing.Color.Black;
            this.TxtApiBaseUrl.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtApiBaseUrl.EditMask = null;
            this.TxtApiBaseUrl.Location = new System.Drawing.Point(11, 293);
            this.TxtApiBaseUrl.Multiline = false;
            this.TxtApiBaseUrl.Name = "TxtApiBaseUrl";
            this.TxtApiBaseUrl.PasswordChar = '\0';
            this.TxtApiBaseUrl.ReadOnly = false;
            this.TxtApiBaseUrl.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtApiBaseUrl.SelectionStart = 0;
            this.TxtApiBaseUrl.SelectStart = 0;
            this.TxtApiBaseUrl.Size = new System.Drawing.Size(494, 23);
            this.TxtApiBaseUrl.TabIndex = 19;
            this.TxtApiBaseUrl.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtApiBaseUrl.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtApiBaseUrl.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtApiBaseUrl.Watermark = null;
            // 
            // SingleProtocolType1
            // 
            this.SingleProtocolType1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleProtocolType1.Captain = "协议类型";
            this.SingleProtocolType1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleProtocolType1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleProtocolType1.CaptainWidth = 69F;
            this.SingleProtocolType1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleProtocolType1.ItemHeight = 18;
            this.SingleProtocolType1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleProtocolType1.Location = new System.Drawing.Point(11, 322);
            this.SingleProtocolType1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.SingleProtocolType1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleProtocolType1.Name = "SingleProtocolType1";
            this.SingleProtocolType1.ReadOnly = false;
            this.SingleProtocolType1.Size = new System.Drawing.Size(244, 23);
            this.SingleProtocolType1.TabIndex = 21;
            this.SingleProtocolType1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SingleMsgHeaderFormatType1
            // 
            this.SingleMsgHeaderFormatType1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleMsgHeaderFormatType1.Captain = "消息头格式类型";
            this.SingleMsgHeaderFormatType1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleMsgHeaderFormatType1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleMsgHeaderFormatType1.CaptainWidth = 111F;
            this.SingleMsgHeaderFormatType1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleMsgHeaderFormatType1.ItemHeight = 18;
            this.SingleMsgHeaderFormatType1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleMsgHeaderFormatType1.Location = new System.Drawing.Point(261, 322);
            this.SingleMsgHeaderFormatType1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.SingleMsgHeaderFormatType1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleMsgHeaderFormatType1.Name = "SingleMsgHeaderFormatType1";
            this.SingleMsgHeaderFormatType1.ReadOnly = false;
            this.SingleMsgHeaderFormatType1.Size = new System.Drawing.Size(244, 23);
            this.SingleMsgHeaderFormatType1.TabIndex = 22;
            this.SingleMsgHeaderFormatType1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // TxtMsgHeader
            // 
            this.TxtMsgHeader.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtMsgHeader.Captain = "消息头";
            this.TxtMsgHeader.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtMsgHeader.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtMsgHeader.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtMsgHeader.CaptainWidth = 70F;
            this.TxtMsgHeader.ContentForeColor = System.Drawing.Color.Black;
            this.TxtMsgHeader.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtMsgHeader.EditMask = null;
            this.TxtMsgHeader.Location = new System.Drawing.Point(511, 322);
            this.TxtMsgHeader.Multiline = false;
            this.TxtMsgHeader.Name = "TxtMsgHeader";
            this.TxtMsgHeader.PasswordChar = '\0';
            this.TxtMsgHeader.ReadOnly = false;
            this.TxtMsgHeader.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtMsgHeader.SelectionStart = 0;
            this.TxtMsgHeader.SelectStart = 0;
            this.TxtMsgHeader.Size = new System.Drawing.Size(244, 23);
            this.TxtMsgHeader.TabIndex = 23;
            this.TxtMsgHeader.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtMsgHeader.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtMsgHeader.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtMsgHeader.Watermark = null;
            // 
            // TxtMsgTail
            // 
            this.TxtMsgTail.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtMsgTail.Captain = "消息尾";
            this.TxtMsgTail.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtMsgTail.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtMsgTail.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtMsgTail.CaptainWidth = 70F;
            this.TxtMsgTail.ContentForeColor = System.Drawing.Color.Black;
            this.TxtMsgTail.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtMsgTail.EditMask = null;
            this.TxtMsgTail.Location = new System.Drawing.Point(11, 351);
            this.TxtMsgTail.Multiline = false;
            this.TxtMsgTail.Name = "TxtMsgTail";
            this.TxtMsgTail.PasswordChar = '\0';
            this.TxtMsgTail.ReadOnly = false;
            this.TxtMsgTail.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtMsgTail.SelectionStart = 0;
            this.TxtMsgTail.SelectStart = 0;
            this.TxtMsgTail.Size = new System.Drawing.Size(244, 23);
            this.TxtMsgTail.TabIndex = 24;
            this.TxtMsgTail.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtMsgTail.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtMsgTail.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtMsgTail.Watermark = null;
            // 
            // TxtAckSuccess
            // 
            this.TxtAckSuccess.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtAckSuccess.Captain = "接收成功";
            this.TxtAckSuccess.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtAckSuccess.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAckSuccess.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtAckSuccess.CaptainWidth = 70F;
            this.TxtAckSuccess.ContentForeColor = System.Drawing.Color.Black;
            this.TxtAckSuccess.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtAckSuccess.EditMask = null;
            this.TxtAckSuccess.Location = new System.Drawing.Point(261, 351);
            this.TxtAckSuccess.Multiline = false;
            this.TxtAckSuccess.Name = "TxtAckSuccess";
            this.TxtAckSuccess.PasswordChar = '\0';
            this.TxtAckSuccess.ReadOnly = false;
            this.TxtAckSuccess.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtAckSuccess.SelectionStart = 0;
            this.TxtAckSuccess.SelectStart = 0;
            this.TxtAckSuccess.Size = new System.Drawing.Size(244, 23);
            this.TxtAckSuccess.TabIndex = 25;
            this.TxtAckSuccess.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtAckSuccess.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAckSuccess.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtAckSuccess.Watermark = null;
            // 
            // TxtAckFailure
            // 
            this.TxtAckFailure.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtAckFailure.Captain = "接收失败";
            this.TxtAckFailure.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtAckFailure.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAckFailure.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtAckFailure.CaptainWidth = 70F;
            this.TxtAckFailure.ContentForeColor = System.Drawing.Color.Black;
            this.TxtAckFailure.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtAckFailure.EditMask = null;
            this.TxtAckFailure.Location = new System.Drawing.Point(511, 351);
            this.TxtAckFailure.Multiline = false;
            this.TxtAckFailure.Name = "TxtAckFailure";
            this.TxtAckFailure.PasswordChar = '\0';
            this.TxtAckFailure.ReadOnly = false;
            this.TxtAckFailure.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtAckFailure.SelectionStart = 0;
            this.TxtAckFailure.SelectStart = 0;
            this.TxtAckFailure.Size = new System.Drawing.Size(244, 23);
            this.TxtAckFailure.TabIndex = 26;
            this.TxtAckFailure.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtAckFailure.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtAckFailure.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtAckFailure.Watermark = null;
            // 
            // TxtDescription
            // 
            this.TxtDescription.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtDescription.Captain = "备    注";
            this.TxtDescription.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtDescription.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtDescription.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtDescription.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtDescription, 3);
            this.TxtDescription.ContentForeColor = System.Drawing.Color.Black;
            this.TxtDescription.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtDescription.EditMask = null;
            this.TxtDescription.Location = new System.Drawing.Point(11, 409);
            this.TxtDescription.Multiline = true;
            this.TxtDescription.Name = "TxtDescription";
            this.TxtDescription.PasswordChar = '\0';
            this.TxtDescription.ReadOnly = false;
            this.TxtDescription.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.TxtDescription.SelectionStart = 0;
            this.TxtDescription.SelectStart = 0;
            this.TxtDescription.Size = new System.Drawing.Size(744, 46);
            this.TxtDescription.TabIndex = 29;
            this.TxtDescription.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtDescription.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtDescription.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top;
            this.TxtDescription.Watermark = null;
            // 
            // TxtModel
            // 
            this.TxtModel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtModel.Captain = "设备型号";
            this.TxtModel.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtModel.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtModel.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtModel.CaptainWidth = 70F;
            this.TxtModel.ContentForeColor = System.Drawing.Color.Black;
            this.TxtModel.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtModel.EditMask = null;
            this.TxtModel.Location = new System.Drawing.Point(511, 61);
            this.TxtModel.Multiline = false;
            this.TxtModel.Name = "TxtModel";
            this.TxtModel.PasswordChar = '\0';
            this.TxtModel.ReadOnly = false;
            this.TxtModel.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtModel.SelectionStart = 0;
            this.TxtModel.SelectStart = 0;
            this.TxtModel.Size = new System.Drawing.Size(244, 23);
            this.TxtModel.TabIndex = 3;
            this.TxtModel.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtModel.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtModel.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtModel.Watermark = null;
            // 
            // TxtVendor
            // 
            this.TxtVendor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtVendor.Captain = "厂商名称";
            this.TxtVendor.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtVendor.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtVendor.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtVendor.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtVendor, 2);
            this.TxtVendor.ContentForeColor = System.Drawing.Color.Black;
            this.TxtVendor.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtVendor.EditMask = null;
            this.TxtVendor.Location = new System.Drawing.Point(11, 61);
            this.TxtVendor.Multiline = false;
            this.TxtVendor.Name = "TxtVendor";
            this.TxtVendor.PasswordChar = '\0';
            this.TxtVendor.ReadOnly = false;
            this.TxtVendor.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtVendor.SelectionStart = 0;
            this.TxtVendor.SelectStart = 0;
            this.TxtVendor.Size = new System.Drawing.Size(494, 23);
            this.TxtVendor.TabIndex = 2;
            this.TxtVendor.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtVendor.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtVendor.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtVendor.Watermark = null;
            // 
            // TxtSerialNo
            // 
            this.TxtSerialNo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtSerialNo.Captain = "出厂序列号";
            this.TxtSerialNo.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtSerialNo.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtSerialNo.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtSerialNo.CaptainWidth = 70F;
            this.TxtSerialNo.ContentForeColor = System.Drawing.Color.Black;
            this.TxtSerialNo.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtSerialNo.EditMask = null;
            this.TxtSerialNo.Location = new System.Drawing.Point(11, 90);
            this.TxtSerialNo.Multiline = false;
            this.TxtSerialNo.Name = "TxtSerialNo";
            this.TxtSerialNo.PasswordChar = '\0';
            this.TxtSerialNo.ReadOnly = false;
            this.TxtSerialNo.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtSerialNo.SelectionStart = 0;
            this.TxtSerialNo.SelectStart = 0;
            this.TxtSerialNo.Size = new System.Drawing.Size(244, 23);
            this.TxtSerialNo.TabIndex = 4;
            this.TxtSerialNo.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtSerialNo.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtSerialNo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtSerialNo.Watermark = null;
            // 
            // TxtLocation
            // 
            this.TxtLocation.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtLocation.Captain = "设备位置";
            this.TxtLocation.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtLocation.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLocation.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtLocation.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtLocation, 2);
            this.TxtLocation.ContentForeColor = System.Drawing.Color.Black;
            this.TxtLocation.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtLocation.EditMask = null;
            this.TxtLocation.Location = new System.Drawing.Point(261, 90);
            this.TxtLocation.Multiline = false;
            this.TxtLocation.Name = "TxtLocation";
            this.TxtLocation.PasswordChar = '\0';
            this.TxtLocation.ReadOnly = false;
            this.TxtLocation.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtLocation.SelectionStart = 0;
            this.TxtLocation.SelectStart = 0;
            this.TxtLocation.Size = new System.Drawing.Size(494, 23);
            this.TxtLocation.TabIndex = 5;
            this.TxtLocation.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtLocation.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLocation.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtLocation.Watermark = null;
            // 
            // NumIpPort
            // 
            this.NumIpPort.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumIpPort.Captain = "IP端口";
            this.NumIpPort.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumIpPort.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumIpPort.CaptainWidth = 70F;
            this.NumIpPort.Location = new System.Drawing.Point(261, 206);
            this.NumIpPort.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumIpPort.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumIpPort.Name = "NumIpPort";
            this.NumIpPort.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumIpPort.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumIpPort.ReadOnly = false;
            this.NumIpPort.Size = new System.Drawing.Size(244, 23);
            this.NumIpPort.TabIndex = 14;
            this.NumIpPort.ValueIsDbNull = false;
            // 
            // TxtIpHost
            // 
            this.TxtIpHost.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtIpHost.Captain = "IP地址";
            this.TxtIpHost.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtIpHost.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtIpHost.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtIpHost.CaptainWidth = 70F;
            this.TxtIpHost.ContentForeColor = System.Drawing.Color.Black;
            this.TxtIpHost.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtIpHost.EditMask = null;
            this.TxtIpHost.Location = new System.Drawing.Point(11, 206);
            this.TxtIpHost.Multiline = false;
            this.TxtIpHost.Name = "TxtIpHost";
            this.TxtIpHost.PasswordChar = '\0';
            this.TxtIpHost.ReadOnly = false;
            this.TxtIpHost.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtIpHost.SelectionStart = 0;
            this.TxtIpHost.SelectStart = 0;
            this.TxtIpHost.Size = new System.Drawing.Size(244, 23);
            this.TxtIpHost.TabIndex = 13;
            this.TxtIpHost.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtIpHost.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtIpHost.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtIpHost.Watermark = null;
            // 
            // NumKeepAliveSec
            // 
            this.NumKeepAliveSec.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumKeepAliveSec.Captain = "心跳间隔";
            this.NumKeepAliveSec.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumKeepAliveSec.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumKeepAliveSec.CaptainWidth = 70F;
            this.NumKeepAliveSec.Location = new System.Drawing.Point(511, 206);
            this.NumKeepAliveSec.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumKeepAliveSec.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumKeepAliveSec.Name = "NumKeepAliveSec";
            this.NumKeepAliveSec.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumKeepAliveSec.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumKeepAliveSec.ReadOnly = false;
            this.NumKeepAliveSec.Size = new System.Drawing.Size(244, 23);
            this.NumKeepAliveSec.TabIndex = 15;
            this.NumKeepAliveSec.ValueIsDbNull = false;
            // 
            // TxtDbConnStr
            // 
            this.TxtDbConnStr.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtDbConnStr.Captain = "数据库连接字符串";
            this.TxtDbConnStr.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtDbConnStr.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtDbConnStr.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtDbConnStr.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtDbConnStr, 2);
            this.TxtDbConnStr.ContentForeColor = System.Drawing.Color.Black;
            this.TxtDbConnStr.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtDbConnStr.EditMask = null;
            this.TxtDbConnStr.Location = new System.Drawing.Point(261, 264);
            this.TxtDbConnStr.Multiline = false;
            this.TxtDbConnStr.Name = "TxtDbConnStr";
            this.TxtDbConnStr.PasswordChar = '\0';
            this.TxtDbConnStr.ReadOnly = false;
            this.TxtDbConnStr.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtDbConnStr.SelectionStart = 0;
            this.TxtDbConnStr.SelectStart = 0;
            this.TxtDbConnStr.Size = new System.Drawing.Size(494, 23);
            this.TxtDbConnStr.TabIndex = 18;
            this.TxtDbConnStr.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtDbConnStr.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtDbConnStr.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtDbConnStr.Watermark = null;
            // 
            // TxtApiAuthToken
            // 
            this.TxtApiAuthToken.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtApiAuthToken.Captain = "API令牌";
            this.TxtApiAuthToken.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtApiAuthToken.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtApiAuthToken.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtApiAuthToken.CaptainWidth = 70F;
            this.TxtApiAuthToken.ContentForeColor = System.Drawing.Color.Black;
            this.TxtApiAuthToken.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtApiAuthToken.EditMask = null;
            this.TxtApiAuthToken.Location = new System.Drawing.Point(511, 293);
            this.TxtApiAuthToken.Multiline = false;
            this.TxtApiAuthToken.Name = "TxtApiAuthToken";
            this.TxtApiAuthToken.PasswordChar = '\0';
            this.TxtApiAuthToken.ReadOnly = false;
            this.TxtApiAuthToken.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtApiAuthToken.SelectionStart = 0;
            this.TxtApiAuthToken.SelectStart = 0;
            this.TxtApiAuthToken.Size = new System.Drawing.Size(244, 23);
            this.TxtApiAuthToken.TabIndex = 20;
            this.TxtApiAuthToken.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtApiAuthToken.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtApiAuthToken.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtApiAuthToken.Watermark = null;
            // 
            // SingleComPort1
            // 
            this.SingleComPort1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleComPort1.Captain = "串口号";
            this.SingleComPort1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleComPort1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleComPort1.CaptainWidth = 55F;
            this.SingleComPort1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleComPort1.ItemHeight = 18;
            this.SingleComPort1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleComPort1.Location = new System.Drawing.Point(11, 148);
            this.SingleComPort1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.SingleComPort1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleComPort1.Name = "SingleComPort1";
            this.SingleComPort1.ReadOnly = false;
            this.SingleComPort1.Size = new System.Drawing.Size(244, 23);
            this.SingleComPort1.TabIndex = 8;
            this.SingleComPort1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SingleBaudRate1
            // 
            this.SingleBaudRate1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleBaudRate1.Captain = "波特率";
            this.SingleBaudRate1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleBaudRate1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleBaudRate1.CaptainWidth = 55F;
            this.SingleBaudRate1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleBaudRate1.ItemHeight = 18;
            this.SingleBaudRate1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleBaudRate1.Location = new System.Drawing.Point(261, 148);
            this.SingleBaudRate1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.SingleBaudRate1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleBaudRate1.Name = "SingleBaudRate1";
            this.SingleBaudRate1.ReadOnly = false;
            this.SingleBaudRate1.Size = new System.Drawing.Size(244, 23);
            this.SingleBaudRate1.TabIndex = 9;
            this.SingleBaudRate1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SingleConnType1
            // 
            this.SingleConnType1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleConnType1.Captain = "通讯类型";
            this.SingleConnType1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleConnType1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleConnType1.CaptainWidth = 69F;
            this.SingleConnType1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleConnType1.ItemHeight = 18;
            this.SingleConnType1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleConnType1.Location = new System.Drawing.Point(11, 119);
            this.SingleConnType1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.SingleConnType1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleConnType1.Name = "SingleConnType1";
            this.SingleConnType1.ReadOnly = false;
            this.SingleConnType1.Size = new System.Drawing.Size(244, 23);
            this.SingleConnType1.TabIndex = 6;
            this.SingleConnType1.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleConnType1.SelectedValueChanged += new System.EventHandler(this.SingleConnType1_SelectedValueChanged);
            // 
            // singleDbType1
            // 
            this.singleDbType1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.singleDbType1.Captain = "数据库类型";
            this.singleDbType1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.singleDbType1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.singleDbType1.CaptainWidth = 83F;
            this.singleDbType1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.singleDbType1.ItemHeight = 18;
            this.singleDbType1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.singleDbType1.Location = new System.Drawing.Point(11, 264);
            this.singleDbType1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.singleDbType1.MinimumSize = new System.Drawing.Size(0, 20);
            this.singleDbType1.Name = "singleDbType1";
            this.singleDbType1.ReadOnly = false;
            this.singleDbType1.Size = new System.Drawing.Size(244, 23);
            this.singleDbType1.TabIndex = 17;
            this.singleDbType1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SingleParity1
            // 
            this.SingleParity1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleParity1.Captain = "校验位";
            this.SingleParity1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleParity1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleParity1.CaptainWidth = 55F;
            this.SingleParity1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleParity1.ItemHeight = 18;
            this.SingleParity1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleParity1.Location = new System.Drawing.Point(511, 148);
            this.SingleParity1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.SingleParity1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleParity1.Name = "SingleParity1";
            this.SingleParity1.ReadOnly = false;
            this.SingleParity1.Size = new System.Drawing.Size(244, 23);
            this.SingleParity1.TabIndex = 10;
            this.SingleParity1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SingleDataBits1
            // 
            this.SingleDataBits1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleDataBits1.Captain = "数据位";
            this.SingleDataBits1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleDataBits1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleDataBits1.CaptainWidth = 55F;
            this.SingleDataBits1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleDataBits1.ItemHeight = 18;
            this.SingleDataBits1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleDataBits1.Location = new System.Drawing.Point(11, 177);
            this.SingleDataBits1.MaximumSize = new System.Drawing.Size(9999, 23);
            this.SingleDataBits1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleDataBits1.Name = "SingleDataBits1";
            this.SingleDataBits1.ReadOnly = false;
            this.SingleDataBits1.Size = new System.Drawing.Size(244, 23);
            this.SingleDataBits1.TabIndex = 11;
            this.SingleDataBits1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SingleStopBits1
            // 
            this.SingleStopBits1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.SingleStopBits1.Captain = "停止位";
            this.SingleStopBits1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SingleStopBits1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.SingleStopBits1.CaptainWidth = 55F;
            this.SingleStopBits1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.SingleStopBits1.ItemHeight = 18;
            this.SingleStopBits1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.SingleStopBits1.Location = new System.Drawing.Point(261, 177);
            this.SingleStopBits1.MaximumSize = new System.Drawing.Size(9999, 23);
            this.SingleStopBits1.MinimumSize = new System.Drawing.Size(0, 20);
            this.SingleStopBits1.Name = "SingleStopBits1";
            this.SingleStopBits1.ReadOnly = false;
            this.SingleStopBits1.Size = new System.Drawing.Size(244, 23);
            this.SingleStopBits1.TabIndex = 12;
            this.SingleStopBits1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // TxtFirmwareVersion
            // 
            this.TxtFirmwareVersion.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtFirmwareVersion.Captain = "固件版本";
            this.TxtFirmwareVersion.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtFirmwareVersion.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFirmwareVersion.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtFirmwareVersion.CaptainWidth = 70F;
            this.TxtFirmwareVersion.ContentForeColor = System.Drawing.Color.Black;
            this.TxtFirmwareVersion.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtFirmwareVersion.EditMask = null;
            this.TxtFirmwareVersion.Location = new System.Drawing.Point(511, 380);
            this.TxtFirmwareVersion.Multiline = false;
            this.TxtFirmwareVersion.Name = "TxtFirmwareVersion";
            this.TxtFirmwareVersion.PasswordChar = '\0';
            this.TxtFirmwareVersion.ReadOnly = false;
            this.TxtFirmwareVersion.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtFirmwareVersion.SelectionStart = 0;
            this.TxtFirmwareVersion.SelectStart = 0;
            this.TxtFirmwareVersion.Size = new System.Drawing.Size(244, 23);
            this.TxtFirmwareVersion.TabIndex = 28;
            this.TxtFirmwareVersion.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtFirmwareVersion.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtFirmwareVersion.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtFirmwareVersion.Watermark = null;
            // 
            // singleInstrumentDriverClass1
            // 
            this.singleInstrumentDriverClass1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.singleInstrumentDriverClass1.Captain = "驱动类型";
            this.singleInstrumentDriverClass1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.singleInstrumentDriverClass1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.singleInstrumentDriverClass1.CaptainWidth = 69F;
            this.TableLayoutPanel1.SetColumnSpan(this.singleInstrumentDriverClass1, 2);
            this.singleInstrumentDriverClass1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.singleInstrumentDriverClass1.ItemHeight = 18;
            this.singleInstrumentDriverClass1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.singleInstrumentDriverClass1.Location = new System.Drawing.Point(11, 380);
            this.singleInstrumentDriverClass1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.singleInstrumentDriverClass1.MinimumSize = new System.Drawing.Size(0, 20);
            this.singleInstrumentDriverClass1.Name = "singleInstrumentDriverClass1";
            this.singleInstrumentDriverClass1.ReadOnly = false;
            this.singleInstrumentDriverClass1.Size = new System.Drawing.Size(494, 23);
            this.singleInstrumentDriverClass1.TabIndex = 27;
            this.singleInstrumentDriverClass1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // Panel1
            // 
            this.Panel1.Controls.Add(this.BtnSave);
            this.Panel1.Controls.Add(this.BtnCancel);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel1.Location = new System.Drawing.Point(0, 461);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(767, 38);
            this.Panel1.TabIndex = 9;
            // 
            // BtnSave
            // 
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(371, 0);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.BtnCancel.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnCancel.Location = new System.Drawing.Point(447, 0);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(70, 35);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // singleMsgFormatType1
            // 
            this.singleMsgFormatType1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.singleMsgFormatType1.Captain = "保存消息格式类型";
            this.singleMsgFormatType1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.singleMsgFormatType1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.singleMsgFormatType1.CaptainWidth = 125F;
            this.singleMsgFormatType1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.singleMsgFormatType1.ItemHeight = 18;
            this.singleMsgFormatType1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.singleMsgFormatType1.Location = new System.Drawing.Point(261, 119);
            this.singleMsgFormatType1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.singleMsgFormatType1.MinimumSize = new System.Drawing.Size(0, 20);
            this.singleMsgFormatType1.Name = "singleMsgFormatType1";
            this.singleMsgFormatType1.ReadOnly = false;
            this.singleMsgFormatType1.Size = new System.Drawing.Size(244, 23);
            this.singleMsgFormatType1.TabIndex = 7;
            this.singleMsgFormatType1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // DictLabInstrument2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(767, 499);
            this.Controls.Add(this.TableLayoutPanel1);
            this.Controls.Add(this.Panel1);
            this.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Name = "DictLabInstrument2";
            this.Text = "仪器信息字典";
            this.Load += new System.EventHandler(this.DictLabInstrument2_Load);
            this.TableLayoutPanel1.ResumeLayout(false);
            this.Panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.TableLayoutPanel TableLayoutPanel1;
        internal CustomControl.MyTextBox TxtInstrCode;
        internal CustomControl.MyTextBox TxtInstrName;
        internal CustomControl.MyTextBox TxtVendor;
        internal CustomControl.MyTextBox TxtModel;
        internal CustomControl.MyTextBox TxtSerialNo;
        internal CustomControl.MyTextBox TxtLocation;
        internal ZTLisControl.SingleConnType SingleConnType1;
        internal ZTLisControl.SingleComPort SingleComPort1;
        internal ZTLisControl.SingleBaudRate SingleBaudRate1;
        internal ZTLisControl.SingleParity SingleParity1;
        internal ZTLisControl.SingleDataBits SingleDataBits1;
        internal ZTLisControl.SingleStopBits SingleStopBits1;
        internal CustomControl.MyTextBox TxtIpHost;
        internal CustomControl.MyNumericEdit NumIpPort;
        internal CustomControl.MyNumericEdit NumKeepAliveSec;
        internal CustomControl.MyTextBox TxtFilePath;
        internal CustomControl.MyTextBox TxtDbConnStr;
        internal CustomControl.MyTextBox TxtApiBaseUrl;
        internal CustomControl.MyTextBox TxtApiAuthToken;
        internal ZTLisControl.SingleProtocolType SingleProtocolType1;
        internal ZTLisControl.SingleMsgFormatType SingleMsgHeaderFormatType1;
        internal CustomControl.MyTextBox TxtMsgHeader;
        internal CustomControl.MyTextBox TxtMsgTail;
        internal CustomControl.MyTextBox TxtAckSuccess;
        internal CustomControl.MyTextBox TxtAckFailure;
        internal CustomControl.MyTextBox TxtFirmwareVersion;
        internal CustomControl.MyTextBox TxtDescription;
        internal System.Windows.Forms.Panel Panel1;
        internal CustomControl.MyButton BtnSave;
        internal CustomControl.MyButton BtnCancel;
        private ZTLisControl.SingleDbType singleDbType1;
        private ZTLisControl.SingleInstrumentDriverClass singleInstrumentDriverClass1;
        internal ZTLisControl.SingleMsgFormatType singleMsgFormatType1;
    }
}
