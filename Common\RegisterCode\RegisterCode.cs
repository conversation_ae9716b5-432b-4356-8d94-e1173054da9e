﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using NPOI.HPSF;

namespace Common.RegisterCode
{
    public class RegisterCodeCheck
    {
        /// <summary>
        /// 取本机机器码
        /// </summary>
        public static string GetMachineCode()
        {
            //CPU信息
            string cpuId = GetCpuID();
            //磁盘信息
            string diskId = GetDiskID();
            //网卡信息
            string MacAddress = GetMacAddress();

            string m1 = GetMD5(cpuId + typeof(string).ToString());
            string m2 = GetMD5(diskId + typeof(int).ToString());
            string m3 = GetMD5(MacAddress + typeof(double).ToString());

            string code1 = GetNum(m1, 8);
            string code2 = GetNum(m2, 8);
            string code3 = GetNum(m3, 8);

            return code1 + code2 + code3;
        }



        /// <summary>
        /// 检查注册码
        /// </summary>
        /// <param name="registerCode"></param>
        /// <param name="overTime"></param>
        /// <returns></returns>
        public static bool CheckRegister(string registerCode, ref DateTime overTime, ref DateTime registerTime)
        {
            try
            {
                string plbKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDU7YwLpxdzGJIYFTDlhbzalE/r9dRFljxC0mmsmxWpEGBEBdv0nLDQcCMtXtgjnvx+mwHQixuqhhGU7Ml7i27rC8bIun6f2nrxIqTkuczztC9pTFB5WtpRRO8yFF1lWwxRT4kAwWReCn9Jt2trDhNPw6RoRtgsZxi3O/KQsep+QIDAQAB";
                var rsa = new RSAHelper();
                var finalCodeList = rsa.DecryptWithPublicKey(plbKey, registerCode).Split('&');
                if (finalCodeList.Length == 3)
                {
                    DateTime.TryParse(finalCodeList[1], out overTime);
                    DateTime.TryParse(finalCodeList[2], out registerTime);
                    var machineCode = GetMachineCode();
                    return machineCode != null && (finalCodeList[0] == machineCode);
                }
                else
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查注册码
        /// </summary>
        /// <param name="registerCode"></param>
        /// <param name="overTime"></param>
        /// <returns></returns>
        public static bool CheckRegister(string registerCode, string machineCode, ref DateTime overTime, ref DateTime registerTime)
        {
            try
            {
                string plbKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDU7YwLpxdzGJIYFTDlhbzalE/r9dRFljxC0mmsmxWpEGBEBdv0nLDQcCMtXtgjnvx+mwHQixuqhhGU7Ml7i27rC8bIun6f2nrxIqTkuczztC9pTFB5WtpRRO8yFF1lWwxRT4kAwWReCn9Jt2trDhNPw6RoRtgsZxi3O/KQsep+QIDAQAB";
                var rsa = new RSAHelper();
                var finalCodeList = rsa.DecryptWithPublicKey(plbKey, registerCode).Split('&');
                if (finalCodeList.Length == 3)
                {
                    DateTime.TryParse(finalCodeList[1], out overTime);
                    DateTime.TryParse(finalCodeList[2], out registerTime);
                    return machineCode != null && (finalCodeList[0] == machineCode);
                }
                else
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 取网卡Mac地址
        /// </summary>
        /// <returns></returns>
        private static string GetMacAddress()
        {
            try
            {
                string mac = "";
                ManagementClass mc = new ManagementClass("Win32_NetworkAdapterConfiguration");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc)
                {
                    if ((bool)mo["IPEnabled"] == true)
                    {
                        mac = mo["MacAddress"].ToString();
                        break;
                    }
                }
                moc = null;
                mc = null;
                return mac;
            }
            catch
            {
                return "unknow";
            }
            finally
            {
            }
        }

        /// <summary>
        /// 取CPU序列号
        /// </summary>
        /// <returns></returns>
        private static string GetCpuID()
        {
            try
            {
                string cpuInfo = "";//cpu序列号 
                ManagementClass mc = new ManagementClass("Win32_Processor");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc)
                {
                    cpuInfo = mo.Properties["ProcessorId"].Value.ToString();
                }
                moc = null;
                mc = null;
                return cpuInfo;
            }
            catch
            {
                return "unknow";
            }
            finally
            {
            }
        }

        /// <summary>
        /// 取硬盘序列号
        /// </summary>
        /// <returns></returns>
        private static string GetDiskID()
        {
            try
            {
                String HDid = "";
                ManagementClass mc = new ManagementClass("Win32_DiskDrive");
                ManagementObjectCollection moc = mc.GetInstances();
                foreach (ManagementObject mo in moc)
                {
                    if (mo.Properties["InterfaceType"].Value.ToString() == "USB") continue;
                    HDid = (string)mo.Properties["Model"].Value;
                }
                moc = null;
                mc = null;
                return HDid;
            }
            catch
            {
                return "unknow";
            }
            finally
            {
            }
        }


        /// <summary>
        /// 取MD5
        /// </summary>
        /// <param name="myString"></param>
        /// <returns></returns>
        private static string GetMD5(string myString)
        {
            MD5 md5 = new MD5CryptoServiceProvider();
            byte[] fromData = System.Text.Encoding.Unicode.GetBytes(myString);
            byte[] targetData = md5.ComputeHash(fromData);
            string byte2String = null;
            for (int i = 0; i < targetData.Length; i++)
            {
                byte2String += targetData[i].ToString("x");
            }
            return byte2String;
        }

        /// <summary>
        /// 取数字
        /// </summary>
        /// <param name="md5"></param>
        /// <param name="len"></param>
        /// <returns></returns>
        private static string GetNum(string md5, int len)
        {
            Regex regex = new Regex(@"\d");
            MatchCollection listMatch = regex.Matches(md5);
            string str = "";
            for (int i = 0; i < len; i++)
            {
                str += listMatch[i].Value;
            }
            while (str.Length < len)
            {
                //不足补0
                str += "0";
            }
            return str;
        }

        /// <summary>
        /// 字符串解密
        /// </summary>
        /// <param name="key">加密key</param>
        /// <param name="str">要解密的字符串</param>
        public static string ToDecryptString(string key, string str)
        {
            try
            {
                //将密钥字符串转换为字节序列
                var P_byte_key = Encoding.Unicode.GetBytes(key);
                //将加密后的字符串转换为字节序列
                var P_byte_data = Convert.FromBase64String(str);
                //创建内存流对象并写入数据,创建加密流对象
                CryptoStream cStream = new CryptoStream(new MemoryStream(P_byte_data), new DESCryptoServiceProvider().CreateDecryptor(P_byte_key, P_byte_key), CryptoStreamMode.Read);
                //创建字节序列对象
                var tempDate = new byte[200];
                //创建内存流对象
                MemoryStream mStream = new MemoryStream();
                //创建记数器
                int i = 0;
                //使用while循环得到解密数据
                while ((i = cStream.Read(tempDate, 0, tempDate.Length)) > 0)
                {
                    //将解密后的数据放入内存流
                    mStream.Write(tempDate, 0, i);
                }
                var res = Encoding.Unicode.GetString(mStream.ToArray());
                mStream.Dispose();
                cStream.Dispose();
                return res;
            }
            catch (CryptographicException ce)
            {
                throw new Exception(ce.Message);
            }
        }
    }
}
