using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictDoctor1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictDoctor _bllDictDoctor = new BllDictDoctor();

        public DictDoctor1()
        {
            InitializeComponent();
        }

        private void DictDoctor1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "DoctorCode", 120, "中", "", false);
            myGrid1.Init_Column("姓名", "DoctorName", 160, "左", "", false);
            myGrid1.Init_Column("HIS编码", "HisDoctorCode", 120, "左", "", false);
            myGrid1.Init_Column("拼音", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("科室", "DeptName", 120, "左", "", false);
            myGrid1.Init_Column("职称", "Title", 100, "左", "", false);
            myGrid1.Init_Column("执业证书号", "LicenseNo", 150, "左", "", false);
            myGrid1.Init_Column("联系方式", "Mobile", 120, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllDictDoctor.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["DoctorId"] };
            base.MyTable.Columns["DoctorId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictDoctor2 vform = new DictDoctor2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除医生：" + this.myGrid1.Columns["DoctorName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictDoctor.Delete(int.Parse(base.MyRow["DoctorId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("DoctorCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("DoctorName", new Common.MdlExcel(15, "", "姓名", true));
            colconfig.Add("HisDoctorCode", new Common.MdlExcel(12, "", "HIS编码", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "拼音", true));
            colconfig.Add("Title", new Common.MdlExcel(10, "", "职称", true));
            colconfig.Add("LicenseNo", new Common.MdlExcel(15, "", "执业证书号", true));
            colconfig.Add("Mobile", new Common.MdlExcel(12, "", "联系方式", true));
            Common.ExcelHelper.Export(base.MyTable, "医生字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictDoctor mdlDictDoctor = new MdlDictDoctor();
                    if (row["姓名"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "姓名为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictDoctor.DoctorCode = row["编码"] + "";
                    mdlDictDoctor.DoctorName = row["姓名"] + "";
                    mdlDictDoctor.PinYin = base.MyChs2Spell.GetPy(row["姓名"].ToString());
                    mdlDictDoctor.HisDoctorCode = row["HIS编码"] + "";
                    mdlDictDoctor.Title = row["职称"] + "";
                    mdlDictDoctor.LicenseNo = row["执业证书号"] + "";
                    mdlDictDoctor.Mobile = row["联系方式"] + "";
                    mdlDictDoctor.Status = true;
                    mdlDictDoctor.CreatedAt = DateTime.Now;
                    mdlDictDoctor.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictDoctor).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictDoctor, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["DoctorId"] = _bllDictDoctor.Add(mdlDictDoctor);
                    base.MyTable.Rows.Add(newRow);

                    UpdateData[i - 1] = "导入成功";
                    i = i + 1;
                }
                base.MyTable.AcceptChanges();
                this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
                MessageBox.Show("导入完成！成功：" + successCnt + "条，失败：" + failCnt + "条", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataEnable(bool Status)
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            Model.MdlDictDoctor mdlDictDoctor = _bllDictDoctor.GetModel(int.Parse(base.MyRow["DoctorId"].ToString()));
            mdlDictDoctor.Status = Status;
            mdlDictDoctor.UpdatedAt = DateTime.Now;
            _bllDictDoctor.Update(mdlDictDoctor);
            base.MyRow["Status"] = Status;
            base.MyRow["UpdatedAt"] = DateTime.Now;
            base.MyTable.AcceptChanges();
        }

        #endregion

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }
        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("DoctorCode+isnull(PinYin,'')+DoctorName", TxtFilter.Text.Trim());
        }
    }
}