using System;
using System.Data;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    /// <summary>
    /// 检验项目参考区间明细窗体
    /// </summary>
    public partial class DictLabItem3 : Common.BaseForm.BaseDict2
    {
        private BllDictLabItemRefRange _bllDictLabItemRefRange;
        private MdlDictLabItemRefRange _mdlDictLabItemRefRange;
        private MdlDictLabItem _labItem;

        public DictLabItem3(MdlDictLabItem labItem, bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            _labItem = labItem;
            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            // 添加事件处理，实现互斥逻辑
            TxtTextualRefValue.TextChanged += TxtTextualRefValue_TextChanged;
            TxtLowerLimit.TextChanged += TxtLowerLimit_TextChanged;
            TxtUpperLimit.TextChanged += TxtUpperLimit_TextChanged;
        }

        private void DictLabItem3_Load(object sender, EventArgs e)
        {
            try
            {
                _bllDictLabItemRefRange = new BllDictLabItemRefRange();
                FormInit();
                if (base.Insert == true)
                    this.DataClear();
                else
                    this.DataShow(base.MyRow);
            }
            catch (Exception ex)
            {
                MessageBox.Show("窗体加载失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new System.Drawing.Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new System.Drawing.Point(BtnSave.Right + 3, 1);

            // 初始化控件
            ComboMethod.Init();
            ComboSex.Init();
            ComboAgeUnit.Init();

            // 设置数值控件格式
            NumAgeLower.CustomFormat = "0.##";
            NumAgeUpper.CustomFormat = "0.##";

            // 设置默认值
            DateEffectiveFrom.Value = DateTime.Now;
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        private void DataClear()
        {
            ComboMethod.SelectedIndex = -1;
            ComboSex.SelectedIndex = 0; // 默认选择第一项
            NumAgeLower.Value = null;
            NumAgeUpper.Value = null;
            ComboAgeUnit.SelectedIndex = 0; // 默认选择年
            TxtLowerLimit.Text = "";
            TxtUpperLimit.Text = "";
            TxtTextualRefValue.Text = "";
            TxtAlertLow.Text = "";
            TxtAlertHigh.Text = "";
            TxtCriticalLow.Text = "";
            TxtCriticalHigh.Text = "";
            comboUnit1.SelectedIndex = -1;
            DateEffectiveFrom.Value = DateTime.Now;
            DateEffectiveTo.Value = null;
            ComboMethod.Focus();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;

            // 设置下拉框值
            if (row["MethodId"] != DBNull.Value)
                ComboMethod.SelectedValue = row["MethodId"];
            if (row["Sex"] != DBNull.Value)
                ComboSex.SelectedValue = row["Sex"];

            NumAgeLower.Value = row["AgeLower"];
            NumAgeUpper.Value = row["AgeUpper"];

            if (row["AgeUnit"] != DBNull.Value)
                ComboAgeUnit.SelectedValue = row["AgeUnit"];

            TxtLowerLimit.Text = row["LowerLimit"] + "";
            TxtUpperLimit.Text = row["UpperLimit"] + "";
            TxtTextualRefValue.Text = row["TextualRefValue"] + "";
            TxtAlertLow.Text = row["AlertLow"] + "";
            TxtAlertHigh.Text = row["AlertHigh"] + "";
            TxtCriticalLow.Text = row["CriticalLow"] + "";
            TxtCriticalHigh.Text = row["CriticalHigh"] + "";
            comboUnit1.Text = row["Unit"] + "";

            if (row["EffectiveFrom"] != DBNull.Value)
                DateEffectiveFrom.Value = row["EffectiveFrom"];
            if (row["EffectiveTo"] != DBNull.Value)
                DateEffectiveTo.Value = row["EffectiveTo"];
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }

        /// <summary>
        /// 数据验证
        /// </summary>
        private bool DataCheck()
        {
            if (CustomControl.Func.NotAllowEmpty(NumAgeLower)) return false;
            if (CustomControl.Func.NotAllowEmpty(NumAgeUpper)) return false;
            if (CustomControl.Func.NotAllowEmpty(ComboAgeUnit)) return false;

            // 验证年龄范围
            if (NumAgeLower.Value != null && NumAgeUpper.Value != null)
            {
                if (Convert.ToDecimal(NumAgeLower.Value) > Convert.ToDecimal(NumAgeUpper.Value))
                {
                    MessageBox.Show("年龄下限不能大于年龄上限！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    NumAgeLower.Focus();
                    return false;
                }
            }

            // 验证生效时间
            if (DateEffectiveTo.Value != null && Convert.ToDateTime(DateEffectiveTo.Value) <= Convert.ToDateTime(DateEffectiveFrom.Value))
            {
                MessageBox.Show("失效时间必须大于生效时间！", "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                DateEffectiveTo.Focus();
                return false;
            }

            // 验证TextualRefValue与LowerLimit/UpperLimit的互斥关系
            bool hasTextualRefValue = !string.IsNullOrWhiteSpace(TxtTextualRefValue.Text);
            bool hasLowerLimit = !string.IsNullOrWhiteSpace(TxtLowerLimit.Text);
            bool hasUpperLimit = !string.IsNullOrWhiteSpace(TxtUpperLimit.Text);
            bool hasNumericLimits = hasLowerLimit || hasUpperLimit;

            if (hasTextualRefValue && hasNumericLimits)
            {
                MessageBox.Show("定性项目不能同时设置文本参考值和数值参考区间！\n" +
                              "请选择其中一种：\n" +
                              "- 定量项目：设置参考下限/上限，清空文本参考值\n" +
                              "- 定性项目：设置文本参考值，清空参考下限/上限",
                              "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                TxtTextualRefValue.Focus();
                return false;
            }

            if (!hasTextualRefValue && !hasNumericLimits)
            {
                MessageBox.Show("必须设置参考值！\n" +
                              "请选择其中一种：\n" +
                              "- 定量项目：设置参考下限/上限\n" +
                              "- 定性项目：设置文本参考值",
                              "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                TxtLowerLimit.Focus();
                return false;
            }
            return true;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictLabItemRefRange = new MdlDictLabItemRefRange();
            _mdlDictLabItemRefRange.LabItemId = _labItem.LabItemId;
            _mdlDictLabItemRefRange.MethodId = ComboMethod.SelectedValue == null ? null : (int?)ComboMethod.SelectedValue;
            _mdlDictLabItemRefRange.Sex = ComboSex.SelectedValue?.ToString();
            _mdlDictLabItemRefRange.AgeLower = NumAgeLower.Value == null ? null : (decimal?)NumAgeLower.Value;
            _mdlDictLabItemRefRange.AgeUpper = NumAgeUpper.Value == null ? null : (decimal?)NumAgeUpper.Value;
            _mdlDictLabItemRefRange.AgeUnit = ComboAgeUnit.SelectedValue?.ToString();
            _mdlDictLabItemRefRange.LowerLimit = TxtLowerLimit.Text.Trim();
            _mdlDictLabItemRefRange.UpperLimit = TxtUpperLimit.Text.Trim();
            _mdlDictLabItemRefRange.TextualRefValue = TxtTextualRefValue.Text.Trim();
            _mdlDictLabItemRefRange.AlertLow = TxtAlertLow.Text.Trim();
            _mdlDictLabItemRefRange.AlertHigh = TxtAlertHigh.Text.Trim();
            _mdlDictLabItemRefRange.CriticalLow = TxtCriticalLow.Text.Trim();
            _mdlDictLabItemRefRange.CriticalHigh = TxtCriticalHigh.Text.Trim();
            _mdlDictLabItemRefRange.Unit = comboUnit1.Text.Trim();
            _mdlDictLabItemRefRange.Status = true;
            _mdlDictLabItemRefRange.EffectiveFrom = Convert.ToDateTime(DateEffectiveFrom.Value);
            _mdlDictLabItemRefRange.EffectiveTo = DateEffectiveTo.Value == null ? null : (DateTime?)Convert.ToDateTime(DateEffectiveTo.Value);
            _mdlDictLabItemRefRange.CreatedAt = DateTime.Now;
            _mdlDictLabItemRefRange.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabItemRefRange, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["RefRangeId"] = _bllDictLabItemRefRange.Add(_mdlDictLabItemRefRange);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                ComboMethod.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictLabItemRefRange = _bllDictLabItemRefRange.GetModel(long.Parse(base.MyRow["RefRangeId"].ToString()));
            _mdlDictLabItemRefRange.LabItemId = _labItem.LabItemId;
            _mdlDictLabItemRefRange.MethodId = ComboMethod.SelectedValue == null ? null : (int?)ComboMethod.SelectedValue;
            _mdlDictLabItemRefRange.Sex = ComboSex.SelectedValue?.ToString();
            _mdlDictLabItemRefRange.AgeLower = NumAgeLower.Value == null ? null : (decimal?)NumAgeLower.Value;
            _mdlDictLabItemRefRange.AgeUpper = NumAgeUpper.Value == null ? null : (decimal?)NumAgeUpper.Value;
            _mdlDictLabItemRefRange.AgeUnit = ComboAgeUnit.SelectedValue?.ToString();
            _mdlDictLabItemRefRange.LowerLimit = TxtLowerLimit.Text.Trim();
            _mdlDictLabItemRefRange.UpperLimit = TxtUpperLimit.Text.Trim();
            _mdlDictLabItemRefRange.TextualRefValue = TxtTextualRefValue.Text.Trim();
            _mdlDictLabItemRefRange.AlertLow = TxtAlertLow.Text.Trim();
            _mdlDictLabItemRefRange.AlertHigh = TxtAlertHigh.Text.Trim();
            _mdlDictLabItemRefRange.CriticalLow = TxtCriticalLow.Text.Trim();
            _mdlDictLabItemRefRange.CriticalHigh = TxtCriticalHigh.Text.Trim();
            _mdlDictLabItemRefRange.Unit = comboUnit1.Text.Trim();
            _mdlDictLabItemRefRange.EffectiveFrom = Convert.ToDateTime(DateEffectiveFrom.Value);
            _mdlDictLabItemRefRange.EffectiveTo = DateEffectiveTo.Value == null ? null : (DateTime?)Convert.ToDateTime(DateEffectiveTo.Value);
            _mdlDictLabItemRefRange.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabItemRefRange, base.MyRow);

            //数据保存
            try
            {
                _bllDictLabItemRefRange.Update(_mdlDictLabItemRefRange);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #region 互斥逻辑事件处理

        /// <summary>
        /// 文本参考值变化事件 - 如果输入文本参考值，则清空数值参考区间
        /// </summary>
        private void TxtTextualRefValue_TextChanged(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(TxtTextualRefValue.Text))
            {
                // 当输入文本参考值时，自动清空数值参考区间
                if (!string.IsNullOrWhiteSpace(TxtLowerLimit.Text) || !string.IsNullOrWhiteSpace(TxtUpperLimit.Text))
                {
                    TxtLowerLimit.Text = "";
                    TxtUpperLimit.Text = "";
                }
            }
        }

        /// <summary>
        /// 参考下限变化事件 - 如果输入数值参考区间，则清空文本参考值
        /// </summary>
        private void TxtLowerLimit_TextChanged(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(TxtLowerLimit.Text))
            {
                // 当输入数值参考区间时，自动清空文本参考值
                if (!string.IsNullOrWhiteSpace(TxtTextualRefValue.Text))
                {
                    TxtTextualRefValue.Text = "";
                }
            }
        }

        /// <summary>
        /// 参考上限变化事件 - 如果输入数值参考区间，则清空文本参考值
        /// </summary>
        private void TxtUpperLimit_TextChanged(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(TxtUpperLimit.Text))
            {
                // 当输入数值参考区间时，自动清空文本参考值
                if (!string.IsNullOrWhiteSpace(TxtTextualRefValue.Text))
                {
                    TxtTextualRefValue.Text = "";
                }
            }
        }

        #endregion
    }
}