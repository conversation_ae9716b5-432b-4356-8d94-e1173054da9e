﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Common.BaseForm;
using ZTLisParsers.Parsers;
using ZTLisEnum;
using BLL;
using Model;

namespace ZTLisParsers
{
    public partial class MainParser : BaseFather
    {
        private ParserManager _parserManager;
        private Timer _parseTimer;
        private bool _isRunning = false;
        private BllLabInstrRawMsg _bllLabInstrRawMsg;

        public MainParser()
        {
            InitializeComponent();
            InitializeParser();
        }

        private void InitializeParser()
        {
            try
            {
                // 初始化业务逻辑层
                _bllLabInstrRawMsg = new BllLabInstrRawMsg();

                // 初始化解析器管理器
                _parserManager = new ParserManager();

                // 注册解析器
                RegisterParsers();

                // 初始化定时器
                _parseTimer = new Timer();
                _parseTimer.Interval = 5000; // 5秒检查一次
                _parseTimer.Tick += ParseTimer_Tick;

                // 事件绑定在Designer中完成

                AddLog("系统", "解析器主程序初始化完成");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"初始化失败：{ex.Message}");
            }
        }

        private void RegisterParsers()
        {
            try
            {
                ParserFactory.RegisterParser<SysmexCA6000Parser>(ProtocolType.自定义);
                ParserFactory.RegisterParser<Hl7Parser>(ProtocolType.HL7);

                var supportedProtocols = ParserFactory.GetSupportedProtocols();
                AddLog("系统", $"已注册 {supportedProtocols.Count} 个解析器");

                foreach (var protocol in supportedProtocols)
                {
                    AddLog("系统", $"支持协议：{protocol}");
                }
            }
            catch (Exception ex)
            {
                AddLog("系统", $"注册解析器失败：{ex.Message}");
            }
        }

        private async void ParseTimer_Tick(object sender, EventArgs e)
        {
            if (!_isRunning) return;

            try
            {
                // 批量处理未解析的报文
                int processedCount = await _parserManager.ParsePendingMessagesAsync(50);

                if (processedCount > 0)
                {
                    AddLog("解析器", $"本次处理了 {processedCount} 条报文");
                }
            }
            catch (Exception ex)
            {
                AddLog("解析器", $"解析过程异常：{ex.Message}");
            }
        }

        private void BtnStart_Click(object sender, EventArgs e)
        {
            StartParser();
        }

        private void BtnStop_Click(object sender, EventArgs e)
        {
            StopParser();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            ClearLog();
        }

        private void StartParser()
        {
            try
            {
                if (_isRunning)
                {
                    AddLog("系统", "解析器已在运行中");
                    return;
                }

                _isRunning = true;
                _parseTimer.Start();

                BtnStart.Enabled = false;
                BtnStop.Enabled = true;

                AddLog("系统", "解析器已启动");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"启动解析器失败：{ex.Message}");
                _isRunning = false;
                BtnStart.Enabled = true;
                BtnStop.Enabled = false;
            }
        }

        private void StopParser()
        {
            try
            {
                if (!_isRunning)
                {
                    AddLog("系统", "解析器未在运行");
                    return;
                }

                _isRunning = false;
                _parseTimer.Stop();

                BtnStart.Enabled = true;
                BtnStop.Enabled = false;

                AddLog("系统", "解析器已停止");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"停止解析器失败：{ex.Message}");
            }
        }

        private void ClearLog()
        {
            try
            {
                ListMessages.Items.Clear();
                AddLog("系统", "日志已清空");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"清空日志失败：{ex.Message}");
            }
        }

        private void AddLog(string category, string message)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action<string, string>(AddLog), category, message);
                    return;
                }

                string logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{category}] {message}";
                ListMessages.Items.Add(logMessage);

                // 保持最新的1000条记录
                if (ListMessages.Items.Count > 1000)
                {
                    ListMessages.Items.RemoveAt(0);
                }

                // 自动滚动到最新消息
                ListMessages.TopIndex = ListMessages.Items.Count - 1;
            }
            catch (Exception ex)
            {
                // 避免日志记录本身出错导致的循环异常
                System.Diagnostics.Debug.WriteLine($"添加日志失败：{ex.Message}");
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                if (_isRunning)
                {
                    StopParser();
                }

                _parseTimer?.Dispose();
                AddLog("系统", "解析器主程序正在关闭");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗体异常：{ex.Message}");
            }

            base.OnFormClosing(e);
        }

        private void MainParser_Load(object sender, EventArgs e)
        {
            try
            {
                // 显示统计信息
                ShowStatistics();
            }
            catch (Exception ex)
            {
                AddLog("系统", $"加载统计信息失败：{ex.Message}");
            }
        }

        private void ShowStatistics()
        {
            try
            {
                // 获取待解析报文数量
                var pendingCount = _bllLabInstrRawMsg.GetRecordCount("ParsedFlag = 0");
                AddLog("统计", $"当前有 {pendingCount} 条待解析报文");

                // 获取今日解析数量
                var todayCount = _bllLabInstrRawMsg.GetRecordCount($"ParsedFlag = 1 AND CONVERT(date, AcquiredAt) = '{DateTime.Today:yyyy-MM-dd}'");
                AddLog("统计", $"今日已解析 {todayCount} 条报文");

                // 显示支持的协议
                var protocols = ParserFactory.GetSupportedProtocols();
                AddLog("统计", $"支持 {protocols.Count} 种协议类型");
            }
            catch (Exception ex)
            {
                AddLog("统计", $"获取统计信息失败：{ex.Message}");
            }
        }
    }
}
