﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Command;
using Common.WinFormVar;

namespace Common.BaseForm
{
    public static class PublicFunc
    {
        public static void AddTabControl(Base objFrm, string captain, Image img)
        {
            if (Var.MainTab.Visible == false)
            {
                Var.MainTab.Visible = true;
            }
            objFrm.Text = captain;
            int pageindex;
            pageindex = ErgodicModiForm(objFrm);
            if (pageindex == -1)
            {
                objFrm.MdiParent = Var.MainForm;
                //声明一个选项卡对象
                C1DockingTabPage tabPage = new C1DockingTabPage();
                tabPage.Dock = DockStyle.Fill;
                //选项卡的名称
                tabPage.Name = objFrm.Name;
                //选项卡的文本
                tabPage.Text = captain;

                tabPage.Parent = Var.MainTab;

                //子窗体显示
                objFrm.TopLevel = false;

                objFrm.FormBorderStyle = FormBorderStyle.None;
                if (objFrm.CanDockFill == true)
                {
                    objFrm.Dock = DockStyle.Fill;
                }
                else
                {
                    objFrm.Location = new Point((tabPage.Width - objFrm.Width) / 2, (tabPage.Height - objFrm.Height) / 2 - 20);
                }
                //objFrm.Show();
                //将子窗体添加到选项卡中
                tabPage.Controls.Add(objFrm);
                objFrm.Show();
                tabPage.ImageIndex = 0;


                if (img != null)
                {
                    tabPage.Image = img;
                }

                //设置当前选项卡为新增选项卡
                Var.MainTab.TabPages.Add(tabPage);
                //选中当前窗体选项页
                Var.MainTab.SelectedTab = tabPage;
            }
            else
            {
                Var.MainTab.SelectedIndex = pageindex;
                Var.MainTab.SelectedTab.Name = objFrm.Name;
                Var.MainTab.SelectedTab.Text = objFrm.Text;
            }
            //在此加入按钮使用次数

        }

        private static int ErgodicModiForm(System.Windows.Forms.Form objFrm)
        {
            //遍历选项卡判断是否存在该子窗体
            int pageindex = -1;
            foreach (C1DockingTabPage con in Var.MainTab.TabPages)
            {
                pageindex = pageindex + 1;
                C1DockingTabPage tab = (C1DockingTabPage)con;
                foreach (object _ctrl in tab.Controls)
                {
                    if (((System.Windows.Forms.Form)_ctrl).Name == objFrm.Name & ((System.Windows.Forms.Form)_ctrl).Text == objFrm.Text & ((System.Windows.Forms.Form)_ctrl).Tag == objFrm.Tag)
                    {
                        //存在
                        return pageindex;
                    }
                }
            }
            return -1;
            //不存在
        }

        public static bool IsChildFormOpen<T>() where T : Form
        {
            foreach (Form f in Application.OpenForms)
            {
                if (f is T)
                {
                    return true;
                }
            }
            return false;
        }
    }
}
