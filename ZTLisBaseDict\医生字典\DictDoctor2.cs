using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisControl;

namespace ZTLisBaseDict
{
    public partial class DictDoctor2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictDoctor _bllDictDoctor = new BllDictDoctor();
        Model.MdlDictDoctor _mdlDictDoctor = new MdlDictDoctor();

        public DictDoctor2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtHisCode.GotFocus += new System.EventHandler(base.InputEn);
            TxtTitle.GotFocus += new System.EventHandler(base.InputCn);
            TxtLicenseNo.GotFocus += new System.EventHandler(base.InputEn);
            TxtMobile.GotFocus += new System.EventHandler(base.InputEn);
        }

        private void DictDoctor2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            ComboDept.Init();
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictDoctor.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtHisCode.Text = "";
            ComboDept.SelectedIndex = -1;
            TxtTitle.Text = "";
            TxtLicenseNo.Text = "";
            TxtMobile.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["DoctorCode"] + "";
            TxtName.Text = row["DoctorName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtHisCode.Text = row["HisDoctorCode"] + "";
            ComboDept.SelectedValue = row["DeptId"] + "";
            TxtTitle.Text = row["Title"] + "";
            TxtLicenseNo.Text = row["LicenseNo"] + "";
            TxtMobile.Text = row["Mobile"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写医生姓名！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写医生编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(ComboDept)) return false;

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("姓名重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("His编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["DoctorCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["DoctorName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("姓名重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
                if (TxtHisCode.Text.Trim() != "" && TxtHisCode.Text.Trim() != base.MyRow["HisDoctorCode"].ToString().Trim() && CheckHisCodeExists(TxtHisCode.Text))
                {
                    MessageBox.Show("His编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtHisCode.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictDoctor.GetRecordCount($"DoctorCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictDoctor.GetRecordCount($"DoctorName='{name}'");
            return cnt > 0;
        }
        private bool CheckHisCodeExists(string hisCode)
        {
            int cnt = _bllDictDoctor.GetRecordCount($"HisDoctorCode='{hisCode}'");
            return cnt > 0;
        }
        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictDoctor.DoctorCode = TxtCode.Text.Trim();
            _mdlDictDoctor.DoctorName = TxtName.Text.Trim();
            _mdlDictDoctor.PinYin = TxtJc.Text.Trim();
            _mdlDictDoctor.HisDoctorCode = TxtHisCode.Text.Trim();
            _mdlDictDoctor.DeptId = int.Parse(ComboDept.SelectedValue.ToString());
            _mdlDictDoctor.Title = TxtTitle.Text.Trim();
            _mdlDictDoctor.LicenseNo = TxtLicenseNo.Text.Trim();
            _mdlDictDoctor.Mobile = TxtMobile.Text.Trim();
            _mdlDictDoctor.Status = true;
            _mdlDictDoctor.CreatedAt = DateTime.Now;
            _mdlDictDoctor.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictDoctor, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["DoctorId"] = _bllDictDoctor.Add(_mdlDictDoctor);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictDoctor = _bllDictDoctor.GetModel(int.Parse(base.MyRow["DoctorId"].ToString()));
            _mdlDictDoctor.DoctorCode = TxtCode.Text.Trim();
            _mdlDictDoctor.DoctorName = TxtName.Text.Trim();
            _mdlDictDoctor.PinYin = TxtJc.Text.Trim();
            _mdlDictDoctor.HisDoctorCode = TxtHisCode.Text.Trim();
            _mdlDictDoctor.DeptId = int.Parse(ComboDept.SelectedValue.ToString());
            _mdlDictDoctor.Title = TxtTitle.Text.Trim();
            _mdlDictDoctor.LicenseNo = TxtLicenseNo.Text.Trim();
            _mdlDictDoctor.Mobile = TxtMobile.Text.Trim();
            _mdlDictDoctor.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictDoctor, base.MyRow);

            //数据保存
            try
            {
                _bllDictDoctor.Update(_mdlDictDoctor);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            if (TxtName.Text.Trim() != "")
            {
                TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text.Trim());
            }
        }
    }
}