﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabInstrItemMap.cs
*
* 功 能： N/A
* 类 名： DalLabInstrItemMap
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabInstrItemMap
	/// </summary>
	public partial class DalLabInstrItemMap : IDalLabInstrItemMap
	{
		public DalLabInstrItemMap()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long MapId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabInstrItemMap");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabInstrItemMap model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabInstrItemMap(");
			strSql.Append("InstrumentId,InstrItemCode,InstrItemName,LabItemId,LisItemName,ResultFormat,Decimals,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@InstrumentId,@InstrItemCode,@InstrItemName,@LabItemId,@LisItemName,@ResultFormat,@Decimals,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@InstrItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@InstrItemName", SqlDbType.NVarChar,100),
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@LisItemName", SqlDbType.NVarChar,100),
					new SqlParameter("@ResultFormat", SqlDbType.Char,3),
					new SqlParameter("@Decimals", SqlDbType.TinyInt,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.InstrumentId;
			parameters[1].Value = model.InstrItemCode;
			parameters[2].Value = model.InstrItemName;
			parameters[3].Value = model.LabItemId;
			parameters[4].Value = model.LisItemName;
			parameters[5].Value = model.ResultFormat;
			parameters[6].Value = Common.Tools.IsValueNull(model.Decimals);
			parameters[7].Value = model.CreatedAt;
			parameters[8].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabInstrItemMap model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabInstrItemMap set ");
			strSql.Append("InstrumentId=@InstrumentId,");
			strSql.Append("InstrItemCode=@InstrItemCode,");
			strSql.Append("InstrItemName=@InstrItemName,");
			strSql.Append("LabItemId=@LabItemId,");
			strSql.Append("LisItemName=@LisItemName,");
			strSql.Append("ResultFormat=@ResultFormat,");
			strSql.Append("Decimals=@Decimals,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@InstrItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@InstrItemName", SqlDbType.NVarChar,100),
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@LisItemName", SqlDbType.NVarChar,100),
					new SqlParameter("@ResultFormat", SqlDbType.Char,3),
					new SqlParameter("@Decimals", SqlDbType.TinyInt,1),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@MapId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.InstrumentId;
			parameters[1].Value = model.InstrItemCode;
			parameters[2].Value = model.InstrItemName;
			parameters[3].Value = model.LabItemId;
			parameters[4].Value = model.LisItemName;
			parameters[5].Value = model.ResultFormat;
			parameters[6].Value = Common.Tools.IsValueNull(model.Decimals);
			parameters[7].Value = model.UpdatedAt;
			parameters[8].Value = model.MapId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long MapId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrItemMap ");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string MapIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrItemMap ");
			strSql.Append(" where MapId in (" + MapIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrItemMap GetModel(long MapId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 MapId,InstrumentId,InstrItemCode,InstrItemName,LabItemId,LisItemName,ResultFormat,Decimals,CreatedAt,UpdatedAt from LabInstrItemMap ");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			Model.MdlLabInstrItemMap model = new Model.MdlLabInstrItemMap();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrItemMap DataRowToModel(DataRow row)
		{
			Model.MdlLabInstrItemMap model = new Model.MdlLabInstrItemMap();
			if (row != null)
			{
				if (row["MapId"] != null && row["MapId"].ToString() != "")
				{
					model.MapId = long.Parse(row["MapId"].ToString());
				}
				if (row["InstrumentId"] != null && row["InstrumentId"].ToString() != "")
				{
					model.InstrumentId = int.Parse(row["InstrumentId"].ToString());
				}
				if (row["InstrItemCode"] != null)
				{
					model.InstrItemCode = row["InstrItemCode"].ToString();
				}
				if (row["InstrItemName"] != null)
				{
					model.InstrItemName = row["InstrItemName"].ToString();
				}
				if (row["LabItemId"] != null && row["LabItemId"].ToString() != "")
				{
					model.LabItemId = int.Parse(row["LabItemId"].ToString());
				}
				if (row["LisItemName"] != null)
				{
					model.LisItemName = row["LisItemName"].ToString();
				}
				if (row["ResultFormat"] != null)
				{
					model.ResultFormat = row["ResultFormat"].ToString();
				}
				if (row["Decimals"] != null && row["Decimals"].ToString() != "")
				{
					model.Decimals = int.Parse(row["Decimals"].ToString());
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select LabInstrItemMap.*,DictLabItem.LabItemCode ");
			strSql.Append(" FROM LabInstrItemMap ");
			strSql.Append(" INNER JOIN DictLabItem ON LabInstrItemMap.LabItemId = DictLabItem.LabItemId ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" MapId,InstrumentId,InstrItemCode,InstrItemName,LabItemId,LisItemName,ResultFormat,Decimals ");
			strSql.Append(" FROM LabInstrItemMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabInstrItemMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.MapId desc");
			}
			strSql.Append(")AS Row, T.*  from LabInstrItemMap T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabInstrItemMap";
			parameters[1].Value = "MapId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

