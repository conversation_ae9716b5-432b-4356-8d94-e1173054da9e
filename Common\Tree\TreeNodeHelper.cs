﻿using System;
using System.Collections.Generic;
using System.Data.Linq.Mapping;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Common
{
    public class TreeNodeHelper
    {
        /// <summary>
        /// 修改Node各个属性，显示数量
        /// </summary>
        /// <param name="node">Tree节点</param>
        /// <param name="changeNum">子集变化数量</param>
        /// <param name="itemName">显示的名称</param>
        public static void UpdateNode(TreeNode node,int changeNum, string itemName="")
        {
            MdlTreeNode mdlTreeNode=new MdlTreeNode();
            mdlTreeNode.ItemName = ((Common.MdlTreeNode) node.Tag).ItemName;
            mdlTreeNode.SubItemCount = ((Common.MdlTreeNode)node.Tag).SubItemCount;
            mdlTreeNode.ItemCode = ((Common.MdlTreeNode)node.Tag).ItemCode;


            mdlTreeNode.SubItemCount = mdlTreeNode.SubItemCount + changeNum;
            //itemName为空代表无变化
            if (itemName!="") mdlTreeNode.ItemName = itemName;

            node.Tag = mdlTreeNode;
            node.Text = mdlTreeNode.ItemName + "(" + mdlTreeNode.SubItemCount + ")";
        }


        /// <summary>
        /// 修改Node各个属性，显示数量
        /// </summary>
        /// <param name="node">Tree节点</param>
        /// <param name="changeNum">子集变化数量</param>
        /// <param name="itemName">显示的名称</param>
        public static void UpdateNode<T>(TreeNode node, int changeNum, string itemName = "")
        {
            MdlTreeNode<T> mdlTreeNode = new MdlTreeNode<T>();
            mdlTreeNode.ItemName = ((Common.MdlTreeNode<T>)node.Tag).ItemName;
            mdlTreeNode.SubItemCount = ((Common.MdlTreeNode<T>)node.Tag).SubItemCount;
            mdlTreeNode.ItemCode = ((Common.MdlTreeNode<T>)node.Tag).ItemCode;
            mdlTreeNode.ExtInfo = ((Common.MdlTreeNode<T>)node.Tag).ExtInfo;


            mdlTreeNode.SubItemCount = mdlTreeNode.SubItemCount + changeNum;
            //itemName为空代表无变化
            if (itemName != "") mdlTreeNode.ItemName = itemName;

            node.Tag = mdlTreeNode;
            node.Text = mdlTreeNode.ItemName + "(" + mdlTreeNode.SubItemCount + ")";
        }

        /// <summary>
        /// 修改Node各个属性，不显示数量
        /// </summary>
        /// <param name="node">Tree节点</param>
        /// <param name="itemName">显示的名称</param>
        public static void UpdateNode(TreeNode node,  string itemName = "")
        {
            MdlTreeNode mdlTreeNode = new MdlTreeNode();
            mdlTreeNode.ItemName = ((Common.MdlTreeNode)node.Tag).ItemName;
            mdlTreeNode.SubItemCount = ((Common.MdlTreeNode)node.Tag).SubItemCount;
            mdlTreeNode.ItemCode = ((Common.MdlTreeNode)node.Tag).ItemCode;
            //itemName为空代表无变化
            if (itemName != "") mdlTreeNode.ItemName = itemName;

            node.Tag = mdlTreeNode;
            node.Text = mdlTreeNode.ItemName;
        }

        public static TreeNode FindNode(TreeNode node, string nodeName)
        {
            TreeNode[] treeNodeList = node.Nodes.Find(nodeName, true);
            if (treeNodeList.Length > 0)
                return treeNodeList[0];
            else
                return null;
        }
    }
}
