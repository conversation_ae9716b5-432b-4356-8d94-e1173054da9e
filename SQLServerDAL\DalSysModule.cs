﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysModule.cs
*
* 功 能： N/A
* 类 名： DalSysModule
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:34   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysModule
	/// </summary>
	public partial class DalSysModule:IDalSysModule
	{
		public DalSysModule()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string ScndCode,string ModuleCode)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysModule");
			strSql.Append(" where ScndCode=@ScndCode and ModuleCode=@ModuleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ScndCode", SqlDbType.Char,3),
					new SqlParameter("@ModuleCode", SqlDbType.Char,4)			};
			parameters[0].Value = ScndCode;
			parameters[1].Value = ModuleCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysModule model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysModule(");
			strSql.Append("ScndCode,ModuleCode,ModuleName,SortNo)");
			strSql.Append(" values (");
			strSql.Append("@ScndCode,@ModuleCode,@ModuleName,@SortNo)");
			SqlParameter[] parameters = {
					new SqlParameter("@ScndCode", SqlDbType.Char,3),
					new SqlParameter("@ModuleCode", SqlDbType.Char,4),
					new SqlParameter("@ModuleName", SqlDbType.VarChar,50),
					new SqlParameter("@SortNo", SqlDbType.Int,4)};
			parameters[0].Value = model.ScndCode;
			parameters[1].Value = model.ModuleCode;
			parameters[2].Value = model.ModuleName;
			parameters[3].Value = model.SortNo;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysModule model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysModule set ");
			strSql.Append("ModuleName=@ModuleName,");
			strSql.Append("SortNo=@SortNo");
			strSql.Append(" where ScndCode=@ScndCode and ModuleCode=@ModuleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ModuleName", SqlDbType.VarChar,50),
					new SqlParameter("@SortNo", SqlDbType.Int,4),
					new SqlParameter("@ScndCode", SqlDbType.Char,3),
					new SqlParameter("@ModuleCode", SqlDbType.Char,4)};
			parameters[0].Value = model.ModuleName;
			parameters[1].Value = model.SortNo;
			parameters[2].Value = model.ScndCode;
			parameters[3].Value = model.ModuleCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string ScndCode,string ModuleCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysModule ");
			strSql.Append(" where ScndCode=@ScndCode and ModuleCode=@ModuleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ScndCode", SqlDbType.Char,3),
					new SqlParameter("@ModuleCode", SqlDbType.Char,4)			};
			parameters[0].Value = ScndCode;
			parameters[1].Value = ModuleCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysModule GetModel(string ScndCode,string ModuleCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ScndCode,ModuleCode,ModuleName,SortNo from SysModule ");
			strSql.Append(" where ScndCode=@ScndCode and ModuleCode=@ModuleCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@ScndCode", SqlDbType.Char,3),
					new SqlParameter("@ModuleCode", SqlDbType.Char,4)			};
			parameters[0].Value = ScndCode;
			parameters[1].Value = ModuleCode;

			Model.MdlSysModule model=new Model.MdlSysModule();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysModule DataRowToModel(DataRow row)
		{
			Model.MdlSysModule model=new Model.MdlSysModule();
			if (row != null)
			{
				if(row["ScndCode"]!=null)
				{
					model.ScndCode=row["ScndCode"].ToString();
				}
				if(row["ModuleCode"]!=null)
				{
					model.ModuleCode=row["ModuleCode"].ToString();
				}
				if(row["ModuleName"]!=null)
				{
					model.ModuleName=row["ModuleName"].ToString();
				}
				if(row["SortNo"]!=null && row["SortNo"].ToString()!="")
				{
					model.SortNo=int.Parse(row["SortNo"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ScndCode,ModuleCode,ModuleName,SortNo ");
			strSql.Append(" FROM SysModule ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ScndCode,ModuleCode,ModuleName,SortNo ");
			strSql.Append(" FROM SysModule ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysModule ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ModuleCode desc");
			}
			strSql.Append(")AS Row, T.*  from SysModule T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysModule";
			parameters[1].Value = "ModuleCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod

        #region  ExtensionMethod
	    /// <summary>
	    /// 根据角色编码获取按钮
	    /// </summary>
	    /// <param name="roleCode"></param>
	    /// <returns></returns>
	    public DataSet GetModule(string roleCode, string scndCode)
	    {
	        StringBuilder strSql = new StringBuilder();
	        strSql.Append("select DISTINCT SysModule.* ");
	        strSql.Append("FROM   ");
	        strSql.Append("       SysModule , ");
	        strSql.Append("       SysRoleModule ");
	        strSql.Append("WHERE  ");
	        strSql.Append("       SysModule.ModuleCode = SysRoleModule.ModuleCode ");
	        strSql.Append("       AND RoleCode='" + roleCode + "' And ScndCode='" + scndCode + "' order by SysModule.SortNo");
	        return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
	    }

        public DataTable GetAllModule()
	    {
	        DataTable dt = new DataTable();
	        dt.Columns.Add("ID", Type.GetType("System.String"));
	        dt.Columns.Add("ParentID", Type.GetType("System.String"));
	        dt.Columns.Add("NodeName", Type.GetType("System.String"));
	        {
	            DataRow newRow = dt.NewRow();
	            newRow["ID"] = "1";
	            newRow["ParentID"] = "-1";
	            newRow["NodeName"] = "所有权限";
	            dt.Rows.Add(newRow);
	        }
	        DalSysMenu1 dalSysMenu1 = new DalSysMenu1();
	        foreach (DataRow row in dalSysMenu1.GetList(0, "", "SortNo").Tables[0].Rows)
	        {
	            DataRow newRow = dt.NewRow();
	            newRow["ID"] = row["FrsCode"];
	            newRow["ParentID"] = "1";
	            newRow["NodeName"] = row["FrsName"];
	            dt.Rows.Add(newRow);
	        }
	        DalSysMenu2 dalSysMenu2 = new DalSysMenu2();
	        foreach (DataRow row in dalSysMenu2.GetList(0, "", "SortNo").Tables[0].Rows)
	        {
	            DataRow newRow = dt.NewRow();
	            newRow["ID"] = row["ScndCode"];
	            newRow["ParentID"] = row["FrsCode"];
	            newRow["NodeName"] = row["ScndName"];
	            dt.Rows.Add(newRow);
	        }
	        DalSysModule dalSysModule = new DalSysModule();
	        foreach (DataRow row in dalSysModule.GetList(0, "", "SortNo").Tables[0].Rows)
	        {
	            DataRow newRow = dt.NewRow();
	            newRow["ID"] = row["ModuleCode"];
	            newRow["ParentID"] = row["ScndCode"];
	            newRow["NodeName"] = row["ModuleName"];
	            dt.Rows.Add(newRow);
	        }

	        DalSysModuleAuth dalSysModuleAuth = new DalSysModuleAuth();
	        foreach (DataRow row in dalSysModuleAuth.GetList("").Tables[0].Rows)
	        {
	            DataRow newRow = dt.NewRow();
	            newRow["ID"] = row["AuthCode"];
	            newRow["ParentID"] = row["ModuleCode"];
	            newRow["NodeName"] = row["AuthName"];
	            dt.Rows.Add(newRow);
	        }

            return dt;
	    }
	    #endregion  ExtensionMethod
    }
}

