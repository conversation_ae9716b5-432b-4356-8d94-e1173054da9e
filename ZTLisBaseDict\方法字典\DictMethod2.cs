using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictMethod2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictMethod _bllDictMethod = new BllDictMethod();
        BLL.BllDictMethodCategory _bllDictMethodCategory = new BllDictMethodCategory();
        Model.MdlDictMethod _mdlDictMethod = new MdlDictMethod();

        public DictMethod2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictMethod2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
            comboMethodCategory1.Init();
        }


        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictMethod.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            comboMethodCategory1.SelectedIndex = -1;
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["MethodCode"] + "";
            TxtName.Text = row["MethodName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            comboMethodCategory1.SelectedValue = row["MethodCategoryId"];
            TxtDescription.Text = row["Description"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写方法名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写方法编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }
            if (comboMethodCategory1.SelectedIndex == -1)
            {
                MessageBox.Show("请选择方法学类别！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                comboMethodCategory1.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["MethodCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["MethodName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictMethod.GetRecordCount($"MethodCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictMethod.GetRecordCount($"MethodName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictMethod.MethodCode = TxtCode.Text.Trim();
            _mdlDictMethod.MethodName = TxtName.Text.Trim();
            _mdlDictMethod.PinYin = TxtJc.Text.Trim();
            _mdlDictMethod.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictMethod.MethodCategoryId = (int)comboMethodCategory1.SelectedValue;
            _mdlDictMethod.Description = TxtDescription.Text.Trim();
            _mdlDictMethod.Status = true;
            _mdlDictMethod.CreatedAt = DateTime.Now;
            _mdlDictMethod.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictMethod, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["MethodId"] = _bllDictMethod.Add(_mdlDictMethod);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictMethod = _bllDictMethod.GetModel(int.Parse(base.MyRow["MethodId"].ToString()));
            _mdlDictMethod.MethodCode = TxtCode.Text.Trim();
            _mdlDictMethod.MethodName = TxtName.Text.Trim();
            _mdlDictMethod.PinYin = TxtJc.Text.Trim();
            _mdlDictMethod.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictMethod.MethodCategoryId = (int)comboMethodCategory1.SelectedValue;
            _mdlDictMethod.Description = TxtDescription.Text.Trim();
            _mdlDictMethod.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictMethod, base.MyRow);

            //数据保存
            try
            {
                _bllDictMethod.Update(_mdlDictMethod);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            if (TxtName.Text.Trim() != "")
            {
                TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
            }
        }
    }
}