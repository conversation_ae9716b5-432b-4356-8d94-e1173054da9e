﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysRpt_Class_Level.cs
*
* 功 能： N/A
* 类 名： DalSysRpt_Class_Level
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-22 11:15:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysRpt_Class_Level
	/// </summary>
	public partial class DalSysRpt_Class_Level:IDalSysRpt_Class_Level
	{
		public DalSysRpt_Class_Level()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return Common.WinFormVar.Var.DbHelper.GetMaxID("Level_Code", "SysRpt_Class_Level"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Level_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysRpt_Class_Level");
			strSql.Append(" where Level_Code=@Level_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Level_Code", SqlDbType.Int,4)			};
			parameters[0].Value = Level_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysRpt_Class_Level model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysRpt_Class_Level(");
			strSql.Append("Level_Code,Level_Width,Level_Name)");
			strSql.Append(" values (");
			strSql.Append("@Level_Code,@Level_Width,@Level_Name)");
			SqlParameter[] parameters = {
					new SqlParameter("@Level_Code", SqlDbType.Int,4),
					new SqlParameter("@Level_Width", SqlDbType.Int,4),
					new SqlParameter("@Level_Name", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Level_Code;
			parameters[1].Value = model.Level_Width;
			parameters[2].Value = model.Level_Name;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysRpt_Class_Level model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysRpt_Class_Level set ");
			strSql.Append("Level_Width=@Level_Width,");
			strSql.Append("Level_Name=@Level_Name");
			strSql.Append(" where Level_Code=@Level_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Level_Width", SqlDbType.Int,4),
					new SqlParameter("@Level_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Level_Code", SqlDbType.Int,4)};
			parameters[0].Value = model.Level_Width;
			parameters[1].Value = model.Level_Name;
			parameters[2].Value = model.Level_Code;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Level_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRpt_Class_Level ");
			strSql.Append(" where Level_Code=@Level_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Level_Code", SqlDbType.Int,4)			};
			parameters[0].Value = Level_Code;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Level_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRpt_Class_Level ");
			strSql.Append(" where Level_Code in ("+Level_Codelist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRpt_Class_Level  GetModel(int Level_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Level_Code,Level_Width,Level_Name from SysRpt_Class_Level ");
			strSql.Append(" where Level_Code=@Level_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Level_Code", SqlDbType.Int,4)			};
			parameters[0].Value = Level_Code;

			Model.MdlSysRpt_Class_Level model=new Model.MdlSysRpt_Class_Level();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRpt_Class_Level DataRowToModel(DataRow row)
		{
			Model.MdlSysRpt_Class_Level model=new Model.MdlSysRpt_Class_Level();
			if (row != null)
			{
				if(row["Level_Code"]!=null && row["Level_Code"].ToString()!="")
				{
					model.Level_Code=int.Parse(row["Level_Code"].ToString());
				}
				if(row["Level_Width"]!=null && row["Level_Width"].ToString()!="")
				{
					model.Level_Width=int.Parse(row["Level_Width"].ToString());
				}
				if(row["Level_Name"]!=null)
				{
					model.Level_Name=row["Level_Name"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Level_Code,Level_Width,Level_Name ");
			strSql.Append(" FROM SysRpt_Class_Level ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Level_Code,Level_Width,Level_Name ");
			strSql.Append(" FROM SysRpt_Class_Level ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysRpt_Class_Level ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Level_Code desc");
			}
			strSql.Append(")AS Row, T.*  from SysRpt_Class_Level T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysRpt_Class_Level";
			parameters[1].Value = "Level_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

