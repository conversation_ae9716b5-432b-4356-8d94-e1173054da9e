using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisControl;

namespace ZTLisSysManage
{
    public partial class SysUserManage2 : Common.BaseForm.BaseDict2
    {
        BLL.BllSysUser _bllSysUser = new BllSysUser();
        Model.MdlSysUser _mdlSysUser = new MdlSysUser();

        public SysUserManage2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtLoginName.GotFocus += new System.EventHandler(base.InputEn);
            TxtUserName.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void SysUserManage2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化下拉控件
            ComboDoctor1.Init();
            ComboRole1.Init();
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtLoginName.Text = "";
            TxtUserName.Text = "";
            ComboDoctor1.SelectedIndex = -1;
            ComboRole1.SelectedIndex = -1;
            ChkIsLockedOut.Checked = false;
            TxtLoginName.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtLoginName.Text = row["LoginName"] + "";
            TxtUserName.Text = row["UserName"] + "";
            ComboDoctor1.SelectedValue = row["DoctorId"] + "";
            ComboRole1.SelectedValue = row["RoleCode"] + "";
            ChkIsLockedOut.Checked = Convert.ToBoolean(row["IsLockedOut"]);
            TxtLoginName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtLoginName.Text))
            {
                MessageBox.Show("请填写登录账号！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtLoginName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtUserName.Text))
            {
                MessageBox.Show("请填写用户姓名！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtUserName.Select();
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(ComboRole1)) return false;

            // 检查登录账号重复
            if (base.Insert == true)
            {
                if (CheckLoginNameExists(TxtLoginName.Text))
                {
                    MessageBox.Show("登录账号重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtLoginName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtLoginName.Text.Trim() != base.MyRow["LoginName"].ToString().Trim() && CheckLoginNameExists(TxtLoginName.Text))
                {
                    MessageBox.Show("登录账号重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtLoginName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckLoginNameExists(string loginName)
        {
            int cnt = _bllSysUser.GetRecordCount($"LoginName='{loginName}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlSysUser.LoginName = TxtLoginName.Text.Trim();
            _mdlSysUser.UserName = TxtUserName.Text.Trim();
            _mdlSysUser.PasswordHash = Common.DESEncryptMD5.Encrypt("000");
            _mdlSysUser.DoctorId = ComboDoctor1.SelectedValue == null ? null : (int?)Convert.ToInt32(ComboDoctor1.SelectedValue);
            _mdlSysUser.RoleCode = ComboRole1.SelectedValue?.ToString();
            _mdlSysUser.IsLockedOut = ChkIsLockedOut.Checked;
            _mdlSysUser.Status = true;
            _mdlSysUser.CreatedBy = ZTLisVar.Var.UserId;
            _mdlSysUser.CreatedAt = DateTime.Now;
            _mdlSysUser.UpdatedBy = ZTLisVar.Var.UserId;
            _mdlSysUser.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlSysUser, base.MyRow);
            base.MyRow["RoleName"] = ComboRole1.Text.Trim();
            base.MyRow["DoctorName"] = ComboDoctor1.Text.Trim();

            //数据保存
            try
            {
                base.MyRow["UserId"] = _bllSysUser.Add(_mdlSysUser);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtLoginName.Focus();
            }

            DataClear();
        }

        private void DataUpdate()
        {
            int id = int.Parse(base.MyRow["UserId"].ToString());
            _mdlSysUser = _bllSysUser.GetModel(id);

            _mdlSysUser.LoginName = TxtLoginName.Text.Trim();
            _mdlSysUser.UserName = TxtUserName.Text.Trim();
            _mdlSysUser.DoctorId = ComboDoctor1.SelectedValue == null ? null : (int?)Convert.ToInt32(ComboDoctor1.SelectedValue);
            _mdlSysUser.RoleCode = ComboRole1.SelectedValue?.ToString();
            _mdlSysUser.IsLockedOut = ChkIsLockedOut.Checked;
            _mdlSysUser.UpdatedBy = ZTLisVar.Var.UserId;
            _mdlSysUser.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlSysUser, base.MyRow);
            base.MyRow["RoleName"] = ComboRole1.Text.Trim();
            base.MyRow["DoctorName"] = ComboDoctor1.Text.Trim();

            //数据保存
            try
            {
                _bllSysUser.Update(_mdlSysUser);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        private void DataSave()
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
                DataAdd();
            else
                DataUpdate();
        }

        #endregion

        #region 控件事件
        private void BtnSave_Click(object sender, EventArgs e)
        {
            this.DataSave();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_TextChanged(object sender, EventArgs e)
        {
            // 根据用户姓名自动生成拼音简称（如果有相关函数）
        }

        #endregion
    }
}