﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using C1.Win.C1List;

namespace CustomControl
{
    public partial class MyDtComobo : UserControl
    {
        public MyDtComobo()
        {
            InitializeComponent();
            Init_TDBCombo();
            //CustomEvent();
        }

        #region 公共函数
        public void Init_Colum(string V_字段, string V_标题, int V_长度, string V_水平, string V_格式 = "")
        {
            c1Combo1.Columns[V_字段].Caption = V_标题;
            c1Combo1.Splits[0].DisplayColumns[V_字段].Width = V_长度;
            if (V_长度 == 0)
            {
                c1Combo1.Splits[0].DisplayColumns[V_字段].Visible = false;
            }
            else
            {
                c1Combo1.Splits[0].DisplayColumns[V_字段].Visible = true;
            }
            //标题修饰

            c1Combo1.Splits[0].DisplayColumns[V_字段].HeadingStyle.Borders.BorderType = C1.Win.C1List.BorderTypeEnum.Raised;
            c1Combo1.Splits[0].DisplayColumns[V_字段].HeadingStyle.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Center;
            c1Combo1.Splits[0].DisplayColumns[V_字段].HeadingStyle.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Center;


            //内容修饰
            switch (V_水平)
            {
                case "左":
                    c1Combo1.Splits[0].DisplayColumns[V_字段].Style.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Near;
                    break;
                case "中":
                    c1Combo1.Splits[0].DisplayColumns[V_字段].Style.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Center;
                    break;
                case "右":
                    c1Combo1.Splits[0].DisplayColumns[V_字段].Style.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Far;
                    break;
            }

            c1Combo1.Splits[0].DisplayColumns[V_字段].Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Center;

            if (!string.IsNullOrEmpty(V_格式))
                c1Combo1.Columns[V_字段].NumberFormat = V_格式;
        }
        #endregion

        #region 私有函数

        private void Init_TDBCombo()
        {
            c1Combo1.AutoSize = false;
            c1Combo1.AllowColMove = false;
            c1Combo1.AllowSort = true;
            c1Combo1.AutoDropDown = true;
            c1Combo1.AutoCompletion = false;
            c1Combo1.AutoSelect = false;
            c1Combo1.AlternatingRows = false;
            //.BorderStyle = Windows.Forms.BorderStyle.FixedSingle
            c1Combo1.LimitToList = false;
            c1Combo1.RowTracking = true;
            c1Combo1.CausesValidation = true;
            c1Combo1.SuperBack = true;
            c1Combo1.CausesValidation = true;
            c1Combo1.ColumnHeaders = true;

            c1Combo1.FetchRowStyles = true;

            c1Combo1.FlatStyle = C1.Win.C1List.FlatModeEnum.System;
            c1Combo1.ItemHeight = 20;
            c1Combo1.MaxDropDownItems = 18;
            //.Height = 20
            //.DropDownWidth = 500

            c1Combo1.Splits[0].AllowFocus = true;
            c1Combo1.Splits[0].ExtendRightColumn = true;
            c1Combo1.Splits[0].ColumnCaptionHeight = 20;

            c1Combo1.Font = new Font("宋体", (float)10.5, FontStyle.Regular);

            //选中行
            //.BackColor = Color.SlateGray
            c1Combo1.Splits[0].HighLightRowStyle.ForeColor = Color.FromArgb(255, 255, 255);
            c1Combo1.Splits[0].HighLightRowStyle.BackColor = Color.FromArgb(205, 92, 92);
            c1Combo1.Splits[0].HighLightRowStyle.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom;


            //  .BackColor = Color.SlateGray
            c1Combo1.Splits[0].SelectedStyle.ForeColor = Color.FromArgb(255, 255, 255);
            c1Combo1.Splits[0].SelectedStyle.BackColor = Color.FromArgb(205, 92, 92);
            c1Combo1.Splits[0].SelectedStyle.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom;
            c1Combo1.VisualStyle = C1.Win.C1List.VisualStyle.Office2010Blue;
            c1Combo1.DataMode = C1.Win.C1List.DataModeEnum.Normal;
            c1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.Single;
            c1Combo1.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown;
            c1Combo1.MatchCol = C1.Win.C1List.MatchColEnum.DisplayMember;
            c1Combo1.SelectedIndex = -1;
        }

        private void c1Combo1_KeyUp(object sender, KeyEventArgs e)
        {
            if ((e.KeyValue > 47 & e.KeyValue < 58) | (e.KeyValue > 95 & e.KeyValue < 123) | (e.KeyValue > 64 & e.KeyValue < 91) | (e.KeyValue == 8) | (e.KeyValue == 189) | (e.KeyValue == 190))
            {

                string s = c1Combo1.Text;

                string strFilter = "";

                char[] split = { ' ' };

                foreach (string substr in c1Combo1.Text
                    .Replace("[", "[[ ")
                    .Replace("]", " ]]")
                    .Replace("*", "[*]")
                    .Replace("%", "[%]")
                    .Replace("[[ ", "[[]")
                    .Replace(" ]]", "[]]")
                    .Replace("\'", "''")
                    .Split(split))
                {
                    strFilter = strFilter + RowFilterNotTextNull + " like '*" + substr + "*' And ";
                }
                strFilter = strFilter.Substring(0, strFilter.Length - 5);

                string strFilterEquals = "";
                if (!string.IsNullOrEmpty(tmp_RowFilterNumberEquals) && Regex.IsMatch(c1Combo1.Text, @"^[-]?[0-9]+[0-9]*[.]?[0-9]*$"))
                {
                    strFilterEquals += tmp_RowFilterNumberEquals + $"={c1Combo1.Text}";
                    strFilter = $"(({strFilter}) Or ({strFilterEquals}))";
                }

                if (string.IsNullOrEmpty(c1Combo1.Text))
                {
                    ((DataView)c1Combo1.DataSource).RowFilter = tmp_RowFilterTextNull;
                }
                else
                {
                    if (!string.IsNullOrEmpty(tmp_RowFilterTextNull))
                    {
                        ((DataView)c1Combo1.DataSource).RowFilter = tmp_RowFilterTextNull + " and " + strFilter;// RowFilterNotTextNull + " like '*" + str + "*'";
                    }
                    else
                    {
                        ((DataView)c1Combo1.DataSource).RowFilter = strFilter;// RowFilterNotTextNull + " like '*" + str + "*'"; 
                    }

                }
                if ((((DataView)c1Combo1.DataSource).Count) == 0 && AllowNew == true)
                {

                }
                else
                {
                    if ((e.KeyValue == 8))
                    {
                        c1Combo1.DroppedDown = false;
                        c1Combo1.DroppedDown = true;
                    }
                }

                c1Combo1.Text = s;
                c1Combo1.SelectionStart = c1Combo1.Text.Length;
                c1Combo1.SelectionLength = 0;
            }
        }

        private void c1Combo1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyValue == 13)
            {
                if (c1Combo1.WillChangeToIndex < 1)
                {
                    if ((((DataView)c1Combo1.DataSource).Count) == 0)
                    {
                        if (AllowNew == false && c1Combo1.Text.Trim() != "")
                        {
                            MessageBox.Show(label1.Text + ":'" + this.c1Combo1.Text + "' 不存在！", "提示:", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                            c1Combo1.Text = "";
                            System.Windows.Forms.SendKeys.Send("^{A}");
                        }
                        else
                        {
                            e.Handled = true;
                            System.Windows.Forms.SendKeys.Send("{Tab}");
                        }
                        return;
                    }
                    if (DefaultIndex == 0)
                    {
                        c1Combo1.SelectedIndex = -1;
                        c1Combo1.SelectedIndex = 0;
                    }
                    else
                    {
                        if (c1Combo1.Text == "")
                        {
                            c1Combo1.SelectedIndex = -1;
                        }
                        else
                        {
                            c1Combo1.SelectedIndex = -1;
                            c1Combo1.SelectedIndex = 0;
                        }
                    }
                }
                else
                {
                    int index = 0;
                    index = c1Combo1.WillChangeToIndex;
                    c1Combo1.SelectedIndex = -1;
                    c1Combo1.SelectedIndex = index;
                }
                e.Handled = true;
                System.Windows.Forms.SendKeys.Send("{Tab}");
            }
        }

        private void label1_MouseHover(object sender, EventArgs e)
        {
            if (CaptainLink == true)
            {
                Cursor = Cursors.Hand;
            }
        }

        private void label1_MouseLeave(object sender, EventArgs e)
        {
            if (CaptainLink == true)
            {
                Cursor = Cursors.Default;
            }
        }

        #endregion

        #region 公开属性
        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        float _CaptainWidth = 70;
        [Description("标题宽度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0 || label1.Text == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }


        [Description("字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                c1Combo1.Font = value;
                c1Combo1.EditorFont = value;
                Invalidate();
            }
        }

        [Description("标题字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }

        [Description("下拉文本字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font ItemTextFont
        {
            get { return c1Combo1.Font; }
            set
            {
                c1Combo1.Font = value;
                Invalidate();
            }
        }

        [Description("文本框文本字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font TextFont
        {
            get { return c1Combo1.EditorFont; }
            set
            {
                c1Combo1.EditorFont = value;
                Invalidate();
            }
        }

        [Description("控件是否启用"), Browsable(true)]
        public new bool Enabled
        {
            get { return c1Combo1.Enabled; }
            set
            {
                c1Combo1.Enabled = value;
                if (value == true)
                {
                    c1Combo1.TabStop = true;
                    this.TabStop = true;
                    c1Combo1.VisualStyle = C1.Win.C1List.VisualStyle.Office2010Blue;
                    c1Combo1.EditorBackColor = SystemColors.Window;
                }
                else
                {
                    c1Combo1.TabStop = false;
                    this.TabStop = false;
                    c1Combo1.EditorBackColor = SystemColors.Info;
                    c1Combo1.ForeColor = Color.Black;
                    c1Combo1.EditorForeColor = Color.Black;
                }

                Invalidate();
            }
        }


        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }

        [Description("数据源")]
        public object DataSource
        {
            get { return c1Combo1.DataSource; }
            set
            {
                c1Combo1.DataSource = value;
                Invalidate();
            }
        }

        private string tmp_RowFilterTextNull = "";
        [Description("Combo事先指定的过滤条件"), Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string RowFilterTextNull
        {
            get { return tmp_RowFilterTextNull; }
            set
            {
                tmp_RowFilterTextNull = value;
                ((DataView)c1Combo1.DataSource).RowFilter = tmp_RowFilterTextNull;
            }
        }

        [Description("Combo按下按键时的过滤条件"), Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string RowFilterNotTextNull { get; set; }

        private string tmp_RowFilterNumberEquals = "";
        [Description("根据数字等于的过滤条件"), Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string RowFilterNumberEquals
        {
            get { return tmp_RowFilterNumberEquals; }
            set
            {
                tmp_RowFilterNumberEquals = value;
            }
        }

        public DataView DataView
        {
            get { return (DataView)c1Combo1.DataSource; }
            set
            {
                c1Combo1.DataSource = value;
                this.c1Combo1.SelectedIndex = -1;
                foreach (C1.Win.C1List.C1DisplayColumn col in c1Combo1.Splits[0].DisplayColumns)
                {
                    col.Width = 0;
                    col.Visible = false;
                }
            }
        }

        private int tmp_DefaultIndex = 0;
        [Description("Combo不选择时默认选项"), Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int DefaultIndex
        {
            get { return tmp_DefaultIndex; }
            set
            {
                tmp_DefaultIndex = value;
            }
        }
        public string DisplayMember
        {
            set { c1Combo1.DisplayMember = value; }
        }

        public string ValueMember
        {
            set { c1Combo1.ValueMember = value; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1List.VisualStyle VisualStyle
        {
            get { return c1Combo1.VisualStyle; }
            set
            {
                c1Combo1.VisualStyle = value;
                Invalidate();
            }
        }

        public C1.Win.C1List.C1DataColumnCollection Columns
        {
            get { return this.c1Combo1.Columns; }
        }

        [Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object SelectedValue
        {
            get { return this.c1Combo1.SelectedValue; }
            set
            {
                if (c1Combo1.DataSource != null) ((DataView)c1Combo1.DataSource).RowFilter = tmp_RowFilterTextNull;
                this.c1Combo1.SelectedValue = value ?? DBNull.Value;
                Invalidate();
            }
        }

        [Browsable(false), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string SelectedText
        {
            get { return this.c1Combo1.SelectedText; }
            set
            {
                this.c1Combo1.SelectedText = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public override string Text
        {
            get { return this.c1Combo1.Text; }
            set
            {
                this.c1Combo1.Text = value;
                Invalidate();
            }
        }

        [Browsable(false), ReadOnlyAttribute(true)]
        public object WillChangeToValue
        {
            get { return this.c1Combo1.WillChangeToValue; }
        }

        public int DroupDownWidth
        {
            set { c1Combo1.DropDownWidth = value; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public short MaxDropDownItems
        {
            get { return c1Combo1.MaxDropDownItems; }
            set
            {
                c1Combo1.MaxDropDownItems = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool AutoCompletion
        {
            get { return this.c1Combo1.AutoCompletion; }
            set
            {
                this.c1Combo1.AutoCompletion = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool AutoSelect
        {
            get { return this.c1Combo1.AutoSelect; }
            set
            {
                this.c1Combo1.AutoSelect = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int SelectedIndex
        {
            get { return this.c1Combo1.SelectedIndex; }
            set
            {
                this.c1Combo1.SelectedIndex = value;
                if (value == -1 && c1Combo1.DataSource != null) ((DataView)c1Combo1.DataSource).RowFilter = tmp_RowFilterTextNull;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1List.ComboStyleEnum ComboStyle
        {

            get { return this.c1Combo1.ComboStyle; }
            set
            {
                this.c1Combo1.ComboStyle = value;
                Invalidate();
            }
        }

        private bool _Captainlink;
        [Description("标题是否超链接"), DefaultValue(false)]
        public bool CaptainLink
        {

            get { return _Captainlink; }
            set
            {
                _Captainlink = value;
                if (value == true)
                {
                    label1.Font = new System.Drawing.Font("宋体", 9, FontStyle.Underline);
                    label1.ForeColor = Color.Blue;
                }
                else
                {
                    label1.Font = new System.Drawing.Font("宋体", 9, FontStyle.Regular);
                    label1.ForeColor = Color.Black;
                }
                Invalidate();
            }
        }

        [Description("是否允许添加新行"), DefaultValue(false)]
        public bool AllowNew { get; set; }

        [Description("下拉高度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public int ItemHeight
        {
            get { return c1Combo1.ItemHeight; }
            set
            {
                c1Combo1.ItemHeight = value;
                Invalidate();
            }
        }


        public bool ReadOnly
        {
            get { return c1Combo1.ReadOnly; }
            set
            {
                if (value == true)
                {
                    c1Combo1.BackColor = Color.MistyRose;
                    c1Combo1.ForeColor = Color.Black;
                }
                else
                {
                    c1Combo1.VisualStyle = C1.Win.C1List.VisualStyle.Office2010Blue;

                }
                c1Combo1.ReadOnly = value;
                Invalidate();
            }
        }

        public int Row
        {
            get { return c1Combo1.Row; }
            set
            {
                c1Combo1.Row = value;
            }
        }

        public int Bookmark
        {
            get { return c1Combo1.Bookmark; }
            set
            {
                c1Combo1.Bookmark = value;
            }
        }
        public int ColumnCaptionHeight
        {
            get { return c1Combo1.Splits[0].ColumnCaptionHeight; }
            set
            {
                c1Combo1.Splits[0].ColumnCaptionHeight = value;
            }
        }
        #endregion

        #region 公开方法
        public new void Select()
        {
            c1Combo1.Select();
        }

        public void OpenCombo()
        {
            c1Combo1.OpenCombo();
        }

        #endregion

        #region 公开事件

        [Description("当控件获得光标焦点时"), Browsable(true)]
        public new event EventHandler GotFocus
        {
            add
            {
                c1Combo1.GotFocus += value;
            }
            remove
            {
                c1Combo1.GotFocus -= value;
            }
        }
        [Description("当用户改变列表中的一行时触发"), Browsable(true)]
        public event EventHandler RowChange
        {
            add
            {
                c1Combo1.RowChange += value;
            }
            remove
            {
                c1Combo1.RowChange -= value;
            }
        }

        [Description("当SelectedValue属性发生变化时触发"), Browsable(true)]
        public event EventHandler SelectedValueChanged
        {
            add
            {
                c1Combo1.SelectedValueChanged += value;
            }
            remove
            {
                c1Combo1.SelectedValueChanged -= value;
            }
        }
        [Description("当Text属性发生变化时触发"), Browsable(true)]
        public new event EventHandler TextChanged
        {
            add
            {
                c1Combo1.TextChanged += value;
            }
            remove
            {
                c1Combo1.TextChanged -= value;
            }
        }

        [Description("在首次按下某个键时发生"), Browsable(true)]
        public new event System.Windows.Forms.KeyEventHandler KeyDown
        {
            add
            {
                c1Combo1.KeyDown += value;
            }
            remove
            {
                c1Combo1.KeyDown -= value;
            }
        }

        public new event KeyPressEventHandler KeyPress
        {
            add
            {
                c1Combo1.KeyPress += value;
            }
            remove
            {
                c1Combo1.KeyPress -= value;
            }
        }

        public event MouseEventHandler LabelMouseClick
        {
            add
            {
                label1.MouseClick += value;
            }
            remove
            {
                label1.MouseClick -= value;
            }
        }

        public event CancelEventHandler BeforeOpen
        {
            add
            {
                c1Combo1.BeforeOpen += value;
            }
            remove
            {
                c1Combo1.BeforeOpen -= value;
            }
        }
        public event FetchCellStyleEventHandler FetchCellStyle
        {
            add
            {
                c1Combo1.FetchCellStyle += value;
            }
            remove
            {
                c1Combo1.FetchCellStyle -= value;
            }
        }
        #endregion

    }
}
