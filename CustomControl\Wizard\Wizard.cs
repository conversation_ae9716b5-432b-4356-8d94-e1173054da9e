﻿using System;
using System.Collections;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Data;
using System.Windows.Forms;
using System.Diagnostics;
using System.Collections.Generic;

namespace CustomControl
{
    /// <summary>
    /// A wizard is the control added to a form to provide a step by step functionality.
    /// It contains <see cref="WizardPage"/>s in the <see cref="Pages"/> collection, which
    /// are containers for other controls. Only one wizard page is shown at a time in the client
    /// are of the wizard.
    /// </summary>
    [Designer(typeof(CustomControl.WizardDesigner))]
    [ToolboxItem(true)]
    [ToolboxBitmap(typeof(CustomControl.Wizard))]
    public class Wizard : System.Windows.Forms.UserControl
    {
        protected internal System.Windows.Forms.Panel pnlButtons;
        private System.Windows.Forms.Panel pnlButtonBright3d;
        private System.Windows.Forms.Panel pnlButtonDark3d;
        protected internal MyButton btnBack;
        protected internal MyButton btnNext;

        protected internal MyButton btnAdd;
        protected internal MyButton btnSave;
        protected internal MyButton btnPrint;
        protected internal MyButton btnSearch;
        protected internal MyButton btnDelete;
        protected internal MyButton btnCancel;
        protected internal MyButton btnCommit;




        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.Container components = null;
        protected internal MyButton btnRead;

        Dictionary<int, MyButton> btnList = new Dictionary<int, MyButton>();

        /// <summary>
        /// Wizard control with designer support
        /// </summary>
        public Wizard()
        {
            //Empty collection of Pages
            vPages = new PageCollection(this);
               // This call is required by the Windows.Forms Form Designer.
            InitializeComponent();
            btnList.Add(1, btnCommit);
            btnList.Add(2, btnCancel);
            btnList.Add(3, btnDelete);
            btnList.Add(4, btnSearch);
            btnList.Add(5, btnPrint);
            btnList.Add(6, btnRead);
            btnList.Add(7, btnSave);
            btnList.Add(8, btnAdd);
            btnList.Add(9, btnNext);
            btnList.Add(10, btnBack);  
        }

        private void Wizard_Load(object sender, System.EventArgs e)
        {
            //Attempt to activate a page
            ActivatePage(0);
            //for (int i = 1; i < 9;i++ )
            //{
            //    changebtnLoc(btnList[i], i, btnList[i].Visible);
            //}
            //Can I add my self as default cancel button
            Form form = this.FindForm();
            if (form != null && DesignMode == false)
                form.CancelButton = btnCancel;
        }

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }
            }
            base.Dispose(disposing);
        }


        #region Component Designer generated code
        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.btnRead = new CustomControl.MyButton();
            this.btnCommit = new CustomControl.MyButton();
            this.btnDelete = new CustomControl.MyButton();
            this.btnAdd = new CustomControl.MyButton();
            this.btnBack = new CustomControl.MyButton();
            this.btnNext = new CustomControl.MyButton();
            this.btnSearch = new CustomControl.MyButton();
            this.btnPrint = new CustomControl.MyButton();
            this.btnSave = new CustomControl.MyButton();
            this.btnCancel = new CustomControl.MyButton();
            this.pnlButtonBright3d = new System.Windows.Forms.Panel();
            this.pnlButtonDark3d = new System.Windows.Forms.Panel();
            this.pnlButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlButtons
            // 
            this.pnlButtons.Controls.Add(this.btnRead);
            this.pnlButtons.Controls.Add(this.btnCommit);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.btnAdd);
            this.pnlButtons.Controls.Add(this.btnBack);
            this.pnlButtons.Controls.Add(this.btnNext);
            this.pnlButtons.Controls.Add(this.btnSearch);
            this.pnlButtons.Controls.Add(this.btnPrint);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.pnlButtonBright3d);
            this.pnlButtons.Controls.Add(this.pnlButtonDark3d);
            this.pnlButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlButtons.Location = new System.Drawing.Point(0, 400);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new System.Drawing.Size(802, 59);
            this.pnlButtons.TabIndex = 0;
            // 
            // btnRead
            // 
            this.btnRead.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRead.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnRead.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnRead.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRead.Location = new System.Drawing.Point(308, 12);
            this.btnRead.Margin = new System.Windows.Forms.Padding(4);
            this.btnRead.Name = "btnRead";
            this.btnRead.Size = new System.Drawing.Size(80, 35);
            this.btnRead.TabIndex = 12;
            this.btnRead.Text = "读取";
            // 
            // btnCommit
            // 
            this.btnCommit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCommit.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnCommit.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnCommit.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCommit.Location = new System.Drawing.Point(708, 12);
            this.btnCommit.Margin = new System.Windows.Forms.Padding(4);
            this.btnCommit.Name = "btnCommit";
            this.btnCommit.Size = new System.Drawing.Size(80, 35);
            this.btnCommit.TabIndex = 11;
            this.btnCommit.Text = "提交";
            // 
            // btnDelete
            // 
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDelete.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnDelete.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnDelete.Font = new System.Drawing.Font("宋体", 12F);
            this.btnDelete.Location = new System.Drawing.Point(548, 12);
            this.btnDelete.Margin = new System.Windows.Forms.Padding(4);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(80, 35);
            this.btnDelete.TabIndex = 10;
            this.btnDelete.Text = "删除";
            // 
            // btnAdd
            // 
            this.btnAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAdd.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnAdd.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnAdd.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAdd.Location = new System.Drawing.Point(148, 12);
            this.btnAdd.Margin = new System.Windows.Forms.Padding(4);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(80, 35);
            this.btnAdd.TabIndex = 9;
            this.btnAdd.Text = "新增";
            // 
            // btnBack
            // 
            this.btnBack.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBack.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnBack.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnBack.Font = new System.Drawing.Font("宋体", 12F);
            this.btnBack.Location = new System.Drawing.Point(-12, 12);
            this.btnBack.Margin = new System.Windows.Forms.Padding(4);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(80, 35);
            this.btnBack.TabIndex = 1;
            this.btnBack.Text = "上页";
            this.btnBack.Click += new System.EventHandler(this.btnBack_Click);
            this.btnBack.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btnBack_MouseDown);
            // 
            // btnNext
            // 
            this.btnNext.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNext.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnNext.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnNext.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnNext.Location = new System.Drawing.Point(68, 12);
            this.btnNext.Margin = new System.Windows.Forms.Padding(4);
            this.btnNext.Name = "btnNext";
            this.btnNext.Size = new System.Drawing.Size(80, 35);
            this.btnNext.TabIndex = 2;
            this.btnNext.Text = "下页";
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            this.btnNext.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btnNext_MouseDown);
            // 
            // btnSearch
            // 
            this.btnSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSearch.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnSearch.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnSearch.Font = new System.Drawing.Font("宋体", 12F);
            this.btnSearch.Location = new System.Drawing.Point(468, 12);
            this.btnSearch.Margin = new System.Windows.Forms.Padding(4);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(80, 35);
            this.btnSearch.TabIndex = 8;
            this.btnSearch.Text = "速查";
            // 
            // btnPrint
            // 
            this.btnPrint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPrint.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnPrint.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnPrint.Font = new System.Drawing.Font("宋体", 12F);
            this.btnPrint.Location = new System.Drawing.Point(388, 12);
            this.btnPrint.Margin = new System.Windows.Forms.Padding(4);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new System.Drawing.Size(80, 35);
            this.btnPrint.TabIndex = 7;
            this.btnPrint.Text = "打印";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnSave.Font = new System.Drawing.Font("宋体", 12F);
            this.btnSave.Location = new System.Drawing.Point(228, 12);
            this.btnSave.Margin = new System.Windows.Forms.Padding(4);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(80, 35);
            this.btnSave.TabIndex = 7;
            this.btnSave.Text = "保存";
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.None;
            this.btnCancel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnCancel.Location = new System.Drawing.Point(628, 12);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(4);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 35);
            this.btnCancel.TabIndex = 6;
            this.btnCancel.Text = "取消";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // pnlButtonBright3d
            // 
            this.pnlButtonBright3d.BackColor = System.Drawing.SystemColors.ControlLightLight;
            this.pnlButtonBright3d.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlButtonBright3d.Location = new System.Drawing.Point(0, 1);
            this.pnlButtonBright3d.Name = "pnlButtonBright3d";
            this.pnlButtonBright3d.Size = new System.Drawing.Size(802, 1);
            this.pnlButtonBright3d.TabIndex = 1;
            // 
            // pnlButtonDark3d
            // 
            this.pnlButtonDark3d.BackColor = System.Drawing.SystemColors.ControlDark;
            this.pnlButtonDark3d.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlButtonDark3d.Location = new System.Drawing.Point(0, 0);
            this.pnlButtonDark3d.Name = "pnlButtonDark3d";
            this.pnlButtonDark3d.Size = new System.Drawing.Size(802, 1);
            this.pnlButtonDark3d.TabIndex = 2;
            // 
            // Wizard
            // 
            this.Controls.Add(this.pnlButtons);
            this.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Name = "Wizard";
            this.Size = new System.Drawing.Size(802, 459);
            this.Load += new System.EventHandler(this.Wizard_Load);
            this.pnlButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }
        #endregion

        private PageCollection vPages;
        /// <summary>
        /// Returns the collection of Pages in the wizard
        /// </summary>
        [Category("Wizard")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        public PageCollection Pages
        {
            get
            {
                return vPages;
            }
        }

        private WizardPage vActivePage = null;
        /// <summary>
        /// Gets/Sets the activePage in the wizard
        /// </summary>
        [Category("Wizard")]
        internal int PageIndex
        {
            get
            {
                return vPages.IndexOf(vActivePage);
            }
            set
            {
                //Do I have any pages?
                if (vPages.Count == 0)
                {
                    //No then show nothing
                    ActivatePage(-1);
                    return;
                }
                // Validate the page asked for
                if (value < -1 || value >= vPages.Count)
                {
                    throw new ArgumentOutOfRangeException("PageIndex",
                        value,
                        "The page index must be between 0 and " + Convert.ToString(vPages.Count - 1)
                        );
                }
                //Select the new page
                ActivatePage(value);
            }
        }

        /// <summary>
        /// Alternative way of getting/Setiing  the current page by using wizardPage objects
        /// </summary>
        public WizardPage Page
        {
            get
            {
                return vActivePage;
            }
            //Dont use this anymore, see Next, Back, NextTo and BackTo
            //			set
            //			{
            //				ActivatePage(value);
            //			}
        }

        protected internal void ActivatePage(int index)
        {
            //If the new page is invalid
            if (index < 0 || index >= vPages.Count)
            {
                btnNext.Enabled = false;
                btnBack.Enabled = false;

                return;
            }


            //Change to the new Page
            WizardPage tWizPage = ((WizardPage)vPages[index]);

            //Really activate the page
            ActivatePage(tWizPage);
        }

        protected internal void ActivatePage(WizardPage page)
        {
            //Deactivate the current
            if (vActivePage != null)
            {
                vActivePage.Visible = false;
            }


            //Activate the new page
            vActivePage = page;

            if (vActivePage != null)
            {
                //Ensure that this panel displays inside the wizard
                vActivePage.Parent = this;
                if (this.Contains(vActivePage) == false)
                {
                    this.Container.Add(vActivePage);
                }
                //Make it fill the space
                vActivePage.Dock = DockStyle.Fill;
                vActivePage.Visible = true;
                vActivePage.BringToFront();
                vActivePage.FocusFirstTabIndex();
            }

            //What should the back button say
            if (this.PageIndex > 0)
            {
                btnBack.Enabled = true;
            }
            else
            {
                btnBack.Enabled = false;
            }

            //What should the Next button say
            if (vPages.IndexOf(vActivePage) < vPages.Count - 1
                && vActivePage.IsFinishPage == false)
            {
                btnNext.Text = "下页";
                btnNext.Enabled = true;
                //Don't close the wizard :)
                btnNext.DialogResult = DialogResult.None;
            }
            else
            {
                btnNext.Text = "下页";
                //Dont allow a finish in designer
                if (DesignMode == true
                    && vPages.IndexOf(vActivePage) == vPages.Count - 1)
                {
                    btnNext.Enabled = false;
                }
                else
                {
                    btnNext.Enabled = false;
                    //If Not in design mode then allow a close

                    //btnNext.DialogResult = DialogResult.OK;
                }
            }

            //Cause a refresh
            if (vActivePage != null)
                vActivePage.Invalidate();
            else
                this.Invalidate();
        }

        private void btnNext_Click(object sender, System.EventArgs e)
        {
            Next();
        }

        private void btnNext_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (DesignMode == true)
                Next();
        }

        private void btnBack_Click(object sender, System.EventArgs e)
        {
            Back();
        }

        private void btnBack_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (DesignMode == true)
                Back();
        }

        private void btnCancel_Click(object sender, System.EventArgs e)
        {
            CancelEventArgs arg = new CancelEventArgs();

            //Throw the event out to subscribers
            if (CloseFromCancel != null)
            {
                CloseFromCancel(this, arg);
            }
            //If nobody told me to cancel
            if (arg.Cancel == false)
            {
                //Then we close the form
                this.FindForm().Close();
            }
        }


        /// <summary>
        /// Gets/Sets the enabled state of the Next button. 
        /// </summary>
        [Category("Wizard")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool NextEnabled
        {
            get
            {
                return btnNext.Enabled;
            }
            set
            {
                btnNext.Enabled = value;
            }
        }

        /// <summary>
        /// Gets/Sets the enabled state of the back button. 
        /// </summary>
        [Category("Wizard")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool BackEnabled
        {
            get
            {
                return btnBack.Enabled;
            }
            set
            {
                btnBack.Enabled = value;
            }
        }

        /// <summary>
        /// Gets/Sets the enabled state of the cancel button. 
        /// </summary>
        [Category("Wizard")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool CancelEnabled
        {
            get
            {
                return btnCancel.Enabled;
            }
            set
            {

                btnCancel.Enabled = value;
            }
        }
        /// <summary>
        ///启用读取按钮. 
        /// </summary>
        [Category("Wizard")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool BtnSaveEnabled
        {
            get
            {
                return btnSave.Enabled;
            }
            set
            {
                btnSave.Enabled = value;
            }
        }

        /// <summary>
        ///启用提交按钮. 
        /// </summary>
        [Category("Wizard")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool BtnCommitEnabled
        {
            get
            {
                return btnCommit.Enabled;
            }
            set
            {
                btnCommit.Enabled = value;
            }
        }

        /// <summary>
        /// 隐藏添加按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnAddVisible
        {
            get
            {
                return btnAdd.Visible;
            }
            set
            {
                btnAdd.Visible = value;
                changebtnLoc(btnAdd, 8, value);
            }
        }

        /// <summary>
        /// 隐藏保存按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnSaveVisible
        {
            get
            {
                return btnSave.Visible;
            }
            set
            {
                btnSave.Visible = value;
                changebtnLoc(btnSave, 7, value);
            }
        }

        /// <summary>
        /// 隐藏读取按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnReadVisible
        {
            get
            {
                return btnRead.Visible;
            }
            set
            {
                btnRead.Visible = value;
                Invalidate();
                changebtnLoc(btnRead, 6, value);
            }
        }


        /// <summary>
        /// 隐藏打印按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnPrintVisible
        {
            get
            {
                return btnPrint.Visible;
            }
            set
            {
                btnPrint.Visible = value;
                changebtnLoc(btnPrint, 5, value);
            }
        }

        /// <summary>
        /// 隐藏速查按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnSearchVisible
        {
            get
            {
                return btnSearch.Visible;
            }
            set
            {
                btnSearch.Visible = value;
                changebtnLoc(btnCommit, 4, value);
            }
        }

        /// <summary>
        /// 隐藏删除按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnDeleteVisible
        {
            get
            {
                return btnDelete.Visible;
            }
            set
            {
                btnDelete.Visible = value;
                changebtnLoc(btnDelete, 3, value);
            }
        }

        /// <summary>
        /// 隐藏取消按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnCancelVisible
        {
            get
            {
                return btnCancel.Visible;
            }
            set
            {
                btnCancel.Visible = value;
                changebtnLoc(btnCancel, 2, value);
            }
        }

        /// <summary>
        /// 隐藏抽查按钮. 
        /// </summary>
        [Category("Wizard")]
        [DefaultValue(true)]
        public bool btnCommitVisible
        {
            get
            {
                return btnCommit.Visible;
            }
            set
            {
                btnCommit.Visible = value;
                changebtnLoc(btnCommit, 1, value);
            }
        }




        /// <summary>
        /// Called when the cancel button is pressed, before the form is closed. Set e.Cancel to true if 
        /// you do not wish the cancel to close the wizard.
        /// </summary>
        public event CancelEventHandler CloseFromCancel;

        /// <summary>
        /// Closes the current page after a <see cref="WizardPage.CloseFromNext"/>, then moves to 
        /// the Next page and calls <see cref="WizardPage.ShowFromNext"/>
        /// </summary>
        public void Next()
        {
            Debug.Assert(this.PageIndex >= 0, "Page Index was below 0");
            //Tell the Application I just closed a Page
            int newPage = vActivePage.OnCloseFromNext(this);

            //Did I just press Finish instead of Next
            if (this.PageIndex < vPages.Count - 1
                && (vActivePage.IsFinishPage == false || DesignMode == true))
            {
                //No still going
                ActivatePage(newPage);
                //Tell the application, I have just shown a page
                vActivePage.OnShowFromNext(this);
            }
            else
            {
                Debug.Assert(this.PageIndex < vPages.Count, "Error I've just gone past the finish",
                    "btnNext_Click tried to go to page " + Convert.ToString(this.PageIndex + 1)
                    + ", but I only have " + Convert.ToString(vPages.Count));
                //yep Finish was pressed
                //if (DesignMode == false)
                //    this.ParentForm.Close();
            }
        }

        /// <summary>
        /// Moves to the page given and calls <see cref="WizardPage.ShowFromNext"/>
        /// </summary>
        /// <remarks>Does NOT call <see cref="WizardPage.CloseFromNext"/> on the current page</remarks>
        /// <param name="page"></param>
        public void NextTo(WizardPage page)
        {
            //Since we have a page to go to, then there is no need to validate most of it
            ActivatePage(page);
            //Tell the application, I have just shown a page
            vActivePage.OnShowFromNext(this);
        }

        /// <summary>
        /// Closes the current page after a <see cref="WizardPage.CloseFromBack"/>, then moves to 
        /// the previous page and calls <see cref="WizardPage.ShowFromBack"/>
        /// </summary>
        public void Back()
        {
            Debug.Assert(this.PageIndex < vPages.Count, "Page Index was beyond Maximum pages");
            //Can I press back
            Debug.Assert(this.PageIndex > 0 && this.PageIndex < vPages.Count, "Attempted to go back to a page that doesn't exist");
            //Tell the application that I closed a page
            int newPage = vActivePage.OnCloseFromBack(this);

            ActivatePage(newPage);
            //Tell the application I have shown a page
            vActivePage.OnShowFromBack(this);
        }

        /// <summary>
        /// Moves to the page given and calls <see cref="WizardPage.ShowFromBack"/>
        /// </summary>
        /// <remarks>Does NOT call <see cref="WizardPage.CloseFromBack"/> on the current page</remarks>
        /// <param name="page"></param>
        public void BackTo(WizardPage page)
        {
            //Since we have a page to go to, then there is no need to validate most of it
            ActivatePage(page);
            //Tell the application, I have just shown a page
            vActivePage.OnShowFromNext(this);
        }

#if DEBUG
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            if (DesignMode)
            {
                const string noPagesText = "No wizard pages inside the wizard.";


                SizeF textSize = e.Graphics.MeasureString(noPagesText, this.Font);
                RectangleF layout = new RectangleF((this.Width - textSize.Width) / 2,
                    (this.pnlButtons.Top - textSize.Height) / 2,
                    textSize.Width, textSize.Height);

                Pen dashPen = (Pen)SystemPens.GrayText.Clone();
                dashPen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;

                e.Graphics.DrawRectangle(dashPen,
                    this.Left + 8, this.Top + 8,
                    this.Width - 17, this.pnlButtons.Top - 17);

                e.Graphics.DrawString(noPagesText, this.Font, new SolidBrush(SystemColors.GrayText), layout);
            }
        }
        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);

            if (DesignMode)
            {
                this.Invalidate();
            }
        }
#endif

        #region 私有函数

        private void changebtnLoc(MyButton mybtn, int key, bool value)
        {
            foreach (KeyValuePair<int, MyButton> kvp in btnList)
            {
                if (kvp.Key > key)
                {
                    if (value == true)
                    {
                        kvp.Value.Location = new Point(btnList[kvp.Key].Location.X - mybtn.Width, btnBack.Location.Y);
                    }
                    else
                    {
                        kvp.Value.Location = new Point(btnList[kvp.Key].Location.X + mybtn.Width, btnBack.Location.Y);
                    }
                }
            }
        }

        #endregion

        #region 公开事件
        [Description("按下保存按钮"), Browsable(true)]
        public event EventHandler btnSaveClick
        {
            add
            {
                btnSave.Click += value;
            }
            remove
            {
                btnSave.Click -= value;
            }
        }



        [Description("按下打印按钮"), Browsable(true)]
        public event EventHandler btnPrintClick
        {
            add
            {
                btnPrint.Click += value;
            }
            remove
            {
                btnPrint.Click -= value;
            }
        }

        [Description("按下速查按钮"), Browsable(true)]
        public event EventHandler btnSearchClick
        {
            add
            {
                btnSearch.Click += value;
            }
            remove
            {
                btnSearch.Click -= value;
            }
        }

        [Description("按下新增按钮"), Browsable(true)]
        public event EventHandler btnAddClick
        {
            add
            {
                btnAdd.Click += value;
            }
            remove
            {
                btnAdd.Click -= value;
            }
        }

        [Description("按下删除按钮"), Browsable(true)]
        public event EventHandler btnDelClick
        {
            add
            {
                btnDelete.Click += value;
            }
            remove
            {
                btnDelete.Click -= value;
            }
        }

        [Description("按下提交按钮"), Browsable(true)]
        public event EventHandler btnCommitClick
        {
            add
            {
                btnCommit.Click += value;
            }
            remove
            {
                btnCommit.Click -= value;
            }
        }
        [Description("按下读取按钮"), Browsable(true)]
        public event EventHandler btnReadClick
        {
            add
            {
                btnRead.Click += value;
            }
            remove
            {
                btnRead.Click -= value;
            }
        }

        #endregion

  

    }
}