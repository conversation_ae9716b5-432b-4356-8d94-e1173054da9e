﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Common.BaseForm;
using BLL;
using Model;
using System.Collections.Concurrent;
using ZTLisEnum;

namespace ZTLisConnectors
{
    public partial class MainConnecter : BaseFather
    {
        private BLL.BllLabInstrument _bllLabInstrument = new BllLabInstrument();
        private BLL.BllLabInstrRawMsg _bllLabInstrRawMsg = new BllLabInstrRawMsg();
        private DataTable _table = new DataTable();
        private CurrencyManager _cm;
        // 连接器管理
        private Dictionary<int, IConnector> _connectors = new Dictionary<int, IConnector>();
        private Dictionary<int, bool> _connectorStatus = new Dictionary<int, bool>();

        // 消息队列
        private ConcurrentQueue<LogMessage> _messageQueue = new ConcurrentQueue<LogMessage>();
        private System.Windows.Forms.Timer _uiUpdateTimer;

        public MainConnecter()
        {
            InitializeComponent();
        }

        private void MainConnecter_Load(object sender, EventArgs e)
        {
            FormInit();
            LoadInstruments();
            InitializeTimer();
        }

        #region 自定义函数

        private void FormInit()
        {
            // 设置窗体标题
            this.Text = "设备数据接收器";
            this.WindowState = FormWindowState.Maximized;
            myGrid1.Init_Grid();
            myGrid1.Init_Column("选择", "IsCheck", 40, "中", "Check", true);
            myGrid1.Init_Column("设备名称", "InstrName", 250, "左", "", false);
            myGrid1.Init_Column("通讯类型", "ConnTypeName", 100, "左", "", false);
            myGrid1.AllowSort = false;

            BtnStart.Enabled = true;
            BtnStop.Enabled = false;
        }

        private void LoadInstruments()
        {
            try
            {
                // 加载启用的仪器列表
                _table = _bllLabInstrument.GetList("Status=1").Tables[0];
                DataColumn col = new DataColumn("IsCheck", System.Type.GetType("System.Boolean"));
                col.DefaultValue = false;
                col.ReadOnly = false;
                _table.Columns.Add(col);
                _cm = (CurrencyManager)BindingContext[_table, ""];
                myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = _table));
                AddLog("系统", $"加载了 {_table.Rows.Count} 个可用设备");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"加载设备列表失败：{ex.Message}");
            }
        }

        private void InitializeTimer()
        {
            _uiUpdateTimer = new System.Windows.Forms.Timer();
            _uiUpdateTimer.Interval = 100;
            _uiUpdateTimer.Tick += UiUpdateTimer_Tick;
            _uiUpdateTimer.Start();
        }

        private void UiUpdateTimer_Tick(object sender, EventArgs e)
        {
            // 处理消息队列，更新UI
            int processedCount = 0;
            while (_messageQueue.TryDequeue(out LogMessage message) && processedCount < 50)
            {
                AppendLogToUI(message);
                processedCount++;
            }

            // 更新状态显示
            UpdateStatusDisplay();
        }

        private void AppendLogToUI(LogMessage message)
        {
            if (ListMessages.InvokeRequired)
            {
                ListMessages.Invoke(new Action<LogMessage>(AppendLogToUI), message);
                return;
            }

            string logText = $"[{message.Timestamp:HH:mm:ss.fff}] [{message.Source}] {message.Message}\r\n";
            ListMessages.Items.Insert(0, logText);
        }

        private void UpdateStatusDisplay()
        {
            int runningCount = _connectorStatus.Count(kv => kv.Value);
            int totalCount = _connectorStatus.Count;

            LblStatus.Text = $"运行状态：{runningCount}/{totalCount} 个设备在线";

            // 更新按钮状态
            BtnStart.Enabled = totalCount == 0 || runningCount < totalCount;
            BtnStop.Enabled = runningCount > 0;
        }

        private void AddLog(string source, string message)
        {
            _messageQueue.Enqueue(new LogMessage
            {
                Timestamp = DateTime.Now,
                Source = source,
                Message = message
            });
        }

        private void StartSelectedInstruments()
        {
            try
            {
                var selectedInstruments = GetSelectedInstruments();
                if (selectedInstruments.Count == 0)
                {
                    MessageBox.Show("请至少选择一个设备！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int startedCount = 0;
                foreach (var instrument in selectedInstruments)
                {
                    if (!_connectorStatus.ContainsKey(instrument.InstrumentId) || !_connectorStatus[instrument.InstrumentId])
                    {
                        if (StartInstrument(instrument))
                        {
                            startedCount++;
                        }
                    }
                }

                AddLog("系统", $"启动了 {startedCount} 个设备连接器");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"启动设备失败：{ex.Message}");
                MessageBox.Show($"启动设备失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void StopAllInstruments()
        {
            try
            {
                int stoppedCount = 0;
                var runningConnectors = _connectors.Where(kv => _connectorStatus.ContainsKey(kv.Key) && _connectorStatus[kv.Key]).ToList();

                foreach (var kv in runningConnectors)
                {
                    if (StopInstrument(kv.Key))
                    {
                        stoppedCount++;
                    }
                }

                AddLog("系统", $"停止了 {stoppedCount} 个设备连接器");
            }
            catch (Exception ex)
            {
                AddLog("系统", $"停止设备失败：{ex.Message}");
                MessageBox.Show($"停止设备失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private List<MdlLabInstrument> GetSelectedInstruments()
        {
            var selectedInstruments = new List<MdlLabInstrument>();

            foreach (DataRow row in _table.AsEnumerable().Where(p => p.Field<bool>("IsCheck") == true))
            {
                int instrumentId = Convert.ToInt32(row["InstrumentId"]);
                var instrument = _bllLabInstrument.GetModel(instrumentId);
                if (instrument != null)
                {
                    selectedInstruments.Add(instrument);
                }
            }
            return selectedInstruments;
        }

        private bool StartInstrument(MdlLabInstrument instrument)
        {
            try
            {
                // 检查连接类型是否已实现
                if (!Func.IsConnectionTypeImplemented((ConnType)instrument.ConnType))
                {
                    AddLog(instrument.InstrName, $"连接类型 {Func.GetConnectionTypeDescription((ConnType)instrument.ConnType)} 尚未实现");
                    return false;
                }

                // 创建连接器配置
                var config = CreateLabInstrumentConfig(instrument);

                // 使用工厂创建连接器
                var connector = ConnectorFactory.CreateConnector(config);
                if (connector == null)
                {
                    AddLog(instrument.InstrName, "创建连接器失败");
                    return false;
                }

                // 订阅事件
                connector.DataReceived += OnDataReceived;
                connector.StatusChanged += OnStatusChanged;
                connector.ErrorOccurred += OnErrorOccurred;

                // 启动连接器
                connector.Start();

                _connectors[instrument.InstrumentId] = connector;
                _connectorStatus[instrument.InstrumentId] = true;

                AddLog(instrument.InstrName, "连接器启动成功");
                return true;
            }
            catch (Exception ex)
            {
                AddLog(instrument.InstrName, $"启动失败：{ex.Message}");
                return false;
            }
        }

        private bool StopInstrument(int instrumentId)
        {
            try
            {
                if (_connectors.ContainsKey(instrumentId))
                {
                    var connector = _connectors[instrumentId];
                    var instrument = _bllLabInstrument.GetModel(instrumentId);
                    string instrumentName = instrument?.InstrName ?? $"设备{instrumentId}";

                    // 取消事件订阅
                    connector.DataReceived -= OnDataReceived;
                    connector.StatusChanged -= OnStatusChanged;
                    connector.ErrorOccurred -= OnErrorOccurred;

                    // 停止连接器
                    connector.Stop();
                    connector.Dispose();

                    _connectors.Remove(instrumentId);
                    _connectorStatus[instrumentId] = false;

                    AddLog(instrumentName, "连接器已停止");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                AddLog($"设备{instrumentId}", $"停止失败：{ex.Message}");
                return false;
            }
        }

        private LabInstrument CreateLabInstrumentConfig(MdlLabInstrument instrument)
        {
            return new LabInstrument
            {
                InstrumentId = instrument.InstrumentId,
                InstrCode = instrument.InstrCode,
                InstrName = instrument.InstrName,
                ConnType = (ConnType)instrument.ConnType,
                MsgFormatType = (MsgFormatType)instrument.MsgFormatType,
                ComPort = instrument.ComPort,
                BaudRate = instrument.BaudRate,
                Parity = instrument.Parity?.ToString().FirstOrDefault(),
                DataBits = Common.ConvertObject.ObjToInt(instrument.DataBits),
                StopBits = Common.ConvertObject.ObjToInt(instrument.StopBits),
                IpHost = instrument.IpHost,
                IpPort = instrument.IpPort,
                KeepAliveSec = instrument.KeepAliveSec,
                FilePath = instrument.FilePath,
                DbConnStr = instrument.DbConnStr,
                ApiBaseUrl = instrument.ApiBaseUrl,
                ApiAuthToken = instrument.ApiAuthToken,
                ProtocolType = instrument.ProtocolType,
                MsgHeaderFormatType = (MsgFormatType)instrument.MsgHeaderFormatType,
                MsgHeader = instrument.MsgHeader,
                MsgTail = instrument.MsgTail,
                AckSuccess = instrument.AckSuccess,
                AckFailure = instrument.AckFailure,
                DriverVersion = instrument.DriverVersion,
                Status = instrument.Status
            };
        }

        #endregion

        #region 连接器事件处理

        private void OnDataReceived(object sender, DataReceivedEventArgs e)
        {
            var instrument = _bllLabInstrument.GetModel(e.InstrumentId);
            string instrumentName = instrument?.InstrName ?? $"设备{e.InstrumentId}";

            AddLog($"{instrumentName}[接收]", e.RawData);

            // 保存原始报文到数据库
            Task.Run(() => SaveRawMessage(e.InstrumentId, e.RawData, "接收"));
        }

        private void OnStatusChanged(object sender, StatusChangedEventArgs e)
        {
            var instrument = _bllLabInstrument.GetModel(e.InstrumentId);
            string instrumentName = instrument?.InstrName ?? $"设备{e.InstrumentId}";

            string statusText = GetStatusText(e.NewStatus);
            if (!string.IsNullOrEmpty(e.Reason))
            {
                statusText += $" - {e.Reason}";
            }

            AddLog($"{instrumentName}[状态]", statusText);

            // 更新连接状态
            _connectorStatus[e.InstrumentId] = e.NewStatus == ConnectorStatus.Running;
        }

        private void OnErrorOccurred(object sender, ErrorOccurredEventArgs e)
        {
            var instrument = _bllLabInstrument.GetModel(e.InstrumentId);
            string instrumentName = instrument?.InstrName ?? $"设备{e.InstrumentId}";

            AddLog($"{instrumentName}[错误]", $"错误代码:{e.ErrorCode} - {e.ErrorMessage}");

            // 错误时标记为离线
            _connectorStatus[e.InstrumentId] = false;
        }

        private string GetStatusText(ConnectorStatus status)
        {
            switch (status)
            {
                case ConnectorStatus.Stopped: return "已停止";
                case ConnectorStatus.Starting: return "正在启动";
                case ConnectorStatus.Running: return "运行中";
                case ConnectorStatus.Error: return "发生错误";
                case ConnectorStatus.Disabled: return "已禁用";
                case ConnectorStatus.Stopping: return "正在停止";
                default: return status.ToString();
            }
        }

        private void SaveRawMessage(int instrumentId, string data, string direction)
        {
            try
            {
                var rawMsg = new MdlLabInstrRawMsg
                {
                    InstrumentId = instrumentId,
                    RawText = data,
                    AcquiredAt = DateTime.Now,
                    ParsedFlag = false
                };

                _bllLabInstrRawMsg.Add(rawMsg);
            }
            catch (Exception ex)
            {
                AddLog("系统", $"保存原始报文失败：{ex.Message}");
            }
        }

        #endregion

        #region 控件事件

        private void BtnStart_Click(object sender, EventArgs e)
        {
            StartSelectedInstruments();
        }

        private void BtnStop_Click(object sender, EventArgs e)
        {
            StopAllInstruments();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            ListMessages.Items.Clear();
            AddLog("系统", "消息已清空");
        }
        private void CmdSelectAll_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            foreach (DataRow row in _table.Rows)
            {
                row["IsCheck"] = true;
            }
        }

        private void CmdSelectNone_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            foreach (DataRow row in _table.Rows)
            {
                row["IsCheck"] = false;
            }
        }
        private void MainConnecter_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 停止所有连接器
            StopAllInstruments();

            // 停止定时器
            _uiUpdateTimer?.Stop();
            _uiUpdateTimer?.Dispose();
        }

        #endregion
    }

}
