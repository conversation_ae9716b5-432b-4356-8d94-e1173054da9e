﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictDepartment.cs
*
* 功 能： N/A
* 类 名： DalDictDepartment
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:12   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictDepartment
	/// </summary>
	public partial class DalDictDepartment : IDalDictDepartment
	{
		public DalDictDepartment()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return Common.WinFormVar.Var.DbHelper.GetMaxID("DeptId", "DictDepartment");
		}
		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = (string)(Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(DeptCode) FROM DictDepartment where DeptCode NOT LIKE '%[^0-9]%' And LEN(DeptCode)=" + length, length));
			return max;
		}
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int DeptId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictDepartment");
			strSql.Append(" where DeptId=@DeptId");
			SqlParameter[] parameters = {
					new SqlParameter("@DeptId", SqlDbType.Int,4)
			};
			parameters[0].Value = DeptId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.MdlDictDepartment model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictDepartment(");
			strSql.Append("DeptCode,DeptName,HisDeptCode,PinYin,ParentDeptId,DeptType,Description,Status,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@DeptCode,@DeptName,@HisDeptCode,@PinYin,@ParentDeptId,@DeptType,@Description,@Status,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@DeptCode", SqlDbType.NVarChar,20),
					new SqlParameter("@DeptName", SqlDbType.NVarChar,100),
					new SqlParameter("@HisDeptCode", SqlDbType.NVarChar,200),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@ParentDeptId", SqlDbType.Int,4),
					new SqlParameter("@DeptType", SqlDbType.NVarChar,20),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.DeptCode;
			parameters[1].Value = model.DeptName;
			parameters[2].Value = model.HisDeptCode;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = Common.Tools.IsValueNull(model.ParentDeptId);
			parameters[5].Value = model.DeptType;
			parameters[6].Value = model.Description;
			parameters[7].Value = model.Status;
			parameters[8].Value = model.CreatedAt;
			parameters[9].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictDepartment model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictDepartment set ");
			strSql.Append("DeptCode=@DeptCode,");
			strSql.Append("DeptName=@DeptName,");
			strSql.Append("HisDeptCode=@HisDeptCode,");
			strSql.Append("PinYin=@PinYin,");
			strSql.Append("ParentDeptId=@ParentDeptId,");
			strSql.Append("DeptType=@DeptType,");
			strSql.Append("Description=@Description,");
			strSql.Append("Status=@Status,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where DeptId=@DeptId");
			SqlParameter[] parameters = {
					new SqlParameter("@DeptCode", SqlDbType.NVarChar,20),
					new SqlParameter("@DeptName", SqlDbType.NVarChar,100),
					new SqlParameter("@HisDeptCode", SqlDbType.NVarChar,200),
					new SqlParameter("@PinYin", SqlDbType.NVarChar,100),
					new SqlParameter("@ParentDeptId", SqlDbType.Int,4),
					new SqlParameter("@DeptType", SqlDbType.NVarChar,20),
					new SqlParameter("@Description", SqlDbType.NVarChar,200),
					new SqlParameter("@Status", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@DeptId", SqlDbType.Int,4)};
			parameters[0].Value = model.DeptCode;
			parameters[1].Value = model.DeptName;
			parameters[2].Value = model.HisDeptCode;
			parameters[3].Value = model.PinYin;
			parameters[4].Value = Common.Tools.IsValueNull(model.ParentDeptId);
			parameters[5].Value = model.DeptType;
			parameters[6].Value = model.Description;
			parameters[7].Value = model.Status;
			parameters[8].Value = model.CreatedAt;
			parameters[9].Value = model.UpdatedAt;
			parameters[10].Value = model.DeptId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int DeptId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictDepartment ");
			strSql.Append(" where DeptId=@DeptId");
			SqlParameter[] parameters = {
					new SqlParameter("@DeptId", SqlDbType.Int,4)
			};
			parameters[0].Value = DeptId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string DeptIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictDepartment ");
			strSql.Append(" where DeptId in (" + DeptIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictDepartment GetModel(int DeptId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 DeptId,DeptCode,DeptName,HisDeptCode,PinYin,ParentDeptId,DeptType,Description,Status,CreatedAt,UpdatedAt from DictDepartment ");
			strSql.Append(" where DeptId=@DeptId");
			SqlParameter[] parameters = {
					new SqlParameter("@DeptId", SqlDbType.Int,4)
			};
			parameters[0].Value = DeptId;

			Model.MdlDictDepartment model = new Model.MdlDictDepartment();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictDepartment DataRowToModel(DataRow row)
		{
			Model.MdlDictDepartment model = new Model.MdlDictDepartment();
			if (row != null)
			{
				if (row["DeptId"] != null && row["DeptId"].ToString() != "")
				{
					model.DeptId = int.Parse(row["DeptId"].ToString());
				}
				if (row["DeptCode"] != null)
				{
					model.DeptCode = row["DeptCode"].ToString();
				}
				if (row["DeptName"] != null)
				{
					model.DeptName = row["DeptName"].ToString();
				}
				if (row["HisDeptCode"] != null)
				{
					model.HisDeptCode = row["HisDeptCode"].ToString();
				}
				if (row["PinYin"] != null)
				{
					model.PinYin = row["PinYin"].ToString();
				}
				if (row["ParentDeptId"] != null && row["ParentDeptId"].ToString() != "")
				{
					model.ParentDeptId = int.Parse(row["ParentDeptId"].ToString());
				}
				if (row["DeptType"] != null)
				{
					model.DeptType = row["DeptType"].ToString();
				}
				if (row["Description"] != null)
				{
					model.Description = row["Description"].ToString();
				}
				if (row["Status"] != null && row["Status"].ToString() != "")
				{
					if ((row["Status"].ToString() == "1") || (row["Status"].ToString().ToLower() == "true"))
					{
						model.Status = true;
					}
					else
					{
						model.Status = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DeptId,DeptCode,DeptName,HisDeptCode,PinYin,ParentDeptId,DeptType,Description,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictDepartment ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" DeptId,DeptCode,DeptName,HisDeptCode,PinYin,ParentDeptId,DeptType,Description,Status,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictDepartment ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictDepartment ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.DeptId desc");
			}
			strSql.Append(")AS Row, T.*  from DictDepartment T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictDepartment";
			parameters[1].Value = "DeptId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

