using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZTLisEnum;
using Model;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// OBR段解析结果
    /// </summary>
    public class ObrInfo
    {
        public string SpecimenId { get; set; }
        public DateTime? CollectionTime { get; set; }
    }

    /// <summary>
    /// HL7协议解析器
    /// 支持HL7 v2.x格式的检验结果报文解析
    /// </summary>
    public class Hl7Parser : BaseParser
    {
        /// <summary>
        /// 解析器名称
        /// </summary>
        public override string Name
        {
            get { return "HL7协议解析器"; }
        }

        /// <summary>
        /// 支持的协议类型
        /// </summary>
        public override ProtocolType SupportedProtocol
        {
            get { return ProtocolType.HL7; }
        }

        // HL7字段分隔符
        private const char FIELD_SEPARATOR = '|';
        private const char COMPONENT_SEPARATOR = '^';
        private const char REPETITION_SEPARATOR = '~';
        private const char ESCAPE_CHARACTER = '\\';
        private const char SUBCOMPONENT_SEPARATOR = '&';

        /// <summary>
        /// 验证HL7报文格式
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <returns>是否有效</returns>
        public override bool ValidateMessage(string rawMessage)
        {
            if (string.IsNullOrEmpty(rawMessage))
                return false;

            // HL7报文必须以MSH开头
            if (!rawMessage.StartsWith("MSH"))
                return false;

            // 检查是否包含必要的分隔符
            if (!rawMessage.Contains(FIELD_SEPARATOR.ToString()))
                return false;

            // 检查MSH段的基本格式
            var lines = rawMessage.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            if (lines.Length == 0)
                return false;

            var mshLine = lines[0];
            var mshFields = mshLine.Split(FIELD_SEPARATOR);

            // MSH段至少应该有12个字段
            if (mshFields.Length < 12)
                return false;

            return true;
        }

        /// <summary>
        /// 解析HL7报文
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <param name="instrumentId">仪器ID</param>
        /// <returns>解析结果列表</returns>
        protected override async Task<List<MdlLabInstrResultQ>> ParseMessageAsync(string rawMessage, int instrumentId)
        {
            var results = new List<MdlLabInstrResultQ>();

            try
            {
                WriteLog($"开始解析HL7报文，长度: {rawMessage.Length}");

                // 按行分割报文
                var lines = rawMessage.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                string patientId = null;
                string specimenId = null;
                DateTime? collectionTime = null;

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line))
                        continue;

                    var segmentType = line.Substring(0, 3);

                    switch (segmentType)
                    {
                        case "MSH":
                            // 消息头段 - 可以获取发送时间等信息
                            ParseMshSegment(line);
                            break;

                        case "PID":
                            // 患者信息段 - 获取患者ID
                            patientId = ParsePidSegment(line);
                            break;

                        case "OBR":
                            // 观察请求段 - 获取标本信息
                            var obrInfo = ParseObrSegment(line);
                            specimenId = obrInfo.SpecimenId;
                            collectionTime = obrInfo.CollectionTime;
                            break;

                        case "OBX":
                            // 观察结果段 - 解析检验结果
                            var result = ParseObxSegment(line, instrumentId, patientId ?? specimenId, collectionTime);
                            if (result != null)
                            {
                                results.Add(result);
                            }
                            break;

                        case "NTE":
                            // 注释段 - 暂时忽略
                            break;

                        default:
                            WriteLog($"未处理的段类型: {segmentType}");
                            break;
                    }
                }

                WriteLog($"HL7解析完成，共解析出 {results.Count} 条结果");
            }
            catch (Exception ex)
            {
                WriteLog($"HL7解析异常: {ex.Message}");
                throw;
            }

            return results;
        }

        /// <summary>
        /// 解析MSH段（消息头）
        /// </summary>
        /// <param name="mshLine">MSH段内容</param>
        private void ParseMshSegment(string mshLine)
        {
            try
            {
                var fields = mshLine.Split(FIELD_SEPARATOR);
                if (fields.Length >= 7)
                {
                    var timestamp = fields[7]; // 时间戳
                    WriteLog($"消息时间戳: {timestamp}");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"解析MSH段异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析PID段（患者信息）
        /// </summary>
        /// <param name="pidLine">PID段内容</param>
        /// <returns>患者ID</returns>
        private string ParsePidSegment(string pidLine)
        {
            try
            {
                var fields = pidLine.Split(FIELD_SEPARATOR);
                if (fields.Length >= 4)
                {
                    var patientIdField = fields[3]; // 患者ID列表
                    var patientIds = patientIdField.Split(COMPONENT_SEPARATOR);
                    if (patientIds.Length > 0)
                    {
                        var patientId = patientIds[0];
                        WriteLog($"患者ID: {patientId}");
                        return patientId;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"解析PID段异常: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 解析OBR段（观察请求）
        /// </summary>
        /// <param name="obrLine">OBR段内容</param>
        /// <returns>标本信息</returns>
        private ObrInfo ParseObrSegment(string obrLine)
        {
            try
            {
                var fields = obrLine.Split(FIELD_SEPARATOR);
                string specimenId = null;
                DateTime? collectionTime = null;

                if (fields.Length >= 4)
                {
                    // 标本ID通常在OBR-3字段
                    var specimenIdField = fields[3];
                    var specimenIds = specimenIdField.Split(COMPONENT_SEPARATOR);
                    if (specimenIds.Length > 0)
                    {
                        specimenId = specimenIds[0];
                    }
                }

                if (fields.Length >= 8)
                {
                    // 采集时间通常在OBR-7字段
                    var collectionTimeStr = fields[7];
                    if (!string.IsNullOrEmpty(collectionTimeStr))
                    {
                        collectionTime = ParseHl7DateTime(collectionTimeStr);
                    }
                }

                WriteLog($"标本ID: {specimenId}, 采集时间: {collectionTime}");
                return new ObrInfo { SpecimenId = specimenId, CollectionTime = collectionTime };
            }
            catch (Exception ex)
            {
                WriteLog($"解析OBR段异常: {ex.Message}");
                return new ObrInfo { SpecimenId = null, CollectionTime = null };
            }
        }

        /// <summary>
        /// 解析OBX段（观察结果）
        /// </summary>
        /// <param name="obxLine">OBX段内容</param>
        /// <param name="instrumentId">仪器ID</param>
        /// <param name="specimenId">标本ID</param>
        /// <param name="collectionTime">采集时间</param>
        /// <returns>解析结果</returns>
        private MdlLabInstrResultQ ParseObxSegment(string obxLine, int instrumentId, string specimenId, DateTime? collectionTime)
        {
            try
            {
                var fields = obxLine.Split(FIELD_SEPARATOR);
                if (fields.Length < 6)
                {
                    WriteLog($"OBX段字段不足: {obxLine}");
                    return null;
                }

                // OBX-1: 序号
                var sequenceNumber = fields[1];

                // OBX-2: 值类型 (NM=数值, ST=字符串, CE=编码等)
                var valueType = fields[2];

                // OBX-3: 观察标识符 (项目代码)
                var observationId = fields[3];
                var observationComponents = observationId.Split(COMPONENT_SEPARATOR);
                var itemCode = observationComponents.Length > 0 ? observationComponents[0] : "";

                // OBX-5: 观察值 (结果值)
                var observationValue = fields[5];

                // OBX-6: 单位
                var units = fields.Length > 6 ? fields[6] : "";

                // OBX-7: 参考范围
                var referenceRange = fields.Length > 7 ? fields[7] : "";

                // OBX-8: 异常标志
                var abnormalFlags = fields.Length > 8 ? fields[8] : "";

                // OBX-14: 观察时间
                DateTime? observationTime = collectionTime;
                if (fields.Length > 14 && !string.IsNullOrEmpty(fields[14]))
                {
                    observationTime = ParseHl7DateTime(fields[14]);
                }

                // 处理结果值，可能包含单位
                var resultValue = observationValue;
                if (!string.IsNullOrEmpty(units))
                {
                    resultValue = $"{observationValue} {units}";
                }

                var result = CreateResult(
                    instrumentId,
                    specimenId ?? "UNKNOWN",
                    itemCode,
                    resultValue,
                    abnormalFlags,
                    0, // resultType: 0=检验结果
                    observationTime
                );

                WriteLog($"解析OBX结果: 项目={itemCode}, 结果={resultValue}, 标本={specimenId}");
                return result;
            }
            catch (Exception ex)
            {
                WriteLog($"解析OBX段异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 解析HL7日期时间格式
        /// </summary>
        /// <param name="hl7DateTime">HL7日期时间字符串</param>
        /// <returns>DateTime对象</returns>
        private DateTime? ParseHl7DateTime(string hl7DateTime)
        {
            if (string.IsNullOrEmpty(hl7DateTime))
                return null;

            try
            {
                // HL7日期时间格式: YYYYMMDDHHMMSS[.SSSS][+/-ZZZZ]
                // 移除时区信息
                var dateTimeStr = hl7DateTime;
                if (dateTimeStr.Contains("+") || dateTimeStr.Contains("-"))
                {
                    var tzIndex = Math.Max(dateTimeStr.LastIndexOf('+'), dateTimeStr.LastIndexOf('-'));
                    if (tzIndex > 8) // 确保不是日期部分的负号
                    {
                        dateTimeStr = dateTimeStr.Substring(0, tzIndex);
                    }
                }

                // 移除小数秒
                if (dateTimeStr.Contains("."))
                {
                    dateTimeStr = dateTimeStr.Substring(0, dateTimeStr.IndexOf('.'));
                }

                // 根据长度解析不同格式
                DateTime result;
                switch (dateTimeStr.Length)
                {
                    case 8: // YYYYMMDD
                        result = DateTime.ParseExact(dateTimeStr, "yyyyMMdd", null);
                        break;
                    case 12: // YYYYMMDDHHMM
                        result = DateTime.ParseExact(dateTimeStr, "yyyyMMddHHmm", null);
                        break;
                    case 14: // YYYYMMDDHHMMSS
                        result = DateTime.ParseExact(dateTimeStr, "yyyyMMddHHmmss", null);
                        break;
                    default:
                        // 尝试标准格式解析
                        if (DateTime.TryParse(hl7DateTime, out result))
                        {
                            return result;
                        }
                        WriteLog($"无法解析日期时间格式: {hl7DateTime}");
                        return null;
                }

                return result;
            }
            catch (Exception ex)
            {
                WriteLog($"解析HL7日期时间异常: {ex.Message}, 输入: {hl7DateTime}");
                return null;
            }
        }
    }
}