﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using C1.C1Excel;
using C1.Win.C1FlexGrid.Classic;
using C1.Win.C1TrueDBGrid;

namespace CustomControl
{
    public partial class MyGrid : UserControl
    {
        private bool resetflag;

        public MyGrid()
        {
            InitializeComponent();
        }

        #region 公共函数
        /// <summary>
        /// 
        /// </summary>
        /// <param name="V_标题"></param>
        /// <param name="V_字段"></param>
        /// <param name="V_长度"></param>
        /// <param name="V_水平"></param>
        /// <param name="V_格式"></param>
        /// <param name="V_可编辑"></param>
        /// <param name="frozen">冻结列</param>
        public void Init_Column(string V_标题, string V_字段, int V_长度, string V_水平, string V_格式, bool V_可编辑, bool frozen = false, int colIndex = -1)
        {
            //字段付值
            C1.Win.C1TrueDBGrid.C1DataColumn My_C = new C1.Win.C1TrueDBGrid.C1DataColumn();
            if (V_格式.ToUpper() == "CHECK")
            {
                My_C.ValueItems.Translate = true;
                My_C.ValueItems.CycleOnClick = true;
                My_C.ValueItems.Validate = true;
                My_C.ValueItems.Presentation = PresentationEnum.CheckBox;
                My_C.ValueItems.Values.Clear();
                My_C.ValueItems.Values.Add(new C1.Win.C1TrueDBGrid.ValueItem("False", false));
                My_C.ValueItems.Values.Add(new C1.Win.C1TrueDBGrid.ValueItem("True", true));
            }
            else if (V_格式.ToUpper() == "COMBOBOX")
            {
                My_C.ValueItems.Translate = true;
                My_C.ValueItems.Validate = true;

                My_C.ValueItems.Presentation = PresentationEnum.ComboBox;
                My_C.ValueItems.Values.Clear();
            }
            else if (V_格式.ToUpper() != "BUTTON" && V_格式.ToUpper() != "CUSTOMBUTTON" && V_格式.ToUpper() != "COMBOBOX")
            {
                if (!string.IsNullOrEmpty(V_格式))
                    My_C.NumberFormat = V_格式;
            }
            My_C.Caption = V_标题.Trim();
            My_C.DataField = V_字段.Trim();
            if (colIndex >= 0)
            {
                int index = c1TrueDBGrid1.Columns.IndexOf(V_字段);
                if (index >= 0)
                {
                    c1TrueDBGrid1.Columns.RemoveAt(index);
                }
                c1TrueDBGrid1.Columns.Insert(colIndex, My_C);
            }
            else
            {
                c1TrueDBGrid1.Columns.Add(My_C);
            }

            C1.Win.C1TrueDBGrid.C1DisplayColumn My_D;
            My_D = c1TrueDBGrid1.Splits[0].DisplayColumns[V_字段];

            if (colIndex >= 0)
            {
                int index = c1TrueDBGrid1.Splits[0].DisplayColumns.IndexOf(My_D);
                if (index >= 0)
                {
                    c1TrueDBGrid1.Splits[0].DisplayColumns.RemoveAt(index);
                    c1TrueDBGrid1.Splits[0].DisplayColumns.Insert(colIndex, My_D);
                    c1TrueDBGrid1.Col = 0;
                }
            }
            if (V_格式.ToUpper() == "BUTTON")
            {
                My_D.ButtonAlways = true;
                My_D.Button = true;
                My_D.ButtonText = true;
                My_D.ButtonCustomStyle = true;
            }
            if (V_格式.ToUpper() == "CUSTOMBUTTON")
            {
                My_D.ButtonAlways = true;
                My_D.Button = true;
                My_D.ButtonCustomStyle = true;
            }
            if (V_格式.ToUpper() == "COMBOBOX")
            {
                My_D.DropDownList = true;
            }
            //字段显示
            if (V_长度 == 0)
            {
                My_D.Visible = false;
            }
            else
            {
                My_D.Visible = true;
            }
            My_D.Width = V_长度;
            //My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.General;
            My_D.Style.Trimming = StringTrimming.EllipsisCharacter;
            My_D.Style.Wrap = TextWrapping.NoWrap;


            if (V_可编辑 == true)
            {
                My_D.Locked = false;
                My_D.Style.BackColor = EditColor;
            }
            else
            {
                My_D.Locked = true;
                My_D.Style.BackColor = LockedColor;
                My_D.ShowReadOnlyAsDisabled = false;
            }

            My_D.Frozen = frozen;
            switch (V_水平)
            {
                case "左":
                    My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near;
                    My_D.FooterStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near;
                    My_D.GroupHeaderStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near;
                    break;
                case "中":
                    My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center;
                    My_D.FooterStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center;
                    My_D.GroupHeaderStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near;
                    break;
                case "右":
                    My_D.Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far;
                    My_D.FooterStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far;
                    My_D.GroupHeaderStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far;
                    break;
            }
            //Header
            My_D.HeadingStyle.Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.Raised;
            My_D.HeadingStyle.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center;

            My_D.HeadingStyle.Wrap = TextWrapping.WrapWithOverflow;

            if (V_长度 == 0)
            {
                My_D.Visible = false;
            }


        }

        public void Init_Grid()
        {
            this.Dock = DockStyle.Fill;
            c1TrueDBGrid1.BorderStyle = BorderStyle.FixedSingle;
            //清空
            c1TrueDBGrid1.Columns.Clear();
            c1TrueDBGrid1.ClearFields();
            //属性
            c1TrueDBGrid1.AllowUpdate = true;
            c1TrueDBGrid1.AllowDelete = true;
            c1TrueDBGrid1.AllowAddNew = false;
            c1TrueDBGrid1.AllowSort = true;
            c1TrueDBGrid1.AllowColSelect = false;
            c1TrueDBGrid1.AllowColMove = false;
            c1TrueDBGrid1.ScrollTips = true;
            c1TrueDBGrid1.ExtendRightColumn = true;

            c1TrueDBGrid1.BorderStyle = BorderStyle.Fixed3D;
            c1TrueDBGrid1.FlatStyle = C1.Win.C1TrueDBGrid.FlatModeEnum.Standard;

            //滚动条()
            c1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic;
            c1TrueDBGrid1.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic;

            //选择行
            c1TrueDBGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow;
            c1TrueDBGrid1.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.None;

            //行状态
            c1TrueDBGrid1.RowHeight = 20;
            c1TrueDBGrid1.RowDivider.Style = C1.Win.C1TrueDBGrid.LineStyleEnum.Single;
            c1TrueDBGrid1.Splits[0].ColumnCaptionHeight = 22;
            c1TrueDBGrid1.Splits[0].BorderStyle = Border3DStyle.SunkenInner;
            c1TrueDBGrid1.Splits[0].Style.ForeColor = Color.Black;
            c1TrueDBGrid1.Splits[0].Style.BackColor = Color.White;
            c1TrueDBGrid1.Splits[0].HighLightRowStyle.ForeColor = Color.Black;
            c1TrueDBGrid1.Splits[0].HighLightRowStyle.BackColor = Color.FromArgb(175, 238, 238);

            c1TrueDBGrid1.Splits[0].EditorStyle.ForeColor = Color.Black;
            c1TrueDBGrid1.Splits[0].EditorStyle.BackColor = Color.FromArgb(175, 238, 238);


            c1TrueDBGrid1.VisualStyle = C1.Win.C1TrueDBGrid.VisualStyle.Office2010Blue;

            c1TrueDBGrid1.FooterStyle.BackColor = Color.FromArgb(218, 231, 245);
            c1TrueDBGrid1.FooterStyle.Borders.BorderType = BorderTypeEnum.Raised;

            c1TrueDBGrid1.FooterStyle.Borders.Top = 1;
            c1TrueDBGrid1.FooterStyle.Borders.Bottom = 1;
            c1TrueDBGrid1.FooterStyle.Borders.Left = 1;
            c1TrueDBGrid1.FooterStyle.Borders.Right = 1;

            //Record
            c1TrueDBGrid1.RecordSelectorWidth = 10;
            c1TrueDBGrid1.RecordSelectorStyle.Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.InsetBevel;

            c1TrueDBGrid1.Font = new Font("宋体", (float)10.5, FontStyle.Regular);
            c1TrueDBGrid1.Style.VerticalAlignment = AlignVertEnum.Center;
            c1TrueDBGrid1.Style.HorizontalAlignment = AlignHorzEnum.Center;

            this.CanGroup = false;
            resetflag = false;
        }

        #endregion

        #region 私有函数

        private void unfoldComm_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.c1TrueDBGrid1.ExpandGroupRow(-1, true);
        }

        private void shrinkComm_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.c1TrueDBGrid1.CollapseGroupRow(-1);
        }

        private void ResetComm_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            if (System.IO.File.Exists(this.Xmlpath) == true)
            {
                System.IO.File.Delete(this.Xmlpath);
            }
            resetflag = true;
            MessageBox.Show("样式重置成功，请关闭窗体重新打开再次查看！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Comm_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            e.CallerLink.Command.Checked = !e.CallerLink.Command.Checked;
            c1TrueDBGrid1.Splits[0].DisplayColumns[e.CallerLink.Command.Name].Visible = e.CallerLink.Command.Checked;
            this.SaveLayout(Xmlpath);
        }

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if ((this.CanCustomCol == true || this.c1TrueDBGrid1.AllowColMove) && resetflag == false && !string.IsNullOrEmpty(this.Xmlpath))
            {
                this.SaveLayout(Xmlpath);
            }
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion

        #region 公共属性

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1Command.C1ContextMenu GridContextMenu
        {
            get { return c1ContextMenu1; }
            set
            {
                c1ContextMenu1 = value;
            }
        }
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int RowHeight
        {
            get { return c1TrueDBGrid1.RowHeight; }
            set
            {
                c1TrueDBGrid1.RowHeight = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool AllowUpdate
        {
            get { return c1TrueDBGrid1.AllowUpdate; }
            set
            {
                c1TrueDBGrid1.AllowUpdate = value;
                if (value == true)
                {
                    c1TrueDBGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRowRaiseCell;
                    Invalidate();
                }
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object this[int row]
        {
            get { return c1TrueDBGrid1[row]; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object this[int row, int col]
        {
            get { return c1TrueDBGrid1[row, col]; }
            set
            {
                c1TrueDBGrid1[row, col] = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object this[int row, string columnName]
        {
            get
            {
                return c1TrueDBGrid1[row, columnName];
            }
            set
            {
                c1TrueDBGrid1[row, columnName] = value;
            }
        }

        public bool ColumnHeaders
        {
            get { return c1TrueDBGrid1.ColumnHeaders; }
            set { c1TrueDBGrid1.ColumnHeaders = value; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool AllowSort
        {
            get { return c1TrueDBGrid1.AllowSort; }
            set { c1TrueDBGrid1.AllowSort = value; }
        }

        public bool AllowColMove
        {
            get { return c1TrueDBGrid1.AllowColMove; }
            set
            {
                c1TrueDBGrid1.AllowColMove = value;
                if (value == true)
                {
                    this.LoadLayout(m_Xmlpath);
                }
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool AllowAddNew
        {
            get { return c1TrueDBGrid1.AllowAddNew; }
            set { c1TrueDBGrid1.AllowAddNew = value; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool AllowDelete
        {
            get { return c1TrueDBGrid1.AllowDelete; }
            set { c1TrueDBGrid1.AllowDelete = value; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1TrueDBGrid.VisualStyle VisualStyle
        {
            get { return c1TrueDBGrid1.VisualStyle; }
            set
            {
                c1TrueDBGrid1.VisualStyle = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool ExtendRightColumn
        {
            get { return c1TrueDBGrid1.ExtendRightColumn; }
            set
            {
                c1TrueDBGrid1.ExtendRightColumn = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1DataColumnCollection Columns
        {
            get { return c1TrueDBGrid1.Columns; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int Row
        {
            get { return c1TrueDBGrid1.Row; }
            set
            {
                c1TrueDBGrid1.Row = value;
                Invalidate();
            }
        }
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int Bookmark
        {
            get { return c1TrueDBGrid1.Bookmark; }
            set
            {
                c1TrueDBGrid1.Bookmark = value;
                Invalidate();
            }
        }

        public C1.Win.C1TrueDBGrid.SelectedRowCollection SelectedRows
        {
            get { return c1TrueDBGrid1.SelectedRows; }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1TrueDBGrid.MultiSelectEnum MultiSelect
        {
            get { return c1TrueDBGrid1.MultiSelect; }
            set
            {
                c1TrueDBGrid1.MultiSelect = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public new BorderStyle BorderStyle
        {
            get { return c1TrueDBGrid1.BorderStyle; }
            set
            {
                c1TrueDBGrid1.BorderStyle = value;
                Invalidate();
            }
        }

        public DirectionAfterEnterEnum DirectionAfterEnter
        {
            get { return c1TrueDBGrid1.DirectionAfterEnter; }
            set
            {
                c1TrueDBGrid1.DirectionAfterEnter = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1TrueDBGrid.SplitCollection Splits
        {
            get { return c1TrueDBGrid1.Splits; }
        }

        public C1.Win.C1TrueDBGrid.MarqueeEnum MarqueeStyle
        {
            get { return c1TrueDBGrid1.MarqueeStyle; }
            set
            {
                c1TrueDBGrid1.MarqueeStyle = value;
                Invalidate();
            }
        }


        public bool FetchRowStyles
        {
            get { return c1TrueDBGrid1.FetchRowStyles; }
            set
            {
                c1TrueDBGrid1.FetchRowStyles = value;
                Invalidate();
            }
        }

        public int RowCount
        {
            get { return c1TrueDBGrid1.RowCount; }
        }

        public bool ColumnFooters
        {
            get { return c1TrueDBGrid1.ColumnFooters; }
            set
            {
                c1TrueDBGrid1.ColumnFooters = value;
                Invalidate();
            }
        }

        public C1.Win.C1TrueDBGrid.TabActionEnum TabAction
        {
            get { return c1TrueDBGrid1.TabAction; }
            set
            {
                c1TrueDBGrid1.TabAction = value;
            }
        }

        public bool CanGroup
        {
            set
            {
                if (value == true)
                {
                    c1TrueDBGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.GroupBy;
                    c1TrueDBGrid1.AllowDrag = true;
                    c1TrueDBGrid1.AllowColMove = true;
                    c1TrueDBGrid1.GroupByAreaVisible = true;
                    c1TrueDBGrid1.GroupedColumns.Clear();
                    //Drag a column header here to group by that column
                    c1TrueDBGrid1.GroupByCaption = "拖动某列到此处,可按该列分组";
                    unfoldComm.Visible = true;
                    shrinkComm.Visible = true;
                    c1CommandHolder1.SetC1ContextMenu(c1TrueDBGrid1, c1ContextMenu1);
                }
                else
                {
                    c1TrueDBGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
                    c1TrueDBGrid1.GroupByAreaVisible = false;
                    c1TrueDBGrid1.GroupedColumns.Clear();
                    unfoldComm.Visible = false;
                    shrinkComm.Visible = false;
                }
                Invalidate();
            }
        }

        private bool m_CanCustomCol;
        [Description("是否允许自定义列"), Browsable(true)]
        public bool CanCustomCol
        {
            get { return m_CanCustomCol; }
            set
            {
                m_CanCustomCol = value;
                if (m_CanCustomCol == true)
                {
                    this.LoadLayout(m_Xmlpath);
                    customComm.Visible = true;
                    ResetComm.Visible = true;
                    c1TrueDBGrid1.AllowColMove = true;
                    c1CommandHolder1.SetC1ContextMenu(c1TrueDBGrid1, c1ContextMenu1);
                    int i = 0;
                    c1ContextMenu1.CommandLinks.RemoveRange(4, c1ContextMenu1.CommandLinks.Count - 4);
                    foreach (C1.Win.C1TrueDBGrid.C1DisplayColumn Col in c1TrueDBGrid1.Splits[0].DisplayColumns)
                    {
                        C1.Win.C1Command.C1CommandLink clink = new C1.Win.C1Command.C1CommandLink();
                        C1.Win.C1Command.C1Command command = new C1.Win.C1Command.C1Command();
                        command.Click += Comm_Click;
                        clink.Command = command;
                        clink.Text = Col.Name;
                        command.Name = Col.DataColumn.DataField;
                        if (Col.Visible == true)
                        {
                            command.Checked = true;
                        }
                        else
                        {
                            command.Checked = false;
                        }
                        c1ContextMenu1.CommandLinks.Add(clink);

                        if (i == 0)
                        {
                            clink.Delimiter = true;
                        }
                        i++;
                    }

                }
                else
                {
                    customComm.Visible = false;
                    ResetComm.Visible = false;
                }
                Invalidate();
            }
        }

        public int Col
        {
            get { return c1TrueDBGrid1.Col; }
            set
            {
                c1TrueDBGrid1.Col = value;
                Invalidate();
            }
        }

        public Object DataTable
        {
            set
            {
                if (value is DataTable && c1TrueDBGrid1.DataSource is DataTable && c1TrueDBGrid1.DataSource != null)
                {
                    ((DataTable)value).DefaultView.Sort = ((DataTable)c1TrueDBGrid1.DataSource).DefaultView.Sort;
                }
                c1TrueDBGrid1.SetDataBinding(value, "", true);
            }
        }

        private string m_Xmlpath;
        public string Xmlpath
        {
            get { return m_Xmlpath; }
            set
            {
                m_Xmlpath = value;
            }
        }

        public object DataSource
        {
            get { return c1TrueDBGrid1.DataSource; }
            set
            {
                c1TrueDBGrid1.DataSource = value;
            }
        }

        public string Caption
        {
            get { return c1TrueDBGrid1.Caption; }
            set
            {
                c1TrueDBGrid1.Caption = value;
            }
        }

        public string DataMember
        {
            get { return c1TrueDBGrid1.DataMember; }
            set
            {
                c1TrueDBGrid1.DataMember = value;
            }
        }

        public C1.Win.C1TrueDBGrid.GroupedColumnCollection GroupedColumns
        {
            get { return c1TrueDBGrid1.GroupedColumns; }
        }

        public bool GroupByAreaVisible
        {
            get { return c1TrueDBGrid1.GroupByAreaVisible; }
            set
            {
                c1TrueDBGrid1.GroupByAreaVisible = value;
                Invalidate();
            }
        }

        public Color EditColor
        {
            get { return SystemColors.Info; }
        }

        public Color LockedColor
        {
            get { return Color.White; }
        }

        public Control Editor
        {
            get { return c1TrueDBGrid1.Editor; }
        }

        public C1TrueDBGrid ChildGrid
        {
            get { return c1TrueDBGrid1.ChildGrid; }
            set
            {
                c1TrueDBGrid1.ChildGrid = value;

            }
        }
        public C1.Win.C1TrueDBGrid.PrintInfo PrintInfo
        {
            get { return c1TrueDBGrid1.PrintInfo; }
        }

        public C1.Win.C1TrueDBGrid.PrintPreviewWinSettings PreviewInfo
        {
            get { return c1TrueDBGrid1.PreviewInfo; }
        }

        public bool FilterBar
        {
            get { return c1TrueDBGrid1.FilterBar; }
            set
            {
                c1TrueDBGrid1.FilterBar = value;

            }
        }

        public bool AllowFilter
        {
            get { return c1TrueDBGrid1.AllowFilter; }
            set
            {
                c1TrueDBGrid1.AllowFilter = value;

            }
        }
        public DataViewEnum DataView
        {
            get { return c1TrueDBGrid1.DataView; }
            set
            {
                c1TrueDBGrid1.DataView = value;

            }
        }
        #endregion

        #region 公开方法

        public new void Select()
        {
            c1TrueDBGrid1.Select();
        }

        public new void Focus()
        {
            c1TrueDBGrid1.Focus();
        }

        public void MoveFirst()
        {
            c1TrueDBGrid1.MoveFirst();
        }

        public void MovePrevious()
        {
            c1TrueDBGrid1.MovePrevious();
        }

        public void MoveNext()
        {
            c1TrueDBGrid1.MoveNext();
        }

        public void MoveLast()
        {
            c1TrueDBGrid1.MoveLast();
        }

        public void Delete()
        {
            c1TrueDBGrid1.Delete();
        }

        public void SetActiveCell(int _Row, int _Col)
        {
            c1TrueDBGrid1.SetActiveCell(_Row, _Col);
        }

        private void LoadLayout(string filename)
        {
            if (System.IO.File.Exists(filename) == true && System.IO.File.ReadAllBytes(filename).Length > 0)
            {
                c1TrueDBGrid1.LoadLayout(filename);
            }
        }

        //public void LoadLayout(System.IO.Stream stream)
        //{
        //    c1TrueDBGrid1.LoadLayout(stream);
        //}

        private void SaveLayout(string filename)
        {
            if (resetflag == false)
            {
                c1TrueDBGrid1.SaveLayout(filename);
            }

        }

        //public void SaveLayout(System.IO.Stream stream)
        //{
        //    c1TrueDBGrid1.SaveLayout(stream);
        //}

        public void UpdateData()
        {
            c1TrueDBGrid1.UpdateData();
        }

        public int RowBookmark(int row)
        {
            return c1TrueDBGrid1.RowBookmark(row);
        }
        public void SetDataBinding(object dataSource, string dataMember, bool holdFields)
        {
            c1TrueDBGrid1.SetDataBinding(dataSource, dataMember, holdFields);
        }

        public void ExpandGroupRow()
        {
            this.c1TrueDBGrid1.ExpandGroupRow(-1, true);
        }

        public void Clear()
        {
            c1TrueDBGrid1.Columns.Clear();
            c1TrueDBGrid1.ClearFields();
        }

        public void ExportToExcel(string filename)
        {
            c1TrueDBGrid1.ExportToExcel(filename);
        }

        public bool ExportExcel(string name, ref string filepath, bool isOpen)
        {
            if (this.RowCount == 0) return false;
            SaveFileDialog save = new SaveFileDialog();
            string filename = "";

            save.Filter = "Excel文件(.Xls)|*.Xls";
            save.FileName = name + DateTime.Now.ToString("yyMMdd") + ".Xls";

            if (filepath == "")
            {
                filename = save.FileName;
            }

            if (filepath == "")
            {
                if (save.ShowDialog() == DialogResult.Cancel)
                {
                    return false;
                }
                filepath = save.FileName.Replace(filename, "");
            }
            else
            {
                save.FileName = filepath + save.FileName;
            }

            save.AddExtension = true;

            int i;
            // step 1: create a new workbook
            C1XLBook book = new C1XLBook();
            // step 2: create styles for odd and even values
            XLStyle styleMain = new XLStyle(book);
            XLStyle styleHeader = new XLStyle(book);
            XLStyle styleGroupHeader = new XLStyle(book);

            // step 3: write content and styles into some cells
            XLSheet sheet = book.Sheets[0];

            int row = 0;
            // Pointer for looping through the rows
            int col = 0;
            int xcol = 0;
            //C1.Win.C1TrueDBGrid.C1DisplayColumn CurrentDColumn = default(C1.Win.C1TrueDBGrid.C1DisplayColumn);
            // Pointer for looping through the columns
            bool blnContinue = true;
            ArrayList arrayGroupedColumnHeaders = new ArrayList();

            XLCell cell = null;
            C1.Win.C1TrueDBGrid.GroupRow gr = null;
            decimal Sum;
            decimal MinMax;
            decimal Denominator;

            i = 0;
            while (i <= this.GroupedColumns.Count - 1)
            {
                arrayGroupedColumnHeaders.Add(string.Empty);
                i++;
            }



            foreach (C1DisplayColumn CurrentDColumn in this.Splits[0].DisplayColumns)
            {
                if ((this.Splits[0].DisplayColumns[col].Visible == true) && (this.Splits[0].DisplayColumns[col].Width != 0))
                {
                    cell = sheet[0, xcol];
                    sheet.Rows[0].Height = 500;
                    cell.Value = CurrentDColumn.DataColumn.Caption;

                    cell.Style = new C1.C1Excel.XLStyle(book);
                    //cell.Style.BackColor = Color.FromArgb(148, 221, 239)
                    cell.Style.ForeColor = Color.Black;
                    cell.Style.Font = new Font("宋体", 10, FontStyle.Bold);

                    cell.Style.BorderBottom = XLLineStyleEnum.Thin;
                    cell.Style.BorderTop = XLLineStyleEnum.Thin;
                    cell.Style.BorderLeft = XLLineStyleEnum.Thin;
                    cell.Style.BorderRight = XLLineStyleEnum.Thin;

                    //cell.Style = styleHeader
                    cell.Style.AlignHorz = (XLAlignHorzEnum)CurrentDColumn.HeadingStyle.HorizontalAlignment;
                    cell.Style.AlignVert = (XLAlignVertEnum)CurrentDColumn.HeadingStyle.VerticalAlignment;
                    xcol = xcol + 1;
                }
                col = col + 1;
            }
            int SelectedRowsOnly = this.SelectedRows.Count;
            if ((SelectedRowsOnly == 0) | (this.GroupedColumns.Count > 0))
            {
                //Rows
                row = 0;
                List<GroupRow> list = new List<GroupRow>();
                while (row <= (this.Splits[0].Rows.Count - 1))
                {
                    col = 0;
                    xcol = 0;

                    foreach (C1DisplayColumn CurrentDColumn in this.Splits[0].DisplayColumns)
                    {
                        if ((this.Splits[0].DisplayColumns[col].Visible == true) && (this.Splits[0].DisplayColumns[col].Width != 0))
                        {
                            cell = sheet[row + 1, xcol];
                            sheet.Rows[row + 1].Height = 400;
                            cell.Style = new C1.C1Excel.XLStyle(book);
                            cell.Style.Font = new Font("宋体", 9, FontStyle.Regular);
                            cell.Style.ForeColor = Color.Black;
                            cell.Style.BorderBottom = XLLineStyleEnum.Thin;
                            cell.Style.BorderTop = XLLineStyleEnum.Thin;
                            cell.Style.BorderLeft = XLLineStyleEnum.Thin;
                            cell.Style.BorderRight = XLLineStyleEnum.Thin;
                            cell.Style.AlignVert = XLAlignVertEnum.Center;
                            if (this.Splits[0].Rows[row].RowType == C1.Win.C1TrueDBGrid.RowTypeEnum.DataRow)
                            {
                                //Regular Data row
                                //cell.Style = styleMain
                                System.Data.DataRowView dr = (System.Data.DataRowView)this[this.RowBookmark(row)];
                                if (dr[CurrentDColumn.DataColumn.DataField] is DateTime)
                                {
                                    if (CurrentDColumn.DataColumn.NumberFormat != string.Empty && dr[CurrentDColumn.DataColumn.DataField] + "" != "")
                                    {
                                        cell.Value = DateTime.Parse(dr[CurrentDColumn.DataColumn.DataField] + "").ToString(CurrentDColumn.DataColumn.NumberFormat);
                                    }
                                    else
                                    {
                                        cell.Value = Convert.ToString(dr[CurrentDColumn.DataColumn.DataField]);
                                    }

                                }
                                else
                                {
                                    if (CurrentDColumn.DataColumn.NumberFormat != string.Empty & CurrentDColumn.DataColumn.NumberFormat != "Percent")
                                    {
                                        if (dr[CurrentDColumn.DataColumn.DataField] != string.Empty)
                                        {
                                            if (dr[CurrentDColumn.DataColumn.DataField] is decimal)
                                            {
                                                // cell.Style.Format = CurrentDColumn.DataColumn.NumberFormat;
                                                cell.Value = Convert.ToDecimal(dr[CurrentDColumn.DataColumn.DataField]);
                                            }
                                            else
                                            {
                                                cell.Value = dr[CurrentDColumn.DataColumn.DataField];
                                            }


                                        }
                                    }
                                    else
                                    {
                                        cell.Value = dr[CurrentDColumn.DataColumn.DataField];
                                    }
                                }
                                sheet.Columns[xcol].Width = this.Splits[0].DisplayColumns[col].Width * 18;
                                cell.Style.AlignHorz = (XLAlignHorzEnum)CurrentDColumn.Style.HorizontalAlignment;


                                if ((gr != null) & xcol == 0)
                                {
                                    i = 0;
                                    while (i <= gr.Level)
                                    {
                                        cell.Value = " " + cell.Value;
                                        cell.Style.AlignHorz = XLAlignHorzEnum.Left;
                                        i++;
                                    }
                                }
                            }
                            else
                            {
                                cell.Style = new C1.C1Excel.XLStyle(book);
                                cell.Style.BackColor = Color.FromArgb(218, 238, 243);
                                cell.Style.ForeColor = Color.Black;
                                cell.Style.Font = new Font("宋体", 9, FontStyle.Bold);

                                //Group Header row
                                if (xcol == 0)
                                {
                                    cell.Style.BorderBottom = XLLineStyleEnum.Thin;
                                    cell.Style.BorderTop = XLLineStyleEnum.Thin;
                                    cell.Style.BorderLeft = XLLineStyleEnum.Thin;
                                    // cell.Style.BorderRight = XLLineStyleEnum.Thin;
                                    gr = (GroupRow)this.Splits[0].Rows[row];
                                    list.Add(gr);
                                    cell.Value = (gr.GroupedText == string.Empty ? "No " + this.GroupedColumns[gr.Level].Caption : this.GroupedColumns[gr.Level].Caption + ": " + gr.GroupedText) + " (" + gr.Count + ")";

                                    i = 0;
                                    while (i <= gr.Level - 1)
                                    {
                                        cell.Value = "       " + cell.Value;
                                        i++;
                                    }

                                    cell.Style.AlignHorz = XLAlignHorzEnum.Left;
                                    cell.Style.AlignVert = XLAlignVertEnum.Center;
                                }
                                else
                                {
                                    cell.Style.BorderBottom = XLLineStyleEnum.Thin;
                                    cell.Style.BorderTop = XLLineStyleEnum.Thin;
                                    if (xcol > 1) cell.Style.BorderLeft = XLLineStyleEnum.Thin;
                                    cell.Style.BorderRight = XLLineStyleEnum.Thin;
                                    switch (this.Columns[CurrentDColumn.DataColumn.DataField].Aggregate)
                                    {
                                        case C1.Win.C1TrueDBGrid.AggregateEnum.Sum:
                                            Sum = 0;
                                            i = gr.StartIndex;
                                            while (i <= gr.EndIndex)
                                            {
                                                System.Data.DataRowView dr = (System.Data.DataRowView)this[i];
                                                if (!Convert.IsDBNull(dr[CurrentDColumn.DataColumn.DataField]))
                                                {
                                                    Sum += Convert.ToDecimal(dr[CurrentDColumn.DataColumn.DataField]);
                                                }
                                                i++;
                                            }

                                            cell.Value = Sum;
                                            break;
                                        case C1.Win.C1TrueDBGrid.AggregateEnum.Count:
                                            Sum = 0;
                                            i = gr.StartIndex;
                                            while (i <= gr.EndIndex)
                                            {
                                                Sum++;
                                                i++;
                                            }

                                            cell.Value = Sum;
                                            break;
                                        case C1.Win.C1TrueDBGrid.AggregateEnum.Average:
                                            Sum = 0;
                                            Denominator = 0;
                                            i = gr.StartIndex;
                                            while (i <= gr.EndIndex)
                                            {
                                                if (!Convert.IsDBNull(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i)))
                                                {
                                                    Sum += Convert.ToDecimal(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i));
                                                    Denominator += 1;
                                                }
                                                i++;
                                            }

                                            if (Denominator > 0)
                                            {
                                                if (Sum / Denominator == Math.Round(Sum / Denominator, 0))
                                                {
                                                    cell.Value = Sum / Denominator;
                                                }
                                                else if (Sum / Denominator == Math.Round(Sum / Denominator, 1))
                                                {
                                                    cell.Value = String.Format("##,###,##0.0", Sum / Denominator);
                                                }
                                                else if (Sum / Denominator == Math.Round(Sum / Denominator, 1))
                                                {
                                                    cell.Value = String.Format("##,###,##0.00", Sum / Denominator);
                                                }
                                                else
                                                {
                                                    cell.Value = String.Format("##,###,##0.000", Sum / Denominator);
                                                }
                                            }
                                            else
                                            {
                                                cell.Value = 0;
                                            }

                                            break;


                                        case C1.Win.C1TrueDBGrid.AggregateEnum.Min:
                                            MinMax = Convert.ToDecimal(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(gr.StartIndex));
                                            i = gr.StartIndex;
                                            while (i <= gr.EndIndex)
                                            {
                                                if (!Convert.IsDBNull(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i)))
                                                {
                                                    if ((Decimal)this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i) < MinMax)
                                                    {
                                                        MinMax = Convert.ToDecimal(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i));
                                                    }
                                                }
                                                i++;
                                            }

                                            cell.Value = MinMax;
                                            break;


                                        case C1.Win.C1TrueDBGrid.AggregateEnum.Max:
                                            MinMax = Convert.ToDecimal(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(gr.StartIndex));
                                            i = gr.StartIndex;
                                            while (i <= gr.EndIndex)
                                            {
                                                if (!Convert.IsDBNull(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i)))
                                                {
                                                    if (Convert.ToInt32(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i)) > MinMax)
                                                    {
                                                        MinMax = Convert.ToDecimal(this.Columns[CurrentDColumn.DataColumn.DataField].CellValue(i));
                                                    }
                                                }
                                                i++;
                                            }

                                            cell.Value = MinMax;
                                            break;


                                        case C1.Win.C1TrueDBGrid.AggregateEnum.Custom:
                                            if (gr.Level == 0)
                                            {
                                                cell.Value = ((decimal)(gr.EndIndex - gr.StartIndex + 1) / (decimal)this.RowCount * 100).ToString("0.00") + "%";
                                            }
                                            else
                                            {
                                                var parent = list.Where(p => (p.Level == gr.Level - 1 && p.StartIndex <= gr.StartIndex && p.EndIndex >= gr.EndIndex)).FirstOrDefault();
                                                cell.Value = ((decimal)(gr.EndIndex - gr.StartIndex + 1) / (decimal)parent.Count * 100).ToString("0.00") + "%";
                                            }
                                            break;
                                        case C1.Win.C1TrueDBGrid.AggregateEnum.None:
                                            break;
                                        default:
                                            cell.Value = this.Columns[CurrentDColumn.DataColumn.DataField].Aggregate.ToString();
                                            break;
                                    }
                                    cell.Style.AlignHorz = (XLAlignHorzEnum)CurrentDColumn.Style.HorizontalAlignment;
                                    cell.Style.AlignVert = XLAlignVertEnum.Center;
                                }
                            }
                            xcol = xcol + 1;
                        }
                        col = col + 1;
                    }
                    row++;
                }
            }
            else
            {
                //Rows
                row = 0;
                while (row <= (this.SelectedRows.Count - 1))
                {
                    col = 0;
                    xcol = 0;

                    foreach (C1DisplayColumn CurrentDColumn in this.Splits[0].DisplayColumns)
                    {
                        if ((this.Splits[0].DisplayColumns[col].Visible == true) && (this.Splits[0].DisplayColumns[col].Width != 0))
                        {
                            cell = sheet[row + 1, xcol];
                            if (CurrentDColumn.DataColumn.CellValue(this.SelectedRows[row]) is DateTime)
                            {
                                cell.Value = Convert.ToString(CurrentDColumn.DataColumn.CellText(this.SelectedRows[row]));
                            }
                            else
                            {
                                if (CurrentDColumn.DataColumn.NumberFormat != string.Empty & CurrentDColumn.DataColumn.NumberFormat != "Percent")
                                {
                                    if (CurrentDColumn.DataColumn.CellText(this.SelectedRows[row]) != string.Empty)
                                    {
                                        cell.Value = Convert.ToDecimal(CurrentDColumn.DataColumn.CellText(this.SelectedRows[row]));
                                    }
                                }
                                else
                                {
                                    cell.Value = CurrentDColumn.DataColumn.CellValue(this.SelectedRows[row]);
                                }
                            }
                            //cell.Style = styleMain
                            cell.Style = new C1.C1Excel.XLStyle(book);
                            cell.Style.Font = new Font("宋体", 9, FontStyle.Regular);
                            cell.Style.ForeColor = Color.Black;

                            sheet.Columns[xcol].Width = this.Splits[0].DisplayColumns[col].Width * 18;
                            cell.Style.AlignHorz = (XLAlignHorzEnum)CurrentDColumn.Style.HorizontalAlignment;
                            xcol = xcol + 1;
                        }
                        col = col + 1;
                    }
                    row++;
                }
            }

            //Footers
            col = 0;
            xcol = 0;
            //If SelectedRowsOnly = 0 And this.GroupedColumns.Count = 0 Then

            if (SelectedRowsOnly == 0)
            {
                foreach (C1DisplayColumn CurrentDColumn in this.Splits[0].DisplayColumns)
                {
                    if ((this.Splits[0].DisplayColumns[col].Visible == true) && (this.Splits[0].DisplayColumns[col].Width != 0))
                    {
                        Debug.Write(CurrentDColumn.DataColumn.Caption + " - " + this.Splits[0].DisplayColumns[col].Width + "\r\n");
                        cell = sheet[row + 1, xcol];
                        cell.Value = CurrentDColumn.DataColumn.FooterText;
                        cell.Style = new C1.C1Excel.XLStyle(book);
                        cell.Style.AlignHorz = (XLAlignHorzEnum)CurrentDColumn.Style.HorizontalAlignment;
                        cell.Style.AlignVert = XLAlignVertEnum.Center;
                        xcol = xcol + 1;
                    }
                    col = col + 1;
                }
            }

            if (blnContinue)
            {
                book.Save(save.FileName);
            }
            if (isOpen == true)
            {
                System.Diagnostics.Process.Start(save.FileName);
            }
            else
            {
                MessageBox.Show($"导出成功，文件路径：{save.FileName}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            return true;
        }

        public void SetFilterBarHeight(int height)
        {
            c1TrueDBGrid1.SetFilterBarHeight(height);
        }
        #endregion

        #region 公开事件

        [Description("当系统自动加载未绑定的列表数据时触发"), Browsable(true)]
        public event C1.Win.C1TrueDBGrid.UnboundColumnFetchEventHandler UnboundColumnFetch
        {
            add
            {
                c1TrueDBGrid1.UnboundColumnFetch += value;
            }
            remove
            {
                c1TrueDBGrid1.UnboundColumnFetch -= value;
            }
        }

        [Description("当用户改变列表中的一行时触发"), Browsable(true)]
        public event C1.Win.C1TrueDBGrid.RowColChangeEventHandler RowColChange
        {
            add
            {
                c1TrueDBGrid1.RowColChange += value;
            }
            remove
            {
                c1TrueDBGrid1.RowColChange -= value;
            }
        }
        [Description("在首次按下某个键时发生"), Browsable(true)]
        public new event System.Windows.Forms.KeyEventHandler KeyDown
        {
            add
            {
                c1TrueDBGrid1.KeyDown += value;
            }
            remove
            {
                c1TrueDBGrid1.KeyDown -= value;
            }
        }

        [Browsable(true)]
        public event ColEventHandler AfterColUpdate
        {
            add
            {
                c1TrueDBGrid1.AfterColUpdate += value;
            }
            remove
            {
                c1TrueDBGrid1.AfterColUpdate -= value;
            }
        }

        [Browsable(true)]
        public event EventHandler AfterUpdate
        {
            add
            {
                c1TrueDBGrid1.AfterUpdate += value;
            }
            remove
            {
                c1TrueDBGrid1.AfterUpdate -= value;
            }
        }

        [Browsable(true)]
        public event EventHandler AfterInsert
        {
            add
            {
                c1TrueDBGrid1.AfterInsert += value;
            }
            remove
            {
                c1TrueDBGrid1.AfterInsert -= value;
            }
        }

        [Browsable(true)]
        public event ColEventHandler AfterColEdit
        {
            add
            {
                c1TrueDBGrid1.AfterColEdit += value;
            }
            remove
            {
                c1TrueDBGrid1.AfterColEdit -= value;
            }
        }


        [Browsable(true)]
        public new event MouseEventHandler MouseDown
        {
            add
            {
                c1TrueDBGrid1.MouseDown += value;
            }
            remove
            {
                c1TrueDBGrid1.MouseDown -= value;
            }
        }

        [Browsable(true)]
        public new event MouseEventHandler MouseUp
        {
            add
            {
                c1TrueDBGrid1.MouseUp += value;
            }
            remove
            {
                c1TrueDBGrid1.MouseUp -= value;
            }
        }

        [Browsable(true)]
        public event ColEventHandler ComboSelect
        {
            add
            {
                c1TrueDBGrid1.ComboSelect += value;
            }
            remove
            {
                c1TrueDBGrid1.ComboSelect -= value;
            }
        }

        public new event EventHandler DoubleClick
        {
            add
            {
                c1TrueDBGrid1.DoubleClick += value;
            }
            remove
            {
                c1TrueDBGrid1.DoubleClick -= value;
            }
        }

        public new event MouseEventHandler MouseDoubleClick
        {
            add
            {
                c1TrueDBGrid1.MouseDoubleClick += value;
            }
            remove
            {
                c1TrueDBGrid1.MouseDoubleClick -= value;
            }
        }


        public new event MouseEventHandler MouseClick
        {
            add
            {
                c1TrueDBGrid1.MouseClick += value;
            }
            remove
            {
                c1TrueDBGrid1.MouseClick -= value;
            }
        }

        [Browsable(true)]
        public event ErrorEventHandler Error
        {
            add
            {
                c1TrueDBGrid1.Error += value;
            }
            remove
            {
                c1TrueDBGrid1.Error -= value;
            }
        }

        [Browsable(true)]
        public event BeforeColUpdateEventHandler BeforeColUpdate
        {
            add
            {
                c1TrueDBGrid1.BeforeColUpdate += value;
            }
            remove
            {
                c1TrueDBGrid1.BeforeColUpdate -= value;
            }
        }

        [Browsable(true)]
        public event BeforeColEditEventHandler BeforeColEdit
        {
            add
            {
                c1TrueDBGrid1.BeforeColEdit += value;
            }
            remove
            {
                c1TrueDBGrid1.BeforeColEdit -= value;
            }
        }

        [Browsable(true)]
        public event EventHandler AfterDelete
        {
            add
            {
                c1TrueDBGrid1.AfterDelete += value;
            }
            remove
            {
                c1TrueDBGrid1.AfterDelete -= value;
            }
        }

        [Browsable(true)]
        public event OwnerDrawCellEventHandler OwnerDrawCell
        {
            add
            {
                c1TrueDBGrid1.OwnerDrawCell += value;
            }
            remove
            {
                c1TrueDBGrid1.OwnerDrawCell -= value;
            }
        }

        [Browsable(true)]
        public event ColEventHandler ButtonClick
        {
            add
            {
                c1TrueDBGrid1.ButtonClick += value;
            }
            remove
            {
                c1TrueDBGrid1.ButtonClick -= value;
            }
        }

        [Browsable(true)]
        public event FetchRowStyleEventHandler FetchRowStyle
        {
            add
            {
                c1TrueDBGrid1.FetchRowStyle += value;
            }
            remove
            {
                c1TrueDBGrid1.FetchRowStyle -= value;
            }
        }


        [Browsable(true)]
        public event FetchCellStyleEventHandler FetchCellStyle
        {
            add
            {
                c1TrueDBGrid1.FetchCellStyle += value;
            }
            remove
            {
                c1TrueDBGrid1.FetchCellStyle -= value;
            }
        }

        [Browsable(true)]
        public new event EventHandler GotFocus
        {
            add
            {
                c1TrueDBGrid1.GotFocus += value;
            }
            remove
            {
                c1TrueDBGrid1.GotFocus -= value;
            }
        }

        [Browsable(true)]
        public event SplitEventHandler FirstRowChange
        {
            add
            {
                c1TrueDBGrid1.FirstRowChange += value;
            }
            remove
            {
                c1TrueDBGrid1.FirstRowChange -= value;
            }
        }

        public event BandEventHandler Expand
        {
            add
            {
                c1TrueDBGrid1.Expand += value;
            }
            remove
            {
                c1TrueDBGrid1.Expand -= value;
            }
        }

        public event EventHandler FilterChange
        {
            add
            {
                c1TrueDBGrid1.FilterChange += value;
            }
            remove
            {
                c1TrueDBGrid1.FilterChange -= value;
            }
        }

        public event GroupIntervalEventHandler GroupInterval
        {
            add
            {
                c1TrueDBGrid1.GroupInterval += value;
            }
            remove
            {
                c1TrueDBGrid1.GroupInterval -= value;
            }
        }

        public event GroupTextEventHandler GroupAggregate
        {
            add
            {
                c1TrueDBGrid1.GroupAggregate += value;
            }
            remove
            {
                c1TrueDBGrid1.GroupAggregate -= value;
            }
        }
        #endregion


    }
}
