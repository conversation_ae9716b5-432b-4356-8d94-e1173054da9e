﻿/**/
/*
* Copyright (c) 2008 黑色珊瑚::Tsorgy.Utils, Reserved.
* 
* Filename:    @(#)Hook.cs
* Create by:   TsOrgY
* Email:       <EMAIL>
* Date:        2008/12/20 16:30:09
* 
* Classname:   Hook
* Description: 钩子组件.
*              
*/

using System;
using System.Runtime.InteropServices;
using System.Reflection;
using System.ComponentModel;

namespace CustomControl
{

    /**/
    /// <summary>
    /// 钩子工具类.
    /// </summary>
    [DefaultEvent("KeyUp")]
    [DefaultProperty("Tag")]
    public class Hook : Component
    {
        #region 属性
        private bool _enabled = true;
        /**/
        /// <summary>
        /// 获取或设置一个布尔值，表示该钩子是否可用.
        /// </summary>
        [Description("一个布尔值，表示该钩子是否可用。")]
        [Category("Behavior")]
        [DefaultValue(true)]
        public bool Enabled
        {
            get { return _enabled; }
            set
            {
                if (_enabled == value)
                    return;
                _enabled = value;
                if (!this.DesignMode)
                {
                    if (value)
                        SetHook();
                    else
                        RemoveHook();
                }
            }
        }

        /**/
        /// <summary>
        /// 获取或设置一个任意字符串，表示某种类型的用户状态.
        /// </summary>
        [Description("任意字符串，表示某种类型的用户状态。")]
        [Localizable(false)]
        [Bindable(true)]
        [TypeConverter(typeof(StringConverter))]
        [DefaultValue((string)null)]
        public object Tag { get; set; }
        #endregion

        #region 事件
        public delegate void KeyDownHandler(int keyCode, ShiftKey shift);
        public delegate void KeyUpHandler(int keyCode, ShiftKey shift);
        public delegate void SystemKeyDownHandler(int keyCode);
        public delegate void SystemKeyUpHandler(int keyCode);
        public delegate void MouseDownHandler(Button button, ShiftKey shift, int x, int y);
        public delegate void MouseUpHandler(Button button, ShiftKey shift, int x, int y);
        public delegate void MouseMoveHandler(Button button, ShiftKey shift, int x, int y);

        /**/
        /// <summary>
        /// 当键盘按下时触发.
        /// </summary>
        [Description("键盘按下时触发")]
        [Category("Behavior")]
        public event KeyDownHandler KeyDown;
        /**/
        /// <summary>
        /// 当键盘弹起时触发.
        /// </summary>
        [Description("键盘弹起时触发")]
        [Category("Behavior")]
        public event KeyUpHandler KeyUp;
        /**/
        /// <summary>
        /// 当系统键盘按下时触发.
        /// </summary>
        [Description("系统键盘按下时触发")]
        [Category("Behavior")]
        public event SystemKeyDownHandler SystemKeyDown;
        /**/
        /// <summary>
        /// 当系统键盘弹起时触发.
        /// </summary>
        [Description("系统键盘弹起时触发")]
        [Category("Behavior")]
        public event SystemKeyUpHandler SystemKeyUp;
        /**/
        /// <summary>
        /// 当鼠标按下时触发.
        /// </summary>
        [Description("鼠标按下时触发")]
        [Category("Behavior")]
        public event MouseDownHandler MouseDown;
        /**/
        /// <summary>
        /// 当鼠标弹起时触发.
        /// </summary>
        [Description("鼠标弹起时触发")]
        [Category("Behavior")]
        public event MouseUpHandler MouseUp;
        /**/
        /// <summary>
        /// 当鼠标移动时触发.
        /// </summary>
        [Description("鼠标移动时触发")]
        [Category("Behavior")]
        public event MouseMoveHandler MouseMove;
        #endregion

        #region  公共方法
        /**/
        /// <summary>
        /// 设置钩子.
        /// </summary>
        /// <returns></returns>
        public bool SetHook()
        {
            bool rtn = true;
            if (hJournalHook != 0 || hAppHook != 0)
                rtn = rtn && RemoveHook();
            if (rtn)
            {
                IntPtr instance = Marshal.GetHINSTANCE(Assembly.GetExecutingAssembly().GetModules()[0]);
                hJournalHook = SetWindowsHookEx(WH_JOURNALRECORD, procJournal, instance, 0);
                hAppHook = SetWindowsHookEx(WH_GETMESSAGE, procAppHook, instance, GetCurrentThreadId());
            }

            return rtn && hJournalHook != 0 && hAppHook != 0;
        }

        /**/
        /// <summary>
        /// 卸载钩子.
        /// </summary>
        public bool RemoveHook()
        {
            bool rtn = UnhookWindowsHookEx(hAppHook);
            rtn = rtn && UnhookWindowsHookEx(hJournalHook);

            return rtn;
        }
        #endregion

        #region 枚举
        /**/
        /// <summary>
        /// 上档键枚举.
        /// </summary>
        public enum ShiftKey
        {
            None = 0,
            Shift = 1,
            Control = 2,
            ControlShift = 3,
            Menu = 4,
            MenuShift = 5,
            ControlMenu = 6,
            ControlMenuShift = 7
        }

        /**/
        /// <summary>
        /// 当前鼠标键枚举.
        /// </summary>
        public enum Button
        {
            Left = 1,
            Right = 2,
            LeftRight = 3,
            Middle = 4,
            LeftMiddle = 5,
            RightMiddle = 6,
            LeftRightMiddle = 7
        }
        #endregion

        #region API 相关
        /**/
        /// <summary>
        /// 消息类型.
        /// </summary>
        private enum MessageType
        {
            WM_CANCELJOURNAL = 0x4B,
            WM_KEYDOWN = 0x100,
            WM_KEYUP = 0x101,
            WM_MOUSEMOVE = 0x200,
            WM_LBUTTONDOWN = 0x201,
            WM_LBUTTONUP = 0x202,
            WM_LBUTTONDBLCLK = 0x203,
            WM_RBUTTONDOWN = 0x204,
            WM_RBUTTONUP = 0x205,
            WM_RBUTTONDBLCLK = 0x206,
            WM_MBUTTONDOWN = 0x207,
            WM_MBUTTONUP = 0x208,
            WM_MBUTTONDBLCLK = 0x209,
            WM_MOUSEWHEEL = 0x20A,
            WM_SYSTEMKEYDOWN = 0x104,
            WM_SYSTEMKEYUP = 0x105
        }
        // API 类型.
        /**/
        /// <summary>
        /// API 点.
        /// </summary>
        private struct PointApi
        {
            public int x { get; set; }
            public int y { get; set; }
        }
        /**/
        /// <summary>
        /// 消息.
        /// </summary>
        private struct TMsg
        {
            public int hwnd { get; set; }
            public int message { get; set; }
            public int wParam { get; set; }
            public int lParam { get; set; }
            public int time { get; set; }
            public PointApi pt { get; set; }
        }
        /**/
        /// <summary>
        /// 事件消息.
        /// </summary>
        private struct EventMsg
        {
            public int wMsg { get; set; }
            public int lParamLow { get; set; }
            public int lParamHigh { get; set; }
            public int msgTime { get; set; }
            public int hWndMsg { get; set; }
        }

        // API 函数声明.
        public delegate int HookProc(int nCode, int wParam, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern int CallNextHookEx(int hHook, int nCode, int wParam, IntPtr lParam);
        [DllImport("user32.dll")]
        private static extern int SetWindowsHookEx(int idHook, HookProc lpfn, IntPtr hInstance, int threadId);
        [DllImport("user32.dll")]
        private static extern bool UnhookWindowsHookEx(int idHook);
        [DllImport("User32.dll")]
        private static extern short GetAsyncKeyState(int vKey);
        [DllImport("kernel32.dll")]
        public static extern int GetCurrentThreadId();

        // API 常量.
        private const int WH_JOURNALRECORD = 0;
        private const int WH_GETMESSAGE = 3;
        #endregion

        #region  回调函数
        /**/
        /// <summary>
        /// Journal 回调.
        /// </summary>
        /// <param name="nCode"></param>
        /// <param name="wParam"></param>
        /// <param name="lParam"></param>
        /// <returns></returns>
        private int JournalRecordProc(int nCode, int wParam, IntPtr lParam)
        {
            if (nCode < 0)
            {
                return CallNextHookEx(hJournalHook, nCode, wParam, lParam);
            }
            FireEvent(lParam);
            return CallNextHookEx(hJournalHook, nCode, wParam, lParam);
        }

        /**/
        /// <summary>
        /// 应用程序钩子 回调.
        /// </summary>
        /// <param name="nCode"></param>
        /// <param name="wParam"></param>
        /// <param name="lParam"></param>
        /// <returns></returns>
        private int AppHookProc(int nCode, int wParam, IntPtr lParam)
        {
            if (nCode < 0)
            {
                return CallNextHookEx(hAppHook, nCode, wParam, lParam);
            }
            TMsg msg = (TMsg)Marshal.PtrToStructure(lParam, typeof(TMsg));
            switch ((MessageType)msg.message)
            {
                case MessageType.WM_CANCELJOURNAL:
                    if (wParam == 1)
                        FireEvent(new IntPtr((int)MessageType.WM_CANCELJOURNAL));
                    break;
                default:
                    break;
            }
            return CallNextHookEx(hAppHook, nCode, wParam, lParam);
        }

        #endregion

        #region  全局变量
        // 全局变量.
        private int hJournalHook, hAppHook;
        private HookProc procJournal, procAppHook;
        #endregion

        #region  构造方法
        /**/
        /// <summary>
        /// 初始化 <see cref="StepMania.Utils.Hook"/> 类的新实例.
        /// </summary>
        public Hook()
        {
            if (!this.DesignMode)
            {
                hJournalHook = 0;
                hAppHook = 0;
                procAppHook = new HookProc(AppHookProc);
                procJournal = new HookProc(JournalRecordProc);

                SetHook();
            }
        }
        #endregion

        #region  私有方法
        /**/
        /// <summary>
        /// 取得当前上档键状态.
        /// </summary>
        /// <returns></returns>
        private ShiftKey GetShiftNow()
        {
            ShiftKey shift = ShiftKey.None;
            if (GetAsyncKeyState(0x10) != 0) //Shift
                shift |= ShiftKey.Shift;
            if (GetAsyncKeyState(0x11) != 0) //Control
                shift |= ShiftKey.Control;
            if (GetAsyncKeyState(0x12) != 0) //Menu
                shift |= ShiftKey.Menu;
            return shift;
        }

        /**/
        /// <summary>
        /// 取得当前鼠标按钮状态.
        /// </summary>
        /// <returns></returns>
        private Button GetButtonNow()
        {
            Button button = Button.Left;
            if (GetAsyncKeyState(0x1) != 0)
                button |= Button.Left;
            if (GetAsyncKeyState(0x2) != 0)
                button |= Button.Right;
            if (GetAsyncKeyState(0x4) != 0)
                button |= Button.Middle;
            return button;
        }

        /**/
        /// <summary>
        /// 得到消息，分析后触发事件.
        /// </summary>
        /// <param name="lParam"></param>
        private void FireEvent(IntPtr lParam)
        {
            if (lParam.ToInt32() == (int)MessageType.WM_CANCELJOURNAL)
            {
                hJournalHook = 0;
                SetHook();
                return;
            }
            EventMsg EMSG = (EventMsg)Marshal.PtrToStructure(lParam, typeof(EventMsg));
            switch ((MessageType)EMSG.wMsg)
            {
                case MessageType.WM_KEYDOWN:
                    if (KeyDown != null)
                        KeyDown(EMSG.lParamLow & 0xFF, GetShiftNow());
                    break;
                case MessageType.WM_KEYUP:
                    if (KeyUp != null)
                        KeyUp(EMSG.lParamLow & 0xFF, GetShiftNow());
                    break;
                case MessageType.WM_MOUSEMOVE:
                    if (MouseMove != null)
                        MouseMove(GetButtonNow(), GetShiftNow(), EMSG.lParamLow, EMSG.lParamHigh);
                    break;
                case MessageType.WM_LBUTTONDOWN:
                case MessageType.WM_RBUTTONDOWN:
                case MessageType.WM_MBUTTONDOWN:
                    if (MouseDown != null)
                        MouseDown((Button)(Math.Pow(2, (EMSG.wMsg - 513) / 3)), GetShiftNow(),
                            EMSG.lParamLow, EMSG.lParamHigh);
                    break;
                case MessageType.WM_LBUTTONUP:
                case MessageType.WM_RBUTTONUP:
                case MessageType.WM_MBUTTONUP:
                    if (MouseUp != null)
                        MouseUp((Button)(Math.Pow(2, (EMSG.wMsg - 513) / 3)), GetShiftNow(),
                            EMSG.lParamLow, EMSG.lParamHigh);
                    break;
                case MessageType.WM_SYSTEMKEYDOWN:
                    if (SystemKeyDown != null)
                        SystemKeyDown(EMSG.lParamLow & 0xFF);
                    break;
                case MessageType.WM_SYSTEMKEYUP:
                    if (SystemKeyUp != null)
                        SystemKeyUp(EMSG.lParamLow & 0xFF);
                    break;
                default:
                    break;
            }
        }
        #endregion

        #region  重写方法
        /**/
        /// <summary>
        /// 释放由 <see cref="T:System.ComponentModel.Component"></see> 占用的非托管资源，还可以另外再释放托管资源.
        /// </summary>
        /// <param name="disposing">为 true 则释放托管资源和非托管资源；为 false 则仅释放非托管资源。</param>
        protected override void Dispose(bool disposing)
        {
            RemoveHook();
            base.Dispose(disposing);
        }
        #endregion
    }
}