-- =============================================
-- 初始化脚本 - 基于 Menu.json 数据
-- 创建日期: 2025/7/22
-- 说明: 根据 Menu.json 初始化系统菜单数据
-- =============================================

USE lis
GO

-- =============================================
-- 清空现有数据 - 按照外键关系顺序删除
-- =============================================

-- 1. 首先删除用户相关表数据
IF OBJECT_ID('dbo.SysUser', 'U') IS NOT NULL
BEGIN   
    -- 然后清空数据
    DELETE FROM dbo.SysUser;
END

-- 2. 删除角色权限关系表数据
IF OBJECT_ID('dbo.SysRoleAuth', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysRoleAuth;
END

-- 3. 删除角色模块关系表数据
IF OBJECT_ID('dbo.SysRoleModule', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysRoleModule;
END

-- 4. 删除角色表数据
IF OBJECT_ID('dbo.SysRole', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysRole;
END

-- 5. 删除模块权限表数据
IF OBJECT_ID('dbo.SysModuleAuth', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysModuleAuth;
END

-- 6. 删除模块表数据
IF OBJECT_ID('dbo.SysModule', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysModule;
END

-- 7. 删除二级菜单表数据
IF OBJECT_ID('dbo.SysMenu2', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysMenu2;
END

-- 8. 删除一级菜单表数据
IF OBJECT_ID('dbo.SysMenu1', 'U') IS NOT NULL
BEGIN
    DELETE FROM dbo.SysMenu1;
END

-- 不需要删除医生表记录，因为我们不会插入医生数据

PRINT '已清空相关表数据，准备重新初始化...'
GO

-- =============================================
-- 1. 插入一级菜单数据 (SysMenu1)
-- =============================================
INSERT INTO [dbo].[SysMenu1] ([FrsCode], [FrsName], [SortNo])
VALUES 
    ('01', N'检验管理', 1),
    ('02', N'字典管理', 2),
    ('03', N'系统管理', 3);

-- =============================================
-- 2. 插入二级菜单数据 (SysMenu2)
-- =============================================
INSERT INTO [dbo].[SysMenu2] ([ScndCode], [FrsCode], [ScndName], [SortNo])
VALUES 
    -- 检验管理下的二级菜单
    ('001', '01', N'检验管理', 1),
    
    -- 字典管理下的二级菜单
    ('002', '02', N'基础字典', 1),
    ('003', '02', N'项目字典', 2),
    ('004', '02', N'仪器字典', 3),
    
    -- 系统管理下的二级菜单
    ('005', '03', N'用户设置', 1);

-- =============================================
-- 3. 插入模块数据 (SysModule)
-- =============================================
INSERT INTO [dbo].[SysModule] ([ScndCode], [ModuleCode], [ModuleName], [SortNo])
VALUES 
    -- 检验管理 -> 检验管理
    ('001', '0001', N'检验申请', 1),
    
    -- 字典管理 -> 基础字典
    ('002', '0002', N'就诊类型', 1),
    ('002', '0003', N'科室信息', 2),
    ('002', '0004', N'医生信息', 3),
    ('002', '0005', N'标本类型', 4),
    ('002', '0006', N'标本状态', 5),
    ('002', '0007', N'标本参数', 6),
    ('002', '0008', N'方法类别', 7),
    ('002', '0009', N'检验方法', 8),
    
    -- 字典管理 -> 项目字典
    ('003', '0010', N'项目类别', 1),
    ('003', '0011', N'项目单位', 2),
    ('003', '0012', N'检验项目', 3),
    ('003', '0013', N'检验组合', 4),
    
    -- 字典管理 -> 仪器字典
    ('004', '0014', N'仪器信息', 1),
    ('004', '0015', N'仪器项目映射', 2),
    ('004', '0016', N'仪器标本参数映射', 3),
    
    -- 系统管理 -> 用户设置
    ('005', '0017', N'角色管理', 1),
    ('005', '0018', N'用户管理', 2);

-- =============================================
-- 验证插入结果
-- =============================================
PRINT '=== 一级菜单数据 ==='
SELECT * FROM SysMenu1 ORDER BY SortNo;

PRINT '=== 二级菜单数据 ==='
SELECT * FROM SysMenu2 ORDER BY FrsCode, SortNo;

PRINT '=== 模块数据 ==='
SELECT * FROM SysModule ORDER BY ScndCode, SortNo;

-- =============================================
-- 4. 插入角色数据 (SysRole)
-- =============================================
INSERT INTO [dbo].[SysRole] ([RoleCode], [RoleName], [RolePinYin], [RoleWuBi], [RoleMemo])
VALUES 
    ('01', N'管理员', N'GLY', N'GLRY', N'系统管理员，拥有所有模块权限');

-- =============================================
-- 5. 插入角色模块权限 (SysRoleModule)
-- 为管理员角色分配所有模块权限
-- =============================================
INSERT INTO [dbo].[SysRoleModule] ([RoleCode], [ModuleCode])
SELECT '01', [ModuleCode] FROM [dbo].[SysModule];

-- =============================================
-- 验证角色和权限数据
-- =============================================
PRINT '=== 角色数据 ==='
SELECT * FROM SysRole;

PRINT '=== 角色模块权限数据 ==='
SELECT 
    r.RoleName,
    m.ModuleName,
    rm.RoleCode,
    rm.ModuleCode
FROM SysRoleModule rm
INNER JOIN SysRole r ON rm.RoleCode = r.RoleCode
INNER JOIN SysModule m ON rm.ModuleCode = m.ModuleCode
ORDER BY rm.RoleCode, rm.ModuleCode;

-- =============================================
-- 6. 插入用户数据 (SysUser)
-- =============================================
IF OBJECT_ID('dbo.SysUser', 'U') IS NOT NULL
BEGIN
    -- 插入管理员用户，医生ID直接设置为NULL
    INSERT INTO [dbo].[SysUser] 
    ([LoginName], [PasswordHash], [UserName], [DoctorId], [RoleCode], [IsLockedOut], [Status])
    VALUES 
    ('admin', '2B5761050A88A3C4', N'管理员', NULL, '01', 0, 1);
END

-- =============================================
-- 验证用户数据
-- =============================================
IF OBJECT_ID('dbo.SysUser', 'U') IS NOT NULL
BEGIN
    PRINT '=== 用户数据 ==='
    SELECT 
        u.UserId, 
        u.LoginName, 
        u.UserName, 
        r.RoleName,
        u.Status
    FROM SysUser u
    LEFT JOIN SysRole r ON u.RoleCode = r.RoleCode;
END

PRINT '初始化脚本执行完成！'