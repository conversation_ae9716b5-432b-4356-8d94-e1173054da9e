using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictLabProfile3 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictLabProfileItemMap _bllDictLabProfileItemMap = new BllDictLabProfileItemMap();
        BLL.BllDictLabItem _bllDictLabItem = new BllDictLabItem();
        Model.MdlDictLabProfileItemMap _mdlDictLabProfileItemMap = new MdlDictLabProfileItemMap();
        private int _profileId;
        public DictLabProfile3(bool insert, DataRow row, DataTable table, int profileId)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;
            _profileId = profileId;

            ComboLabItem.GotFocus += new System.EventHandler(base.InputCn);
            NumSequence.GotFocus += new System.EventHandler(base.InputEn);
        }

        private void DictLabProfile3_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            // 初始化控件
            string strWhere = "";
            ComboLabItem.Init();
            NumSequence.Value = _bllDictLabProfileItemMap.GetNextSequenceNo(_profileId);
            ChkIsDefault.Checked = false;
        }

        private void DataClear()
        {
            base.Insert = true;
            ComboLabItem.SelectedIndex = -1;
            NumSequence.Value = _bllDictLabProfileItemMap.GetNextSequenceNo(_profileId);
            ChkIsDefault.Checked = false;
            ComboLabItem.Select();
        }

        private void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;

            if (row["LabItemId"] != DBNull.Value)
                ComboLabItem.SelectedValue = row["LabItemId"];

            NumSequence.Value = row["SequenceNo"];

            if (row["IsDefault"] != DBNull.Value)
                ChkIsDefault.Checked = (bool)row["IsDefault"];

            ComboLabItem.Select();
        }

        private bool DataCheck()
        {
            if (ComboLabItem.SelectedValue == null)
            {
                MessageBox.Show("请选择检验项目！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                ComboLabItem.Select();
                return false;
            }

            if (NumSequence.Value == null || Common.ConvertObject.ObjToInt(NumSequence.Value).Value <= 0)
            {
                MessageBox.Show("请填写正确的显示顺序！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                NumSequence.Select();
                return false;
            }

            // 检查项目重复
            if (base.Insert == true)
            {
                if (CheckItemExists((int)ComboLabItem.SelectedValue))
                {
                    MessageBox.Show("该项目已存在于此组合中！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    ComboLabItem.Select();
                    return false;
                }
            }
            else
            {
                if ((int)ComboLabItem.SelectedValue != (int)base.MyRow["LabItemId"] && CheckItemExists((int)ComboLabItem.SelectedValue))
                {
                    MessageBox.Show("该项目已存在于此组合中！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    ComboLabItem.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckItemExists(int labItemId)
        {
            string condition = $"ProfileId={_profileId} AND LabItemId={labItemId}";
            int cnt = _bllDictLabProfileItemMap.GetRecordCount(condition);
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictLabProfileItemMap.ProfileId = _profileId;
            _mdlDictLabProfileItemMap.LabItemId = Common.ConvertObject.ObjToInt(ComboLabItem.SelectedValue).Value;
            _mdlDictLabProfileItemMap.SequenceNo = Common.ConvertObject.ObjToInt(NumSequence.Value);
            _mdlDictLabProfileItemMap.IsDefault = ChkIsDefault.Checked;
            _mdlDictLabProfileItemMap.CreatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabProfileItemMap, base.MyRow);

            // 添加关联的检验项目信息用于显示
            MdlDictLabItem labItemModel = _bllDictLabItem.GetModel((int)ComboLabItem.SelectedValue);
            if (labItemModel != null)
            {
                base.MyRow["LabItemCode"] = labItemModel.LabItemCode;
                base.MyRow["LabItemName"] = labItemModel.LabItemName;
                base.MyRow["PinYin"] = labItemModel.PinYin;
            }

            //数据保存
            try
            {
                base.MyRow["MapId"] = _bllDictLabProfileItemMap.Add(_mdlDictLabProfileItemMap);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                ComboLabItem.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictLabProfileItemMap.MapId = int.Parse(base.MyRow["MapId"].ToString());
            _mdlDictLabProfileItemMap.ProfileId = _profileId;
            _mdlDictLabProfileItemMap.LabItemId = Common.ConvertObject.ObjToInt(ComboLabItem.SelectedValue).Value;
            _mdlDictLabProfileItemMap.SequenceNo = Common.ConvertObject.ObjToInt(NumSequence.Value).Value;
            _mdlDictLabProfileItemMap.IsDefault = ChkIsDefault.Checked;
            _mdlDictLabProfileItemMap.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabProfileItemMap, base.MyRow);

            // 更新关联的检验项目信息用于显示
            MdlDictLabItem labItemModel = _bllDictLabItem.GetModel((int)ComboLabItem.SelectedValue);
            if (labItemModel != null)
            {
                base.MyRow["LabItemCode"] = labItemModel.LabItemCode;
                base.MyRow["LabItemName"] = labItemModel.LabItemName;
                base.MyRow["PinYin"] = labItemModel.PinYin;
            }

            //数据保存
            try
            {
                _bllDictLabProfileItemMap.Update(_mdlDictLabProfileItemMap);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }
        #endregion

        #region 事件响应
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (this.DataCheck() == false)
                return;

            if (base.Insert == true)
                this.DataAdd();
            else
                this.DataEdit();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion
    }
}