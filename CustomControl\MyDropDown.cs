﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public partial class MyDropDown : UserControl
    {
        public MyDropDown()
        {
            InitializeComponent();
        }

        #region 公开属性
        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        float _CaptainWidth = 70;
        [Description("标题宽度"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0 || label1.Text == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }

                Invalidate();
            }
        }

        [Description("字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                c1DropDownControl1.Font = value;
                c1DropDownControl1.DropDownForm.Font = value;
                Invalidate();
            }
        }

        [Description("标题字体"), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }

        [Description("控件是否启用"), Browsable(true)]
        public new bool Enabled
        {
            get { return c1DropDownControl1.Enabled; }
            set
            {
                c1DropDownControl1.Enabled = value;
                if (value == true)
                {
                    c1DropDownControl1.TabStop = true;
                    this.TabStop = true;
                }
                else
                {
                    c1DropDownControl1.TabStop = false;
                    this.TabStop = false;
                }

                Invalidate();
            }
        }
        
        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }


        public C1.Win.C1Input.DropDownForm DropDownForm
        {
            get { return c1DropDownControl1.DropDownForm; }
            set
            {
                c1DropDownControl1.DropDownForm = value;
                Invalidate();
            }
        }

        public bool AutoOpen
        {
            get { return c1DropDownControl1.AutoOpen; }
            set
            {
                c1DropDownControl1.AutoOpen = value;
                Invalidate();
            }
        }
        #endregion
    }
}
