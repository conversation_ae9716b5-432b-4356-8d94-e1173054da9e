﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using Common;
using Common.Delegate;
using CustomControl;


namespace Common.BaseForm
{
    public partial class OneFormCK1 : Common.BaseForm.BaseFather
    {
        #region 基础变量
        private Common.Delegate.TransmitTxt _transmitTxt = new TransmitTxt();
        Common.Delegate.TransmitModel _transmitModel = new Common.Delegate.TransmitModel();
        private CustomControl.MyGrid _baseMyGrid;

        protected DataTable MyTable { get; set; }
        protected CurrencyManager MyCm { get; set; }
        protected DataRow SubItemRow { get; set; }
        protected bool Insert { get; set; }
        protected Label BaseLblTotal { get; set; }
        protected CustomControl.MyGrid BaseMyGrid
        {
            get { return _baseMyGrid; }
            set
            {
                _baseMyGrid = value;
                this.BaseMyGrid.KeyDown += myGrid1_KeyDown;
                this.BaseMyGrid.GotFocus += myGrid1_GotFocus;
                this.BaseMyGrid.AfterColUpdate += myGrid1_AfterColUpdate;
            }
        }
        protected Common.Delegate.TransmitTxt MyTransmitTxt
        {
            get { return _transmitTxt; }
            set
            {
                _transmitTxt = value;
            }
        }

        protected Common.Delegate.TransmitModel MyTransmitModel
        {
            get { return _transmitModel; }
            set
            {
                _transmitModel = value;
            }
        }

        #region 按钮    
        protected MyButton BaseBtnDelete { get; set; }
        protected MyButton BaseBtnSave { get; set; }
        protected MyButton BaseBtnNew { get; set; }
        protected MyButton BaseBtnComplete { get; set; }
        protected MyButton BaseBtnPrint { get; set; }
        protected MyButton BaseBtnSearch { get; set; }
        protected MyButton BaseBtnClose { get; set; }
        #endregion

        #endregion
        public OneFormCK1()
        {
            InitializeComponent();
        }

        #region 控件动作

        private void RK1_Load(object sender, EventArgs e)
        {
            if (!base.IsDesignMode())
            {
                this.MyTransmitTxt.SetText += DataSum;
                this.MyTransmitModel.SetModel += ChangeModel;

            }
        }

        private void RK1_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.MyTransmitTxt.SetText -= DataSum;
            this.MyTransmitModel.SetModel -= ChangeModel;
        }

        #region Grid动作

        private void myGrid1_AfterColUpdate(object sender, C1.Win.C1TrueDBGrid.ColEventArgs e)
        {
            this.SubDataSum(e.ColIndex);
        }
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Delete:
                    DataDeleteOne();
                    break;
            }
        }


        private void myGrid1_GotFocus(object sender, EventArgs e)
        {
            CancelButton = null;
        }

        #endregion

        #region 快捷键
        private void DoubleFormRK1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control == true & e.KeyCode == Keys.S & BaseBtnSave.Enabled == true)
            {
                DataSave(true);
            }
            if (e.KeyData == Keys.F2 & BaseBtnNew.Enabled == true)
            {
                DataNew();
            }

            if (e.KeyData == Keys.F3 & BaseBtnSave.Enabled == true)
            {
                DataSave(true);
            }

            if (e.KeyData == Keys.F4 & BaseBtnComplete.Enabled == true)
            {
                DataComplete();
            }

            if (e.KeyData == Keys.F5 & BaseBtnPrint.Enabled == true)
            {
                DataPrint();
            }
            if (e.KeyData == Keys.F6 & BaseBtnSearch.Enabled == true)
            {
                DataSearch();
            }
            if (e.KeyData == Keys.F9 & BaseBtnClose.Enabled == true)
            {
                this.Close();
            }
        }


        #endregion

        #endregion

        #region 公共函数

        #region 虚函数

        #region 初始化函数
        protected virtual void BtnState()
        {

        }

        #endregion

        #region  显示函数
        protected virtual void Zb_Clear()
        {

        }

        protected virtual void ChangeModel(object model)
        {

        }
        #endregion

        #region 按钮函数
        protected virtual bool DataDeleteOne()
        {
            if (MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) == DialogResult.OK)
            {
                try
                {
                    BaseMyGrid.Delete();
                    BaseLblTotal.Text = "∑=" + BaseMyGrid.Splits[0].Rows.Count.ToString();
                    return true;
                }
                catch (Exception ex)
                {
                    if (ex.Message.ToString() == "索引 -1 不是为负数，就是大于行数。")
                    {
                        MessageBox.Show("未选中数据行", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    return false;
                }

            }
            return false;
        }

        protected virtual bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {
            if (MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) == DialogResult.OK)
            {
                if (Delete(PrimaryKey))
                {
                    MyTable.AcceptChanges();
                    DataNew();
                    return true;
                }
            }
            return false;
        }

        protected virtual void DataNew()
        {

        }

        protected virtual bool DataSave(bool showMsg)
        {
            return default(bool);
        }
        protected virtual void DataComplete()
        {
        }
        protected virtual void DataPrint()
        {
        }
        protected virtual void DataSearch()
        {
        }

        #endregion

        #region 数据操作函数
        protected virtual void DataSum(string s)
        {

        }

        protected virtual void SubDataSum(int index)
        {

        }

        protected virtual void SubDataEdit()
        {

        }
        #endregion

       #endregion

        #endregion

 
    }
}
