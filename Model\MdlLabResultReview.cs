﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabResultReview.cs
*
* 功 能： N/A
* 类 名： MdlLabResultReview
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验结果审核记录表
	/// </summary>
	[Serializable]
	public partial class MdlLabResultReview
	{
		public MdlLabResultReview()
		{}
		#region Model
		private long _reviewid;
		private long _resultid;
		private int _reviewlevel;
		private int _reviewaction;
		private int _operatorid;
		private string _operatorname;
		private int _responsibledoctorid;
		private string _responsibledoctorname;
		private string _responsibledoctortitle;
		private string _reviewcomment;
		private DateTime _reviewedat= DateTime.Now;
		/// <summary>
		/// 审核记录ID，自增主键
		/// </summary>
		public long ReviewId
		{
			set{ _reviewid=value;}
			get{return _reviewid;}
		}
		/// <summary>
		/// 关联的检验结果ID
		/// </summary>
		public long ResultId
		{
			set{ _resultid=value;}
			get{return _resultid;}
		}
		/// <summary>
		/// 审核级别：1=初审 2=复审 3=三审
		/// </summary>
		public int ReviewLevel
		{
			set{ _reviewlevel=value;}
			get{return _reviewlevel;}
		}
		/// <summary>
		/// 审核动作：1=通过 2=退回 3=修改
		/// </summary>
		public int ReviewAction
		{
			set{ _reviewaction=value;}
			get{return _reviewaction;}
		}
		/// <summary>
		/// 操作员ID（实际执行审核操作的人）
		/// </summary>
		public int OperatorId
		{
			set{ _operatorid=value;}
			get{return _operatorid;}
		}
		/// <summary>
		/// 操作员姓名（冗余存储）
		/// </summary>
		public string OperatorName
		{
			set{ _operatorname=value;}
			get{return _operatorname;}
		}
		/// <summary>
		/// 责任医师ID（承担医疗责任的医师）
		/// </summary>
		public int ResponsibleDoctorId
		{
			set{ _responsibledoctorid=value;}
			get{return _responsibledoctorid;}
		}
		/// <summary>
		/// 责任医师姓名（冗余存储）
		/// </summary>
		public string ResponsibleDoctorName
		{
			set{ _responsibledoctorname=value;}
			get{return _responsibledoctorname;}
		}
		/// <summary>
		/// 责任医师职称（用于报告显示）
		/// </summary>
		public string ResponsibleDoctorTitle
		{
			set{ _responsibledoctortitle=value;}
			get{return _responsibledoctortitle;}
		}
		/// <summary>
		/// 审核意见/备注
		/// </summary>
		public string ReviewComment
		{
			set{ _reviewcomment=value;}
			get{return _reviewcomment;}
		}
		/// <summary>
		/// 审核时间
		/// </summary>
		public DateTime ReviewedAt
		{
			set{ _reviewedat=value;}
			get{return _reviewedat;}
		}
		#endregion Model

	}
}

