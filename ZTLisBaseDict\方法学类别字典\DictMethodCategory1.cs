using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictMethodCategory1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictMethodCategory _bllDictMethodCategory = new BllDictMethodCategory();

        public DictMethodCategory1()
        {
            InitializeComponent();
        }

        private void DictMethodCategory1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }
        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "MethodCategoryCode", 120, "中", "", false);
            myGrid1.Init_Column("名称", "MethodCategoryName", 160, "左", "", false);
            myGrid1.Init_Column("简称", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("英文名称", "EnglishName", 150, "左", "", false);
            myGrid1.Init_Column("备注", "Description", 300, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;

        }

        private void DataInit()
        {
            base.MyTable = _bllDictMethodCategory.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["MethodCategoryId"] };
            base.MyTable.Columns["MethodCategoryId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictMethodCategory2 vform = new DictMethodCategory2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }

        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除方法学类别：" + this.myGrid1.Columns["MethodCategoryName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictMethodCategory.Delete(int.Parse(base.MyRow["MethodCategoryId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();

        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("MethodCategoryCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("MethodCategoryName", new Common.MdlExcel(15, "", "名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "简称", true));
            colconfig.Add("EnglishName", new Common.MdlExcel(15, "", "英文名称", true));
            colconfig.Add("Description", new Common.MdlExcel(20, "", "描述", true));
            Common.ExcelHelper.Export(base.MyTable, "方法学类别字典", colconfig);
        }
        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictMethodCategory mdlDictMethodCategory = new MdlDictMethodCategory();
                    if (row["名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictMethodCategory.MethodCategoryCode = row["编码"] + "";
                    mdlDictMethodCategory.MethodCategoryName = row["名称"] + "";
                    mdlDictMethodCategory.PinYin = base.MyChs2Spell.GetPy(row["名称"].ToString());
                    mdlDictMethodCategory.EnglishName = row["英文名称"] + "";
                    mdlDictMethodCategory.Description = row["描述"] + "";
                    mdlDictMethodCategory.Status = true;
                    mdlDictMethodCategory.CreatedAt = DateTime.Now;
                    mdlDictMethodCategory.UpdatedAt = DateTime.Now;

                    DataRow newRow = base.MyTable.NewRow();
                    PropertyInfo[] pis = typeof(MdlDictMethodCategory).GetProperties();

                    foreach (PropertyInfo pi in pis)
                    {
                        //得到属性的值
                        object value1 = pi.GetValue(mdlDictMethodCategory, null);
                        //得到属性的名称
                        string attr = pi.Name.ToString();
                        if (base.MyTable.Columns.Contains(attr))
                        {
                            newRow[attr] = value1;
                        }
                    }

                    successCnt++;
                    newRow["MethodCategoryId"] = _bllDictMethodCategory.Add(mdlDictMethodCategory);
                    base.MyTable.Rows.Add(newRow);

                    UpdateData[i - 1] = "导入成功";
                    i = i + 1;
                }
                base.MyTable.AcceptChanges();
                this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
                MessageBox.Show("导入完成！成功：" + successCnt + "条，失败：" + failCnt + "条", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void DataEnable(bool Status)
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            Model.MdlDictMethodCategory mdlDictMethodCategory = _bllDictMethodCategory.GetModel(int.Parse(base.MyRow["MethodCategoryId"].ToString()));
            mdlDictMethodCategory.Status = Status;
            mdlDictMethodCategory.UpdatedAt = DateTime.Now;
            _bllDictMethodCategory.Update(mdlDictMethodCategory);
            base.MyRow["Status"] = Status;
            base.MyRow["UpdatedAt"] = DateTime.Now;
            base.MyRow.AcceptChanges();
        }

        #endregion

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }
        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }
        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("MethodCategoryCode+isnull(MethodCategoryName,'')+isnull(PinYin,'')+isnull(EnglishName,'')", TxtFilter.Text.Trim());
        }

    }
}