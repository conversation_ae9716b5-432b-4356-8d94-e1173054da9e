﻿/**  版本信息模板在安装目录下，可自行修改。
* BllLabSpecimen.cs
*
* 功 能： N/A
* 类 名： BllLabSpecimen
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using Model;
using DALFactory;
using IDAL;
namespace BLL
{
	/// <summary>
	/// 检验标本信息表
	/// </summary>
	public partial class BllLabSpecimen
	{
		private readonly IDalLabSpecimen dal = DataAccess.CreateDalLabSpecimen();
		public BllLabSpecimen()
		{ }
		#region  BasicMethod
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long SpecimenId)
		{
			return dal.Exists(SpecimenId);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabSpecimen model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabSpecimen model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long SpecimenId)
		{

			return dal.Delete(SpecimenId);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string SpecimenIdlist)
		{
			return dal.DeleteList(SpecimenIdlist);
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabSpecimen GetModel(long SpecimenId)
		{

			return dal.GetModel(SpecimenId);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
		public Model.MdlLabSpecimen GetModelByCache(long SpecimenId)
		{

			string CacheKey = "MdlLabSpecimenModel-" + SpecimenId;
			object objModel = Common.DataCache.GetCache(CacheKey);
			if (objModel == null)
			{
				try
				{
					objModel = dal.GetModel(SpecimenId);
					if (objModel != null)
					{
						int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
						Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
					}
				}
				catch { }
			}
			return (Model.MdlLabSpecimen)objModel;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			return dal.GetList(Top, strWhere, filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlLabSpecimen> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlLabSpecimen> DataTableToList(DataTable dt)
		{
			List<Model.MdlLabSpecimen> modelList = new List<Model.MdlLabSpecimen>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				Model.MdlLabSpecimen model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage(strWhere, orderby, startIndex, endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
		//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 根据SeqPrefix和SpecimenDate获取最大的DailySeqNo
		/// </summary>
		/// <param name="seqPrefix">序列前缀</param>
		/// <param name="specimenDate">标本日期</param>
		/// <returns>最大的DailySeqNo，如果没有记录则返回0</returns>
		public int GetMaxDailySeqNo(string seqPrefix, DateTime specimenDate)
		{
			return dal.GetMaxDailySeqNo(seqPrefix, specimenDate);
		}

		/// <summary>
		/// 获取LabSpecimen与LabOrder关联的数据列表
		/// </summary>
		/// <param name="strWhere">查询条件</param>
		/// <returns>关联查询结果</returns>
		public DataSet GetListWithLabOrder(string strWhere)
		{
			return dal.GetListWithLabOrder(strWhere);
		}

		#endregion  ExtensionMethod
	}
}

