using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Model;
using ZTLisEnum;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// QR-100 特定蛋白分析仪解析器
    /// 解析已去除随机数、帧开始字、帧结束字的22字节有效数据
    /// </summary>
    public class QR100Parser : BaseParser
    {
        /// <summary>
        /// 解析器名称
        /// </summary>
        public override string Name => "QR-100 特定蛋白分析仪解析器";

        /// <summary>
        /// 支持的协议类型
        /// </summary>
        public override ProtocolType SupportedProtocol => ProtocolType.自定义;

        /// <summary>
        /// 验证报文格式 - 检查数据长度是否为22字节
        /// </summary>
        public override bool ValidateMessage(string rawMessage)
        {
            if (string.IsNullOrEmpty(rawMessage))
                return false;

            // 将十六进制字符串转换为字节数组来验证长度
            try
            {
                var bytes = ConvertHexStringToBytes(rawMessage);
                return bytes.Length == 18;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 解析消息
        /// </summary>
        protected override async Task<List<MdlLabInstrResultQ>> ParseMessageAsync(string rawMessage, int instrumentId)
        {
            var results = new List<MdlLabInstrResultQ>();

            try
            {
                WriteLog($"开始解析QR100消息: {rawMessage}");

                // 将十六进制字符串转换为字节数组
                byte[] data = ConvertHexStringToBytes(rawMessage);

                if (data.Length != 18)
                {
                    throw new ArgumentException($"数据长度不正确，期望28字节，实际{data.Length}字节");
                }

                // 解析数据
                var parseData = ParseFrameData(data);

                // 创建检验结果对象
                var labResult = CreateLabResult(
                    instrumentId,
                    parseData.SampleNumber.ToString(),
                    parseData.ProjectType.ToString("X2"),
                    FormatResultValue(parseData.ResultValue, parseData.HasDecimalPoint),
                    GetResultFlag(parseData.ResultFlag),
                    parseData.MeasureTime
                );

                results.Add(labResult);

                // 创建标本参数对象
                var specimenParam = CreateSpecimenParam(
                    instrumentId,
                    parseData.SampleNumber.ToString(),
                    parseData.SampleParamType.ToString("X2"),
                    FormatSpecimenParamValue(parseData.SampleParamInt, parseData.SampleParamDecimal, parseData.SampleParamDecimalFlag),
                    "",
                    parseData.MeasureTime
                );

                results.Add(specimenParam);

                WriteLog($"成功解析QR100数据 - 样本号: {parseData.SampleNumber}, 项目: {GetProjectName(parseData.ProjectType)}, 结果: {labResult.ResultValue}, 标本参数: {GetSpecimenParamName(parseData.SampleParamType)} = {specimenParam.ResultValue}");
            }
            catch (Exception ex)
            {
                WriteLog($"解析QR100消息异常: {ex.Message}");
                throw;
            }

            return results;
        }

        /// <summary>
        /// 解析帧数据
        /// 根据协议文档28字节帧结构解析
        /// </summary>
        private QR100FrameData ParseFrameData(byte[] data)
        {
            var frameData = new QR100FrameData();

            // 字节1: 随机数 (跳过)
            // 字节2-6: 帧开始字 "start" (跳过)

            // 字节7-8: 标本编号 (高字节在前)
            frameData.SampleNumber = (data[0] << 8) | data[1];

            // 字节9: 年字节
            frameData.Year = 2000 + data[2];

            // 字节10: 月字节
            frameData.Month = data[3];

            // 字节11: 日字节
            frameData.Day = data[4];

            // 字节12: 时字节
            frameData.Hour = data[5];

            // 字节13: 分字节
            frameData.Minute = data[6];

            // 字节14: 秒字节
            frameData.Second = data[7];

            // 构造测量时间
            try
            {
                frameData.MeasureTime = new DateTime(frameData.Year, frameData.Month, frameData.Day,
                    frameData.Hour, frameData.Minute, frameData.Second);
            }
            catch
            {
                frameData.MeasureTime = DateTime.Now;
            }

            // 字节15: 标本类型字节
            frameData.SampleType = data[8];

            // 字节16: 标本参数类型字节
            frameData.SampleParamType = data[9];

            // 字节17: 标本参数值-整数
            frameData.SampleParamInt = data[10];

            // 字节18: 标本参数值-小数
            frameData.SampleParamDecimal = data[11];

            // 字节19: 标本参数值-小数点标志
            frameData.SampleParamDecimalFlag = data[12];

            // 字节20: 项目字节
            frameData.ProjectType = data[13];

            // 字节21: 结果-整数高8位
            // 字节22: 结果-整数低8位
            frameData.ResultIntPart = (data[14] << 8) | data[15];

            // 字节23: 结果-小数部分
            frameData.ResultDecimalPart = data[16];

            // 字节24: 结果-标志字节
            frameData.ResultFlag = data[17];

            // 字节25-27: 帧结束字 "end" (跳过)

            // 计算最终结果值
            frameData.ResultValue = frameData.ResultIntPart + (frameData.ResultDecimalPart / 100.0);

            // 判断是否有小数点
            frameData.HasDecimalPoint = (frameData.ResultFlag & 0x0F) == 0x01;

            return frameData;
        }

        /// <summary>
        /// 将十六进制字符串转换为字节数组
        /// </summary>
        private byte[] ConvertHexStringToBytes(string hexString)
        {
            // 移除空格和其他分隔符
            hexString = hexString.Replace(" ", "").Replace("-", "").Replace(":", "");

            if (hexString.Length % 2 != 0)
                throw new ArgumentException("十六进制字符串长度必须是偶数");

            byte[] bytes = new byte[hexString.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
            }
            return bytes;
        }

        /// <summary>
        /// 获取项目名称
        /// </summary>
        private string GetProjectName(byte projectType)
        {
            switch (projectType)
            {
                case 0x11: return "C反应蛋白";
                case 0x12: return "超敏C反应蛋白";
                case 0x13: return "微量白蛋白";
                case 0x14: return "胱抑素C";
                case 0x15: return "降钙素原";
                default: return $"未知项目_{projectType:X2}";
            }
        }

        /// <summary>
        /// 格式化结果值
        /// </summary>
        private string FormatResultValue(double value, bool hasDecimalPoint)
        {
            if (hasDecimalPoint)
                return value.ToString("F2");
            else
                return ((int)value).ToString();
        }

        /// <summary>
        /// 获取结果标志
        /// </summary>
        private string GetResultFlag(byte resultFlag)
        {
            byte rangeFlag = (byte)((resultFlag & 0xF0) >> 4);

            switch (rangeFlag)
            {
                case 0x1: return "H"; // 结果超上限
                case 0x4: return "L"; // 结果低于下限
                case 0x3: return "";  // 结果在范围内
                default: return "";
            }
        }

        /// <summary>
        /// 获取标本参数类型名称
        /// </summary>
        private string GetSpecimenParamName(byte paramType)
        {
            switch (paramType)
            {
                case 0x21: return "全血HCT";
                case 0x22: return "血浆稀释倍数";
                case 0x23: return "血清稀释倍数";
                case 0x24: return "尿液稀释倍数";
                case 0x25: return "其它稀释倍数";
                default: return $"未知参数_{paramType:X2}";
            }
        }

        /// <summary>
        /// 格式化标本参数值
        /// 根据协议文档：参数最终值 = (字节17) + (字节18 / 100)，0x31 有小数点, 0x32 无小数点
        /// </summary>
        private string FormatSpecimenParamValue(byte intPart, byte decimalPart, byte decimalFlag)
        {
            double value = intPart + (decimalPart / 100.0);
            bool hasDecimalPoint = decimalFlag == 0x31;

            if (hasDecimalPoint)
                return value.ToString("F2");
            else
                return ((int)value).ToString();
        }
    }

    /// <summary>
    /// QR100帧数据结构
    /// </summary>
    internal class QR100FrameData
    {
        public int SampleNumber { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public int Day { get; set; }
        public int Hour { get; set; }
        public int Minute { get; set; }
        public int Second { get; set; }
        public DateTime MeasureTime { get; set; }
        public byte SampleType { get; set; }
        public byte SampleParamType { get; set; }
        public byte SampleParamInt { get; set; }
        public byte SampleParamDecimal { get; set; }
        public byte SampleParamDecimalFlag { get; set; }
        public byte ProjectType { get; set; }
        public int ResultIntPart { get; set; }
        public byte ResultDecimalPart { get; set; }
        public byte ResultFlag { get; set; }
        public double ResultValue { get; set; }
        public bool HasDecimalPoint { get; set; }
    }
}