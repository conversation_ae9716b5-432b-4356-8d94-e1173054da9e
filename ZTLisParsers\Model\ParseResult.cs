using System;
using System.Collections.Generic;
using Model;

namespace ZTLisParsers.Parsers
{
    /// <summary>
    /// 解析结果实现类
    /// </summary>
    public class ParseResult
    {
        /// <summary>
        /// 解析是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息（解析失败时）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 解析出的结果列表
        /// </summary>
        public List<MdlLabInstrResultQ> Results { get; set; }

        /// <summary>
        /// 原始报文
        /// </summary>
        public string RawMessage { get; set; }

        /// <summary>
        /// 解析时间
        /// </summary>
        public DateTime ParsedAt { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ParseResult()
        {
            Results = new List<MdlLabInstrResultQ>();
            ParsedAt = DateTime.Now;
        }

        /// <summary>
        /// 创建成功的解析结果
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <param name="results">解析结果列表</param>
        /// <returns>解析结果</returns>
        public static ParseResult Success(string rawMessage, List<MdlLabInstrResultQ> results)
        {
            return new ParseResult
            {
                IsSuccess = true,
                RawMessage = rawMessage,
                Results = results ?? new List<MdlLabInstrResultQ>(),
                ErrorMessage = null
            };
        }

        /// <summary>
        /// 创建失败的解析结果
        /// </summary>
        /// <param name="rawMessage">原始报文</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>解析结果</returns>
        public static ParseResult Failure(string rawMessage, string errorMessage)
        {
            return new ParseResult
            {
                IsSuccess = false,
                RawMessage = rawMessage,
                ErrorMessage = errorMessage,
                Results = new List<MdlLabInstrResultQ>()
            };
        }
    }
}