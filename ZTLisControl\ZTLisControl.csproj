﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{186009E9-7A04-4519-A696-79E57BB80B4E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ZTLisControl</RootNamespace>
    <AssemblyName>ZTLisControl</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ComboDoctor.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboDoctor.designer.cs">
      <DependentUpon>ComboDoctor.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboLabInstrument.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboLabInstrument.Designer.cs">
      <DependentUpon>ComboLabInstrument.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboLabItem.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboLabItem.Designer.cs">
      <DependentUpon>ComboLabItem.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboLabItemCategory.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboLabItemCategory.designer.cs">
      <DependentUpon>ComboLabItemCategory.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboDept.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboDept.designer.cs">
      <DependentUpon>ComboDept.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboLabProfile.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboLabProfile.Designer.cs">
      <DependentUpon>ComboLabProfile.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboMethod.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboMethod.designer.cs">
      <DependentUpon>ComboMethod.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboMethodCategory.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboMethodCategory.designer.cs">
      <DependentUpon>ComboMethodCategory.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboRole.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboRole.Designer.cs">
      <DependentUpon>ComboRole.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboSampleType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboSampleType.designer.cs">
      <DependentUpon>ComboSampleType.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboSpecimenCondition.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboSpecimenCondition.designer.cs">
      <DependentUpon>ComboSpecimenCondition.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboSpecimenParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboSpecimenParam.Designer.cs">
      <DependentUpon>ComboSpecimenParam.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboUnit.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboUnit.Designer.cs">
      <DependentUpon>ComboUnit.cs</DependentUpon>
    </Compile>
    <Compile Include="ComboVisitType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ComboVisitType.Designer.cs">
      <DependentUpon>ComboVisitType.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SingleAbnormalFlag.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleAbnormalFlag.Designer.cs">
      <DependentUpon>SingleAbnormalFlag.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleAgeUnit.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleAgeUnit.Designer.cs">
      <DependentUpon>SingleAgeUnit.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleBaudRate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleBaudRate.Designer.cs">
      <DependentUpon>SingleBaudRate.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleComPort.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleComPort.Designer.cs">
      <DependentUpon>SingleComPort.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleDataBits.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleDataBits.Designer.cs">
      <DependentUpon>SingleDataBits.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleDbType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleDbType.Designer.cs">
      <DependentUpon>SingleDbType.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleConnType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleConnType.Designer.cs">
      <DependentUpon>SingleConnType.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleMsgFormatType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleMsgFormatType.Designer.cs">
      <DependentUpon>SingleMsgFormatType.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleInstrumentDriverClass.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleInstrumentDriverClass.Designer.cs">
      <DependentUpon>SingleInstrumentDriverClass.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleParamType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleParamType.Designer.cs">
      <DependentUpon>SingleParamType.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleParity.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleParity.Designer.cs">
      <DependentUpon>SingleParity.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleProtocolType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleProtocolType.Designer.cs">
      <DependentUpon>SingleProtocolType.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleResultFormat.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleResultFormat.Designer.cs">
      <DependentUpon>SingleResultFormat.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleDeptType.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleDeptType.Designer.cs">
      <DependentUpon>SingleDeptType.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleSex.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleSex.Designer.cs">
      <DependentUpon>SingleSex.cs</DependentUpon>
    </Compile>
    <Compile Include="SingleStopBits.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SingleStopBits.Designer.cs">
      <DependentUpon>SingleStopBits.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\DbProviderFactory\DbProviderFactory.csproj">
      <Project>{fdf5d6d6-d281-4884-a81a-d0c49c2f3bc7}</Project>
      <Name>DbProviderFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTLisEnum\ZTLisEnum.csproj">
      <Project>{2658ea9e-035b-43e4-b40f-6cebe092f702}</Project>
      <Name>ZTLisEnum</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ComboDoctor.resx">
      <DependentUpon>ComboDoctor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboLabInstrument.resx">
      <DependentUpon>ComboLabInstrument.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboLabItem.resx">
      <DependentUpon>ComboLabItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboLabProfile.resx">
      <DependentUpon>ComboLabProfile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboMethodCategory.resx">
      <DependentUpon>ComboMethodCategory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboSpecimenParam.resx">
      <DependentUpon>ComboSpecimenParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboUnit.resx">
      <DependentUpon>ComboUnit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ComboVisitType.resx">
      <DependentUpon>ComboVisitType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="SingleAgeUnit.resx">
      <DependentUpon>SingleAgeUnit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleBaudRate.resx">
      <DependentUpon>SingleBaudRate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleComPort.resx">
      <DependentUpon>SingleComPort.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleDbType.resx">
      <DependentUpon>SingleDbType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleInstrumentDriverClass.resx">
      <DependentUpon>SingleInstrumentDriverClass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleParamType.resx">
      <DependentUpon>SingleParamType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleParity.resx">
      <DependentUpon>SingleParity.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleDeptType.resx">
      <DependentUpon>SingleDeptType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleSex.resx">
      <DependentUpon>SingleSex.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SingleStopBits.resx">
      <DependentUpon>SingleStopBits.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>