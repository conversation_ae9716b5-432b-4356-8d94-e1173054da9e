﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlSysRpt_Class.cs
*
* 功 能： N/A
* 类 名： MdlSysRpt_Class
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-20 11:31:02   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 系统报表
	/// </summary>
	[Serializable]
	public partial class MdlSysRpt_Class
	{
		public MdlSysRpt_Class()
		{}
		#region Model
		private string _rpt_class_code;
		private string _rpt_class_name;
		/// <summary>
		/// 类别编码
		/// </summary>
		public string Rpt_Class_Code
		{
			set{ _rpt_class_code=value;}
			get{return _rpt_class_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string Rpt_Class_Name
		{
			set{ _rpt_class_name=value;}
			get{return _rpt_class_name;}
		}
		#endregion Model

	}
}

