﻿/// <summary>
/// 类说明：Assistant
/// 编 码 人：苏飞
/// 联系方式：361983679  
/// 更新网站：http://www.sufeinet.com/thread-655-1-1.html
/// </summary>

using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace Common
{
    /// <summary>
    /// 操作正则表达式的公共类
    /// </summary>    
    public class RegexHelper
    {
        #region 验证输入字符串是否与模式字符串匹配
        /// <summary>
        /// 验证输入字符串是否与模式字符串匹配，匹配返回true
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="pattern">模式字符串</param>        
        public static bool IsMatch(string input, string pattern)
        {
            return IsMatch(input, pattern, RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 验证输入字符串是否与模式字符串匹配，匹配返回true
        /// </summary>
        /// <param name="input">输入的字符串</param>
        /// <param name="pattern">模式字符串</param>
        /// <param name="options">筛选条件</param>
        public static bool IsMatch(string input, string pattern, RegexOptions options)
        {
            return Regex.IsMatch(input, pattern, options);
        }

        /// <summary>
        /// 验证是否是身份证号
        /// </summary>
        /// <param name="idCard">身份证号</param>
        /// <returns></returns>
        public static bool IsIDCard(string idCard)
        {
            return IsMatch(idCard, @"^([1-9]{1}\d{5}[1-2]{1}[09]{1}\d{2}(([0]{1}[1-9]{1})|([1]{1}[012]{1}))(([0]{1}[1-9]{1})|([12]{1}\d{1})|([3]{1}[01]{1}))(\d{4}|(\d{3}[x]{1})))$");
        }

        /// <summary>
        /// 验证手机号
        /// </summary>
        /// <param name="idCard">手机号</param>
        /// <returns></returns>
        public static bool IsMobile(string mobileNo)
        {
            return IsMatch(mobileNo, @"^[1][3,4,5,7,8,9][0-9]{9}$");
        }
        /// <summary>
        /// 验证IPv4地址
        /// </summary>
        /// <param name="idCard">ipv4的地址</param>
        /// <returns></returns>
        public static bool IsIPv4(string IPAddress)
        {
            return IsMatch(IPAddress, @"^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$");
        }
        /// <summary>
        /// 验证网址Url
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static bool IsValidUrl(string url)
        {
            string pattern = @"^(https?://)([a-zA-Z0-9]+([-.][a-zA-Z0-9]+)*\.[a-zA-Z]{2,}|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(:\d{1,5})?(/.*)?$";
            return IsMatch(url, pattern);
        }
        /// <summary>
        /// 文件名检查
        /// </summary>
        /// <returns></returns>
        public static bool CheckFileName(string fileName)
        {
            bool opResult = Regex.IsMatch(fileName, @"(?!((^(con)$)|^(con)\\..*|(^(prn)$)|^(prn)\\..*|(^(aux)$)|^(aux)\\..*|(^(nul)$)|^(nul)\\..*|(^(com)[1-9]$)|^(com)[1-9]\\..*|(^(lpt)[1-9]$)|^(lpt)[1-9]\\..*)|^\\s+|.*\\s$)(^[^\\\\\\/\\:\\<\\>\\*\\?\\\\\\""\\\\|]{1,255}$)");
            return opResult;
        }
        #endregion
    }
}
