using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictLabItemCategory2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictLabItemCategory _bllDictLabItemCategory = new BllDictLabItemCategory();
        Model.MdlDictLabItemCategory _mdlDictLabItemCategory = new MdlDictLabItemCategory();

        public DictLabItemCategory2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictLabItemCategory2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictLabItemCategory.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["ItemCategoryCode"] + "";
            TxtName.Text = row["ItemCategoryName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            TxtDescription.Text = row["Description"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写检验项目类别名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写检验项目类别编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["CategoryCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["CategoryName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictLabItemCategory.GetRecordCount($"ItemCategoryCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictLabItemCategory.GetRecordCount($"ItemCategoryName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictLabItemCategory.ItemCategoryCode = TxtCode.Text.Trim();
            _mdlDictLabItemCategory.ItemCategoryName = TxtName.Text.Trim();
            _mdlDictLabItemCategory.PinYin = TxtJc.Text.Trim();
            _mdlDictLabItemCategory.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictLabItemCategory.Description = TxtDescription.Text.Trim();
            _mdlDictLabItemCategory.Status = true;
            _mdlDictLabItemCategory.CreatedAt = DateTime.Now;
            _mdlDictLabItemCategory.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabItemCategory, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["ItemCategoryId"] = _bllDictLabItemCategory.Add(_mdlDictLabItemCategory);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictLabItemCategory = _bllDictLabItemCategory.GetModel(int.Parse(base.MyRow["ItemCategoryId"].ToString()));
            _mdlDictLabItemCategory.ItemCategoryCode = TxtCode.Text.Trim();
            _mdlDictLabItemCategory.ItemCategoryName = TxtName.Text.Trim();
            _mdlDictLabItemCategory.PinYin = TxtJc.Text.Trim();
            _mdlDictLabItemCategory.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictLabItemCategory.Description = TxtDescription.Text.Trim();
            _mdlDictLabItemCategory.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictLabItemCategory, base.MyRow);

            //数据保存
            try
            {
                _bllDictLabItemCategory.Update(_mdlDictLabItemCategory);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 控件动作
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert == true)
                {
                    this.DataAdd();
                }
                else
                {
                    this.DataEdit();
                }
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }

        #endregion
    }
}