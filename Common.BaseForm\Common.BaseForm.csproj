﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1DD7020C-8603-438A-8015-34702DABC229}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Common.BaseForm</RootNamespace>
    <AssemblyName>Common.BaseForm</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.TreeView.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="1.基础窗体\Base.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.基础窗体\Base.Designer.cs">
      <DependentUpon>Base.cs</DependentUpon>
    </Compile>
    <Compile Include="1.基础窗体\BaseChild.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.基础窗体\BaseChild.Designer.cs">
      <DependentUpon>BaseChild.cs</DependentUpon>
    </Compile>
    <Compile Include="1.基础窗体\BaseFather.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.基础窗体\BaseFather.Designer.cs">
      <DependentUpon>BaseFather.cs</DependentUpon>
    </Compile>
    <Compile Include="2.一级字典\BaseDict1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.一级字典\BaseDict1.Designer.cs">
      <DependentUpon>BaseDict1.cs</DependentUpon>
    </Compile>
    <Compile Include="2.一级字典\BaseDict2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.一级字典\BaseDict2.Designer.cs">
      <DependentUpon>BaseDict2.cs</DependentUpon>
    </Compile>
    <Compile Include="3.二级字典\BaseDict21.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="3.二级字典\BaseDict21.Designer.cs">
      <DependentUpon>BaseDict21.cs</DependentUpon>
    </Compile>
    <Compile Include="3.二级字典\BaseDict22.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="3.二级字典\BaseDict22.Designer.cs">
      <DependentUpon>BaseDict22.cs</DependentUpon>
    </Compile>
    <Compile Include="4.一对一\OneFormRk1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.一对一\OneFormRk1.Designer.cs">
      <DependentUpon>OneFormRk1.cs</DependentUpon>
    </Compile>
    <Compile Include="4.一对多\DoubleFormRK1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.一对多\DoubleFormRK1.Designer.cs">
      <DependentUpon>DoubleFormRK1.cs</DependentUpon>
    </Compile>
    <Compile Include="4.一对多\DoubleFormRK2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.一对多\DoubleFormRK2.Designer.cs">
      <DependentUpon>DoubleFormRK2.cs</DependentUpon>
    </Compile>
    <Compile Include="4.一对多\DoubleFormSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.一对多\DoubleFormSearch.Designer.cs">
      <DependentUpon>DoubleFormSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="4.递归二级字典\RecursionBaseDict21.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.递归二级字典\RecursionBaseDict21.Designer.cs">
      <DependentUpon>RecursionBaseDict21.cs</DependentUpon>
    </Compile>
    <Compile Include="4.递归二级字典\RecursionBaseDict22.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.递归二级字典\RecursionBaseDict22.Designer.cs">
      <DependentUpon>RecursionBaseDict22.cs</DependentUpon>
    </Compile>
    <Compile Include="LoginBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginBase.Designer.cs">
      <DependentUpon>LoginBase.cs</DependentUpon>
    </Compile>
    <Compile Include="MainBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainBase.Designer.cs">
      <DependentUpon>MainBase.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="PublicFunc.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="1.基础窗体\Base.resx">
      <DependentUpon>Base.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.基础窗体\BaseChild.resx">
      <DependentUpon>BaseChild.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.基础窗体\BaseFather.resx">
      <DependentUpon>BaseFather.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.一级字典\BaseDict1.resx">
      <DependentUpon>BaseDict1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.一级字典\BaseDict2.resx">
      <DependentUpon>BaseDict2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="3.二级字典\BaseDict21.resx">
      <DependentUpon>BaseDict21.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.一对一\OneFormRk1.resx">
      <DependentUpon>OneFormRk1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.一对多\DoubleFormRK1.resx">
      <DependentUpon>DoubleFormRK1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.一对多\DoubleFormRK2.resx">
      <DependentUpon>DoubleFormRK2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.一对多\DoubleFormSearch.resx">
      <DependentUpon>DoubleFormSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.递归二级字典\RecursionBaseDict21.resx">
      <DependentUpon>RecursionBaseDict21.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginBase.resx">
      <DependentUpon>LoginBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainBase.resx">
      <DependentUpon>MainBase.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>