using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ZTLisControl
{
    public partial class SingleDataBits : CustomControl.MySingleComobo
    {
        public SingleDataBits()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        public void Init()
        {
            Additem = "5,5";
            Additem = "6,6";
            Additem = "7,7";
            Additem = "8,8";
            DisplayColumns[1].Visible = false;
            DroupDownWidth = Width - (int)CaptainWidth;
            SelectedIndex = 0;
            ItemHeight = 20;
        }
    }
}