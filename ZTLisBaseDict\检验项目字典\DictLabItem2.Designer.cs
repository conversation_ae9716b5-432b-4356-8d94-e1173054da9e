namespace ZTLisBaseDict
{
    partial class DictLabItem2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.TableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.TxtCode = new CustomControl.MyTextBox();
            this.TxtName = new CustomControl.MyTextBox();
            this.TxtJc = new CustomControl.MyTextBox();
            this.TxtEnglishName = new CustomControl.MyTextBox();
            this.TxtReportName = new CustomControl.MyTextBox();
            this.ComboItemCategory = new ZTLisControl.ComboLabItemCategory();
            this.ComboSampleType = new ZTLisControl.ComboSampleType();
            this.NumPrecision = new CustomControl.MyNumericEdit();
            this.ComboMethod = new ZTLisControl.ComboMethod();
            this.TxtLoincCode = new CustomControl.MyTextBox();
            this.ChkIsCalculated = new System.Windows.Forms.CheckBox();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.BtnSave = new CustomControl.MyButton();
            this.BtnCancel = new CustomControl.MyButton();
            this.comboUnit1 = new ZTLisControl.ComboUnit();
            this.TableLayoutPanel1.SuspendLayout();
            this.Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // TableLayoutPanel1
            // 
            this.TableLayoutPanel1.ColumnCount = 4;
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.Controls.Add(this.comboUnit1, 1, 4);
            this.TableLayoutPanel1.Controls.Add(this.TxtCode, 1, 0);
            this.TableLayoutPanel1.Controls.Add(this.TxtName, 1, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtJc, 2, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtEnglishName, 1, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtReportName, 2, 2);
            this.TableLayoutPanel1.Controls.Add(this.ComboItemCategory, 1, 3);
            this.TableLayoutPanel1.Controls.Add(this.ComboSampleType, 2, 3);
            this.TableLayoutPanel1.Controls.Add(this.NumPrecision, 2, 4);
            this.TableLayoutPanel1.Controls.Add(this.ComboMethod, 1, 5);
            this.TableLayoutPanel1.Controls.Add(this.TxtLoincCode, 2, 5);
            this.TableLayoutPanel1.Controls.Add(this.ChkIsCalculated, 1, 6);
            this.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.TableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.TableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.TableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.TableLayoutPanel1.Name = "TableLayoutPanel1";
            this.TableLayoutPanel1.RowCount = 8;
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.TableLayoutPanel1.Size = new System.Drawing.Size(556, 212);
            this.TableLayoutPanel1.TabIndex = 8;
            // 
            // TxtCode
            // 
            this.TxtCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtCode.Captain = "编    码";
            this.TxtCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtCode.CaptainWidth = 70F;
            this.TxtCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtCode.EditMask = null;
            this.TxtCode.Location = new System.Drawing.Point(31, 3);
            this.TxtCode.Multiline = false;
            this.TxtCode.Name = "TxtCode";
            this.TxtCode.PasswordChar = '\0';
            this.TxtCode.ReadOnly = false;
            this.TxtCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtCode.SelectionStart = 0;
            this.TxtCode.SelectStart = 0;
            this.TxtCode.Size = new System.Drawing.Size(244, 23);
            this.TxtCode.TabIndex = 0;
            this.TxtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtCode.Watermark = null;
            // 
            // TxtName
            // 
            this.TxtName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtName.Captain = "名    称";
            this.TxtName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtName.CaptainWidth = 70F;
            this.TxtName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtName.EditMask = null;
            this.TxtName.Location = new System.Drawing.Point(31, 32);
            this.TxtName.Multiline = false;
            this.TxtName.Name = "TxtName";
            this.TxtName.PasswordChar = '\0';
            this.TxtName.ReadOnly = false;
            this.TxtName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtName.SelectionStart = 0;
            this.TxtName.SelectStart = 0;
            this.TxtName.Size = new System.Drawing.Size(244, 23);
            this.TxtName.TabIndex = 1;
            this.TxtName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtName.Watermark = null;
            this.TxtName.Validated += new System.EventHandler(this.TxtName_Validated);
            // 
            // TxtJc
            // 
            this.TxtJc.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtJc.Captain = "简    称";
            this.TxtJc.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtJc.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtJc.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtJc.CaptainWidth = 70F;
            this.TxtJc.ContentForeColor = System.Drawing.Color.Black;
            this.TxtJc.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtJc.EditMask = null;
            this.TxtJc.Location = new System.Drawing.Point(281, 32);
            this.TxtJc.Multiline = false;
            this.TxtJc.Name = "TxtJc";
            this.TxtJc.PasswordChar = '\0';
            this.TxtJc.ReadOnly = false;
            this.TxtJc.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtJc.SelectionStart = 0;
            this.TxtJc.SelectStart = 0;
            this.TxtJc.Size = new System.Drawing.Size(244, 23);
            this.TxtJc.TabIndex = 2;
            this.TxtJc.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtJc.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtJc.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtJc.Watermark = null;
            // 
            // TxtEnglishName
            // 
            this.TxtEnglishName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtEnglishName.Captain = "英文名称";
            this.TxtEnglishName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtEnglishName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtEnglishName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtEnglishName.CaptainWidth = 70F;
            this.TxtEnglishName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtEnglishName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtEnglishName.EditMask = null;
            this.TxtEnglishName.Location = new System.Drawing.Point(31, 61);
            this.TxtEnglishName.Multiline = false;
            this.TxtEnglishName.Name = "TxtEnglishName";
            this.TxtEnglishName.PasswordChar = '\0';
            this.TxtEnglishName.ReadOnly = false;
            this.TxtEnglishName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtEnglishName.SelectionStart = 0;
            this.TxtEnglishName.SelectStart = 0;
            this.TxtEnglishName.Size = new System.Drawing.Size(244, 23);
            this.TxtEnglishName.TabIndex = 3;
            this.TxtEnglishName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtEnglishName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtEnglishName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtEnglishName.Watermark = null;
            // 
            // TxtReportName
            // 
            this.TxtReportName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtReportName.Captain = "报告名称";
            this.TxtReportName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtReportName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtReportName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtReportName.CaptainWidth = 70F;
            this.TxtReportName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtReportName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtReportName.EditMask = null;
            this.TxtReportName.Location = new System.Drawing.Point(281, 61);
            this.TxtReportName.Multiline = false;
            this.TxtReportName.Name = "TxtReportName";
            this.TxtReportName.PasswordChar = '\0';
            this.TxtReportName.ReadOnly = false;
            this.TxtReportName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtReportName.SelectionStart = 0;
            this.TxtReportName.SelectStart = 0;
            this.TxtReportName.Size = new System.Drawing.Size(244, 23);
            this.TxtReportName.TabIndex = 4;
            this.TxtReportName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtReportName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtReportName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtReportName.Watermark = null;
            // 
            // ComboItemCategory
            // 
            this.ComboItemCategory.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboItemCategory.Bookmark = -1;
            this.ComboItemCategory.Captain = "项目类别";
            this.ComboItemCategory.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboItemCategory.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboItemCategory.CaptainWidth = 70F;
            this.ComboItemCategory.ColumnCaptionHeight = 20;
            this.ComboItemCategory.DataSource = null;
            this.ComboItemCategory.DataView = null;
            this.ComboItemCategory.ItemHeight = 18;
            this.ComboItemCategory.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboItemCategory.Location = new System.Drawing.Point(31, 90);
            this.ComboItemCategory.MaximumSize = new System.Drawing.Size(8571, 23);
            this.ComboItemCategory.MinimumSize = new System.Drawing.Size(0, 17);
            this.ComboItemCategory.Name = "ComboItemCategory";
            this.ComboItemCategory.ReadOnly = false;
            this.ComboItemCategory.Row = 0;
            this.ComboItemCategory.Size = new System.Drawing.Size(244, 23);
            this.ComboItemCategory.TabIndex = 5;
            this.ComboItemCategory.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // ComboSampleType
            // 
            this.ComboSampleType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboSampleType.Bookmark = -1;
            this.ComboSampleType.Captain = "标本类型";
            this.ComboSampleType.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboSampleType.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboSampleType.CaptainWidth = 70F;
            this.ComboSampleType.ColumnCaptionHeight = 20;
            this.ComboSampleType.DataSource = null;
            this.ComboSampleType.DataView = null;
            this.ComboSampleType.ItemHeight = 18;
            this.ComboSampleType.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboSampleType.Location = new System.Drawing.Point(281, 90);
            this.ComboSampleType.MaximumSize = new System.Drawing.Size(8571, 23);
            this.ComboSampleType.MinimumSize = new System.Drawing.Size(0, 17);
            this.ComboSampleType.Name = "ComboSampleType";
            this.ComboSampleType.ReadOnly = false;
            this.ComboSampleType.Row = 0;
            this.ComboSampleType.Size = new System.Drawing.Size(244, 23);
            this.ComboSampleType.TabIndex = 6;
            this.ComboSampleType.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // NumPrecision
            // 
            this.NumPrecision.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumPrecision.Captain = "精    度";
            this.NumPrecision.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumPrecision.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumPrecision.CaptainWidth = 70F;
            this.NumPrecision.Location = new System.Drawing.Point(281, 119);
            this.NumPrecision.MaximumSize = new System.Drawing.Size(85714, 69);
            this.NumPrecision.MinimumSize = new System.Drawing.Size(0, 17);
            this.NumPrecision.Name = "NumPrecision";
            this.NumPrecision.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Plus) 
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal) 
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumPrecision.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumPrecision.ReadOnly = false;
            this.NumPrecision.Size = new System.Drawing.Size(244, 23);
            this.NumPrecision.TabIndex = 8;
            this.NumPrecision.ValueIsDbNull = false;
            // 
            // ComboMethod
            // 
            this.ComboMethod.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ComboMethod.Bookmark = -1;
            this.ComboMethod.Captain = "默认方法";
            this.ComboMethod.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboMethod.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.ComboMethod.CaptainWidth = 70F;
            this.ComboMethod.ColumnCaptionHeight = 20;
            this.ComboMethod.DataSource = null;
            this.ComboMethod.DataView = null;
            this.ComboMethod.ItemHeight = 18;
            this.ComboMethod.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ComboMethod.Location = new System.Drawing.Point(31, 148);
            this.ComboMethod.MaximumSize = new System.Drawing.Size(8571, 23);
            this.ComboMethod.MinimumSize = new System.Drawing.Size(0, 17);
            this.ComboMethod.Name = "ComboMethod";
            this.ComboMethod.ReadOnly = false;
            this.ComboMethod.Row = 0;
            this.ComboMethod.Size = new System.Drawing.Size(244, 23);
            this.ComboMethod.TabIndex = 9;
            this.ComboMethod.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // TxtLoincCode
            // 
            this.TxtLoincCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtLoincCode.Captain = "LOINC码";
            this.TxtLoincCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtLoincCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLoincCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtLoincCode.CaptainWidth = 70F;
            this.TxtLoincCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtLoincCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtLoincCode.EditMask = null;
            this.TxtLoincCode.Location = new System.Drawing.Point(281, 148);
            this.TxtLoincCode.Multiline = false;
            this.TxtLoincCode.Name = "TxtLoincCode";
            this.TxtLoincCode.PasswordChar = '\0';
            this.TxtLoincCode.ReadOnly = false;
            this.TxtLoincCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtLoincCode.SelectionStart = 0;
            this.TxtLoincCode.SelectStart = 0;
            this.TxtLoincCode.Size = new System.Drawing.Size(244, 23);
            this.TxtLoincCode.TabIndex = 10;
            this.TxtLoincCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtLoincCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtLoincCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtLoincCode.Watermark = null;
            // 
            // ChkIsCalculated
            // 
            this.ChkIsCalculated.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.ChkIsCalculated.AutoSize = true;
            this.ChkIsCalculated.Location = new System.Drawing.Point(31, 179);
            this.ChkIsCalculated.Name = "ChkIsCalculated";
            this.ChkIsCalculated.Size = new System.Drawing.Size(244, 18);
            this.ChkIsCalculated.TabIndex = 11;
            this.ChkIsCalculated.Text = "是否为计算项目";
            this.ChkIsCalculated.UseVisualStyleBackColor = true;
            // 
            // Panel1
            // 
            this.Panel1.Controls.Add(this.BtnSave);
            this.Panel1.Controls.Add(this.BtnCancel);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel1.Location = new System.Drawing.Point(0, 216);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(556, 33);
            this.Panel1.TabIndex = 9;
            // 
            // BtnSave
            // 
            this.BtnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(386, -5);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.BtnCancel.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnCancel.Location = new System.Drawing.Point(462, -5);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(70, 35);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // comboUnit1
            // 
            this.comboUnit1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.comboUnit1.Bookmark = -1;
            this.comboUnit1.Captain = "默认单位";
            this.comboUnit1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboUnit1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.comboUnit1.CaptainWidth = 70F;
            this.comboUnit1.ColumnCaptionHeight = 20;
            this.comboUnit1.DataSource = null;
            this.comboUnit1.DataView = null;
            this.comboUnit1.ItemHeight = 18;
            this.comboUnit1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboUnit1.Location = new System.Drawing.Point(31, 119);
            this.comboUnit1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.comboUnit1.MinimumSize = new System.Drawing.Size(0, 20);
            this.comboUnit1.Name = "comboUnit1";
            this.comboUnit1.ReadOnly = false;
            this.comboUnit1.Row = 0;
            this.comboUnit1.Size = new System.Drawing.Size(244, 23);
            this.comboUnit1.TabIndex = 7;
            this.comboUnit1.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // DictLabItem2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(556, 249);
            this.Controls.Add(this.TableLayoutPanel1);
            this.Controls.Add(this.Panel1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Name = "DictLabItem2";
            this.Text = "检验项目详细信息";
            this.Load += new System.EventHandler(this.DictLabItem2_Load);
            this.TableLayoutPanel1.ResumeLayout(false);
            this.TableLayoutPanel1.PerformLayout();
            this.Panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.TableLayoutPanel TableLayoutPanel1;
        internal CustomControl.MyTextBox TxtCode;
        internal CustomControl.MyTextBox TxtName;
        internal CustomControl.MyTextBox TxtJc;
        internal CustomControl.MyTextBox TxtEnglishName;
        internal CustomControl.MyTextBox TxtReportName;
        internal ZTLisControl.ComboLabItemCategory ComboItemCategory;
        internal ZTLisControl.ComboSampleType ComboSampleType;
        internal CustomControl.MyNumericEdit NumPrecision;
        internal ZTLisControl.ComboMethod ComboMethod;
        internal CustomControl.MyTextBox TxtLoincCode;
        internal System.Windows.Forms.CheckBox ChkIsCalculated;
        internal System.Windows.Forms.Panel Panel1;
        internal CustomControl.MyButton BtnSave;
        internal CustomControl.MyButton BtnCancel;
        private ZTLisControl.ComboUnit comboUnit1;
    }
}