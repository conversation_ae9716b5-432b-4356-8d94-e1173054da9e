﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysRpt.cs
*
* 功 能： N/A
* 类 名： DalSysRpt
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-20 11:31:02   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysRpt
	/// </summary>
	public partial class DalSysRpt:IDalSysRpt
	{
		public DalSysRpt()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string RptCode)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysRpt");
			strSql.Append(" where RptCode=@RptCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RptCode", SqlDbType.Char,4)			};
			parameters[0].Value = RptCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysRpt model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysRpt(");
			strSql.Append("Rpt_Class_Code,RptCode,RptName,RptPath,RptContent,EditDTime,Editor)");
			strSql.Append(" values (");
			strSql.Append("@Rpt_Class_Code,@RptCode,@RptName,@RptPath,@RptContent,@EditDTime,@Editor)");
			SqlParameter[] parameters = {
					new SqlParameter("@Rpt_Class_Code", SqlDbType.VarChar,50),
					new SqlParameter("@RptCode", SqlDbType.Char,4),
					new SqlParameter("@RptName", SqlDbType.VarChar,50),
					new SqlParameter("@RptPath", SqlDbType.VarChar,50),
					new SqlParameter("@RptContent", SqlDbType.Image),
					new SqlParameter("@EditDTime", SqlDbType.SmallDateTime),
					new SqlParameter("@Editor", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Rpt_Class_Code;
			parameters[1].Value = model.RptCode;
			parameters[2].Value = model.RptName;
			parameters[3].Value = model.RptPath;
			parameters[4].Value = model.RptContent;
			parameters[5].Value = model.EditDTime;
			parameters[6].Value = model.Editor;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysRpt model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysRpt set ");
			strSql.Append("Rpt_Class_Code=@Rpt_Class_Code,");
			strSql.Append("RptName=@RptName,");
			strSql.Append("RptPath=@RptPath,");
			strSql.Append("RptContent=@RptContent,");
			strSql.Append("EditDTime=@EditDTime,");
			strSql.Append("Editor=@Editor");
			strSql.Append(" where RptCode=@RptCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@Rpt_Class_Code", SqlDbType.VarChar,50),
					new SqlParameter("@RptName", SqlDbType.VarChar,50),
					new SqlParameter("@RptPath", SqlDbType.VarChar,50),
					new SqlParameter("@RptContent", SqlDbType.Image),
					new SqlParameter("@EditDTime", SqlDbType.SmallDateTime),
					new SqlParameter("@Editor", SqlDbType.VarChar,50),
					new SqlParameter("@RptCode", SqlDbType.Char,4)};
			parameters[0].Value = model.Rpt_Class_Code;
			parameters[1].Value = model.RptName;
			parameters[2].Value = model.RptPath;
			parameters[3].Value = model.RptContent;
			parameters[4].Value = model.EditDTime;
			parameters[5].Value = model.Editor;
			parameters[6].Value = model.RptCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string RptCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRpt ");
			strSql.Append(" where RptCode=@RptCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RptCode", SqlDbType.Char,4)			};
			parameters[0].Value = RptCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string RptCodelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRpt ");
			strSql.Append(" where RptCode in ("+RptCodelist + ")  ");
			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRpt GetModel(string RptCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Rpt_Class_Code,RptCode,RptName,RptPath,RptContent,EditDTime,Editor from SysRpt ");
			strSql.Append(" where RptCode=@RptCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RptCode", SqlDbType.Char,4)			};
			parameters[0].Value = RptCode;

			Model.MdlSysRpt model=new Model.MdlSysRpt();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRpt DataRowToModel(DataRow row)
		{
			Model.MdlSysRpt model=new Model.MdlSysRpt();
			if (row != null)
			{
				if(row["Rpt_Class_Code"]!=null)
				{
					model.Rpt_Class_Code=row["Rpt_Class_Code"].ToString();
				}
				if(row["RptCode"]!=null)
				{
					model.RptCode=row["RptCode"].ToString();
				}
				if(row["RptName"]!=null)
				{
					model.RptName=row["RptName"].ToString();
				}
				if(row["RptPath"]!=null)
				{
					model.RptPath=row["RptPath"].ToString();
				}
				if(row["RptContent"]!=null && row["RptContent"].ToString()!="")
				{
					model.RptContent=(byte[])row["RptContent"];
				}
				if(row["EditDTime"]!=null && row["EditDTime"].ToString()!="")
				{
					model.EditDTime=DateTime.Parse(row["EditDTime"].ToString());
				}
				if(row["Editor"]!=null)
				{
					model.Editor=row["Editor"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Rpt_Class_Code,RptCode,RptName,RptPath,RptContent,EditDTime,Editor ");
			strSql.Append(" FROM SysRpt ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Rpt_Class_Code,RptCode,RptName,RptPath,RptContent,EditDTime,Editor ");
			strSql.Append(" FROM SysRpt ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysRpt ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.RptCode desc");
			}
			strSql.Append(")AS Row, T.*  from SysRpt T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysRpt";
			parameters[1].Value = "RptCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.MdlSysRpt GetModelByRptName(string RptName)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Rpt_Class_Code,RptCode,RptName,RptPath,RptContent,EditDTime,Editor from SysRpt ");
            strSql.Append(" where RptName=@RptName ");
            SqlParameter[] parameters = {
                new SqlParameter("@RptName", SqlDbType.VarChar,50)          };
            parameters[0].Value = RptName;

            Model.MdlSysRpt model = new Model.MdlSysRpt();
            DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

