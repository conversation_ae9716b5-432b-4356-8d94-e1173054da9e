﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisVar
{
    public class Var
    {
        /// <summary>
        /// 人员编码
        /// </summary>
        public static int UserId { get; set; }
        /// <summary>
        /// 人员姓名
        /// </summary>
        public static string UserName { get; set; }
        /// <summary>
        /// 医生ID
        /// </summary>
        public static int DoctorId { get; set; }
        /// <summary>
        /// 角色编码
        /// </summary>
        public static string Role_Code { get; set; }

        /// <summary>
        /// 用户权限
        /// </summary>
        public static List<Model.MdlSysMenu1> UserPermission { get; set; }
    }
}
