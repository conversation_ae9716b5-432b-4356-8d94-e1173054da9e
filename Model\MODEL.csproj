﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3FB6EA13-2C32-4D08-A426-C22224F72121}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Model</RootNamespace>
    <AssemblyName>Model</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MdlDictDepartment.cs" />
    <Compile Include="MdlDictDoctor.cs" />
    <Compile Include="MdlDictLabItem.cs" />
    <Compile Include="MdlDictLabItemCalculation.cs" />
    <Compile Include="MdlDictLabItemCategory.cs" />
    <Compile Include="MdlDictLabItemRefRange.cs" />
    <Compile Include="MdlDictLabProfile.cs" />
    <Compile Include="MdlDictLabProfileItemMap.cs" />
    <Compile Include="MdlDictMethod.cs" />
    <Compile Include="MdlDictMethodCategory.cs" />
    <Compile Include="MdlDictSampleType.cs" />
    <Compile Include="MdlDictSpecimenCondition.cs" />
    <Compile Include="MdlDictSpecimenParam.cs" />
    <Compile Include="MdlDictUnit.cs" />
    <Compile Include="MdlDictVisitType.cs" />
    <Compile Include="MdlLabInstrItemMap.cs" />
    <Compile Include="MdlLabInstrRawMsg.cs" />
    <Compile Include="MdlLabInstrResultQ.cs" />
    <Compile Include="MdlLabInstrSpecimenParamMap.cs" />
    <Compile Include="MdlLabInstrument.cs" />
    <Compile Include="MdlLabOrder.cs" />
    <Compile Include="MdlLabOrderItem.cs" />
    <Compile Include="MdlLabResult.cs" />
    <Compile Include="MdlLabResultReview.cs" />
    <Compile Include="MdlLabSpecimen.cs" />
    <Compile Include="MdlSysMenu1.cs" />
    <Compile Include="MdlSysMenu2.cs" />
    <Compile Include="MdlSysModule.cs" />
    <Compile Include="MdlSysModuleAuth.cs" />
    <Compile Include="MdlSysPara.cs" />
    <Compile Include="MdlSysRole.cs" />
    <Compile Include="MdlSysRoleAuth.cs" />
    <Compile Include="MdlSysRoleModule.cs" />
    <Compile Include="MdlSysRpt.cs" />
    <Compile Include="MdlSysRpt_Class.cs" />
    <Compile Include="MdlSysRpt_Class_Level.cs" />
    <Compile Include="MdlSysUser.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\DBUtility\DBUtility.csproj">
      <Project>{9aa4cca5-b6d1-4719-b1af-880910bbc87e}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>