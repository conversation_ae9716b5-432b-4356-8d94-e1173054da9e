using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;
using ZTLisControl;

namespace ZTLisBaseDict
{
    public partial class LabInstrSpecimenParamMap1 : Common.BaseForm.BaseDict1
    {
        BLL.BllLabInstrSpecimenParamMap _bllLabInstrSpecimenParamMap = new BllLabInstrSpecimenParamMap();
        public LabInstrSpecimenParamMap1()
        {
            InitializeComponent();
        }

        private void LabInstrSpecimenParamMap1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }

        #region 自定义函数

        private void FormInit()
        {
            // 初始化仪器下拉框
            comboLabInstrument1.Init();
            comboLabInstrument1.SelectedIndex = 0;

            // 初始化网格
            myGrid1.Init_Grid();
            myGrid1.Init_Column("仪器参数编码", "InstrParamCode", 120, "左", "", false);
            myGrid1.Init_Column("仪器参数名称", "InstrParamName", 180, "左", "", false);
            myGrid1.Init_Column("LIS参数编码", "ParamCode", 120, "左", "", false);
            myGrid1.Init_Column("LIS参数名称", "ParamName", 180, "左", "", false);
            myGrid1.Init_Column("参数类型", "ParamType", 80, "中", "", false);
            myGrid1.Init_Column("单位", "Unit", 60, "左", "", false);
            myGrid1.Init_Column("转换系数", "ConversionFactor", 80, "右", "0.0000", false);
            myGrid1.Init_Column("转换偏移", "ConversionOffset", 80, "右", "0.0000", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.AllowSort = true;
        }

        private void DataInit()
        {
            if (comboLabInstrument1.SelectedValue == null || comboLabInstrument1.SelectedValue.ToString() == "")
            {
                return;
            }
            string strWhere = "LabInstrSpecimenParamMap.InstrumentId = " + comboLabInstrument1.SelectedValue.ToString();
            base.MyTable = _bllLabInstrSpecimenParamMap.GetListWithParams(strWhere).Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["MapId"] };
            base.MyTable.Columns["MapId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        protected override void DataEdit(bool insert)
        {
            if (comboLabInstrument1.SelectedValue == null || comboLabInstrument1.SelectedValue.ToString() == "")
            {
                MessageBox.Show("请先选择仪器!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
                base.MyRow["InstrumentId"] = comboLabInstrument1.SelectedValue;
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            LabInstrSpecimenParamMap2 vform = new LabInstrSpecimenParamMap2(int.Parse(comboLabInstrument1.SelectedValue.ToString()), base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除参数映射：" + this.myGrid1.Columns["InstrParamName"].Value, "提示",
                MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;

            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllLabInstrSpecimenParamMap.Delete(long.Parse(base.MyRow["MapId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        #endregion

        #region 控件事件
        private void comboLabInstrument1_SelectedValueChanged(object sender, EventArgs e)
        {
            this.DataInit();
        }

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("InstrParamCode+InstrParamName+ParamCode+ParamName", TxtFilter.Text.Trim());
        }

        #endregion
    }
}