using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// TCP连接器实现，用于通过TCP/IP协议与仪器通讯
    /// </summary>
    public class TcpConnector : BaseConnector
    {
        #region 私有字段

        private TcpClient _tcpClient;
        private NetworkStream _networkStream;
        private Thread _receiveThread;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TcpConnector()
        {
        }

        #endregion

        #region 基类抽象方法实现

        /// <summary>
        /// 验证连接类型是否支持
        /// </summary>
        /// <param name="instrumentConfig">仪器配置</param>
        protected override void ValidateConnectionType(LabInstrument instrumentConfig)
        {
            if (string.IsNullOrEmpty(instrumentConfig.IpHost))
                throw new ArgumentException("TCP连接器需要配置IP地址", nameof(instrumentConfig));

            if (!instrumentConfig.IpPort.HasValue || instrumentConfig.IpPort.Value <= 0)
                throw new ArgumentException("TCP连接器需要配置有效的端口号", nameof(instrumentConfig));
        }

        /// <summary>
        /// 初始化特定连接的配置
        /// </summary>
        protected override void InitializeSpecificConfiguration()
        {
            // TCP连接器不需要特殊的初始化配置
        }

        /// <summary>
        /// 启动特定类型的连接
        /// </summary>
        /// <returns>是否成功</returns>
        protected override bool StartSpecificConnection()
        {
            // 启动连接线程
            _receiveThread = new Thread(ReceiveThreadProc)
            {
                Name = $"TCP_Receive_{InstrumentId}",
                IsBackground = true
            };
            _receiveThread.Start();

            return true;
        }

        /// <summary>
        /// 停止特定类型的连接
        /// </summary>
        protected override void StopSpecificConnection()
        {
            // 关闭TCP连接
            CloseConnection();

            // 等待线程结束
            if (_receiveThread != null && _receiveThread.IsAlive)
            {
                if (!_receiveThread.Join(5000))
                {
                    _receiveThread.Abort();
                }
                _receiveThread = null;
            }
        }

        /// <summary>
        /// 获取连接器类型名称
        /// </summary>
        /// <returns>连接器类型名称</returns>
        protected override string GetConnectorTypeName()
        {
            return "TCP";
        }

        /// <summary>
        /// 获取连接信息字符串
        /// </summary>
        /// <returns>连接信息</returns>
        protected override string GetConnectionInfo()
        {
            return $"{_config.IpHost}:{_config.IpPort}";
        }



        /// <summary>
        /// 检查连接是否活跃
        /// </summary>
        /// <returns>连接是否活跃</returns>
        protected override bool IsConnectionAlive()
        {
            return _networkStream != null && _networkStream.CanWrite;
        }

        /// <summary>
        /// 发送心跳包
        /// </summary>
        protected override void SendHeartbeat()
        {
            // TCP心跳可以发送一个简单的字符或者什么都不发送
            // 这里可以根据具体协议需求实现
        }

        /// <summary>
        /// 发送原始数据
        /// </summary>
        /// <param name="data">要发送的原始数据</param>
        /// <returns>是否成功</returns>
        protected override bool SendRawData(byte[] data)
        {
            if (data == null || data.Length == 0)
                return false;

            if (!_isRunning || _networkStream == null || !_networkStream.CanWrite)
            {
                WriteLog("TCP连接未建立，无法发送原始数据");
                return false;
            }

            try
            {
                lock (_lockObject)
                {
                    _networkStream.Write(data, 0, data.Length);
                    _networkStream.Flush();

                    _statistics.SentCommandsCount++;
                    _statistics.LastActivityTime = DateTime.Now;

                    WriteLog($"发送原始数据成功: {ConvertBytesToHexString(data)}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetSendFailureErrorCode(), $"发送原始数据失败: {ex.Message}", ex));
                return false;
            }
        }

        /// <summary>
        /// 释放子类特定的资源
        /// </summary>
        protected override void DisposeSpecificResources()
        {
            CloseConnection();
        }



        #endregion

        #region IConnector接口实现

        /// <summary>
        /// 发送命令到仪器
        /// </summary>
        /// <param name="command">要发送的命令</param>
        /// <returns>操作是否成功</returns>
        public override bool SendCommand(string command)
        {
            if (string.IsNullOrEmpty(command))
                return false;

            if (!_isRunning || _networkStream == null || !_networkStream.CanWrite)
            {
                WriteLog("TCP连接未建立，无法发送命令");
                return false;
            }

            try
            {
                lock (_lockObject)
                {
                    byte[] data = Encoding.UTF8.GetBytes(command);
                    _networkStream.Write(data, 0, data.Length);
                    _networkStream.Flush();

                    _statistics.SentCommandsCount++;
                    _statistics.LastActivityTime = DateTime.Now;

                    WriteLog($"发送命令成功: {command.Replace("\r", "\\r").Replace("\n", "\\n")}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _statistics.ErrorsCount++;
                OnErrorOccurred(new ErrorOccurredEventArgs(InstrumentId, GetSendFailureErrorCode(), $"发送命令失败: {ex.Message}", ex));
                return false;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 建立TCP连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        private bool EstablishConnection()
        {
            try
            {
                CloseConnection();

                _tcpClient = new TcpClient();

                // 设置连接超时
                var connectTask = _tcpClient.ConnectAsync(_config.IpHost, _config.IpPort.Value);
                if (!connectTask.Wait(10000)) // 10秒超时
                {
                    WriteLog("TCP连接超时");
                    return false;
                }

                _networkStream = _tcpClient.GetStream();

                // 设置读写超时
                _networkStream.ReadTimeout = 30000;  // 30秒读超时
                _networkStream.WriteTimeout = 5000;  // 5秒写超时

                WriteLog($"TCP连接建立成功: {_config.IpHost}:{_config.IpPort}");
                return true;
            }
            catch (Exception ex)
            {
                WriteLog($"建立TCP连接失败: {ex.Message}");
                CloseConnection();
                return false;
            }
        }

        /// <summary>
        /// 关闭TCP连接
        /// </summary>
        private void CloseConnection()
        {
            try
            {
                // 关闭网络流
                if (_networkStream != null)
                {
                    try
                    {
                        _networkStream.Close();
                        _networkStream.Dispose();
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"关闭NetworkStream时发生错误: {ex.Message}");
                    }
                    finally
                    {
                        _networkStream = null;
                    }
                }

                // 关闭TCP客户端
                if (_tcpClient != null)
                {
                    try
                    {
                        _tcpClient.Close();
                        ((IDisposable)_tcpClient).Dispose();
                    }
                    catch (Exception ex)
                    {
                        WriteLog($"关闭TcpClient时发生错误: {ex.Message}");
                    }
                    finally
                    {
                        _tcpClient = null;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"关闭TCP连接时发生未知错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 接收线程处理过程
        /// </summary>
        private void ReceiveThreadProc()
        {
            WriteLog("TCP接收线程已启动");

            while (_isRunning)
            {
                try
                {
                    // 尝试建立连接
                    if (!EstablishConnection())
                    {
                        WriteLog("TCP连接失败，5秒后重试...");
                        Thread.Sleep(5000);
                        continue;
                    }

                    Status = ConnectorStatus.Running;

                    // 接收数据循环
                    byte[] buffer = new byte[4096];
                    while (_isRunning && _networkStream != null && _networkStream.CanRead)
                    {
                        try
                        {
                            int bytesRead = _networkStream.Read(buffer, 0, buffer.Length);
                            if (bytesRead > 0)
                            {
                                byte[] receivedData = new byte[bytesRead];
                                Array.Copy(buffer, receivedData, bytesRead);

                                // 使用基类的消息处理机制
                                ProcessRawData(receivedData);
                            }
                            else
                            {
                                WriteLog("TCP连接已断开");
                                break;
                            }
                        }
                        catch (IOException ex)
                        {
                            WriteLog($"TCP读取数据异常: {ex.Message}");
                            break;
                        }
                        catch (ObjectDisposedException)
                        {
                            WriteLog("TCP连接已释放");
                            break;
                        }
                    }

                    CloseConnection();

                    if (_isRunning)
                    {
                        WriteLog("TCP连接断开，5秒后重连...");
                        Thread.Sleep(5000);
                    }
                }
                catch (Exception ex)
                {
                    _statistics.ErrorsCount++;
                    WriteLog($"TCP接收线程异常: {ex.Message}");

                    if (_isRunning)
                    {
                        Thread.Sleep(5000);
                    }
                }
            }

            WriteLog("TCP接收线程已退出");
        }

        #endregion
    }
}