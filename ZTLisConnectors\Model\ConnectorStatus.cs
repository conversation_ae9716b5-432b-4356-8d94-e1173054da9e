using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisConnectors
{
    /// <summary>
    /// 连接器状态枚举
    /// </summary>
    public enum ConnectorStatus
    {
        /// <summary>已停止</summary>
        Stopped = 0,

        /// <summary>正在启动</summary>
        Starting = 1,

        /// <summary>正在运行</summary>
        Running = 2,

        /// <summary>发生错误</summary>
        Error = 3,

        /// <summary>已禁用</summary>
        Disabled = 4,

        /// <summary>正在关闭</summary>
        Stopping = 5
    }
}