﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictSpecimenParam.cs
*
* 功 能： N/A
* 类 名： MdlDictSpecimenParam
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/24 10:36:31   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 标本参数字典表：标准化管理标本参数（如HCT、HIL指数等），为系统提供统一的参数定义
	/// </summary>
	[Serializable]
	public partial class MdlDictSpecimenParam
	{
		public MdlDictSpecimenParam()
		{}
		#region Model
		private int _paramid;
		private string _paramcode;
		private string _paramname;
		private string _englishname;
		private string _pinyin;
		private int _paramtype=0;
		private string _unit;
		private string _description;
		private bool _status= true;
		private DateTime _createdat= DateTime.Now;
		private DateTime _updatedat= DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int ParamId
		{
			set{ _paramid=value;}
			get{return _paramid;}
		}
		/// <summary>
		/// 参数唯一编码（如 HCT、HIL_H、HIL_I、HIL_L）
		/// </summary>
		public string ParamCode
		{
			set{ _paramcode=value;}
			get{return _paramcode;}
		}
		/// <summary>
		/// 参数中文名称
		/// </summary>
		public string ParamName
		{
			set{ _paramname=value;}
			get{return _paramname;}
		}
		/// <summary>
		/// 参数英文名称
		/// </summary>
		public string EnglishName
		{
			set{ _englishname=value;}
			get{return _englishname;}
		}
		/// <summary>
		/// 拼音/首字母，用于快速检索
		/// </summary>
		public string PinYin
		{
			set{ _pinyin=value;}
			get{return _pinyin;}
		}
		/// <summary>
		/// 参数类型：0=常规参数 1=质量指数 2=其他
		/// </summary>
		public int ParamType
		{
			set{ _paramtype=value;}
			get{return _paramtype;}
		}
		/// <summary>
		/// 参数单位（如 %、Index等）
		/// </summary>
		public string Unit
		{
			set{ _unit=value;}
			get{return _unit;}
		}
		/// <summary>
		/// 参数描述和说明
		/// </summary>
		public string Description
		{
			set{ _description=value;}
			get{return _description;}
		}
		/// <summary>
		/// 状态：1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set{ _status=value;}
			get{return _status;}
		}
		/// <summary>
		/// 创建时间
		/// </summary>
		public DateTime CreatedAt
		{
			set{ _createdat=value;}
			get{return _createdat;}
		}
		/// <summary>
		/// 更新时间
		/// </summary>
		public DateTime UpdatedAt
		{
			set{ _updatedat=value;}
			get{return _updatedat;}
		}
		#endregion Model

	}
}

