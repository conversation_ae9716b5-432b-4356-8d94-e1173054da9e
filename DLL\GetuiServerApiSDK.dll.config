<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, 
            log4net-net-1.0"/>
  </configSections>


  <log4net>
    <root>
      <level value="ALL"/>
      <appender-ref ref="RollingFileAppender-error"/>
      <appender-ref ref="RollingFileAppender-debug"/>
      <appender-ref ref="RollingFileAppender-info"/>
    </root>


    <appender name="RollingFileAppender-error" type="log4net.Appender.RollingFileAppender">
      <file value="gt_error_" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <datePattern value="yyyyMMdd&quot;.log&quot;" />
      <rollingStyle value="Date" />
      <appendToFile value="true" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%t] %-5p %c - Line:%L %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="ERROR"/>
        <param name="LevelMax" value="ERROR"/>
      </filter>
    </appender>

    <appender name="RollingFileAppender-debug" type="log4net.Appender.RollingFileAppender">
      <file value="gt_debug_" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <datePattern value="yyyyMMdd&quot;.log&quot;" />
      <rollingStyle value="Date" />
      <appendToFile value="true" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%t] %-5p %c - Line:%L %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="DEBUG"/>
        <param name="LevelMax" value="DEBUG"/>
      </filter>
    </appender>

    <appender name="RollingFileAppender-info" type="log4net.Appender.RollingFileAppender">
      <file value="gt_info_" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <datePattern value="yyyyMMdd&quot;.log&quot;" />
      <rollingStyle value="Date" />
      <appendToFile value="true" />
      <staticLogFileName value="false" />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%t] %-5p %c - Line:%L %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="INFO"/>
        <param name="LevelMax" value="INFO"/>
      </filter>
    </appender>


  </log4net>
  <startup>

    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
  </startup>
</configuration>
