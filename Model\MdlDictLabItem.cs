﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictLabItem.cs
*
* 功 能： N/A
* 类 名： MdlDictLabItem
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:12   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验项目字典表：统一管理项目编码、名称、类别、标本、方法学、LOINC 等信息
	/// </summary>
	[Serializable]
	public partial class MdlDictLabItem
	{
		public MdlDictLabItem()
		{ }
		#region Model
		private int _labitemid;
		private string _labitemcode;
		private string _labitemname;
		private string _englishname;
		private string _pinyin;
		private string _reportname;
		private int _itemcategoryid;
		private int _sampletypeid;
		private string _defaultunit;
		private int? _precision;
		private int? _defaultmethodid;
		private string _loinccode;
		private bool _iscalculated = false;
		private bool _status = true;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int LabItemId
		{
			set { _labitemid = value; }
			get { return _labitemid; }
		}
		/// <summary>
		/// 项目唯一编码（院内统一）
		/// </summary>
		public string LabItemCode
		{
			set { _labitemcode = value; }
			get { return _labitemcode; }
		}
		/// <summary>
		/// 项目中文名称
		/// </summary>
		public string LabItemName
		{
			set { _labitemname = value; }
			get { return _labitemname; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string EnglishName
		{
			set { _englishname = value; }
			get { return _englishname; }
		}
		/// <summary>
		/// 拼音/首字母
		/// </summary>
		public string PinYin
		{
			set { _pinyin = value; }
			get { return _pinyin; }
		}
		/// <summary>
		/// 报告显示名称
		/// </summary>
		public string ReportName
		{
			set { _reportname = value; }
			get { return _reportname; }
		}
		/// <summary>
		/// 项目类别ID
		/// </summary>
		public int ItemCategoryId
		{
			set { _itemcategoryid = value; }
			get { return _itemcategoryid; }
		}
		/// <summary>
		/// 默认标本类型ID (FK)
		/// </summary>
		public int SampleTypeId
		{
			set { _sampletypeid = value; }
			get { return _sampletypeid; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string DefaultUnit
		{
			set { _defaultunit = value; }
			get { return _defaultunit; }
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Precision
		{
			set { _precision = value; }
			get { return _precision; }
		}
		/// <summary>
		/// 默认方法学ID (FK)
		/// </summary>
		public int? DefaultMethodId
		{
			set { _defaultmethodid = value; }
			get { return _defaultmethodid; }
		}
		/// <summary>
		/// LOINC 国际统一编码
		/// </summary>
		public string LoincCode
		{
			set { _loinccode = value; }
			get { return _loinccode; }
		}
		/// <summary>
		/// 是否为计算项目 (1=是, 0=否)
		/// </summary>
		public bool IsCalculated
		{
			set { _iscalculated = value; }
			get { return _iscalculated; }
		}
		/// <summary>
		/// 状态：1=启用 0=停用
		/// </summary>
		public bool Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

