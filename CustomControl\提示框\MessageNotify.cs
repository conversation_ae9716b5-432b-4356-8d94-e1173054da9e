﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public class MessageNotify
    {
        //当前显示的提示框的数量
        private static int _Count = 0;
        //提示框宽度
        private static int _WidthMax = 200;
        //提示框高度
        private static int _HeightMax = 146;
        //提示框停留时间（单位：毫秒）
        private static int _StayTime = 5000;
        /// 提示框宽度
        public static int WidthMax
        {
            set { _WidthMax = value; }
            get { return _WidthMax; }
        }
        /// 提示框高度
        public static int HeightMax
        {
            set { _HeightMax = value; }
            get { return _HeightMax; }
        }
        /// 提示框停留时间（单位：秒）
        public static int StayTime
        {
            set { _StayTime = value; }
            get { return _StayTime / 1000; }
        }
        public static void Show(string text,MessageBoxIcon messageBoxIcon=MessageBoxIcon.Information)
        {
            MessageNotifyForm messageNotifyForm = new MessageNotifyForm();
            messageNotifyForm.Width = _WidthMax;
            messageNotifyForm.HeightMax = _HeightMax;
            messageNotifyForm.StayTime = _StayTime;
            messageNotifyForm.ShowMessage(text, messageBoxIcon);
        }
        public static void Show(string text, string caption, MessageBoxIcon messageBoxIcon = MessageBoxIcon.Information)
        {
            MessageNotifyForm messageNotifyForm = new MessageNotifyForm();
            messageNotifyForm.Width = _WidthMax;
            messageNotifyForm.HeightMax = _HeightMax;
            messageNotifyForm.StayTime = _StayTime;
            messageNotifyForm.ShowMessage(text, caption, messageBoxIcon);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="text"></param>
        /// <param name="caption"></param>
        /// <param name="StayTime">停留的秒数</param>
        /// <remarks></remarks>
        public static void Show(string text, string caption, int StayTime, MessageBoxIcon messageBoxIcon = MessageBoxIcon.Information)
        {
            MessageNotifyForm messageNotifyForm = new MessageNotifyForm();
            messageNotifyForm.Width = _WidthMax;
            messageNotifyForm.HeightMax = _HeightMax;
            messageNotifyForm.StayTime = StayTime * 1000;
            messageNotifyForm.ShowMessage(text, caption,messageBoxIcon);
        }

        public static void Show(string text, int StayTime, MessageBoxIcon messageBoxIcon = MessageBoxIcon.Information)
        {
            MessageNotifyForm messageNotifyForm = new MessageNotifyForm();
            messageNotifyForm.Width = _WidthMax;
            messageNotifyForm.HeightMax = _HeightMax;
            messageNotifyForm.StayTime = StayTime * 1000;
            messageNotifyForm.ShowMessage(text, messageBoxIcon);
        }
    }
}
