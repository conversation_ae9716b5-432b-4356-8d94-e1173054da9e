﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{46B795C2-6EFA-41E6-948E-66F92E591B6A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BLL</RootNamespace>
    <AssemblyName>BLL</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BllDictDepartment.cs" />
    <Compile Include="BllDictDoctor.cs" />
    <Compile Include="BllDictLabItem.cs" />
    <Compile Include="BllDictLabItemCalculation.cs" />
    <Compile Include="BllDictLabItemCategory.cs" />
    <Compile Include="BllDictLabItemRefRange.cs" />
    <Compile Include="BllDictLabProfile.cs" />
    <Compile Include="BllDictLabProfileItemMap.cs" />
    <Compile Include="BllDictMethod.cs" />
    <Compile Include="BllDictMethodCategory.cs" />
    <Compile Include="BllDictSampleType.cs" />
    <Compile Include="BllDictSpecimenCondition.cs" />
    <Compile Include="BllDictSpecimenParam.cs" />
    <Compile Include="BllDictUnit.cs" />
    <Compile Include="BllDictVisitType.cs" />
    <Compile Include="BllLabInstrItemMap.cs" />
    <Compile Include="BllLabInstrRawMsg.cs" />
    <Compile Include="BllLabInstrResultQ.cs" />
    <Compile Include="BllLabInstrSpecimenParamMap.cs" />
    <Compile Include="BllLabInstrument.cs" />
    <Compile Include="BllLabOrder.cs" />
    <Compile Include="BllLabOrderItem.cs" />
    <Compile Include="BllLabResult.cs" />
    <Compile Include="BllLabResultReview.cs" />
    <Compile Include="BllLabSpecimen.cs" />
    <Compile Include="BllSysMenu1.cs" />
    <Compile Include="BllSysMenu2.cs" />
    <Compile Include="BllSysModule.cs" />
    <Compile Include="BllSysModuleAuth.cs" />
    <Compile Include="BllSysPara.cs" />
    <Compile Include="BllSysRole.cs" />
    <Compile Include="BllSysRoleAuth.cs" />
    <Compile Include="BllSysRoleModule.cs" />
    <Compile Include="BllSysRpt.cs" />
    <Compile Include="BllSysRpt_Class.cs" />
    <Compile Include="BllSysRpt_Class_Level.cs" />
    <Compile Include="BllSysUser.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\DALFactory\DALFactory.csproj">
      <Project>{ff83107b-71c8-4924-8755-bdca5ab64ed4}</Project>
      <Name>DALFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\DBUtility\DBUtility.csproj">
      <Project>{9aa4cca5-b6d1-4719-b1af-880910bbc87e}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDAL\IDAL.csproj">
      <Project>{8dee3ca3-dde2-40b7-8e77-bc5d0fce94c1}</Project>
      <Name>IDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\SQLServerDAL\SQLServerDAL.csproj">
      <Project>{ac6eb101-399f-43a9-93cd-e4cac537fc45}</Project>
      <Name>SQLServerDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>