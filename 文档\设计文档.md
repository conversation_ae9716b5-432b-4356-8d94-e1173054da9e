# LIS 系统详细设计文档 (增强版)

**核心目标:** 构建一个稳定、高效、易用、可扩展的实验室信息系统（LIS），实现检验流程自动化、标准化、无纸化，提升检验质量与工作效率，并与医院信息系统（HIS/EMR）无缝集成。

---

## 一、基础数据与配置管理模块

### 1. 仪器管理 (基于 `LabInstrument` 表)
#### 功能：
*   **仪器档案：** 新增、编辑、查询、删除、启用/停用检验仪器信息。
*   **通讯配置：** 针对不同 `ConnType`（串口、TCP/IP、文件、中间库、API、HL7 MLLP）提供动态配置界面。
    *   串口参数（`ComPort`, `BaudRate`, `Parity`, `DataBits`, `StopBits`）。
    *   网络参数（`IpHost`, `IpPort`, `KeepAliveSec`）。
    *   路径/连接串/URL（`FilePath`, `DbConnStr`, `ApiBaseUrl`, `ApiAuthToken` - 后两者应有加密存储/输入的提示或机制）。
*   **协议配置：** 选择仪器通讯协议（`ProtocolType`），关联对应的驱动/解析器版本 (`DriverVersion`)。

#### WinForms UI 考量：
*   使用 `DataGridView` 显示仪器列表，支持筛选和排序。
*   使用 `PropertyGrid` 或动态生成的控件组来显示/编辑不同 `ConnType` 的参数，以保持界面整洁。
*   敏感信息（如 `ApiAuthToken`, `DbConnStr` 中的密码）输入时使用密码字符。
*   提供 "测试连接" 功能（如适用，例如 TCP/IP ping，数据库连接测试）。

### 2. LIS 检验项目字典管理 (基于 `DictLabItem`, `DictLabItemCategory`, `DictSampleType`, `DictMethod`, `DictMethodCategory`)
#### 功能：
*   **项目类别管理 (`DictLabItemCategory`)：** 如生化、免疫、临检、微生物等。
*   **标本类型管理 (`DictSampleType`)：** 如血清、全血、尿液等，包含颜色、容器、默认采血量、预处理信息。
*   **方法学类别管理 (`DictMethodCategory`)：** 如生化法、免疫法、分子法。
*   **具体方法学管理 (`DictMethod`)：** 如酶速率法、化学发光法，并关联到方法学类别。
*   **LIS 项目维护 (`DictLabItem`)：**
    *   核心信息：`LabItemCode` (院内唯一), `LabItemName`, 英文名, 拼音码。
    *   关联信息：关联项目类别、默认标本类型、默认方法学 (`DefaultMethodCode`)。
    *   报告属性：`DefaultUnit`, `Precision` (小数位数), `LoincCode` (国际标准)。
    *   状态管理：`Status` (启用/停用)。

#### WinForms UI 考量：
*   使用 `TreeView` 或选项卡组织各类字典。
*   每个字典使用 `DataGridView` 进行展示，提供增删改查功能。
*   下拉框 (`ComboBox`) 用于选择关联字典项（如为检验项目选类别、标本类型）。

### 3. 仪器-LIS项目映射管理 (基于 `LabInstrItemMap` 表)
#### 功能：
*   为每台仪器（`InstrumentId`）的仪器项目代码（`InstrItemCode`）建立与 `DictLabItem.LisItemCode` 的映射。
*   配置 `InstrItemName` (仪器端名称，参考用)、`ResultFormat` (NUM, TXT, NEG)、`Decimals` (数值型小数位)。

#### WinForms UI 考量：
*   主界面选择仪器，然后 `DataGridView` 显示该仪器的项目映射列表。
*   添加/编辑映射时，提供 `ComboBox` 或弹出选择框来选择 `LisItemCode`。
*   校验 `(InstrumentId, InstrItemCode)` 组合的唯一性。

### 4. 参考范围管理 (基于 `DictLabItemRefRange` 表)
#### 功能：
*   为每个 `LabItemCode` 配置参考范围。
*   支持多维度定义：`MethodCode` (特定方法学)、`Sex` (性别)、`AgeLower`/`AgeUpper` (年龄段)、`EffectiveFrom`/`EffectiveTo` (有效期)。
*   记录 `LowerLimit`, `UpperLimit`, `Unit`。

#### WinForms UI 考量：
*   选择 LIS 项目后，`DataGridView` 显示其所有参考范围配置。
*   提供清晰的界面输入年龄（如x天、x月、x岁，并统一转换为岁的小数形式）。
*   有效期管理，确保历史版本可追溯。

### 5. 用户与权限管理
#### 功能：
*   **科室/分组管理：** 定义实验室内部的科室或专业组。
*   **用户管理：** 员工工号、姓名、所属科室/组、职称、登录密码（加密存储）、状态（启用/禁用）。
*   **角色管理：** 定义角色（如：样本处理员、检验技师、审核技师、科室主管、系统管理员）。
*   **权限分配：** 为角色分配模块访问权限和操作权限（查看、新增、修改、删除、审核、反审核、报告打印、配置修改等）。

#### WinForms UI 考量：
*   用户列表、角色列表使用 `DataGridView`。
*   权限分配使用 `TreeView` 结合 `CheckBox` 的方式，清晰展示模块层级和可选权限。

### 6. 其他核心配置
*   **危急值管理：**
    *   为 `DictLabItem` 配置危急值上下限（可按年龄性别细分）。
    *   危急值处理流程配置（如通知方式、确认时限）。
*   **组合项目管理（医嘱套餐）：**
    *   定义组合项目，包含多个 `DictLabItem`。方便临床开单和计费。
*   **报告模板管理：**
    *   设计和维护不同类型（如生化报告、血常规报告）的检验报告打印模板。
    *   (WinForms: 可能使用第三方报表工具如 DevExpress XtraReports, FastReport, Stimulsoft Reports, 或 RDLC)。
*   **系统参数配置：**
    *   LIS 系统名称、版本号。
    *   条码规则配置。
    *   默认打印机设置。
    *   数据自动归档策略。
    *   接口参数配置（HIS 等）。

---

## 二、医嘱与样本管理模块

### 1. 医嘱接收与处理 (通常需要 `LabOrder` / `LabPatient` / `LabOrderItem` 等表，此处未在提供文档中详述，但为 LIS 必备)
#### 功能：
*   **接口接收：** 通过 HL7、Web Service 或数据库中间表从 HIS/EMR 接收检验申请。
*   **手动录入：** 支持无缝接口外或急诊场景的手动补录申请。
*   **信息包含：** 患者基本信息（ID、姓名、性别、年龄、床号、病区、就诊卡号）、申请科室、申请医生、申请项目/组合、开单时间、临床诊断、样本类型、费用信息等。
*   **医嘱校验与拆分：** 校验医嘱项目有效性，将组合项目拆分为单个LIS检验项目。

#### WinForms UI 考量：
*   医嘱列表 `DataGridView`，支持按日期、患者、状态等筛选。
*   手动录入界面，患者信息可通过读卡器或输入ID快速调取。

### 2. 条码生成与打印
#### 功能：
*   根据已确认的检验申请自动或手动生成唯一样本条码。
*   支持批量打印条码标签（包含患者信息、样本类型、检验项目简称等）。
*   条码与申请信息（`OrderItemId`）严格关联。

#### WinForms UI 考量：
*   条码打印预览。
*   选择标签打印机。

### 3. 样本登记与接收 (签收)
#### 功能：
*   实验室通过扫描样本条码进行签收。
*   记录签收人、签收时间、样本状态（如：已接收、待离心）。
*   核对样本信息与申请信息，标记不合格样本（如溶血、量不足）。

#### WinForms UI 考量：
*   扫描枪友好界面，扫描后自动填充信息并定位。
*   不合格样本处理界面，记录原因并可通知临床。

### 4. 样本分发与流转 (上机)
#### 功能：
*   将样本分配到具体的仪器或检验台位。
*   记录样本上机操作人、上机时间、使用的仪器。
*   更新样本状态（如：检验中）。

#### WinForms UI 考量：
*   可视化样本架管理（如果仪器支持样本架）。
*   批量上机功能。

### 5. 样本预处理
#### 功能：
*   记录样本离心、分装等前处理操作及操作人、时间。

#### WinForms UI 考量：
*   简洁的批量处理界面。

---

## 三、仪器通讯与数据采集模块

### 1. 仪器连接器 (Connectors - 后台服务/线程)
#### 功能：
*   根据 `LabInstrument` 表中各仪器的 `ConnType` 和参数，建立与仪器的通讯。
*   **主动模式：** 如 TCP 客户端连接仪器服务器，串口主动查询。
*   **被动模式：** 如 TCP 服务器监听仪器连接，文件目录监控，HL7 MLLP 监听。

#### WinForms UI 考量：
*   提供一个 "仪器通讯监控" 界面，显示各仪器连接状态（连接中、已连接、断开、错误信息）、最后通讯时间、收发字节数等。
*   每个仪器连接通常在独立的后台线程 (`BackgroundWorker` 或 TPL `Task`)中运行，避免UI卡顿。

### 2. 原始报文接收与存储 (写入 `LabInstrRawMsg`)
#### 功能：
*   连接器接收到仪器发送的完整数据报文后，立即存入 `LabInstrRawMsg` 表。
*   记录 `InstrumentId`, `RawText` (原始报文), `AcquiredAt` (自动)。`ParsedFlag` 默认为 0。

#### WinForms UI 考量：
*   提供原始报文查询界面，按仪器、时间范围筛选，用于故障排查。`RawText` 应以可读方式显示，并支持复制。

### 3. 报文解析器 (Parsers - 后台服务/线程)
#### 功能：
*   独立的后台服务/线程轮询 `LabInstrRawMsg` 表中 `ParsedFlag = 0` 的记录。
*   根据 `LabInstrument.ProtocolType` (ASTM, HL7, LIS2-A2, 自定义等) 和 `InstrumentId` 调用相应的解析逻辑。
*   **解析内容：** 样本条码 (`Barcode`)、仪器项目代码 (`InstrItemCode`)、结果值 (`ResultValue`)、测量时间 (`MeasuredAt`)、单位、稀释信息、警告标志、质控标识等。
*   **成功：** 将解析出的单项结果（可能一条报文含多项结果）写入 `LabInstrResultQ` 表，并更新 `LabInstrRawMsg.ParsedFlag` 为 1。
*   **失败：** 更新 `LabInstrRawMsg.ParsedFlag` 为 1 (或特定错误状态)，并在 `LabInstrRawMsg.ParseError` 中记录详细错误信息。

#### WinForms UI 考量：
*   解析错误日志查看界面。
*   可配置的解析器插件机制（如通过反射加载不同协议的DLL）。

### 4. 结果导入与预处理 (Importer - 后台服务/线程，处理 `LabInstrResultQ`)
#### 功能：
*   独立的后台服务/线程轮询 `LabInstrResultQ` 表中 `ProcessedFlag = 0` 的记录。
*   **项目映射：** 使用 `InstrumentId` 和 `InstrItemCode` 到 `LabInstrItemMap` 查找 `LisItemCode` 及 `ResultFormat`, `Decimals`。
*   **结果格式化：**
    *   `NUM`: 调整小数位，转换科学计数法。
    *   `TXT`: 直接使用。
    *   `NEG`: 根据预设规则转为 "阴性"/"阳性" 或 "+/-"。
*   **数据校验：** 结果值是否在合理范围（仪器本身的技术限）、是否为空。
*   **关联医嘱：** 通过 `Barcode` 和可能的 `LisItemCode` 找到对应的 `OrderItemId`（假设此表存在）。
*   **写入结果：** 将符合要求的结果写入 `LabResult` 表（`ReviewStatus` 初始为0 - 待审）。
*   **更新状态：** 更新 `LabInstrResultQ.ProcessedFlag` (1=成功导入 `LabResult`, 2=导入失败)。记录失败原因到 `LabInstrResultQ.ParseError` (此字段名可能需调整为 `ProcessError` 更准确)。

#### WinForms UI 考量：
*   提供 "待处理结果队列" (`LabInstrResultQ` 中 `ProcessedFlag=0` 或 `2`) 的查看和手动处理界面。
*   手动匹配样本条码与医嘱的功能。
*   显示导入错误信息。

---

## 四、结果审核与发布模块 (基于 `LabResult` 表)

### 1. 结果录入与修改
#### 功能：
*   支持手动录入无法自动采集的项目结果（如镜检、微生物培养描述）。
*   有权限用户可修改已录入/已采集的结果（需记录修改日志，`LabResult` 表可增加 `ModifiedBy`, `ModifiedAt`, `OriginalValue` 字段或单独的审计表）。

#### WinForms UI 考量：
*   结果录入界面，按样本条码或患者信息查询，`DataGridView` 显示项目列表及结果输入框。

### 2. 结果审核
#### 功能：
*   提供多级审核流程（如初审 `FirstReviewedBy`/`FirstReviewedAt`, 复审 `FinalReviewedBy`/`FinalReviewedAt`）。
*   审核界面显示：患者信息、样本信息、本次检验结果、单位、`AbnormalFlag` (高低箭头，由参考范围自动判断)、历史结果比对、趋势图、参考范围、危急值提示。
*   审核通过后更新 `LabResult.ReviewStatus`。

#### WinForms UI 考量：
*   审核工作列表 (`DataGridView`)，按审核状态、仪器、日期筛选。
*   审核界面布局合理，信息一目了然。历史结果用折线图或表格展示。
*   批量审核/单个审核。
*   审核意见备注。

### 3. 危急值处理与通知
#### 功能：
*   当 `LabResult.ResultValue` 触发预设的危急值规则时，系统自动高亮或弹窗告警。
*   记录危急值发现时间、通知临床的时间、接收人、通知方式、处理措施。
*   (可能需要 `LabCriticalValueLog` 表来详细记录处理过程)。

#### WinForms UI 考量：
*   危急值列表实时刷新，声音提示。
*   危急值处理表单，记录处理详情。

### 4. 报告生成与打印
#### 功能：
*   根据审核通过 (`ReviewStatus=2`) 的 `LabResult` 数据，选择合适的报告模板。
*   生成PDF预览或直接打印检验报告单。
*   支持按患者、按申请单批量打印、单个打印、补打。
*   报告包含二维码，可供患者扫码查询电子报告（需额外接口）。

#### WinForms UI 考量：
*   报告预览窗口。
*   打印队列管理。

### 5. 报告发布与查询
#### 功能：
*   将最终审核的报告通过接口（HL7 OUL^R21/ORU^R01, Web Service）发送给 HIS/EMR。
*   提供医生工作站或患者服务平台查询历史检验报告的接口。
*   院内LIS提供按患者ID、姓名、日期、项目等条件的报告查询功能。

#### WinForms UI 考量：
*   报告查询界面，支持多种搜索条件，结果列表展示，双击查看报告详情或重印。

---

## 五、质量控制 (QC) 模块 (数据库层面需要补充 `QCLot`, `QCRule`, `QCResult` 等表)

### 1. 质控品管理
#### 功能：
*   登记质控品信息：项目、批号、厂商、有效期、靶值 (Mean)、标准差 (SD)、可接受范围。
#### WinForms UI：
*   质控品档案管理界面。

### 2. 质控规则设置
#### 功能：
*   为每个项目/仪器配置质控规则（如 Westgard 多规则: 1-2s, 1-3s, 2-2s, R-4s, 4-1s, 10x）。
#### WinForms UI：
*   规则选择和配置界面。

### 3. 质控数据录入/采集
#### 功能：
*   仪器自动上传的质控结果（需在解析器中识别质控样本标识）。
*   手动录入质控结果。
#### WinForms UI：
*   质控结果录入表单，或从 `LabInstrResultQ` 中筛选质控数据导入。

### 4. 质控图绘制与分析
#### 功能：
*   自动绘制 Levey-Jennings (L-J) 图、Z-score 图、Youden 图（双质控品）。实时判断质控结果是否在控，违反规则时高亮显示。
#### WinForms UI：
*   图表控件展示 L-J 图，支持缩放、选择日期范围。

### 5. 失控处理与记录
#### 功能：
*   质控失控时，系统报警。记录失控原因分析、采取的纠正措施、复测结果。仪器在该项目上的测试可被暂停，直到质控恢复正常。
#### WinForms UI：
*   失控处理表单，与仪器状态联动。

### 6. 室间质评 (EQA) 管理
#### 功能：
*   登记 EQA 计划、样本信息，录入回报结果，上传结果文件，记录 EQA 报告和评价。
#### WinForms UI：
*   EQA 活动管理界面。

---

## 六、统计分析与报表模块

1.  **工作量统计:** 按科室、仪器、人员、项目、时间段统计样本量、项目数。
2.  **TAT (Turn Around Time) 分析:** 样本从接收到报告发出的各阶段耗时统计。
3.  **阳性率/异常率统计:** 特定项目的阳性率、高低值异常率。
4.  **试剂耗材消耗统计 (若管理):** 根据项目测试数和单耗估算或实际记录。
5.  **自定义查询与报表生成:** 提供灵活的查询工具和报表设计器。
6.  **微生物统计：** 菌株分布、耐药率统计（需微生物模块支持）。

#### WinForms UI 考量：
*   统计查询条件设置界面。
*   使用 `DataGridView` 展示统计结果，支持导出到 Excel。
*   使用图表控件（如 `Chart`）进行可视化展示。

---

## 七、系统接口模块

### 1. HIS/EMR 接口 (关键)
*   **医嘱下载 (Patient & Order Management - PMO):** HL7 ADT (A01, A04, A08), ORM^O01。
*   **患者信息同步。**
*   **检验结果回传 (Result Unsolicited - ORU):** HL7 ORU^R01。
*   **报告查询接口 (可能通过 Web Service 或共享视图)。**
*   **计费接口 (DFT^P03)。**

### 2. 其他外部系统接口
*   公共卫生上报、血库、病理、区域LIS平台等。

#### WinForms UI 考量：
*   接口日志查看界面，显示通讯内容、状态、时间。
*   手动触发接口重发/重收功能。
*   接口参数配置界面。

---

## 八、系统维护与安全模块

### 1. 数据备份与恢复
#### 功能：
*   配置自动备份策略（如 SQL Server Agent），提供手动备份和恢复指引/工具。

### 2. 日志管理
#### 功能：
*   **操作日志：** 记录用户关键操作（登录、修改、删除、审核等）。
*   **错误日志：** 记录系统运行时错误。
*   **接口通讯日志。**
#### WinForms UI：
*   日志查询界面，按用户、时间、模块、事件类型筛选。

### 3. 字典维护工具
*   集中管理各类小型业务字典（如异常标志文本、不合格样本原因等）。

### 4. 参数配置
*   系统全局参数的集中配置。

### 5. 在线更新
*   (WinForms: 如ClickOnce部署或自定义更新程序)。

---

## 设计文档的不足之处与改进建议 (基于当前提供的数据库和初始设计)

### 1. 数据库层面缺失：
*   **患者信息表 (`Patient`)、医嘱主表 (`LabOrder`)、医嘱明细表 (`LabOrderItem`):** 这是 LIS 的核心，`LabResult` 中的 `OrderItemId` 需要这些表来关联完整的患者和申请信息。目前的表结构主要集中在仪器端。
*   **质控相关表 (`QCLot`, `QCRule`, `QCResult`等):** 质量控制是LIS的灵魂，必须有相应的表结构支持。
*   **用户账户表 (`UserAccount`)、角色表 (`Role`)、权限表 (`Permission`, `RolePermissionMap`):** 用于系统安全和操作授权。
*   **审计日志表 (`AuditLog`):** 记录关键操作的详细信息，比 `CreatedAt`/`UpdatedAt` 更全面。
*   **危急值处理日志表 (`CriticalValueLog`):** 详细记录危急值的发现、通知、处理全过程。
*   **试剂耗材库存表 (可选，但大型LIS常有):** 管理试剂批号、有效期、库存量。
*   **微生物相关表 (若支持微生物):** 菌种字典、药敏规则、抗生素字典、培养基字典、结果表等，非常复杂。
*   **报告模板存储表：** 如果报告模板不是文件形式，而是数据库存储。

### 2. 设计文档层面可以深化和补充的：
*   **工作流引擎概念：** 虽然有状态字段，但可以更明确地定义样本/医嘱在 LIS 中的完整生命周期和状态转换机制。
*   **错误处理与重试机制：** 对接口通讯失败、解析失败、导入失败等情况，应有更详细的自动重试、手动干预、告警机制。
*   **性能与可伸缩性：** 对于高通量实验室，需要考虑数据库查询优化、后台服务并发处理能力、UI响应速度。
*   **安全性：** 除了用户权限，还需考虑数据传输加密 (如SSL/TLS for API/HL7 MLLP)、防SQL注入、敏感数据显示脱敏等。
*   **用户体验 (UX)：** WinForms 应用需要特别注意界面布局、操作流程的便捷性、减少弹窗、提供快捷键、可定制的视图等。
*   **微生物检验流程：** 如果系统需要支持微生物检验，这是一个非常庞大的模块，需要单独详细设计。
*   **标本管理深化：** 标本存储位置管理、标本销毁管理、二次分管/合并管理。
*   **知识库与规则引擎：** 如自动审核规则、反射试验规则、注释规则等。
*   **移动端应用：** 考虑为临床医生或护士提供移动端查询报告、危急值提醒的功能。
*   **云部署与SaaS模式：** 现代LIS发展的趋势。

---

## 技术栈与架构考量 (WinForms 视角)

*   **语言：** C# (.NET Framework or .NET Core/.NET 5+ for future-proofing, though WinForms on .NET Core is mature).
*   **UI：** WinForms.
    *   考虑使用第三方UI控件库 (DevExpress, Telerik, Syncfusion) 提升开发效率和界面美观度。
    *   采用 MVP, MVVM (with data binding) 或类似模式分离业务逻辑和UI，提高可维护性。
*   **数据访问：** Entity Framework Core (推荐) 或 Dapper (轻量级高性能)。
*   **后台服务/作业：** Windows Services 或 Topshelf 托管后台任务（仪器连接、报文解析、结果导入）。
*   **多线程：** `Task Parallel Library (TPL)`, `async/await` 用于处理耗时操作，保持UI响应。
*   **报表：** RDLC, Crystal Reports (legacy), DevExpress XtraReports, FastReport等。
*   **部署：** ClickOnce, MSI Installer, 或自定义更新程序。
*   **模块化设计：** 将系统拆分为多个类库项目，按功能分层（UI层、业务逻辑层、数据访问层、接口层、公共组件层）。
