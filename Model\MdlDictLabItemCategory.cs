﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictLabItemCategory.cs
*
* 功 能： N/A
* 类 名： MdlDictLabItemCategory
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:12   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验项目类别字典表，例如 生化、血液、免疫、微生物等
	/// </summary>
	[Serializable]
	public partial class MdlDictLabItemCategory
	{
		public MdlDictLabItemCategory()
		{ }
		#region Model
		private int _itemcategoryid;
		private string _itemcategorycode;
		private string _itemcategoryname;
		private string _englishname;
		private string _pinyin;
		private string _description;
		private bool _status = true;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int ItemCategoryId
		{
			set { _itemcategoryid = value; }
			get { return _itemcategoryid; }
		}
		/// <summary>
		/// 类别唯一编码，如 BIO、HEM
		/// </summary>
		public string ItemCategoryCode
		{
			set { _itemcategorycode = value; }
			get { return _itemcategorycode; }
		}
		/// <summary>
		/// 中文名称，如 生化
		/// </summary>
		public string ItemCategoryName
		{
			set { _itemcategoryname = value; }
			get { return _itemcategoryname; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string EnglishName
		{
			set { _englishname = value; }
			get { return _englishname; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string PinYin
		{
			set { _pinyin = value; }
			get { return _pinyin; }
		}
		/// <summary>
		/// 
		/// </summary>
		public string Description
		{
			set { _description = value; }
			get { return _description; }
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

