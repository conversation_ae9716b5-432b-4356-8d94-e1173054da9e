using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ZTLisControl
{
    public partial class SingleComPort : CustomControl.MySingleComobo
    {
        public SingleComPort()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        public void Init()
        {
            Additem = "COM1,COM1";
            Additem = "COM2,COM2";
            Additem = "COM3,COM3";
            Additem = "COM4,COM4";
            Additem = "COM5,COM5";
            Additem = "COM6,COM6";
            Additem = "COM7,COM7";
            Additem = "COM8,COM8";
            Additem = "COM9,COM9";
            Additem = "COM10,COM10";
            Additem = "COM11,COM11";
            Additem = "COM12,COM12";
            Additem = "COM13,COM13";
            Additem = "COM14,COM14";
            Additem = "COM15,COM15";
            Additem = "COM16,COM16";
            DisplayColumns[1].Visible = false;
            DroupDownWidth = Width - (int)CaptainWidth;
            SelectedIndex = 0;
            ItemHeight = 20;
        }
    }
}