﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabInstrResultQ.cs
*
* 功 能： N/A
* 类 名： MdlLabInstrResultQ
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 解析结果中间队列表：存放解析完成但尚未写入 LabResult 的记录
	/// </summary>
	[Serializable]
	public partial class MdlLabInstrResultQ
	{
		public MdlLabInstrResultQ()
		{ }
		#region Model
		private long _qid;
		private long _rawid;
		private int _instrumentid;
		private string _barcode;
		private string _instritemcode;
		private string _resultvalue;
		private string _instrflag;
		private int _resulttype = 0;
		private DateTime? _measuredat;
		private string _processerror;
		private int _processedflag = 0;
		/// <summary>
		/// 主键 ID（IDENTITY）
		/// </summary>
		public long QId
		{
			set { _qid = value; }
			get { return _qid; }
		}
		/// <summary>
		/// 关联LabInstrRawMsg的RawId
		/// </summary>
		public long RawId
		{
			set { _rawid = value; }
			get { return _rawid; }
		}
		/// <summary>
		/// 关联仪器 ID（LabInstrument.InstrumentId）
		/// </summary>
		public int InstrumentId
		{
			set { _instrumentid = value; }
			get { return _instrumentid; }
		}
		/// <summary>
		/// 样本条码
		/// </summary>
		public string Barcode
		{
			set { _barcode = value; }
			get { return _barcode; }
		}
		/// <summary>
		/// 仪器项目代码（待映射到 LIS 项目）
		/// </summary>
		public string InstrItemCode
		{
			set { _instritemcode = value; }
			get { return _instritemcode; }
		}
		/// <summary>
		/// 结果值（字符或数字）
		/// </summary>
		public string ResultValue
		{
			set { _resultvalue = value; }
			get { return _resultvalue; }
		}
		/// <summary>
		/// 仪器标志（H/L/A等异常标记）
		/// </summary>
		public string InstrFlag
		{
			set { _instrflag = value; }
			get { return _instrflag; }
		}
		/// <summary>
		/// 结果类型：0=检验结果 1=标本参数
		/// </summary>
		public int ResultType
		{
			set { _resulttype = value; }
			get { return _resulttype; }
		}
		/// <summary>
		/// 仪器测定时间（DATETIME）
		/// </summary>
		public DateTime? MeasuredAt
		{
			set { _measuredat = value; }
			get { return _measuredat; }
		}
		/// <summary>
		/// 入库阶段的错误信息（为空表示入库成功）
		/// </summary>
		public string ProcessError
		{
			set { _processerror = value; }
			get { return _processerror; }
		}
		/// <summary>
		/// 处理标记：0待入库 1入库成功 2入库失败
		/// </summary>
		public int ProcessedFlag
		{
			set { _processedflag = value; }
			get { return _processedflag; }
		}
		#endregion Model

	}
}

