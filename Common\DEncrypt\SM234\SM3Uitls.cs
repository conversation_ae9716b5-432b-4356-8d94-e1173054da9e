﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Org.BouncyCastle.Utilities.Encoders;

namespace Common
{
    public static class SM3Uitls
    {
        public static string GenerateSignature(string plainText)
        {
            byte[] md = new byte[32];
            byte[] msg1 = Encoding.UTF8.GetBytes(plainText);
            SM3Digest sm3 = new SM3Digest();
            sm3.BlockUpdate(msg1, 0, msg1.Length);
            sm3.DoFinal(md, 0);
            System.String s = new UTF8Encoding().GetString(Hex.Encode(md));
            return s.ToUpper();
        }
    }
}
