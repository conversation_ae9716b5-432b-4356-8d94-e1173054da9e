﻿using Common.BaseForm;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using C1.Win.C1Command;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ZTLis
{
    public partial class MainForm : Base
    {
        public MainForm()
        {
            InitializeComponent();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            Common.WinFormVar.Var.MainTab = this.MainTab;
            // 动态生成主菜单
            GenerateMenu();
            GenerateToolbar();
        }

        #region 动态生成按钮
        private void GenerateMenu()
        {
            // 清空现有的菜单项
            menuStrip1.Items.Clear();

            foreach (var mdlSysMenu1 in ZTLisVar.Var.UserPermission)
            {
                // 创建一级菜单命令
                ToolStripMenuItem firstLevelCommand = new ToolStripMenuItem();
                firstLevelCommand.Name = mdlSysMenu1.FrsCode;
                firstLevelCommand.Text = mdlSysMenu1.FrsName;
                firstLevelCommand.Image = ZTLisResources.C_Resources.GetImage16(mdlSysMenu1.FrsName);
                firstLevelCommand.TextImageRelation = TextImageRelation.ImageBeforeText;
                menuStrip1.Items.Add(firstLevelCommand);


                foreach (var mdlSysMenu2 in mdlSysMenu1.Menu2Lists)
                {
                    // 创建二级菜单命令
                    ToolStripMenuItem secondLevelCommand = new ToolStripMenuItem();
                    secondLevelCommand.Name = mdlSysMenu2.ScndCode;
                    secondLevelCommand.Text = mdlSysMenu2.ScndName;
                    secondLevelCommand.Image = ZTLisResources.C_Resources.GetImage16(mdlSysMenu2.ScndName);
                    secondLevelCommand.TextImageRelation = TextImageRelation.ImageBeforeText;

                    foreach (var mdlSysModule in mdlSysMenu2.ModuleLists)
                    {
                        // 创建三级菜单命令（模块）
                        ToolStripMenuItem moduleCommand = new ToolStripMenuItem();
                        moduleCommand.Name = mdlSysModule.ModuleCode;
                        moduleCommand.Text = mdlSysModule.ModuleName;
                        moduleCommand.Image = ZTLisResources.C_Resources.GetImage20(mdlSysModule.ModuleName);
                        moduleCommand.TextImageRelation = TextImageRelation.ImageBeforeText;
                        // 添加点击事件
                        moduleCommand.Click += (sender, e) =>
                        {
                            ToolStripMenuItem cmd = sender as ToolStripMenuItem;
                            if (cmd != null)
                            {
                                CallForm(moduleCommand.Text);
                            }
                        };
                        // 将模块链接添加到二级菜单
                        secondLevelCommand.DropDownItems.Add(moduleCommand);
                    }

                    // 将二级菜单链接添加到一级菜单
                    firstLevelCommand.DropDownItems.Add(secondLevelCommand);
                }

                // 将一级菜单链接添加到主菜单
                menuStrip1.Items.Add(firstLevelCommand);
            }
        }
        private void GenerateToolbar()
        {
            ToolBarExit.Image = ZTLisResources.C_Resources.GetImage16(ToolBarExit.Text);
        }
        #endregion

        #region 选项卡动作

        private void MainTab_TabPageClosing(object sender, TabPageCancelEventArgs e)
        {
            foreach (System.Windows.Forms.Form frm in Application.OpenForms)
            {
                foreach (object _ctrl in e.TabPage.Controls)
                {
                    if (_ctrl == frm)
                    {
                        frm.Close();
                        if (frm != null) e.Cancel = true;
                        return;
                    }
                }
            }
        }
        private void MainTab_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                MainTab.Close(MainTab.SelectedTab);
            }
        }

        private void MainTab_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                MainTab.ContextMenuStrip = ContextMenuStrip1;
            }
        }

        private void MainTab_MouseLeave(object sender, System.EventArgs e)
        {
            MainTab.ContextMenuStrip = null;
        }

        private void ToolStripMenuItem1_Click(object sender, System.EventArgs e)
        {
            MainTab.Close(MainTab.SelectedTab);
        }

        private void ToolStripMenuItem2_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            foreach (C1DockingTabPage tap in MainTab.TabPages)
            {
                if (!object.ReferenceEquals(tap, MainTab.SelectedTab))
                {
                    taparray.Add(tap);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem3_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            for (int pageindex = 0; pageindex <= MainTab.TabPages.Count - 1; pageindex++)
            {
                if (pageindex < MainTab.SelectedIndex)
                {
                    taparray.Add(MainTab.TabPages[pageindex]);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem4_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            for (int pageindex = 0; pageindex <= MainTab.TabPages.Count - 1; pageindex++)
            {
                if (pageindex > MainTab.SelectedIndex)
                {
                    taparray.Add(MainTab.TabPages[pageindex]);
                }
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        private void ToolStripMenuItem5_Click(object sender, System.EventArgs e)
        {
            ArrayList taparray = new ArrayList();
            foreach (C1DockingTabPage tap in MainTab.TabPages)
            {
                taparray.Add(tap);
            }
            for (int index = 0; index <= taparray.Count - 1; index++)
            {
                MainTab.Close((C1DockingTabPage)taparray[index]);
            }
        }

        #endregion

        #region 事件
        private void CallForm(string ModuleName)
        {
            Common.BaseForm.Base frm;
            switch (ModuleName)
            {
                case "科室信息":
                    frm = new ZTLisBaseDict.DictDepartment1();
                    break;
                case "医生信息":
                    frm = new ZTLisBaseDict.DictDoctor1();
                    break;
                case "就诊类型":
                    frm = new ZTLisBaseDict.DictVisitType1();
                    break;
                case "标本类型":
                    frm = new ZTLisBaseDict.DictSampleType1();
                    break;
                case "标本状态":
                    frm = new ZTLisBaseDict.DictSpecimenCondition1();
                    break;
                case "标本参数":
                    frm = new ZTLisBaseDict.DictSpecimenParam1();
                    break;
                case "项目类别":
                    frm = new ZTLisBaseDict.DictLabItemCategory1();
                    break;
                case "方法类别":
                    frm = new ZTLisBaseDict.DictMethodCategory1();
                    break;
                case "检验方法":
                    frm = new ZTLisBaseDict.DictMethod1();
                    break;
                case "仪器信息":
                    frm = new ZTLisBaseDict.DictLabInstrument1();
                    break;
                case "检验项目":
                    frm = new ZTLisBaseDict.DictLabItem1();
                    break;
                case "检验组合":
                    frm = new ZTLisBaseDict.DictLabProfile1();
                    break;
                case "仪器项目映射":
                    frm = new ZTLisBaseDict.LabInstrItemMap1();
                    break;
                case "仪器标本参数映射":
                    frm = new ZTLisBaseDict.LabInstrSpecimenParamMap1();
                    break;
                case "项目单位":
                    frm = new ZTLisBaseDict.DictUnit1();
                    break;
                case "检验申请":
                    frm = new ZTLisTest.LabTestRequest();
                    break;
                case "角色管理":
                    frm = new ZTLisSysManage.DictRole1();
                    break;
                case "用户管理":
                    frm = new ZTLisSysManage.SysUserManage1();
                    break;
                default:
                    return;
            }
            base.AddTabControl(frm, ModuleName, ZTLisResources.C_Resources.GetImage16(ModuleName));
        }

        private void MainFormV2_KeyDown(object sender, KeyEventArgs e)
        {

            if (e.Control && e.Alt && e.KeyCode == Keys.H)
            {
                Test frm = new Test();
            }
        }


        #endregion
    }
}
