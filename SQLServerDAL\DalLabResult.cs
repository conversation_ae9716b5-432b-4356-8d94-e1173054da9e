﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabResult.cs
*
* 功 能： N/A
* 类 名： DalLabResult
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabResult
	/// </summary>
	public partial class DalLabResult : IDalLabResult
	{
		public DalLabResult()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long ResultId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabResult");
			strSql.Append(" where ResultId=@ResultId");
			SqlParameter[] parameters = {
					new SqlParameter("@ResultId", SqlDbType.BigInt)
			};
			parameters[0].Value = ResultId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabResult model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabResult(");
			strSql.Append("OrderItemId,InstrumentId,InstrItemCode,ResultValue,Unit,AbnormalFlag,IsSystemGenerated,MeasuredAt,FirstReviewedBy,FirstReviewedAt,FinalReviewedBy,FinalReviewedAt,ReviewStatus)");
			strSql.Append(" values (");
			strSql.Append("@OrderItemId,@InstrumentId,@InstrItemCode,@ResultValue,@Unit,@AbnormalFlag,@IsSystemGenerated,@MeasuredAt,@FirstReviewedBy,@FirstReviewedAt,@FinalReviewedBy,@FinalReviewedAt,@ReviewStatus)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderItemId", SqlDbType.BigInt,8),
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@InstrItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@ResultValue", SqlDbType.NVarChar,60),
					new SqlParameter("@Unit", SqlDbType.NVarChar,20),
					new SqlParameter("@AbnormalFlag", SqlDbType.Char,1),
					new SqlParameter("@IsSystemGenerated", SqlDbType.Bit,1),
					new SqlParameter("@MeasuredAt", SqlDbType.DateTime),
					new SqlParameter("@FirstReviewedBy", SqlDbType.NVarChar,30),
					new SqlParameter("@FirstReviewedAt", SqlDbType.DateTime),
					new SqlParameter("@FinalReviewedBy", SqlDbType.NVarChar,30),
					new SqlParameter("@FinalReviewedAt", SqlDbType.DateTime),
					new SqlParameter("@ReviewStatus", SqlDbType.TinyInt,1)};
			parameters[0].Value = model.OrderItemId;
			parameters[1].Value = model.InstrumentId;
			parameters[2].Value = model.InstrItemCode;
			parameters[3].Value = model.ResultValue;
			parameters[4].Value = model.Unit;
			parameters[5].Value = model.AbnormalFlag;
			parameters[6].Value = model.IsSystemGenerated;
			parameters[7].Value = model.MeasuredAt;
			parameters[8].Value = model.FirstReviewedBy;
			parameters[9].Value = model.FirstReviewedAt;
			parameters[10].Value = model.FinalReviewedBy;
			parameters[11].Value = model.FinalReviewedAt;
			parameters[12].Value = model.ReviewStatus;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabResult model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabResult set ");
			strSql.Append("OrderItemId=@OrderItemId,");
			strSql.Append("InstrumentId=@InstrumentId,");
			strSql.Append("InstrItemCode=@InstrItemCode,");
			strSql.Append("ResultValue=@ResultValue,");
			strSql.Append("Unit=@Unit,");
			strSql.Append("AbnormalFlag=@AbnormalFlag,");
			strSql.Append("IsSystemGenerated=@IsSystemGenerated,");
			strSql.Append("MeasuredAt=@MeasuredAt,");
			strSql.Append("FirstReviewedBy=@FirstReviewedBy,");
			strSql.Append("FirstReviewedAt=@FirstReviewedAt,");
			strSql.Append("FinalReviewedBy=@FinalReviewedBy,");
			strSql.Append("FinalReviewedAt=@FinalReviewedAt,");
			strSql.Append("ReviewStatus=@ReviewStatus");
			strSql.Append(" where ResultId=@ResultId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderItemId", SqlDbType.BigInt,8),
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@InstrItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@ResultValue", SqlDbType.NVarChar,60),
					new SqlParameter("@Unit", SqlDbType.NVarChar,20),
					new SqlParameter("@AbnormalFlag", SqlDbType.Char,1),
					new SqlParameter("@IsSystemGenerated", SqlDbType.Bit,1),
					new SqlParameter("@MeasuredAt", SqlDbType.DateTime),
					new SqlParameter("@FirstReviewedBy", SqlDbType.NVarChar,30),
					new SqlParameter("@FirstReviewedAt", SqlDbType.DateTime),
					new SqlParameter("@FinalReviewedBy", SqlDbType.NVarChar,30),
					new SqlParameter("@FinalReviewedAt", SqlDbType.DateTime),
					new SqlParameter("@ReviewStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@ResultId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.OrderItemId;
			parameters[1].Value = model.InstrumentId;
			parameters[2].Value = model.InstrItemCode;
			parameters[3].Value = model.ResultValue;
			parameters[4].Value = model.Unit;
			parameters[5].Value = model.AbnormalFlag;
			parameters[6].Value = model.IsSystemGenerated;
			parameters[7].Value = model.MeasuredAt;
			parameters[8].Value = model.FirstReviewedBy;
			parameters[9].Value = model.FirstReviewedAt;
			parameters[10].Value = model.FinalReviewedBy;
			parameters[11].Value = model.FinalReviewedAt;
			parameters[12].Value = model.ReviewStatus;
			parameters[13].Value = model.ResultId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long ResultId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabResult ");
			strSql.Append(" where ResultId=@ResultId");
			SqlParameter[] parameters = {
					new SqlParameter("@ResultId", SqlDbType.BigInt)
			};
			parameters[0].Value = ResultId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string ResultIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabResult ");
			strSql.Append(" where ResultId in (" + ResultIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabResult GetModel(long ResultId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 ResultId,OrderItemId,InstrumentId,InstrItemCode,ResultValue,Unit,AbnormalFlag,IsSystemGenerated,MeasuredAt,FirstReviewedBy,FirstReviewedAt,FinalReviewedBy,FinalReviewedAt,ReviewStatus from LabResult ");
			strSql.Append(" where ResultId=@ResultId");
			SqlParameter[] parameters = {
					new SqlParameter("@ResultId", SqlDbType.BigInt)
			};
			parameters[0].Value = ResultId;

			Model.MdlLabResult model = new Model.MdlLabResult();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabResult DataRowToModel(DataRow row)
		{
			Model.MdlLabResult model = new Model.MdlLabResult();
			if (row != null)
			{
				if (row["ResultId"] != null && row["ResultId"].ToString() != "")
				{
					model.ResultId = long.Parse(row["ResultId"].ToString());
				}
				if (row["OrderItemId"] != null && row["OrderItemId"].ToString() != "")
				{
					model.OrderItemId = long.Parse(row["OrderItemId"].ToString());
				}
				if (row["InstrumentId"] != null && row["InstrumentId"].ToString() != "")
				{
					model.InstrumentId = int.Parse(row["InstrumentId"].ToString());
				}
				if (row["InstrItemCode"] != null)
				{
					model.InstrItemCode = row["InstrItemCode"].ToString();
				}
				if (row["ResultValue"] != null)
				{
					model.ResultValue = row["ResultValue"].ToString();
				}
				if (row["Unit"] != null)
				{
					model.Unit = row["Unit"].ToString();
				}
				if (row["AbnormalFlag"] != null)
				{
					model.AbnormalFlag = row["AbnormalFlag"].ToString();
				}
				if (row["IsSystemGenerated"] != null && row["IsSystemGenerated"].ToString() != "")
				{
					model.IsSystemGenerated = bool.Parse(row["IsSystemGenerated"].ToString());
				}
				if (row["MeasuredAt"] != null && row["MeasuredAt"].ToString() != "")
				{
					model.MeasuredAt = DateTime.Parse(row["MeasuredAt"].ToString());
				}
				if (row["FirstReviewedBy"] != null)
				{
					model.FirstReviewedBy = row["FirstReviewedBy"].ToString();
				}
				if (row["FirstReviewedAt"] != null && row["FirstReviewedAt"].ToString() != "")
				{
					model.FirstReviewedAt = DateTime.Parse(row["FirstReviewedAt"].ToString());
				}
				if (row["FinalReviewedBy"] != null)
				{
					model.FinalReviewedBy = row["FinalReviewedBy"].ToString();
				}
				if (row["FinalReviewedAt"] != null && row["FinalReviewedAt"].ToString() != "")
				{
					model.FinalReviewedAt = DateTime.Parse(row["FinalReviewedAt"].ToString());
				}
				if (row["ReviewStatus"] != null && row["ReviewStatus"].ToString() != "")
				{
					model.ReviewStatus = int.Parse(row["ReviewStatus"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ResultId,OrderItemId,InstrumentId,InstrItemCode,ResultValue,Unit,AbnormalFlag,IsSystemGenerated,MeasuredAt,FirstReviewedBy,FirstReviewedAt,FinalReviewedBy,FinalReviewedAt,ReviewStatus ");
			strSql.Append(" FROM LabResult ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" ResultId,OrderItemId,InstrumentId,InstrItemCode,ResultValue,Unit,AbnormalFlag,IsSystemGenerated,MeasuredAt,FirstReviewedBy,FirstReviewedAt,FinalReviewedBy,FinalReviewedAt,ReviewStatus ");
			strSql.Append(" FROM LabResult ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabResult ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.ResultId desc");
			}
			strSql.Append(")AS Row, T.*  from LabResult T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabResult";
			parameters[1].Value = "ResultId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

