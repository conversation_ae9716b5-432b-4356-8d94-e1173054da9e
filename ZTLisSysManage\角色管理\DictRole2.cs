﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Common.BaseForm;
using Model;

namespace ZTLisSysManage
{
    public partial class DictRole2 : Common.BaseForm.BaseDict22
    {
        BLL.BllSysRole _bllSysRole = new BllSysRole();
        Model.MdlSysRole _mdlSysRole = new MdlSysRole();
        public DictRole2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;
        }

        private void DictRole2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 控件动作

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (base.Insert == true)
            {
                this.DataAdd();
            }
            else
            {
                this.DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtPinYin.Text = base.MyChs2Spell.GetPy(TxtName.Text);
            TxtWuBi.Text = base.MyChs2Spell.GetWb(TxtName.Text);
        }

        #endregion

        #region 自定义函数
        private void FormInit()
        {
            TxtCode.Enabled = false;
            TxtPinYin.Enabled = false;
            TxtWuBi.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllSysRole.MaxCode();
            TxtName.Text = "";
            TxtMemo.Text = "";
            TxtWuBi.Text = "";
            TxtPinYin.Text = "";
            TxtName.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;

            TxtCode.Text = row["RoleCode"] + "";
            TxtName.Text = row["RoleName"] + "";
            TxtPinYin.Text = row["RolePinYin"] + "";
            TxtWuBi.Text = row["RoleWuBi"] + "";
            TxtMemo.Text = row["RoleMemo"] + "";
            TxtName.Select();
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlSysRole.RoleCode = _bllSysRole.MaxCode();
            _mdlSysRole.RoleName = TxtName.Text.Trim();
            _mdlSysRole.RolePinYin = TxtPinYin.Text.Trim();
            _mdlSysRole.RoleWuBi = TxtWuBi.Text.Trim();
            _mdlSysRole.RoleMemo = TxtMemo.Text.Trim();


            PropertyInfo[] pis = typeof(Model.MdlSysRole).GetProperties();
            foreach (PropertyInfo pi in pis)
            {
                //得到属性的值
                string value1 = (string)pi.GetValue(_mdlSysRole, null);
                //得到属性的名称
                string attr = pi.Name.ToString();
                if (base.MyTable.Columns.Contains(attr)) base.MyRow[attr] = value1;
            }

            //数据保存
            try
            {
                base.MyTable.Rows.Add(base.MyRow);
                _bllSysRole.Add(_mdlSysRole);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
                TreeNode node = new TreeNode();
                node.Tag = _mdlSysRole.RoleCode;
                node.Name = _mdlSysRole.RoleCode;
                node.Text = _mdlSysRole.RoleName;
                base.MyTransmitTreeNode.OnSetTreeNode(node);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                TxtMemo.Select();
            }

            //数据清空
            DataClear();

        }

        private void DataEdit()
        {
            _mdlSysRole = _bllSysRole.GetModel(TxtCode.Text);
            _mdlSysRole.RoleName = TxtName.Text.Trim();
            _mdlSysRole.RolePinYin = TxtPinYin.Text.Trim();
            _mdlSysRole.RoleWuBi = TxtWuBi.Text.Trim();
            _mdlSysRole.RoleMemo = TxtMemo.Text.Trim();


            PropertyInfo[] pis = typeof(MdlSysRole).GetProperties();
            foreach (PropertyInfo pi in pis)
            {
                //得到属性的值
                string value1 = (string)pi.GetValue(_mdlSysRole, null);
                //得到属性的名称
                string attr = pi.Name.ToString();
                if (base.MyTable.Columns.Contains(attr)) base.MyRow[attr] = value1;
            }

            //数据保存
            try
            {
                _bllSysRole.Update(_mdlSysRole);
                base.MyRow.AcceptChanges();
                TreeNode node = new TreeNode();
                node.Tag = _mdlSysRole.RoleCode;
                node.Name = _mdlSysRole.RoleCode;
                node.Text = _mdlSysRole.RoleName;
                base.MyTransmitTreeNode.OnSetTreeNode(node);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                TxtName.Select();
            }

        }

        #endregion

    }
}
