using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Transactions;
using Common.BaseForm;
using ZTLisControl;
using ZTLisEnum;

namespace ZTLisTest
{
    public partial class LabTestRequest : BaseFather
    {
        BLL.BllLabSpecimen _bllLabSpecimen = new BLL.BllLabSpecimen();
        BLL.BllLabOrder _bllLabOrder = new BLL.BllLabOrder();
        bool _insert = false;

        public LabTestRequest()
        {
            InitializeComponent();
        }

        private void LabTestRequest_Load(object sender, EventArgs e)
        {
            this.FormInit();
            this.DataInit();
            this.DataClear();
        }

        #region 自定义函数

        #region 初始化

        private void FormInit()
        {
            // 初始化左侧样本列表Grid
            myGrid1.Init_Grid();
            myGrid1.Init_Column("状态", "SpecimenStatusName", 60, "中", "", false);
            myGrid1.Init_Column("报告状态", "Printed", 80, "中", "", false);
            myGrid1.Init_Column("样本号", "SampleNo", 60, "中", "", false);
            myGrid1.Init_Column("姓名", "PatientName", 80, "左", "", false);
            myGrid1.Init_Column("条码", "Barcode", 120, "左", "", false);
            myGrid1.Init_Column("性别", "PatientSex", 40, "中", "", false);
            myGrid1.Init_Column("年龄", "PatientAge", 60, "中", "", false);
            myGrid1.Init_Column("申请科室", "DeptName", 80, "左", "", false);
            myGrid1.Init_Column("申请医生", "DoctorName", 80, "左", "", false);
            myGrid1.Init_Column("标本类型", "SampleTypeName", 80, "左", "", false);
            myGrid1.Init_Column("申请时间", "OrderDateTime", 120, "中", "yyyy-MM-dd HH:mm", false);
            myGrid1.AllowSort = true;

            // 初始化右侧检验项目结果Grid
            myGrid2.Init_Grid();
            myGrid2.Init_Column("序号", "RowNo", 40, "中", "", false);
            myGrid2.Init_Column("英文简称", "ItemCode", 100, "左", "", false);
            myGrid2.Init_Column("项目明细", "ItemName", 150, "左", "", false);
            myGrid2.Init_Column("结果", "Result", 80, "右", "", false);
            myGrid2.Init_Column("提示", "Flag", 40, "中", "", false);
            myGrid2.Init_Column("单位", "Unit", 80, "左", "", false);
            myGrid2.Init_Column("参考范围", "ReferenceRange", 100, "左", "", false);
            myGrid2.Init_Column("组合", "Profile", 100, "左", "", false);
            myGrid2.Init_Column("历史结果1", "HistoryResult1", 80, "右", "", false);
            myGrid2.Init_Column("历史结果2", "HistoryResult2", 80, "右", "", false);
            myGrid2.Init_Column("历史结果3", "HistoryResult3", 80, "右", "", false);
            myGrid2.AllowSort = true;

            // 初始化筛选条件
            this.InitFilterControls();
            this.InitMiddleControls();
            this.AddEventHandlers();
        }

        private void InitFilterControls()
        {
            // TODO: Add items to cboSampleStatusFilter
            cboSampleStatusFilter.Additem = ("全部");
            cboSampleStatusFilter.SelectedIndex = 0;
        }

        private void InitMiddleControls()
        {
            dtpCheckDate.Value = DateTime.Now;
            cboInstrument.Init();
            cboSampleType.Init();
            cboSpecimenCondition.Init();
            cboRequestDept.Init();
            cboRequestDoctor.Init();
            cboCheckDoctor.Init();
            cboAuditDoctor.Init();
            cboAgeUnit.Init();
            singleSex1.Init();
            cboVisitType.Init();
            cboLabProfile.Init();
        }

        private void AddEventHandlers()
        {
            CmdDelete.Click += (s, a) => DeleteLabOrder();
            CmdAudit.Click += (s, a) => MessageBox.Show("审核");
            CmdCancelAudit.Click += (s, a) => MessageBox.Show("取消审核");
            CmdAuditPrint.Click += (s, a) => MessageBox.Show("审核打印");
            CmdPrint.Click += (s, a) => MessageBox.Show("打印");
            CmdPreview.Click += (s, a) => MessageBox.Show("预览");
            cboLabProfile.SelectedValueChanged += cboLabProfile_SelectedValueChanged;
        }

        private void DataClear()
        {

            myGrid1.DataTable = null;
            txtDailySeqNo.Text = "";
            txtPatientName.Text = "";
            cboVisitType.SelectedIndex = -1;
            dtpBirthDate.Value = null;
            singleSex1.SelectedIndex = -1;
            numAge.Value = 0;
            cboAgeUnit.SelectedIndex = -1;
            txtBedNo.Text = "";
            cboInstrument.SelectedIndex = -1;
            cboSampleType.SelectedIndex = -1;
            cboSpecimenCondition.SelectedIndex = -1;
            cboRequestDept.SelectedIndex = -1;
            cboRequestDoctor.SelectedIndex = -1;
            cboCheckDoctor.SelectedIndex = -1;
            cboAuditDoctor.SelectedIndex = -1;
            txtRemark.Text = "";
            txtReportComment.Text = "";
            _insert = true;

            if (!chkKeepItems.Checked)
            {
                myGrid2.DataTable = null;
                lblResultCount.Text = "检测项目数: 0";
                cboLabProfile.SelectedIndex = -1;
            }

            // 自动获取下一个DailySeqNo
            GenerateNextDailySeqNo();
        }
        #endregion

        #region 数据初始化

        private void DataInit()
        {
            // 初始化数据
            this.LoadSampleData();
            this.UpdateSampleCount();

            if (myGrid1.Splits[0].Rows.Count > 0)
            {
                myGrid1.Row = 0;
                LoadResultData();
                DataShow();
            }
        }


        private void LoadSampleData()
        {
            // 使用实际的数据库查询
            try
            {
                string where = "1=1"; // 基础查询条件
                DataSet dsData = _bllLabSpecimen.GetListWithLabOrder(where);
                myGrid1.DataTable = dsData.Tables[0];
            }
            catch (Exception ex)
            {
                // 如果数据库查询失败，使用示例数据
                MessageBox.Show($"加载数据失败，: {ex.Message}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void LoadResultData()
        {
            try
            {
                // 根据选中的样本加载检验结果数据
                if (myGrid1.Row < 0 || myGrid1.RowCount == 0)
                {
                    myGrid2.DataTable = null;
                    lblResultCount.Text = "检测项目数: 0";
                    return;
                }

                // 获取选中行的标本ID
                DataRowView drv = myGrid1[myGrid1.Row] as DataRowView;
                if (drv == null || drv.Row == null)
                {
                    myGrid2.DataTable = null;
                    lblResultCount.Text = "检测项目数: 0";
                    return;
                }

                long specimenId = Convert.ToInt64(drv.Row["SpecimenId"]);

                // 使用修改后的GetList方法获取检验项目数据（包含联表查询结果）
                BLL.BllLabOrderItem bllLabOrderItem = new BLL.BllLabOrderItem();
                string strWhere = $"loi.SpecimenId = {specimenId}";
                DataSet dsOrderItems = bllLabOrderItem.GetList(strWhere);

                // 创建显示用的DataTable
                DataTable dtResult = CreateItemResultTable();

                if (dsOrderItems != null && dsOrderItems.Tables.Count > 0 && dsOrderItems.Tables[0].Rows.Count > 0)
                {
                    int rowNo = 1;
                    foreach (DataRow sourceRow in dsOrderItems.Tables[0].Rows)
                    {
                        DataRow newRow = dtResult.NewRow();
                        newRow["RowNo"] = rowNo++;
                        newRow["ItemCode"] = sourceRow["LabItemCode"]?.ToString() ?? "";
                        newRow["ItemName"] = sourceRow["LabItemName"]?.ToString() ?? "";
                        newRow["Result"] = sourceRow["ResultValue"]?.ToString() ?? "";

                        // 设置异常标志
                        string abnormalFlag = sourceRow["AbnormalFlag"]?.ToString() ?? "";
                        newRow["Flag"] = ConvertAbnormalFlag(abnormalFlag);

                        newRow["Unit"] = sourceRow["Unit"]?.ToString() ?? "";
                        newRow["ReferenceRange"] = ""; // 参考范围需要单独查询
                        newRow["Profile"] = sourceRow["ProfileName"]?.ToString() ?? "";
                        newRow["HistoryResult1"] = ""; // 历史结果需要单独查询
                        newRow["HistoryResult2"] = "";
                        newRow["HistoryResult3"] = "";

                        // 保存关键ID用于后续操作
                        newRow["OrderItemId"] = sourceRow["OrderItemId"];
                        newRow["LabItemId"] = sourceRow["LabItemId"];
                        newRow["ProfileId"] = sourceRow["ProfileId"];
                        newRow["ResultId"] = sourceRow["ResultId"];

                        dtResult.Rows.Add(newRow);
                    }
                }

                myGrid2.DataTable = dtResult;
                lblResultCount.Text = $"检测项目数: {dtResult.Rows.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载检验结果数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                myGrid2.DataTable = null;
                lblResultCount.Text = "检测项目数: 0";
            }
        }

        /// <summary>
        /// 转换异常标志为显示符号
        /// </summary>
        /// <param name="abnormalFlag"></param>
        /// <returns></returns>
        private string ConvertAbnormalFlag(string abnormalFlag)
        {
            if (string.IsNullOrEmpty(abnormalFlag))
                return "";

            switch (abnormalFlag.ToUpper())
            {
                case "H":
                case "HIGH":
                    return "↑";
                case "L":
                case "LOW":
                    return "↓";
                case "A":
                case "ABNORMAL":
                    return "A";
                case "POS":
                case "POSITIVE":
                    return "+";
                case "NEG":
                case "NEGATIVE":
                    return "-";
                default:
                    return abnormalFlag;
            }
        }

        /// <summary>
        /// 根据选择的检验组合加载相关的检验项目
        /// </summary>
        private void LoadProfileItems()
        {
            try
            {
                if (cboLabProfile.SelectedValue == null || cboLabProfile.SelectedValue == DBNull.Value)
                {
                    myGrid2.DataTable = null;
                    lblResultCount.Text = "检验项目数: 0";
                    return;
                }

                int profileId = Convert.ToInt32(cboLabProfile.SelectedValue);

                // 获取检验组合的项目列表
                BLL.BllDictLabProfileItemMap bllProfileItemMap = new BLL.BllDictLabProfileItemMap();
                string strWhere = $"ProfileId={profileId} ORDER BY SequenceNo";
                DataSet ds = bllProfileItemMap.GetList(strWhere);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    // 创建显示用的DataTable
                    DataTable dtItems = CreateItemResultTable();

                    int rowNo = 1;
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        int labItemId = Convert.ToInt32(row["LabItemId"]);

                        // 获取检验项目详细信息
                        BLL.BllDictLabItem bllLabItem = new BLL.BllDictLabItem();
                        Model.MdlDictLabItem itemModel = bllLabItem.GetModel(labItemId);

                        if (itemModel != null)
                        {
                            DataRow newRow = dtItems.NewRow();
                            newRow["RowNo"] = rowNo++;
                            newRow["ItemCode"] = itemModel.LabItemCode;
                            newRow["ItemName"] = itemModel.LabItemName;
                            newRow["Result"] = "";
                            newRow["Flag"] = "";
                            newRow["Unit"] = itemModel.DefaultUnit ?? "";
                            newRow["ReferenceRange"] = ""; // 参考范围需要从其他表获取
                            newRow["Profile"] = cboLabProfile.Text;
                            newRow["HistoryResult1"] = "";
                            newRow["HistoryResult2"] = "";
                            newRow["HistoryResult3"] = "";
                            newRow["LabItemId"] = labItemId;
                            newRow["ProfileId"] = profileId;

                            dtItems.Rows.Add(newRow);
                        }
                    }

                    myGrid2.DataTable = dtItems;
                    lblResultCount.Text = $"检验项目数: {dtItems.Rows.Count}";
                }
                else
                {
                    myGrid2.DataTable = null;
                    lblResultCount.Text = "检验项目数: 0";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载检验项目失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 创建检验项目结果显示表
        /// </summary>
        private DataTable CreateItemResultTable()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("RowNo", typeof(int));
            dt.Columns.Add("ItemCode", typeof(string));
            dt.Columns.Add("ItemName", typeof(string));
            dt.Columns.Add("Result", typeof(string));
            dt.Columns.Add("Flag", typeof(string));
            dt.Columns.Add("Unit", typeof(string));
            dt.Columns.Add("ReferenceRange", typeof(string));
            dt.Columns.Add("Profile", typeof(string));
            dt.Columns.Add("HistoryResult1", typeof(string));
            dt.Columns.Add("HistoryResult2", typeof(string));
            dt.Columns.Add("HistoryResult3", typeof(string));
            // 添加关键ID列用于后续操作
            dt.Columns.Add("OrderItemId", typeof(long));
            dt.Columns.Add("LabItemId", typeof(int));
            dt.Columns.Add("ProfileId", typeof(int));
            dt.Columns.Add("ResultId", typeof(long));
            return dt;
        }

        private void DataShow()
        {
            if (myGrid1.Row < 0 || myGrid1.Row >= myGrid1.RowCount) return;

            DataRowView drv = myGrid1[myGrid1.Row] as DataRowView;
            if (drv == null) return;
            DataRow row = drv.Row;

            txtDailySeqNo.Text = row["DailySeqNo"].ToString();
            txtPatientName.Text = row["PatientName"].ToString();
            singleSex1.Text = row["PatientSex"].ToString();
            numAge.Value = row["PatientAge"];
            cboAgeUnit.Text = row["PatientAgeUnit"].ToString();
            cboRequestDept.SelectedValue = row["RequestingDeptId"].ToString();
            cboRequestDoctor.SelectedValue = row["RequestingDoctorId"].ToString();
            cboSampleType.SelectedValue = row["SampleTypeId"].ToString();
            cboSpecimenCondition.SelectedValue = row["SpecCondId"].ToString();
            // cboCheckDoctor.SelectedValue = row["CheckDoctorId"].ToString();
            // cboAuditDoctor.SelectedValue = row["AuditDoctorId"].ToString();
            _insert = false;
        }

        private void UpdateSampleCount()
        {
            lblSampleCount.Text = $"样本数量: {myGrid1.Splits[0].Rows.Count}";
        }

        #endregion

        #region 工具函数

        /// <summary>
        /// 生成下一个DailySeqNo
        /// </summary>
        private void GenerateNextDailySeqNo()
        {
            try
            {
                string seqPrefix = txtSeqPrefix.Text.Trim();
                DateTime specimenDate = ((DateTime)dtpCheckDate.Value).Date;

                int maxSeqNo = _bllLabSpecimen.GetMaxDailySeqNo(seqPrefix, specimenDate);
                int nextSeqNo = maxSeqNo + 1;

                txtDailySeqNo.Text = nextSeqNo.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成流水号失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtDailySeqNo.Text = "1";
            }
        }

        /// <summary>
        /// 更新DataTable中的行数据
        /// </summary>
        /// <param name="orderModel">申请单模型</param>
        /// <param name="specimenModel">标本模型</param>
        private void UpdateDataTableRow(Model.MdlLabOrder orderModel, Model.MdlLabSpecimen specimenModel)
        {
            try
            {
                DataTable dt = myGrid1.DataSource as DataTable;
                if (dt == null) return;

                if (_insert) // 新增模式
                {
                    // 创建新行
                    DataRow newRow = dt.NewRow();
                    FillDataRowFromModels(newRow, orderModel, specimenModel);
                    dt.Rows.Add(newRow);

                    // 选中新添加的行
                    myGrid1.Row = dt.Rows.Count - 1;
                }
                else // 修改模式
                {
                    // 更新当前选中行
                    if (myGrid1.Row >= 0 && myGrid1.Row < dt.Rows.Count)
                    {
                        DataRow currentRow = dt.Rows[myGrid1.Row];
                        FillDataRowFromModels(currentRow, orderModel, specimenModel);
                    }
                }

                // 更新样本计数
                UpdateSampleCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新数据表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 从DataTable中删除当前选中的行
        /// </summary>
        private void RemoveDataTableRow()
        {
            try
            {
                DataTable dt = myGrid1.DataSource as DataTable;
                if (dt == null) return;

                if (myGrid1.Row >= 0 && myGrid1.Row < dt.Rows.Count)
                {
                    // 删除当前选中行
                    dt.Rows.RemoveAt(myGrid1.Row);

                    // 如果还有数据，选中合适的行
                    if (dt.Rows.Count > 0)
                    {
                        // 如果删除的是最后一行，选中新的最后一行
                        if (myGrid1.Row >= dt.Rows.Count)
                        {
                            myGrid1.Row = dt.Rows.Count - 1;
                        }
                        // 否则保持当前行位置
                    }
                    else
                    {
                        // 没有数据了，清空右侧Grid
                        myGrid2.DataSource = null;
                        lblResultCount.Text = "检测项目数: 0";
                    }

                    // 更新样本计数
                    UpdateSampleCount();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除数据行失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 从控件和模型填充DataRow数据（优先使用控件值）
        /// </summary>
        /// <param name="row">要填充的DataRow</param>
        /// <param name="orderModel">申请单模型</param>
        /// <param name="specimenModel">标本模型</param>
        private void FillDataRowFromModels(DataRow row, Model.MdlLabOrder orderModel, Model.MdlLabSpecimen specimenModel)
        {
            // 填充ID字段（来自模型）
            row["OrderId"] = orderModel.OrderId;
            row["SpecimenId"] = specimenModel.SpecimenId;

            // 优先从控件获取值
            row["DailySeqNo"] = string.IsNullOrEmpty(txtDailySeqNo.Text) ? 0 : Convert.ToInt32(txtDailySeqNo.Text);
            row["PatientName"] = txtPatientName.Text.Trim();
            row["Barcode"] = specimenModel.Barcode ?? ""; // 条码由系统生成
            row["PatientSex"] = singleSex1.Text;
            row["PatientAge"] = numAge.Value != null ? Convert.ToInt32(numAge.Value) : 0;
            row["PatientAgeUnit"] = cboAgeUnit.Text;
            row["OrderDateTime"] = dtpCheckDate.Value ?? DateTime.Now;

            // 从控件获取关联ID
            row["RequestingDeptId"] = cboRequestDept.SelectedValue ?? 0;
            row["RequestingDoctorId"] = cboRequestDoctor.SelectedValue ?? 0;
            row["SampleTypeId"] = cboSampleType.SelectedValue ?? 0;
            row["SpecCondId"] = cboSpecimenCondition.SelectedValue ?? 0;

            // 填充状态字段
            row["SpecimenStatus"] = specimenModel.SpecimenStatus;
            row["SpecimenStatusName"] = Enum.GetName(typeof(ZTLisEnum.SpecimenStatus), specimenModel.SpecimenStatus);
            row["Printed"] = false; // 新增的记录默认未打印

            // 从控件获取显示名称字段
            row["DeptName"] = cboRequestDept.Text;
            row["DoctorName"] = cboRequestDoctor.Text;
            row["SampleTypeName"] = cboSampleType.Text;

            // 填充其他字段
            string seqPrefix = txtSeqPrefix.Text.Trim();
            int dailySeqNo = string.IsNullOrEmpty(txtDailySeqNo.Text) ? 0 : Convert.ToInt32(txtDailySeqNo.Text);
            row["SampleNo"] = $"{seqPrefix}{dailySeqNo:000}";
        }





        #endregion

        #region 数据处理函数

        /// <summary>
        /// 数据验证
        /// </summary>
        /// <returns></returns>
        private bool DataCheck()
        {
            if (CustomControl.Func.NotAllowEmpty(cboInstrument)) return false;
            if (CustomControl.Func.NotAllowEmpty(txtPatientName)) return false;
            if (CustomControl.Func.NotAllowEmpty(singleSex1)) return false;
            if (CustomControl.Func.NotAllowEmpty(cboSampleType)) return false;

            // 如果是修改模式，检查标本状态是否允许修改
            if (!_insert)
            {
                Model.MdlLabSpecimen currentSpecimen = GetCurrentSpecimenModel();
                if (currentSpecimen.SpecimenStatus != (int)ZTLisEnum.SpecimenStatus.待采集)
                {
                    MessageBox.Show("只有状态为'待采集'的标本才能修改！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 保存检验申请
        /// </summary>
        private void DataSave()
        {
            try
            {
                // 数据验证
                if (!DataCheck())
                {
                    return;
                }

                // 使用TransactionScope管理事务
                using (TransactionScope scope = new TransactionScope())
                {
                    // 创建或更新LabOrder对象
                    Model.MdlLabOrder orderModel = GetCurrentOrderModel();

                    // 设置患者基本信息
                    orderModel.PatientName = txtPatientName.Text.Trim();
                    orderModel.PatientSex = singleSex1.Text;
                    if (dtpBirthDate.Value != null)
                    {
                        orderModel.PatientBirthDate = (DateTime)dtpBirthDate.Value;
                    }

                    // 处理年龄
                    if (numAge.Value != null && Convert.ToDecimal(numAge.Value) > 0)
                    {
                        orderModel.PatientAge = (int)Convert.ToDecimal(numAge.Value);
                        orderModel.PatientAgeUnit = cboAgeUnit.Text;
                    }

                    // 设置就诊信息
                    orderModel.BedNo = txtBedNo.Text.Trim();
                    orderModel.VisitTypeId = Common.ConvertObject.ObjToInt(cboVisitType.SelectedValue).Value;
                    orderModel.RequestingDeptId = Common.ConvertObject.ObjToInt(cboRequestDept.SelectedValue).Value;
                    orderModel.RequestingDoctorId = Common.ConvertObject.ObjToInt(cboRequestDoctor.SelectedValue).Value;
                    orderModel.ReportComment = txtReportComment.Text.Trim();
                    orderModel.OrderDateTime = dtpCheckDate.Value != null ? (DateTime)dtpCheckDate.Value : DateTime.Now;

                    if (orderModel.OrderId == 0) // 新增
                    {
                        orderModel.VisitId = "";
                        orderModel.PatientId = "";
                        orderModel.OrderStatus = 0; // 已开立
                        orderModel.CreatedAt = DateTime.Now;
                        orderModel.CreatedById = ZTLisVar.Var.UserId;
                        orderModel.CreatedByName = ZTLisVar.Var.UserName;
                        orderModel.UpdatedAt = DateTime.Now;
                        orderModel.UpdatedById = ZTLisVar.Var.UserId;
                        orderModel.UpdatedByName = ZTLisVar.Var.UserName;
                    }
                    else // 修改
                    {
                        orderModel.UpdatedAt = DateTime.Now;
                        orderModel.UpdatedById = ZTLisVar.Var.UserId;
                        orderModel.UpdatedByName = ZTLisVar.Var.UserName;
                    }

                    // 使用BLL层保存LabOrder
                    BLL.BllLabOrder bllOrder = new BLL.BllLabOrder();
                    long orderId;

                    if (orderModel.OrderId == 0) // 新增
                    {
                        orderId = bllOrder.Add(orderModel);
                    }
                    else // 修改
                    {
                        bool result = bllOrder.Update(orderModel);
                        orderId = result ? orderModel.OrderId : 0;
                    }

                    if (orderId > 0)
                    {
                        // 创建或更新LabSpecimen对象
                        Model.MdlLabSpecimen specimenModel = GetCurrentSpecimenModel();
                        specimenModel.OrderId = orderId;

                        // 设置标本基本信息
                        if (specimenModel.SpecimenId == 0) // 新增时才生成条码
                        {
                            specimenModel.Barcode = GenerateBarcode();
                        }

                        specimenModel.SeqPrefix = txtSeqPrefix.Text.Trim();
                        specimenModel.DailySeqNo = string.IsNullOrEmpty(txtDailySeqNo.Text) ? (int?)null : Convert.ToInt32(txtDailySeqNo.Text);
                        specimenModel.SpecimenDate = dtpCheckDate.Value != null ? ((DateTime)dtpCheckDate.Value).Date : DateTime.Now.Date;
                        specimenModel.SampleTypeId = Common.ConvertObject.ObjToInt(cboSampleType.SelectedValue).Value;
                        specimenModel.SpecCondId = Common.ConvertObject.ObjToInt(cboSpecimenCondition.SelectedValue);
                        specimenModel.Remarks = txtRemark.Text.Trim();

                        if (specimenModel.SpecimenId == 0) // 新增
                        {
                            specimenModel.SpecimenStatus = (int)ZTLisEnum.SpecimenStatus.待采集;
                            specimenModel.CreatedAt = DateTime.Now;
                            specimenModel.CreatedById = ZTLisVar.Var.UserId;
                            specimenModel.CreatedByName = ZTLisVar.Var.UserName;
                            specimenModel.UpdatedAt = DateTime.Now;
                            specimenModel.UpdatedById = ZTLisVar.Var.UserId;
                            specimenModel.UpdatedByName = ZTLisVar.Var.UserName;
                        }
                        else // 修改
                        {
                            specimenModel.UpdatedAt = DateTime.Now;
                            specimenModel.UpdatedById = ZTLisVar.Var.UserId;
                            specimenModel.UpdatedByName = ZTLisVar.Var.UserName;
                        }

                        // 使用BLL层保存LabSpecimen
                        long specimenId;
                        if (specimenModel.SpecimenId == 0) // 新增
                        {
                            specimenId = _bllLabSpecimen.Add(specimenModel);
                        }
                        else // 修改
                        {
                            bool result = _bllLabSpecimen.Update(specimenModel);
                            specimenId = result ? specimenModel.SpecimenId : 0;
                        }

                        if (specimenId > 0)
                        {
                            // 保存检验项目明细（LabOrderItem）- 直接在TransactionScope内执行
                            bool orderItemsSaveSuccess = true;

                            // 获取右侧Grid中的检验项目数据
                            DataTable dtItems = myGrid2.DataSource as DataTable;
                            if (dtItems != null && dtItems.Rows.Count > 0)
                            {
                                BLL.BllLabOrderItem bllLabOrderItem = new BLL.BllLabOrderItem();

                                // 如果是修改模式，先删除原有的LabOrderItem记录
                                if (!_insert)
                                {
                                    string deleteWhere = $"loi.SpecimenId = {specimenId}";
                                    DataSet existingItems = bllLabOrderItem.GetList(deleteWhere);
                                    if (existingItems != null && existingItems.Tables.Count > 0 && existingItems.Tables[0].Rows.Count > 0)
                                    {
                                        foreach (DataRow row in existingItems.Tables[0].Rows)
                                        {
                                            long orderItemId = Convert.ToInt64(row["OrderItemId"]);
                                            if (!bllLabOrderItem.Delete(orderItemId))
                                            {
                                                orderItemsSaveSuccess = false;
                                                break;
                                            }
                                        }
                                    }
                                }

                                // 保存当前Grid中的所有检验项目
                                if (orderItemsSaveSuccess)
                                {
                                    foreach (DataRow row in dtItems.Rows)
                                    {
                                        // 检查是否有有效的LabItemId
                                        if (row["LabItemId"] == null || row["LabItemId"] == DBNull.Value)
                                            continue;

                                        int labItemId = Convert.ToInt32(row["LabItemId"]);
                                        if (labItemId <= 0)
                                            continue;

                                        // 创建LabOrderItem模型
                                        Model.MdlLabOrderItem orderItemModel = new Model.MdlLabOrderItem();
                                        orderItemModel.SpecimenId = specimenId;
                                        orderItemModel.LabItemId = labItemId;

                                        // 处理ProfileId（可能为空）
                                        if (row["ProfileId"] != null && row["ProfileId"] != DBNull.Value)
                                        {
                                            int profileId = Convert.ToInt32(row["ProfileId"]);
                                            if (profileId > 0)
                                            {
                                                orderItemModel.ProfileId = profileId;
                                            }
                                        }

                                        orderItemModel.ItemStatus = 0; // 已申请
                                        orderItemModel.CreatedAt = DateTime.Now;
                                        orderItemModel.UpdatedAt = DateTime.Now;

                                        // 保存到数据库
                                        long orderItemId = bllLabOrderItem.Add(orderItemModel);
                                        if (orderItemId <= 0)
                                        {
                                            orderItemsSaveSuccess = false;
                                            break;
                                        }

                                        // 更新Grid中的OrderItemId（用于后续操作）
                                        row["OrderItemId"] = orderItemId;
                                    }
                                }
                            }

                            if (orderItemsSaveSuccess)
                            {
                                // 提交事务
                                scope.Complete();

                                string message = orderModel.OrderId == 0 ? "保存成功！" : "修改成功！";
                                MessageBox.Show(message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                                // 直接操作DataTable而不是重新加载数据
                                UpdateDataTableRow(orderModel, specimenModel);

                                // 清空表单准备下一个录入
                                DataClear();
                            }
                            else
                            {
                                MessageBox.Show("保存检验项目明细失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                // TransactionScope会自动回滚，无需手动处理
                            }
                        }
                        else
                        {
                            MessageBox.Show("保存标本信息失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            // TransactionScope会自动回滚，无需手动处理
                        }
                    }
                    else
                    {
                        MessageBox.Show("保存申请信息失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        // TransactionScope会自动回滚，无需手动处理
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取当前的LabOrder模型（新增或修改）
        /// </summary>
        /// <returns></returns>
        private Model.MdlLabOrder GetCurrentOrderModel()
        {
            if (!_insert && myGrid1.Row >= 0)
            {
                // 修改模式：从选中行获取OrderId
                System.Data.DataRowView drv = myGrid1[myGrid1.Row] as System.Data.DataRowView;
                if (drv != null && drv.Row != null)
                {
                    long orderId = Convert.ToInt64(drv.Row["OrderId"]);
                    BLL.BllLabOrder bllOrder = new BLL.BllLabOrder();
                    return bllOrder.GetModel(orderId);
                }
            }

            // 新增模式：创建新对象
            return new Model.MdlLabOrder();
        }

        /// <summary>
        /// 获取当前的LabSpecimen模型（新增或修改）
        /// </summary>
        /// <returns></returns>
        private Model.MdlLabSpecimen GetCurrentSpecimenModel()
        {
            if (!_insert && myGrid1.Row >= 0)
            {
                // 修改模式：从选中行获取SpecimenId
                System.Data.DataRowView drv = myGrid1[myGrid1.Row] as System.Data.DataRowView;
                if (drv != null && drv.Row != null)
                {
                    long specimenId = Convert.ToInt64(drv.Row["SpecimenId"]);
                    return _bllLabSpecimen.GetModel(specimenId);
                }
            }

            // 新增模式：创建新对象
            return new Model.MdlLabSpecimen();
        }

        /// <summary>
        /// 生成条码
        /// </summary>
        /// <returns></returns>
        private string GenerateBarcode()
        {
            // 生成格式：日期+流水号，例如：20240101001
            string dateStr = DateTime.Now.ToString("yyyyMMdd");
            string seqStr = txtDailySeqNo.Text.PadLeft(3, '0');
            return $"{dateStr}{seqStr}";
        }



        /// <summary>
        /// 删除检验申请
        /// </summary>
        private void DeleteLabOrder()
        {
            if (myGrid1.Row < 0 || myGrid1.RowCount == 0)
            {
                MessageBox.Show("请先选择要删除的记录！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 获取选中行的数据
            System.Data.DataRowView drv = myGrid1[myGrid1.Row] as System.Data.DataRowView;
            if (drv == null || drv.Row == null)
            {
                MessageBox.Show("获取选中记录失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string patientName = drv.Row["PatientName"].ToString();
            string barcode = drv.Row["Barcode"].ToString();
            long specimenId = Convert.ToInt64(drv.Row["SpecimenId"]);

            // 检查标本状态是否允许删除
            Model.MdlLabSpecimen specimenModel = _bllLabSpecimen.GetModel(specimenId);
            if (specimenModel.SpecimenStatus != (int)ZTLisEnum.SpecimenStatus.待采集)
            {
                MessageBox.Show("只有状态为'待采集'的标本才能删除！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 确认删除
            DialogResult result = MessageBox.Show(
                $"确认要删除患者 {patientName} 的标本 {barcode} 吗？\n\n注意：此操作将同时删除相关的申请单和标本信息！",
                "确认删除",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2);

            if (result != DialogResult.Yes)
            {
                return;
            }

            try
            {
                // 获取OrderId
                long orderId = Convert.ToInt64(drv.Row["OrderId"]);

                // 使用TransactionScope管理事务
                using (TransactionScope scope = new TransactionScope())
                {
                    // 使用三层架构删除
                    BLL.BllLabOrder bllOrder = new BLL.BllLabOrder();

                    // 1. 获取并更新LabOrder状态为已取消(9)
                    Model.MdlLabOrder orderModel = bllOrder.GetModel(orderId);
                    if (orderModel == null)
                    {
                        MessageBox.Show("未找到申请信息！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    orderModel.OrderStatus = 9; // 已取消
                    orderModel.UpdatedAt = DateTime.Now;
                    bool orderResult = bllOrder.Update(orderModel);

                    if (!orderResult)
                    {
                        MessageBox.Show("删除申请信息失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 2. 获取并更新LabSpecimen状态为已作废(99)
                    Model.MdlLabSpecimen specimenToDelete = _bllLabSpecimen.GetModel(specimenId);
                    if (specimenToDelete == null)
                    {
                        MessageBox.Show("未找到标本信息！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    specimenToDelete.SpecimenStatus = (int)ZTLisEnum.SpecimenStatus.已作废;
                    specimenToDelete.UpdatedAt = DateTime.Now;
                    bool specimenResult = _bllLabSpecimen.Update(specimenToDelete);

                    if (!specimenResult)
                    {
                        MessageBox.Show("删除标本信息失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 提交事务
                    scope.Complete();

                    MessageBox.Show("删除成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 直接从DataTable中删除行
                    RemoveDataTableRow();

                    // 清空表单
                    DataClear();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #endregion

        #region 事件处理
        private void myGrid1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.DataShow();
                this.LoadResultData();
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            this.LoadSampleData();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            // 刷新按钮点击事件
            this.DataInit();
        }

        /// <summary>
        /// 出生日期变化事件处理
        /// </summary>
        private void dtpBirthDate_ValueChanged(object sender, EventArgs e)
        {
            if (dtpBirthDate.Value != null && dtpBirthDate.Value != DBNull.Value)
            {
                ZTLisPublicFunction.Utilities.CalculateAge((DateTime)dtpBirthDate.Value, out decimal age, out AgeUnit ageUnit);
                numAge.Value = age;
                cboAgeUnit.Text = ageUnit.ToString();
            }
        }

        /// <summary>
        /// txtSeqPrefix文本改变事件处理
        /// </summary>
        private void txtSeqPrefix_TextChanged(object sender, EventArgs e)
        {
            // 当样本号前缀改变时，重新生成DailySeqNo
            GenerateNextDailySeqNo();
        }

        /// <summary>
        /// 检验组合选择变化事件处理
        /// </summary>
        private void cboLabProfile_SelectedValueChanged(object sender, EventArgs e)
        {
            LoadProfileItems();
        }

        #region ToolBar事件
        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataClear();
        }

        private void CmdSave_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataSave();
        }

        #endregion

        #endregion


    }
}