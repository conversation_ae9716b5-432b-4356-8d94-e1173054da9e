﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZTLisEnum;

namespace ZTLisConnectors
{
    /// <summary>
    /// 仪器配置类，映射LabInstrument表
    /// </summary>
    public class LabInstrument
    {
        /// <summary>
        /// 是否为调试模式
        /// </summary>
        public bool Debug { get; set; } = false;

        /// <summary>
        /// 仪器ID
        /// </summary>
        public int InstrumentId { get; set; }

        /// <summary>
        /// 仪器代码
        /// </summary>
        public string InstrCode { get; set; }

        /// <summary>
        /// 仪器名称
        /// </summary>
        public string InstrName { get; set; }

        /// <summary>
        /// 连接类型：0=串口 1=TCP 2=文件共享 3=中间库 4=REST API 5=HL7 MLLP
        /// </summary>
        public ConnType ConnType { get; set; }

        /// <summary>
        /// 消息格式类型：0=字符串 1=字节流
        /// </summary>
        public MsgFormatType MsgFormatType { get; set; }

        /// <summary>
        /// 串口号
        /// </summary>
        public string ComPort { get; set; }

        /// <summary>
        /// 波特率
        /// </summary>
        public int? BaudRate { get; set; }

        /// <summary>
        /// 校验位：N=无校验, E=偶校验, O=奇校验, M=标记, S=空格
        /// </summary>
        public char? Parity { get; set; }

        /// <summary>
        /// 数据位
        /// </summary>
        public int? DataBits { get; set; }

        /// <summary>
        /// 停止位
        /// </summary>
        public int? StopBits { get; set; }

        /// <summary>
        /// IP地址或域名
        /// </summary>
        public string IpHost { get; set; }

        /// <summary>
        /// IP端口
        /// </summary>
        public int? IpPort { get; set; }

        /// <summary>
        /// 心跳间隔(秒)
        /// </summary>
        public int? KeepAliveSec { get; set; }

        /// <summary>
        /// 文件共享路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 中间库连接字符串
        /// </summary>
        public string DbConnStr { get; set; }

        /// <summary>
        /// API基础URL
        /// </summary>
        public string ApiBaseUrl { get; set; }

        /// <summary>
        /// API认证令牌
        /// </summary>
        public string ApiAuthToken { get; set; }

        /// <summary>
        /// 协议类型：0=ASTM 1=LIS2 2=POCT1-A 3=HL7 4=自定义
        /// </summary>
        public int ProtocolType { get; set; }

        /// <summary>
        /// 消息格式类型：0=字符串 1=字节流
        /// </summary>
        public MsgFormatType MsgHeaderFormatType { get; set; }

        /// <summary>
        /// 消息头（ASCII码16进制，多个用空格分割）
        /// </summary>
        public string MsgHeader { get; set; }

        /// <summary>
        /// 消息尾（ASCII码16进制，多个用空格分割）
        /// </summary>
        public string MsgTail { get; set; }

        /// <summary>
        /// 接收成功应答（ASCII码16进制，多个用空格分割）
        /// </summary>
        public string AckSuccess { get; set; }

        /// <summary>
        /// 接收失败应答（ASCII码16进制，多个用空格分割）
        /// </summary>
        public string AckFailure { get; set; }
        /// <summary>
        /// 驱动版本
        /// </summary>
        public string DriverVersion { get; set; }

        /// <summary>
        /// 状态：1=启用 0=停用
        /// </summary>
        public bool Status { get; set; }

    }
}
