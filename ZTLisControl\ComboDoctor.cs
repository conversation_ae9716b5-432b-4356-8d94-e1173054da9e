using System.Data;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    public partial class ComboDoctor : MyDtComobo
    {
        public ComboDoctor()
        {
            InitializeComponent();
        }

        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            strWhere += " and DictDoctor.Status=1";
            BLL.BllDictDoctor _bllDict_Doctor = new BllDictDoctor();
            this.DataView = _bllDict_Doctor.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("DoctorName", "名称", 130, "左");
            this.Init_Colum("DoctorCode", "编码", 0, "左");
            this.Init_Colum("PinYin", "拼音", 0, "左");
            this.Init_Colum("DeptName", "科室", 0, "左");
            this.DisplayMember = "DoctorName";
            this.ValueMember = "DoctorId";
            int width = 230;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "DoctorCode+isnull(PinYin,'')+DoctorName";
        }
    }
}