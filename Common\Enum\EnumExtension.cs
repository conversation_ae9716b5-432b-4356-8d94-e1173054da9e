﻿/*********************************************************************************** 
*        文 件 名 :Class1 
*        功能描述 :  
*        生成日期 :2017/1/18 14:57:13 
*        作   者 :董铮
*        机 器 名 :2013-20160817SN
*        All rights reserved 
************************************************************************************/
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Common
{
    public static class EnumExtension
    {
        private static readonly ConcurrentDictionary<string, Dictionary<string, string>> EnumCache = new ConcurrentDictionary<string, Dictionary<string, string>>();

        public static string GetEnumText(this Enum en)
        {
            if (en == null) return string.Empty;

            var type = en.GetType();
            string enString = en.ToString();

            // 使用 GetOrAdd 方法，它原子性地完成了所有工作。
            // 它会检查键是否存在。如果存在，返回现有值。
            // 如果不存在，它会执行第二个参数（工厂委托）来创建值，
            // 然后将值存入字典并返回，整个过程是线程安全的。
            var valueDict = EnumCache.GetOrAdd(type.FullName, (key) =>
            {
                var fields = type.GetFields();
                var temp = new Dictionary<string, string>();
                foreach (var item in fields)
                {
                    if (item.IsLiteral)
                    {
                        var attrs = item.GetCustomAttributes(typeof(TextAttribute), false);
                        if (attrs.Length == 1)
                        {
                            var v = ((TextAttribute)attrs[0]).Value;
                            temp.Add(item.Name, v);
                        }
                    }
                }
                return temp;
            });

            if (valueDict.TryGetValue(enString, out var textValue))
            {
                return textValue;
            }

            return enString;
        }
    }

    public class TextAttribute : Attribute
    {
        public TextAttribute(string value)
        {
            Value = value;
        }

        public string Value { get; set; }
    }

}
