﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabInstrResultQ.cs
*
* 功 能： N/A
* 类 名： DalLabInstrResultQ
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:14   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabInstrResultQ
	/// </summary>
	public partial class DalLabInstrResultQ : IDalLabInstrResultQ
	{
		public DalLabInstrResultQ()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long QId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabInstrResultQ");
			strSql.Append(" where QId=@QId");
			SqlParameter[] parameters = {
					new SqlParameter("@QId", SqlDbType.BigInt)
			};
			parameters[0].Value = QId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabInstrResultQ model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabInstrResultQ(");
			strSql.Append("RawId,InstrumentId,Barcode,InstrItemCode,ResultValue,InstrFlag,ResultType,MeasuredAt,ProcessError,ProcessedFlag)");
			strSql.Append(" values (");
			strSql.Append("@RawId,@InstrumentId,@Barcode,@InstrItemCode,@ResultValue,@InstrFlag,@ResultType,@MeasuredAt,@ProcessError,@ProcessedFlag)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@RawId", SqlDbType.BigInt,8),
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@Barcode", SqlDbType.NVarChar,30),
					new SqlParameter("@InstrItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@ResultValue", SqlDbType.NVarChar,60),
					new SqlParameter("@InstrFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@ResultType", SqlDbType.TinyInt,1),
					new SqlParameter("@MeasuredAt", SqlDbType.DateTime),
					new SqlParameter("@ProcessError", SqlDbType.NVarChar,200),
					new SqlParameter("@ProcessedFlag", SqlDbType.TinyInt,1)};
			parameters[0].Value = model.RawId;
			parameters[1].Value = model.InstrumentId;
			parameters[2].Value = model.Barcode;
			parameters[3].Value = model.InstrItemCode;
			parameters[4].Value = model.ResultValue;
			parameters[5].Value = Common.Tools.IsValueNull(model.InstrFlag);
			parameters[6].Value = model.ResultType;
			parameters[7].Value = model.MeasuredAt;
			parameters[8].Value = Common.Tools.IsValueNull(model.ProcessError);
			parameters[9].Value = model.ProcessedFlag;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabInstrResultQ model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabInstrResultQ set ");
			strSql.Append("RawId=@RawId,");
			strSql.Append("InstrumentId=@InstrumentId,");
			strSql.Append("Barcode=@Barcode,");
			strSql.Append("InstrItemCode=@InstrItemCode,");
			strSql.Append("ResultValue=@ResultValue,");
			strSql.Append("InstrFlag=@InstrFlag,");
			strSql.Append("ResultType=@ResultType,");
			strSql.Append("MeasuredAt=@MeasuredAt,");
			strSql.Append("ProcessError=@ProcessError,");
			strSql.Append("ProcessedFlag=@ProcessedFlag");
			strSql.Append(" where QId=@QId");
			SqlParameter[] parameters = {
					new SqlParameter("@RawId", SqlDbType.BigInt,8),
					new SqlParameter("@InstrumentId", SqlDbType.Int,4),
					new SqlParameter("@Barcode", SqlDbType.NVarChar,30),
					new SqlParameter("@InstrItemCode", SqlDbType.NVarChar,30),
					new SqlParameter("@ResultValue", SqlDbType.NVarChar,60),
					new SqlParameter("@InstrFlag", SqlDbType.NVarChar,10),
					new SqlParameter("@ResultType", SqlDbType.TinyInt,1),
					new SqlParameter("@MeasuredAt", SqlDbType.DateTime),
					new SqlParameter("@ProcessError", SqlDbType.NVarChar,200),
					new SqlParameter("@ProcessedFlag", SqlDbType.TinyInt,1),
					new SqlParameter("@QId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.RawId;
			parameters[1].Value = model.InstrumentId;
			parameters[2].Value = model.Barcode;
			parameters[3].Value = model.InstrItemCode;
			parameters[4].Value = model.ResultValue;
			parameters[5].Value = Common.Tools.IsValueNull(model.InstrFlag);
			parameters[6].Value = model.ResultType;
			parameters[7].Value = model.MeasuredAt;
			parameters[8].Value = Common.Tools.IsValueNull(model.ProcessError);
			parameters[9].Value = model.ProcessedFlag;
			parameters[10].Value = model.QId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long QId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrResultQ ");
			strSql.Append(" where QId=@QId");
			SqlParameter[] parameters = {
					new SqlParameter("@QId", SqlDbType.BigInt)
			};
			parameters[0].Value = QId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string QIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabInstrResultQ ");
			strSql.Append(" where QId in (" + QIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrResultQ GetModel(long QId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 QId,RawId,InstrumentId,Barcode,InstrItemCode,ResultValue,InstrFlag,ResultType,MeasuredAt,ProcessError,ProcessedFlag from LabInstrResultQ ");
			strSql.Append(" where QId=@QId");
			SqlParameter[] parameters = {
					new SqlParameter("@QId", SqlDbType.BigInt)
			};
			parameters[0].Value = QId;

			Model.MdlLabInstrResultQ model = new Model.MdlLabInstrResultQ();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabInstrResultQ DataRowToModel(DataRow row)
		{
			Model.MdlLabInstrResultQ model = new Model.MdlLabInstrResultQ();
			if (row != null)
			{
				if (row["QId"] != null && row["QId"].ToString() != "")
				{
					model.QId = long.Parse(row["QId"].ToString());
				}
				if (row["RawId"] != null && row["RawId"].ToString() != "")
				{
					model.RawId = long.Parse(row["RawId"].ToString());
				}
				if (row["InstrumentId"] != null && row["InstrumentId"].ToString() != "")
				{
					model.InstrumentId = int.Parse(row["InstrumentId"].ToString());
				}
				if (row["Barcode"] != null)
				{
					model.Barcode = row["Barcode"].ToString();
				}
				if (row["InstrItemCode"] != null)
				{
					model.InstrItemCode = row["InstrItemCode"].ToString();
				}
				if (row["ResultValue"] != null)
				{
					model.ResultValue = row["ResultValue"].ToString();
				}
				if (row["InstrFlag"] != null)
				{
					model.InstrFlag = row["InstrFlag"].ToString();
				}
				if (row["ResultType"] != null && row["ResultType"].ToString() != "")
				{
					model.ResultType = int.Parse(row["ResultType"].ToString());
				}
				if (row["MeasuredAt"] != null && row["MeasuredAt"].ToString() != "")
				{
					model.MeasuredAt = DateTime.Parse(row["MeasuredAt"].ToString());
				}
				if (row["ProcessError"] != null)
				{
					model.ProcessError = row["ProcessError"].ToString();
				}
				if (row["ProcessedFlag"] != null && row["ProcessedFlag"].ToString() != "")
				{
					model.ProcessedFlag = int.Parse(row["ProcessedFlag"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select QId,RawId,InstrumentId,Barcode,InstrItemCode,ResultValue,InstrFlag,ResultType,MeasuredAt,ProcessError,ProcessedFlag ");
			strSql.Append(" FROM LabInstrResultQ ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" QId,RawId,InstrumentId,Barcode,InstrItemCode,ResultValue,InstrFlag,ResultType,MeasuredAt,ProcessError,ProcessedFlag ");
			strSql.Append(" FROM LabInstrResultQ ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabInstrResultQ ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.QId desc");
			}
			strSql.Append(")AS Row, T.*  from LabInstrResultQ T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabInstrResultQ";
			parameters[1].Value = "QId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

