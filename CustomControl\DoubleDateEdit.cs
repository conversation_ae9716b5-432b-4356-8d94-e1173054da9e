﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Input;
using Microsoft.VisualBasic;
using System.Text.RegularExpressions;
namespace CustomControl
{
    public partial class DoubleDateEdit : UserControl
    {
        bool loadOk;
        public DoubleDateEdit()
        {
            InitializeComponent();
            loadOk = false;
            mySingleComobo1.Additem = "";
            mySingleComobo1.Additem = "今天";
            mySingleComobo1.Additem = "昨天";
            mySingleComobo1.Additem = "本周";
            mySingleComobo1.Additem = "上周";
            mySingleComobo1.Additem = "本月";
            mySingleComobo1.Additem = "上月";
            mySingleComobo1.Additem = "往月";
            mySingleComobo1.Additem = "本年";
            mySingleComobo1.Additem = "上年";
            mySingleComobo1.Additem = "往年";

            mySingleComobo1.DroupDownWidth = (int)mySingleComobo1.Width - (int)mySingleComobo1.CaptainWidth;
            mySingleComobo1.DisplayColumns[1].Visible = false;
            mySingleComobo1.ItemHeight = 20;
            mySingleComobo1.SelectedIndex = 1;
            //myDateEdit1.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit1.EditFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit1.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit2.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit2.EditFormat = "yyyy-MM-dd HH:mm:ss";
            //myDateEdit2.DisplayFormat = "yyyy-MM-dd HH:mm:ss";


            myDateEdit1.CustomFormat = "yyyy-MM-dd HH:mm";
            myDateEdit1.EditFormat = "yyyy-MM-dd HH:mm";
            myDateEdit1.DisplayFormat = "yyyy-MM-dd HH:mm";
            myDateEdit2.CustomFormat = "yyyy-MM-dd HH:mm";
            myDateEdit2.EditFormat = "yyyy-MM-dd HH:mm";
            myDateEdit2.DisplayFormat = "yyyy-MM-dd HH:mm";
            loadOk = true;


        }

        private void mySingleComobo1_RowChange(object sender, EventArgs e)
        {
            if (loadOk == false)
            {
                return;
            }
            DateTime? start;
            DateTime? end;
            string time;
            int weeknow;
            DateTime firstday;
            int daydiff;
            Regex R;
            switch (mySingleComobo1.Text)
            {
                case "":
                    start = null;
                    end = null;
                    break;
                case "今天":
                    start = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "昨天":
                    start = DateTime.Parse(DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "本周":
                    //星期一为第一天  
                    weeknow = Convert.ToInt32(DateTime.Now.DayOfWeek);
                    //因为是以星期一为第一天，所以要判断weeknow等于0时，要向前推6天。  
                    weeknow = (weeknow == 0 ? (7 - 1) : (weeknow - 1));
                    daydiff = (-1) * weeknow;
                    start = DateTime.Parse(DateTime.Now.AddDays(daydiff).ToString("yyyy-MM-dd 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "上周":
                    //星期一为第一天  
                    weeknow = Convert.ToInt32(DateTime.Now.DayOfWeek);
                    //因为是以星期一为第一天，所以要判断weeknow等于0时，要向前推6天。  
                    weeknow = (weeknow == 0 ? (7 - 1) : (weeknow - 1));
                    daydiff = (-1) * weeknow;
                    start = DateTime.Parse(DateTime.Now.AddDays(daydiff - 7).ToString("yyyy-MM-dd 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.AddDays(daydiff - 1).ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "本月":
                    start = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "上月":
                    firstday = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01 00:00:00"));
                    start = DateTime.Parse(DateTime.Now.AddMonths(-1).ToString("yyyy-MM-01 00:00:00"));
                    end = DateTime.Parse(firstday.AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "往月":
                    time = Microsoft.VisualBasic.Interaction.InputBox("请输入月份");
                    R = new Regex("^[1-9]$|^[1][0-2]$");
                    if (R.IsMatch(time) == true)
                    {
                        start = DateTime.Parse(DateTime.Now.Year + "-" + time + "-01 00:00:00");
                        end = DateTime.Parse(((DateTime)start).AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
                    }
                    else
                    {
                        MessageBox.Show("请输入正确的月份数字", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        start = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"));
                        end = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
                    }
                    break;
                case "本年":
                    start = DateTime.Parse(DateTime.Now.ToString("yyyy-01-01 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
                    break;
                case "上年":
                    start = DateTime.Parse(DateTime.Now.AddYears(-1).ToString("yyyy-01-01 00:00:00"));
                    end = DateTime.Parse(DateTime.Now.AddYears(-1).ToString("yyyy-12-31 23:59:59"));
                    break;
                case "往年":
                    time = Interaction.InputBox("请输入年份");
                    R = new Regex("^[1][9][0-9][0-9]$|^[2][0-9][0-9][0-9]$");
                    if (R.IsMatch(time) == true)
                    {
                        start = DateTime.Parse(time + "-" + "01-01 00:00:00");
                        end = DateTime.Parse(time + "-" + "12-31 23:59:59");
                    }
                    else
                    {
                        MessageBox.Show("请输入正确的年份数字", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        start = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"));
                        end = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"));
                    }
                    break;
                default:
                    start = DateTime.Now;
                    end = DateTime.Now;
                    break;
            }
            myDateEdit1.Value = start;
            myDateEdit2.Value = end;
        }

        #region 公开属性

        [Description("字体")]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                label2.Font = value;
                myDateEdit1.Font = value;
                myDateEdit2.Font = value;
                mySingleComobo1.Font = value;
                Invalidate();
            }
        }

        [Description("控件字体")]
        public new Font ControlFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                label2.Font = value;
                myDateEdit1.Font = value;
                myDateEdit2.Font = value;
                mySingleComobo1.Font = value;
                Invalidate();
            }
        }

        float _CaptainWidth = 60;
        [Description("标题宽度"),]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0)
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object StartValue
        {
            get { return myDateEdit1.Value; }
            set
            {
                myDateEdit1.Value = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object EndValue
        {
            get { return myDateEdit2.Value; }
            set
            {
                myDateEdit2.Value = value;
                Invalidate();
            }
        }

        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }


        [Description("自定义格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string CustomFormat
        {
            get { return myDateEdit1.CustomFormat; }
            set
            {
                myDateEdit1.CustomFormat = value;
                myDateEdit2.CustomFormat = value;
                Invalidate();
            }
        }

        [Description("显示格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string DisplayFormat
        {
            get { return myDateEdit1.DisplayFormat; }
            set
            {
                myDateEdit1.DisplayFormat = value;
                myDateEdit2.DisplayFormat = value;
                Invalidate();
            }
        }

        [Description("编辑格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string EditFormat
        {
            get { return myDateEdit1.EditFormat; }
            set
            {
                myDateEdit1.EditFormat = value;
                myDateEdit2.EditFormat = value;
                Invalidate();
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int SelectedIndex
        {
            get { return this.mySingleComobo1.SelectedIndex; }
            set
            {
                this.mySingleComobo1.SelectedIndex = value;
                Invalidate();
            }
        }

        #endregion

        #region 公开方法

        public void SelectStart()
        {
            myDateEdit1.Select();
        }

        public void SelectEnd()
        {
            myDateEdit2.Select();
        }

        #endregion

        #region 公开事件

        [Description("当值改变时时触发"), Browsable(true)]
        public event EventHandler ValueChanged
        {
            add
            {
                myDateEdit1.ValueChanged += value;
                myDateEdit2.ValueChanged += value;
            }
            remove
            {
                myDateEdit1.ValueChanged -= value;
                myDateEdit2.ValueChanged -= value;
            }
        }

        #endregion

    }
}
