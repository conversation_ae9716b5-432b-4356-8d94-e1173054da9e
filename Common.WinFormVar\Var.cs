﻿/*********************************************************************************** 
*        Filename :Common.WinFormVar 
*        Description :  WinForm通用变量，在任何项目都可以使用，如果是项目的特有变量，放到BaseVar里
*        Date:2017/06/04 09:57:19 
 *       Machinename:DESKTOP-PJMK973 
*        Created by: <T> 董铮
*        All rights reserved 
* 
*       ChangeLog: 
*       2017/06/04 09:57:19: 
 *          Created! 
************************************************************************************/
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using IDBUtility;

namespace Common.WinFormVar
{
   public class Var
    {

        /// <summary>
        /// 主程序
        /// </summary>
        public static Form MainForm { get; set; }
        /// <summary>
        /// 主Tab
        /// </summary>
        public static C1.Win.C1Command.C1DockingTab MainTab { get; set; }
        /// <summary>
        /// 数据库连接
        /// </summary>
        public static IDbHelper DbHelper { get; set; }

        /// <summary>
        /// 数据库连接配置库
        /// </summary>
        public static IDbHelper ConnDbHelper { get; set; }

        /// <summary>
        /// 本地配置数据库连接
        /// </summary>
        public static IDbHelper ConfigDbHelper { get; set; }
        /// <summary>
        /// DAL路径
        /// </summary>
        public static string DALPath { get; set; }

        /// <summary>
        /// DAL2路径
        /// </summary>
        public static string DALPath2 { get; set; }
        /// <summary>
        /// 参数路径
        /// </summary>
        public static string ParaPath { get; set; }
        /// <summary>
        /// 习惯输入法
        /// </summary>
        public static InputLanguage InputMethod { get; set; }
        /// <summary>
        /// 输入法类型：拼音、五笔
        /// </summary>
        public static Common.Enum.InPutEnum InputType { get; set; }
        /// <summary>
        /// 程序配置文件
        /// </summary>
        public static Common.INIFileHelper IniFileHelper { get; set; }

 


    }
}
