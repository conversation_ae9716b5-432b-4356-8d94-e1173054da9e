﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using C1.Win;

namespace CustomControl
{
    public partial class MyTextBox : UserControl
    {
        private const int EM_SETCUEBANNER = 0x1501;
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        private static extern Int32 SendMessage(IntPtr hWnd, int msg, int wParam, [MarshalAs(UnmanagedType.LPWStr)] string lParam);


        public MyTextBox()
        {
            InitializeComponent();
            Init_TextBox();
        }


        #region 私有函数

        private void Init_TextBox()
        {
            c1TextBox1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
        }

        private void c1TextBox1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (EnterToTab == true)
            {
                if (e.KeyChar != (char)(Keys.Return))
                    return;
                e.Handled = true;
                System.Windows.Forms.SendKeys.Send("{Tab}");
            }

        }

        private void label1_MouseHover(object sender, EventArgs e)
        {
            if (CaptainLink == true)
            {
                label1.ForeColor = Color.Blue;
            }
        }

        private void c1TextBox1_MouseLeave(object sender, EventArgs e)
        {
            if (CaptainLink == true)
            {
                label1.ForeColor = Color.Black;
            }
        }


        #endregion

        #region 公开属性
        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        float _CaptainWidth = 70;
        [Description("标题宽度"),]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
      
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0 || label1.Text == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        private bool _entertotab = true;
        [Description("回车键走Tab键"), Browsable(true), DefaultValue(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public bool EnterToTab
        {
            get { return _entertotab; }
            set
            {
                _entertotab = value;
            }
        }

        private bool _CaptainVisble = true;
        [Description("标题是否可见"), DefaultValue(true), Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public bool CaptainVisble
        {
            get { return _CaptainVisble; }
            set
            {
                _CaptainVisble = value;
                label1.Visible = _CaptainVisble;
                if (_CaptainVisble == false)
                {
                    tableLayoutPanel1.ColumnStyles[0].Width = 0;
                }
                Invalidate();
            }
        }

        [Description("控件文本"), Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public override string Text
        {
            get { return c1TextBox1.Text; }
            set
            {
                c1TextBox1.Text = value;
                c1TextBox1.Refresh();
                Invalidate();
            }
        }

        [Description("控件是否启用"), Browsable(true)]
        public new bool Enabled
        {
            get { return c1TextBox1.Enabled; }
            set
            {
                if (value == true)
                {
                    c1TextBox1.TabStop = true;
                    this.TabStop = true;
                    c1TextBox1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
                }
                else
                {
                    c1TextBox1.TabStop = false;
                    this.TabStop = false;
                    c1TextBox1.BackColor = SystemColors.Info;
                    c1TextBox1.DisabledForeColor = Color.Black;
                }
                c1TextBox1.Enabled = value;
                Invalidate();
            }
        }

        public bool ReadOnly
        {
            get { return c1TextBox1.ReadOnly; }
            set
            {
                if (value == true)
                {
                    c1TextBox1.BackColor = Color.MistyRose;
                    c1TextBox1.DisabledForeColor = Color.Black;
                }
                else
                {
                    c1TextBox1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;

                }
                c1TextBox1.ReadOnly = value;
                Invalidate();
            }
        }

        [Description("多行")]
        public bool Multiline
        {
            get { return c1TextBox1.Multiline; }
            set
            {
                c1TextBox1.Multiline = value;
                if (c1TextBox1.Multiline == true)
                {
                    c1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top;
                    c1TextBox1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
                }
                else
                {
                    c1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom;
                    c1TextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None;
                }
                Invalidate();
            }
        }

        [Description("滚动条"), Browsable(false)]
        public ScrollBars ScrollBars
        {
            get { return c1TextBox1.ScrollBars; }
            set
            {
                c1TextBox1.ScrollBars = value;
                Invalidate();
            }
        }

        [Description("垂直对齐方式")]
        public C1.Win.C1Input.VerticalAlignEnum VerticalAlign
        {
            get { return c1TextBox1.VerticalAlign; }
            set
            {
                c1TextBox1.VerticalAlign = value;
                Invalidate();
            }
        }

        [Description("水平对齐方式")]
        public HorizontalAlignment TextAlign
        {
            get { return c1TextBox1.TextAlign; }
            set
            {
                c1TextBox1.TextAlign = value;
                Invalidate();
            }
        }

        [Description("背景色"), Browsable(true)]
        public new Color BackColor
        {
            get { return c1TextBox1.BackColor; }
            set
            {
                c1TextBox1.BackColor = value;
                Invalidate();
            }
        }

        [Description("标题背景色"), Browsable(true)]
        public Color CaptainBackColor
        {
            get { return label1.BackColor; }
            set
            {
                label1.BackColor = value;
                Invalidate();
            }
        }

        [Description("禁用时的前景色"), Browsable(false)]
        public Color DisabledForeColor
        {
            get { return c1TextBox1.DisabledForeColor; }
            set
            {
                c1TextBox1.DisabledForeColor = value;
                Invalidate();
            }
        }

        [Description("内容前景色")]
        public Color ContentForeColor
        {
            get { return c1TextBox1.ForeColor; }
            set
            {
                c1TextBox1.ForeColor = value;
                Invalidate();
            }
        }

        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }

        [Description("字体")]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                c1TextBox1.Font = value;
                Invalidate();
            }
        }



        [Description("标题字体")]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }

        [Description("文本字体")]
        public Font TextFont
        {
            get { return c1TextBox1.Font; }
            set
            {
                c1TextBox1.Font = value;
                Invalidate();
            }
        }

        [Browsable(true)]
        public char PasswordChar
        {
            get { return c1TextBox1.PasswordChar; }
            set
            {
                c1TextBox1.PasswordChar = value;
                Invalidate();
            }
        }

        [Description("标题是否超链接"), DefaultValue(false)]
        public bool CaptainLink { get; set; }

        [Browsable(true)]
        public int SelectionStart
        {
            get { return c1TextBox1.SelectionStart; }
            set
            {
                c1TextBox1.SelectionStart = value;
                Invalidate();
            }
        }

        public int SelectStart
        {
            get { return c1TextBox1.SelectionStart; }
            set
            {
                c1TextBox1.SelectionStart = value;
                Invalidate();
            }
        }

        public ImeMode ImeMode
        {
            get { return c1TextBox1.ImeMode; }
            set
            {
                c1TextBox1.ImeMode = value;
                Invalidate();
            }
        }

        string _Watermark;
        [Description("水印")]
        public string Watermark
        {
            get { return _Watermark; }
            set
            {
                _Watermark = value;
                SendMessage(c1TextBox1.Handle, EM_SETCUEBANNER, 0, _Watermark);
                Invalidate();
            }
        }

        [Description("掩码")]
        public string EditMask
        {
            get { return c1TextBox1.EditMask; }
            set
            {
                c1TextBox1.EditMask = value;
                Invalidate();
            }
        }

        #endregion

        #region 公开方法
        public new void Select()
        {
            c1TextBox1.Select();
        }

        public void Select(int start, int length)
        {
            c1TextBox1.Select(start, length);
        }

        public new void Focus()
        {
            c1TextBox1.Focus();
        }

        public void SelectAll()
        {
            c1TextBox1.SelectAll();
        }
        #endregion

        #region 公开事件
        [Description("当控件获得光标焦点时"), Browsable(true)]
        public new event EventHandler GotFocus
        {
            add
            {
                c1TextBox1.GotFocus += value;
            }
            remove
            {
                c1TextBox1.GotFocus -= value;
            }
        }

        public new event KeyPressEventHandler KeyPress
        {
            add
            {
                c1TextBox1.KeyPress += value;
            }
            remove
            {
                c1TextBox1.KeyPress -= value;
            }
        }

        public new event KeyEventHandler KeyDown
        {
            add
            {
                c1TextBox1.KeyDown += value;
            }
            remove
            {
                c1TextBox1.KeyDown -= value;
            }
        }

        [Description("更改Text值时触发"), Browsable(true)]
        public new event EventHandler TextChanged
        {
            add
            {
                c1TextBox1.TextChanged += value;
            }
            remove
            {
                c1TextBox1.TextChanged -= value;
            }
        }

        [Description("双击组件时发生"), Browsable(true)]
        public new event EventHandler DoubleClick
        {
            add
            {
                c1TextBox1.DoubleClick += value;
            }
            remove
            {
                c1TextBox1.DoubleClick -= value;
            }
        }

        [Description("双击标签时发生"), Browsable(true)]
        public event EventHandler CaptainDoubleClick
        {
            add
            {
                label1.DoubleClick += value;
            }
            remove
            {
                label1.DoubleClick -= value;
            }
        }
        #endregion

    }


}
