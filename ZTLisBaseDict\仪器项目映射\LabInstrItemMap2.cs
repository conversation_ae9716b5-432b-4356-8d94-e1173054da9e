using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisControl;
using ZTLisEnum;

namespace ZTLisBaseDict
{
    public partial class LabInstrItemMap2 : Common.BaseForm.BaseDict2
    {
        BLL.BllLabInstrItemMap _bllLabInstrItemMap = new BllLabInstrItemMap();
        int _instrumentId = 0;
        public LabInstrItemMap2()
        {
            InitializeComponent();
        }

        public LabInstrItemMap2(int instrumentId, bool insert, DataRow myRow, DataTable myTable)
        {
            InitializeComponent();
            base.Insert = insert;
            base.MyRow = myRow;
            base.MyTable = myTable;
            _instrumentId = instrumentId;
        }

        private void LabInstrItemMap2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);

            TxtInstrItemCode.GotFocus += new System.EventHandler(base.InputEn);
            TxtInstrItemName.GotFocus += new System.EventHandler(base.InputEn);
            NumDecimals.GotFocus += new System.EventHandler(base.InputEn);
        }

        #region 自定义函数

        private void FormInit()
        {
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);

            NumDecimals.CustomFormat = "0";

            // 初始化检验项目下拉框
            ComboLabItem.Init();

            // 初始化结果格式下拉框
            ComboResultFormat.Init();
        }

        private void DataClear()
        {
            base.Insert = true;
            TxtInstrItemCode.Text = "";
            TxtInstrItemName.Text = "";
            ComboLabItem.SelectedIndex = -1;
            ComboResultFormat.SelectedIndex = 0; // 默认数值格式
            NumDecimals.Value = 2;


            // 根据结果格式显示/隐藏小数位数
            UpdateDecimalsVisibility();

            TxtInstrItemCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;

            TxtInstrItemCode.Text = row["InstrItemCode"] + "";
            TxtInstrItemName.Text = row["InstrItemName"] + "";

            if (row["LabItemId"] != DBNull.Value)
            {
                ComboLabItem.SelectedValue = row["LabItemId"];
            }

            if (row["ResultFormat"] != DBNull.Value)
            {
                string resultFormat = row["ResultFormat"].ToString();
                switch (resultFormat.ToUpper())
                {
                    case "NUM":
                        ComboResultFormat.SelectedIndex = 0; // 数值
                        break;
                    case "TXT":
                        ComboResultFormat.SelectedIndex = 1; // 文本
                        break;
                    case "NEG":
                        ComboResultFormat.SelectedIndex = 2; // 阴阳性
                        break;
                    default:
                        ComboResultFormat.SelectedIndex = 0;
                        break;
                }
            }

            if (row["Decimals"] != DBNull.Value)
            {
                NumDecimals.Value = int.Parse(row["Decimals"].ToString());
            }

            // 根据结果格式显示/隐藏小数位数
            UpdateDecimalsVisibility();

            TxtInstrItemCode.Select();
        }

        private void UpdateDecimalsVisibility()
        {
            // 只有数值格式才显示小数位数设置
            bool showDecimals = ComboResultFormat.SelectedIndex == 0; // 数值格式
            NumDecimals.Visible = showDecimals;
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtInstrItemCode.Text))
            {
                MessageBox.Show("请填写仪器项目代码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtInstrItemCode.Select();
                return false;
            }

            if (string.IsNullOrEmpty(TxtInstrItemName.Text))
            {
                MessageBox.Show("请填写仪器项目名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtInstrItemName.Select();
                return false;
            }

            if (ComboLabItem.SelectedValue == null)
            {
                MessageBox.Show("请选择LIS项目！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                ComboLabItem.Select();
                return false;
            }

            // 验证小数位数
            if (ComboResultFormat.SelectedIndex == 0)
            {
                if (CustomControl.Func.NotAllowEmpty(NumDecimals)) return false;

                int decimals;
                if (!int.TryParse(NumDecimals.Value.ToString(), out decimals) || decimals < 0 || decimals > 10)
                {
                    MessageBox.Show("小数位数必须是0-10之间的整数！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    NumDecimals.Select();
                    return false;
                }
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtInstrItemCode.Text))
                {
                    MessageBox.Show("项目代码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrItemCode.Select();
                    return false;
                }
            }
            else
            {
                if (TxtInstrItemCode.Text.Trim() != base.MyRow["InstrItemCode"].ToString().Trim() && CheckCodeExists(TxtInstrItemCode.Text))
                {
                    MessageBox.Show("项目代码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtInstrItemCode.Select();
                    return false;
                }
            }

            return true;
        }

        private bool CheckCodeExists(string code)
        {
            string strWhere = $"InstrumentId = {_instrumentId} AND InstrItemCode = '{code.Trim()}'";
            if (!base.Insert)
            {
                strWhere += $" AND MapId <> {base.MyRow["MapId"]}";
            }

            DataSet ds = _bllLabInstrItemMap.GetList(strWhere);
            return ds.Tables[0].Rows.Count > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            // 获取结果格式字符串
            string resultFormat = "NUM";
            switch (ComboResultFormat.SelectedIndex)
            {
                case 0:
                    resultFormat = "NUM";
                    break;
                case 1:
                    resultFormat = "TXT";
                    break;
                case 2:
                    resultFormat = "NEG";
                    break;
            }

            Model.MdlLabInstrItemMap model = new MdlLabInstrItemMap();
            model.InstrumentId = _instrumentId;
            model.InstrItemCode = TxtInstrItemCode.Text.Trim();
            model.InstrItemName = TxtInstrItemName.Text.Trim();
            model.LabItemId = int.Parse(ComboLabItem.SelectedValue.ToString());
            model.LisItemName = ComboLabItem.Text;
            model.ResultFormat = resultFormat;

            if (resultFormat == "NUM")
            {
                model.Decimals = int.Parse(NumDecimals.Value.ToString());
            }

            model.CreatedAt = DateTime.Now;
            model.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(model, base.MyRow);
            base.MyRow["LabItemCode"] = ComboLabItem.Columns["LabItemCode"].Value;
            //数据保存
            try
            {
                base.MyRow["MapId"] = _bllLabInstrItemMap.Add(model);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtInstrItemCode.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            // 获取结果格式字符串
            string resultFormat = "NUM";
            switch (ComboResultFormat.SelectedIndex)
            {
                case 0:
                    resultFormat = "NUM";
                    break;
                case 1:
                    resultFormat = "TXT";
                    break;
                case 2:
                    resultFormat = "NEG";
                    break;
            }

            Model.MdlLabInstrItemMap model = _bllLabInstrItemMap.GetModel(long.Parse(base.MyRow["MapId"].ToString()));
            model.InstrItemCode = TxtInstrItemCode.Text.Trim();
            model.InstrItemName = TxtInstrItemName.Text.Trim();
            model.LabItemId = int.Parse(ComboLabItem.SelectedValue.ToString());
            model.LisItemName = ComboLabItem.Text;
            model.ResultFormat = resultFormat;

            if (resultFormat == "NUM")
            {
                model.Decimals = int.Parse(NumDecimals.Value.ToString());
            }
            else
            {
                model.Decimals = null;
            }

            model.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(model, base.MyRow);
            base.MyRow["LabItemCode"] = ComboLabItem.Columns["LabItemCode"].Value;
            //数据保存
            try
            {
                _bllLabInstrItemMap.Update(model);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }

        #endregion

        #region 控件事件
        private void ComboResultFormat_SelectedValueChanged(object sender, EventArgs e)
        {
            UpdateDecimalsVisibility();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck())
            {
                if (base.Insert == true)
                {
                    this.DataAdd();
                }
                else
                {
                    this.DataEdit();
                }
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        #endregion

    }
}