﻿/**  版本信息模板在安装目录下，可自行修改。
* BllSysRpt_Class_Level.cs
*
* 功 能： N/A
* 类 名： BllSysRpt_Class_Level
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018-07-22 11:15:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using Model;
using DALFactory;
using IDAL;
namespace BLL
{
	/// <summary>
	/// 店铺编码规则
	/// </summary>
	public partial class BllSysRpt_Class_Level
	{
		private readonly IDalSysRpt_Class_Level dal=DataAccess.CreateDalSysRpt_Class_Level();
		public BllSysRpt_Class_Level()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
			return dal.GetMaxId();
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Level_Code)
		{
			return dal.Exists(Level_Code);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysRpt_Class_Level model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysRpt_Class_Level model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Level_Code)
		{
			
			return dal.Delete(Level_Code);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string Level_Codelist )
		{
			return dal.DeleteList(Level_Codelist);
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRpt_Class_Level GetModel(int Level_Code)
		{
			
			return dal.GetModel(Level_Code);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
		public Model.MdlSysRpt_Class_Level GetModelByCache(int Level_Code)
		{
			
			string CacheKey = "MdlSysRpt_Class_LevelModel-" + Level_Code;
			object objModel = Common.DataCache.GetCache(CacheKey);
			if (objModel == null)
			{
				try
				{
					objModel = dal.GetModel(Level_Code);
					if (objModel != null)
					{
						int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
						Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
					}
				}
				catch{}
			}
			return (Model.MdlSysRpt_Class_Level)objModel;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlSysRpt_Class_Level> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlSysRpt_Class_Level> DataTableToList(DataTable dt)
		{
			List<Model.MdlSysRpt_Class_Level> modelList = new List<Model.MdlSysRpt_Class_Level>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				Model.MdlSysRpt_Class_Level model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

