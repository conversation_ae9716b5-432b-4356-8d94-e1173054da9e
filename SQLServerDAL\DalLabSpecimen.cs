﻿/**  版本信息模板在安装目录下，可自行修改。
* DalLabSpecimen.cs
*
* 功 能： N/A
* 类 名： DalLabSpecimen
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalLabSpecimen
	/// </summary>
	public partial class DalLabSpecimen : IDalLabSpecimen
	{
		public DalLabSpecimen()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long SpecimenId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from LabSpecimen");
			strSql.Append(" where SpecimenId=@SpecimenId");
			SqlParameter[] parameters = {
					new SqlParameter("@SpecimenId", SqlDbType.BigInt)
			};
			parameters[0].Value = SpecimenId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabSpecimen model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into LabSpecimen(");
			strSql.Append("OrderId,Barcode,SeqPrefix,DailySeqNo,SpecimenDate,SampleTypeId,SpecimenStatus,CollectedById,CollectedByName,CollectedAt,ReceivedById,ReceivedByName,ReceivedAt,SpecCondId,RejectReason,Remarks,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName)");
			strSql.Append(" values (");
			strSql.Append("@OrderId,@Barcode,@SeqPrefix,@DailySeqNo,@SpecimenDate,@SampleTypeId,@SpecimenStatus,@CollectedById,@CollectedByName,@CollectedAt,@ReceivedById,@ReceivedByName,@ReceivedAt,@SpecCondId,@RejectReason,@Remarks,@CreatedAt,@CreatedById,@CreatedByName,@UpdatedAt,@UpdatedById,@UpdatedByName)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderId", SqlDbType.BigInt,8),
					new SqlParameter("@Barcode", SqlDbType.NVarChar,30),
					new SqlParameter("@SeqPrefix", SqlDbType.NVarChar,20),
					new SqlParameter("@DailySeqNo", SqlDbType.Int,4),
					new SqlParameter("@SpecimenDate", SqlDbType.Date,3),
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4),
					new SqlParameter("@SpecimenStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@CollectedById", SqlDbType.Int,4),
					new SqlParameter("@CollectedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@CollectedAt", SqlDbType.DateTime),
					new SqlParameter("@ReceivedById", SqlDbType.Int,4),
					new SqlParameter("@ReceivedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@ReceivedAt", SqlDbType.DateTime),
					new SqlParameter("@SpecCondId", SqlDbType.Int,4),
					new SqlParameter("@RejectReason", SqlDbType.NVarChar,200),
					new SqlParameter("@Remarks", SqlDbType.NVarChar,500),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@CreatedById", SqlDbType.Int,4),
					new SqlParameter("@CreatedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedById", SqlDbType.Int,4),
					new SqlParameter("@UpdatedByName", SqlDbType.NVarChar,100)};
			parameters[0].Value = model.OrderId;
			parameters[1].Value = model.Barcode;
			parameters[2].Value = model.SeqPrefix;
			parameters[3].Value = model.DailySeqNo;
			parameters[4].Value = model.SpecimenDate;
			parameters[5].Value = model.SampleTypeId;
			parameters[6].Value = model.SpecimenStatus;
			parameters[7].Value = Common.Tools.IsValueNull(model.CollectedById);
			parameters[8].Value = Common.Tools.IsValueNull(model.CollectedByName);
			parameters[9].Value = Common.Tools.IsValueNull(model.CollectedAt);
			parameters[10].Value = Common.Tools.IsValueNull(model.ReceivedById);
			parameters[11].Value = Common.Tools.IsValueNull(model.ReceivedByName);
			parameters[12].Value = Common.Tools.IsValueNull(model.ReceivedAt);
			parameters[13].Value = model.SpecCondId;
			parameters[14].Value = Common.Tools.IsValueNull(model.RejectReason);
			parameters[15].Value = Common.Tools.IsValueNull(model.Remarks);
			parameters[16].Value = model.CreatedAt;
			parameters[17].Value = model.CreatedById;
			parameters[18].Value = model.CreatedByName;
			parameters[19].Value = model.UpdatedAt;
			parameters[20].Value = model.UpdatedById;
			parameters[21].Value = model.UpdatedByName;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabSpecimen model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update LabSpecimen set ");
			strSql.Append("OrderId=@OrderId,");
			strSql.Append("Barcode=@Barcode,");
			strSql.Append("SeqPrefix=@SeqPrefix,");
			strSql.Append("DailySeqNo=@DailySeqNo,");
			strSql.Append("SpecimenDate=@SpecimenDate,");
			strSql.Append("SampleTypeId=@SampleTypeId,");
			strSql.Append("SpecimenStatus=@SpecimenStatus,");
			strSql.Append("CollectedById=@CollectedById,");
			strSql.Append("CollectedByName=@CollectedByName,");
			strSql.Append("CollectedAt=@CollectedAt,");
			strSql.Append("ReceivedById=@ReceivedById,");
			strSql.Append("ReceivedByName=@ReceivedByName,");
			strSql.Append("ReceivedAt=@ReceivedAt,");
			strSql.Append("SpecCondId=@SpecCondId,");
			strSql.Append("RejectReason=@RejectReason,");
			strSql.Append("Remarks=@Remarks,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("CreatedById=@CreatedById,");
			strSql.Append("CreatedByName=@CreatedByName,");
			strSql.Append("UpdatedAt=@UpdatedAt,");
			strSql.Append("UpdatedById=@UpdatedById,");
			strSql.Append("UpdatedByName=@UpdatedByName");
			strSql.Append(" where SpecimenId=@SpecimenId");
			SqlParameter[] parameters = {
					new SqlParameter("@OrderId", SqlDbType.BigInt,8),
					new SqlParameter("@Barcode", SqlDbType.NVarChar,30),
					new SqlParameter("@SeqPrefix", SqlDbType.NVarChar,20),
					new SqlParameter("@DailySeqNo", SqlDbType.Int,4),
					new SqlParameter("@SpecimenDate", SqlDbType.Date,3),
					new SqlParameter("@SampleTypeId", SqlDbType.Int,4),
					new SqlParameter("@SpecimenStatus", SqlDbType.TinyInt,1),
					new SqlParameter("@CollectedById", SqlDbType.Int,4),
					new SqlParameter("@CollectedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@CollectedAt", SqlDbType.DateTime),
					new SqlParameter("@ReceivedById", SqlDbType.Int,4),
					new SqlParameter("@ReceivedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@ReceivedAt", SqlDbType.DateTime),
					new SqlParameter("@SpecCondId", SqlDbType.Int,4),
					new SqlParameter("@RejectReason", SqlDbType.NVarChar,200),
					new SqlParameter("@Remarks", SqlDbType.NVarChar,500),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@CreatedById", SqlDbType.Int,4),
					new SqlParameter("@CreatedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedById", SqlDbType.Int,4),
					new SqlParameter("@UpdatedByName", SqlDbType.NVarChar,100),
					new SqlParameter("@SpecimenId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.OrderId;
			parameters[1].Value = model.Barcode;
			parameters[2].Value = model.SeqPrefix;
			parameters[3].Value = model.DailySeqNo;
			parameters[4].Value = model.SpecimenDate;
			parameters[5].Value = model.SampleTypeId;
			parameters[6].Value = model.SpecimenStatus;
			parameters[7].Value = Common.Tools.IsValueNull(model.CollectedById);
			parameters[8].Value = Common.Tools.IsValueNull(model.CollectedByName);
			parameters[9].Value = Common.Tools.IsValueNull(model.CollectedAt);
			parameters[10].Value = Common.Tools.IsValueNull(model.ReceivedById);
			parameters[11].Value = Common.Tools.IsValueNull(model.ReceivedByName);
			parameters[12].Value = Common.Tools.IsValueNull(model.ReceivedAt);
			parameters[13].Value = model.SpecCondId;
			parameters[14].Value = Common.Tools.IsValueNull(model.RejectReason);
			parameters[15].Value = Common.Tools.IsValueNull(model.Remarks);
			parameters[16].Value = model.CreatedAt;
			parameters[17].Value = model.CreatedById;
			parameters[18].Value = model.CreatedByName;
			parameters[19].Value = model.UpdatedAt;
			parameters[20].Value = model.UpdatedById;
			parameters[21].Value = model.UpdatedByName;
			parameters[22].Value = model.SpecimenId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long SpecimenId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabSpecimen ");
			strSql.Append(" where SpecimenId=@SpecimenId");
			SqlParameter[] parameters = {
					new SqlParameter("@SpecimenId", SqlDbType.BigInt)
			};
			parameters[0].Value = SpecimenId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string SpecimenIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from LabSpecimen ");
			strSql.Append(" where SpecimenId in (" + SpecimenIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabSpecimen GetModel(long SpecimenId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 SpecimenId,OrderId,Barcode,SeqPrefix,DailySeqNo,SpecimenDate,SampleTypeId,SpecimenStatus,CollectedById,CollectedByName,CollectedAt,ReceivedById,ReceivedByName,ReceivedAt,SpecCondId,RejectReason,Remarks,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName from LabSpecimen ");
			strSql.Append(" where SpecimenId=@SpecimenId");
			SqlParameter[] parameters = {
					new SqlParameter("@SpecimenId", SqlDbType.BigInt)
			};
			parameters[0].Value = SpecimenId;

			Model.MdlLabSpecimen model = new Model.MdlLabSpecimen();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabSpecimen DataRowToModel(DataRow row)
		{
			Model.MdlLabSpecimen model = new Model.MdlLabSpecimen();
			if (row != null)
			{
				if (row["SpecimenId"] != null && row["SpecimenId"].ToString() != "")
				{
					model.SpecimenId = long.Parse(row["SpecimenId"].ToString());
				}
				if (row["OrderId"] != null && row["OrderId"].ToString() != "")
				{
					model.OrderId = long.Parse(row["OrderId"].ToString());
				}
				if (row["Barcode"] != null)
				{
					model.Barcode = row["Barcode"].ToString();
				}
				if (row["SeqPrefix"] != null)
				{
					model.SeqPrefix = row["SeqPrefix"].ToString();
				}
				if (row["DailySeqNo"] != null && row["DailySeqNo"].ToString() != "")
				{
					model.DailySeqNo = int.Parse(row["DailySeqNo"].ToString());
				}
				if (row["SpecimenDate"] != null && row["SpecimenDate"].ToString() != "")
				{
					model.SpecimenDate = DateTime.Parse(row["SpecimenDate"].ToString());
				}
				if (row["SampleTypeId"] != null && row["SampleTypeId"].ToString() != "")
				{
					model.SampleTypeId = int.Parse(row["SampleTypeId"].ToString());
				}
				if (row["SpecimenStatus"] != null && row["SpecimenStatus"].ToString() != "")
				{
					model.SpecimenStatus = int.Parse(row["SpecimenStatus"].ToString());
				}
				if (row["CollectedById"] != null && row["CollectedById"].ToString() != "")
				{
					model.CollectedById = int.Parse(row["CollectedById"].ToString());
				}
				if (row["CollectedByName"] != null)
				{
					model.CollectedByName = row["CollectedByName"].ToString();
				}
				if (row["CollectedAt"] != null && row["CollectedAt"].ToString() != "")
				{
					model.CollectedAt = DateTime.Parse(row["CollectedAt"].ToString());
				}
				if (row["ReceivedById"] != null && row["ReceivedById"].ToString() != "")
				{
					model.ReceivedById = int.Parse(row["ReceivedById"].ToString());
				}
				if (row["ReceivedByName"] != null)
				{
					model.ReceivedByName = row["ReceivedByName"].ToString();
				}
				if (row["ReceivedAt"] != null && row["ReceivedAt"].ToString() != "")
				{
					model.ReceivedAt = DateTime.Parse(row["ReceivedAt"].ToString());
				}
				if (row["SpecCondId"] != null && row["SpecCondId"].ToString() != "")
				{
					model.SpecCondId = int.Parse(row["SpecCondId"].ToString());
				}
				if (row["RejectReason"] != null)
				{
					model.RejectReason = row["RejectReason"].ToString();
				}
				if (row["Remarks"] != null)
				{
					model.Remarks = row["Remarks"].ToString();
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["CreatedById"] != null && row["CreatedById"].ToString() != "")
				{
					model.CreatedById = int.Parse(row["CreatedById"].ToString());
				}
				if (row["CreatedByName"] != null)
				{
					model.CreatedByName = row["CreatedByName"].ToString();
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
				if (row["UpdatedById"] != null && row["UpdatedById"].ToString() != "")
				{
					model.UpdatedById = int.Parse(row["UpdatedById"].ToString());
				}
				if (row["UpdatedByName"] != null)
				{
					model.UpdatedByName = row["UpdatedByName"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select SpecimenId,OrderId,Barcode,SeqPrefix,DailySeqNo,SpecimenDate,SampleTypeId,SpecimenStatus,CollectedById,CollectedByName,CollectedAt,ReceivedById,ReceivedByName,ReceivedAt,SpecCondId,RejectReason,Remarks,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName ");
			strSql.Append(" FROM LabSpecimen ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" SpecimenId,OrderId,Barcode,SeqPrefix,DailySeqNo,SpecimenDate,SampleTypeId,SpecimenStatus,CollectedById,CollectedByName,CollectedAt,ReceivedById,ReceivedByName,ReceivedAt,SpecCondId,RejectReason,Remarks,CreatedAt,CreatedById,CreatedByName,UpdatedAt,UpdatedById,UpdatedByName ");
			strSql.Append(" FROM LabSpecimen ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM LabSpecimen ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.SpecimenId desc");
			}
			strSql.Append(")AS Row, T.*  from LabSpecimen T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LabSpecimen";
			parameters[1].Value = "SpecimenId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 根据SeqPrefix和SpecimenDate获取最大的DailySeqNo
		/// </summary>
		/// <param name="seqPrefix">序列前缀</param>
		/// <param name="specimenDate">标本日期</param>
		/// <returns>最大的DailySeqNo，如果没有记录则返回0</returns>
		public int GetMaxDailySeqNo(string seqPrefix, DateTime specimenDate)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ISNULL(MAX(DailySeqNo), 0) FROM LabSpecimen ");
			strSql.Append("WHERE SpecimenDate = @SpecimenDate ");

			SqlParameter[] parameters;
			if (string.IsNullOrEmpty(seqPrefix))
			{
				strSql.Append("AND (SeqPrefix IS NULL OR SeqPrefix = '')");
				parameters = new SqlParameter[] {
					new SqlParameter("@SpecimenDate", SqlDbType.Date)
				};
				parameters[0].Value = specimenDate.Date;
			}
			else
			{
				strSql.Append("AND SeqPrefix = @SeqPrefix");
				parameters = new SqlParameter[] {
					new SqlParameter("@SpecimenDate", SqlDbType.Date),
					new SqlParameter("@SeqPrefix", SqlDbType.NVarChar, 20)
				};
				parameters[0].Value = specimenDate.Date;
				parameters[1].Value = seqPrefix;
			}

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

		/// <summary>
		/// 获取LabSpecimen与LabOrder关联的数据列表
		/// </summary>
		/// <param name="strWhere">查询条件</param>
		/// <returns>关联查询结果</returns>
		public DataSet GetListWithLabOrder(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT s.SpecimenId, s.OrderId, s.Barcode, s.SeqPrefix, s.DailySeqNo, s.SpecimenDate, ");
			strSql.Append("s.SampleTypeId, s.SpecimenStatus, s.CollectedAt, s.ReceivedAt, s.SpecCondId, s.Remarks, ");
			strSql.Append("o.PatientName, o.PatientSex, o.PatientAge, o.PatientAgeUnit, o.BedNo, ");
			strSql.Append("o.VisitTypeId, o.RequestingDeptId, o.RequestingDoctorId, o.OrderDateTime, ");
			strSql.Append("st.SampleTypeName, ");
			strSql.Append("d.DeptName, ");
			strSql.Append("dr.DoctorName,s.SeqPrefix+s.DailySeqNo as SampleNo, ");
			strSql.Append("CASE s.SpecimenStatus ");
			strSql.Append("  WHEN 10 THEN '待采集' ");
			strSql.Append("  WHEN 20 THEN '已采集' ");
			strSql.Append("  WHEN 30 THEN '已接收' ");
			strSql.Append("  WHEN 40 THEN '已完成' ");
			strSql.Append("  WHEN 90 THEN '已废弃' ");
			strSql.Append("  WHEN 99 THEN '已拒收' ");
			strSql.Append("  ELSE '未知' END AS SpecimenStatusName ");
			strSql.Append("FROM LabSpecimen s ");
			strSql.Append("INNER JOIN LabOrder o ON s.OrderId = o.OrderId ");
			strSql.Append("INNER JOIN DictSampleType st ON s.SampleTypeId = st.SampleTypeId ");
			strSql.Append("INNER JOIN DictDepartment d ON o.RequestingDeptId = d.DeptId ");
			strSql.Append("INNER JOIN DictDoctor dr ON o.RequestingDoctorId = dr.DoctorId ");

			if (!string.IsNullOrEmpty(strWhere) && strWhere.Trim() != "")
			{
				strSql.Append(" WHERE " + strWhere);
			}

			strSql.Append(" ORDER BY s.SpecimenDate DESC, s.DailySeqNo DESC");

			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		#endregion  ExtensionMethod
	}
}

