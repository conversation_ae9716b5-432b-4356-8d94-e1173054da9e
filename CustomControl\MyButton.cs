﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;

using System.Windows.Forms;

namespace CustomControl
{
    [DefaultEvent("Click")]
    public partial class MyButton : UserControl, IButtonControl
    {
        public MyButton()
        {
            InitializeComponent();
        }

        #region 私有属性
        private void c1Button1_Click(object sender, EventArgs e)
        {
            OnClick(e);
        }

        #endregion

        #region 公开属性
        [Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public override string Text
        {
            get { return c1Button1.Text; }
            set
            {
                c1Button1.Text = value;
                if (c1Button1.Text.Length >= 2)
                {
                    if (value.Contains("保存") || value.Contains("确定"))
                    {
                        c1Button1.ImageIndex = 0;
                    }
                    else if (value.Contains("取消") || value.Contains("撤销"))
                    {
                        c1Button1.ImageIndex = 1;
                    }
                    else if (value.Contains("查询") || value.Contains("速查"))
                    {
                        c1Button1.ImageIndex = 2;
                    }
                    else if (value.Contains("删除"))
                    {
                        c1Button1.ImageIndex = 3;
                    }
                    else if (value.Contains("导出"))
                    {
                        c1Button1.ImageIndex = 4;
                    }
                    else if (value.Contains("打印") || value.Contains("打本") || value.Contains("重打"))
                    {
                        c1Button1.ImageIndex = 5;
                    }
                    else if (value.Contains("关闭") || value.Contains("归档"))
                    {
                        c1Button1.ImageKey = "关闭";
                    }
                    else if (value.Contains("复制"))
                    {
                        c1Button1.ImageIndex = 7;
                    }
                    else if (value.Contains("清空"))
                    {
                        c1Button1.ImageIndex = 8;
                    }
                    else if (value.Contains("刷新"))
                    {
                        c1Button1.ImageIndex = 9;
                    }
                    else if (value.Contains("上页") || value.Contains("前"))
                    {
                        c1Button1.ImageIndex = 11;
                    }
                    else if (value.Contains("下页") || value.Contains("后"))
                    {
                        c1Button1.ImageIndex = 10;
                    }
                    else if (value.Contains("结束") || value.Contains("完成"))
                    {
                        c1Button1.ImageIndex = 12;
                    }
                    else if (value.Contains("新增") || value.Contains("新建"))
                    {
                        c1Button1.ImageIndex = 13;
                    }
                    else if (value.Contains("反选"))
                    {
                        c1Button1.ImageKey = "反选";
                    }
                    else if (value.Contains("增加"))
                    {
                        c1Button1.ImageIndex = 15;
                    }
                    else if (value.Contains("作废"))
                    {
                        c1Button1.ImageIndex = 16;
                    }
                    else if (value.Contains("全选"))
                    {
                        c1Button1.ImageKey = "全选";
                    }
                    else if (value.Contains("重置"))
                    {
                        c1Button1.ImageIndex = 18;
                    }
                    else if (value.Contains("明细"))
                    {
                        c1Button1.ImageIndex = 19;
                    }
                    else if (value.Contains("拒绝") || value.Contains("拒收") || value.Contains("停止"))
                    {
                        c1Button1.ImageIndex = 20;
                    }
                    else if (value.Contains("通过") || value.Contains("接收") || value.Contains("确认"))
                    {
                        c1Button1.ImageIndex = 21;
                    }
                    else if (value.Contains("修改"))
                    {
                        c1Button1.ImageIndex = 22;
                    }
                    else if (value.Contains("读取"))
                    {
                        c1Button1.ImageIndex = 23;
                    }
                    else if (value.Contains("退出"))
                    {
                        c1Button1.ImageIndex = 24;
                    }
                    else if (value.Contains("添加"))
                    {
                        c1Button1.ImageIndex = 25;
                    }
                    else if (value.Contains("插入"))
                    {
                        c1Button1.ImageKey = "插入";
                    }
                    else if (value.Contains("新单"))
                    {
                        c1Button1.ImageKey = "新单";
                    }
                    else if (value.Contains("结算") || value.Contains("缴费"))
                    {
                        c1Button1.ImageKey = "收银";
                    }
                    else if (value.Contains("上传"))
                    {
                        c1Button1.ImageKey = "上传";
                    }
                    else if (value.Contains("转移"))
                    {
                        c1Button1.ImageKey = "转移";
                    }
                    else if (value.Contains("一并给药"))
                    {
                        c1Button1.ImageKey = "一并给药";
                    }
                    else if (value.Contains("登记"))
                    {
                        c1Button1.ImageKey = "入院登记";
                    }
                }

                Invalidate();
            }
        }

        public enum imageSize
        {
            small,
            large,
            huge
        }
        [Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public new Object Tag
        {
            get { return c1Button1.Tag; }
            set
            {
                c1Button1.Tag = value;
                Invalidate();
            }

        }

        public DialogResult DialogResult
        {
            get { return c1Button1.DialogResult; }
            set
            {
                if (Enum.IsDefined(typeof(DialogResult), value))
                {
                    c1Button1.DialogResult = value;
                    Invalidate();
                }
            }
        }
        private bool IsDefault { get; set; }
        // Add implementation to the IButtonControl.NotifyDefault method.
        public void NotifyDefault(bool value)
        {
            if (this.IsDefault != value)
            {
                this.IsDefault = value;
            }
        }

        // Add implementation to the IButtonControl.PerformClick method.
        public void PerformClick()
        {
            if (this.CanSelect)
            {
                this.OnClick(EventArgs.Empty);
            }
        }

        private imageSize _imageSize = imageSize.large;
        [Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public imageSize ButtonImageSize
        {
            get { return _imageSize; }
            set
            {
                _imageSize = value;
                switch (_imageSize)
                {
                    case imageSize.small:
                        c1Button1.ImageList = imageList1;
                        break;
                    case imageSize.large:
                        c1Button1.ImageList = imageList2;
                        break;
                    case imageSize.huge:
                        c1Button1.ImageList = imageList3;
                        break;
                }
            }
        }




        #endregion

        #region 公开方法
        public new void Select()
        {
            c1Button1.Select();
        }
        #endregion

    }

}