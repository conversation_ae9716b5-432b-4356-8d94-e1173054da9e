﻿namespace CustomControl
{
    partial class MyButton
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MyButton));
            this.c1Button1 = new C1.Win.C1Input.C1Button();
            this.imageList3 = new System.Windows.Forms.ImageList(this.components);
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.imageList2 = new System.Windows.Forms.ImageList(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.c1Button1)).BeginInit();
            this.SuspendLayout();
            // 
            // c1Button1
            // 
            this.c1Button1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.c1Button1.ImageList = this.imageList3;
            this.c1Button1.Location = new System.Drawing.Point(0, 0);
            this.c1Button1.Margin = new System.Windows.Forms.Padding(0);
            this.c1Button1.Name = "c1Button1";
            this.c1Button1.Size = new System.Drawing.Size(70, 35);
            this.c1Button1.TabIndex = 0;
            this.c1Button1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.c1Button1.UseVisualStyleBackColor = true;
            this.c1Button1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
            this.c1Button1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
            this.c1Button1.Click += new System.EventHandler(this.c1Button1_Click);
            // 
            // imageList3
            // 
            this.imageList3.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList3.ImageStream")));
            this.imageList3.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList3.Images.SetKeyName(0, "保存");
            this.imageList3.Images.SetKeyName(1, "取消");
            this.imageList3.Images.SetKeyName(2, "查询");
            this.imageList3.Images.SetKeyName(3, "删除");
            this.imageList3.Images.SetKeyName(4, "导出");
            this.imageList3.Images.SetKeyName(5, "打印");
            this.imageList3.Images.SetKeyName(6, "关闭");
            this.imageList3.Images.SetKeyName(7, "复制");
            this.imageList3.Images.SetKeyName(8, "清空");
            this.imageList3.Images.SetKeyName(9, "刷新");
            this.imageList3.Images.SetKeyName(10, "上页");
            this.imageList3.Images.SetKeyName(11, "下页");
            this.imageList3.Images.SetKeyName(12, "结束");
            this.imageList3.Images.SetKeyName(13, "新增");
            this.imageList3.Images.SetKeyName(14, "反选");
            this.imageList3.Images.SetKeyName(15, "增加");
            this.imageList3.Images.SetKeyName(16, "作废");
            this.imageList3.Images.SetKeyName(17, "全选");
            this.imageList3.Images.SetKeyName(18, "重置");
            this.imageList3.Images.SetKeyName(19, "明细");
            this.imageList3.Images.SetKeyName(20, "拒绝");
            this.imageList3.Images.SetKeyName(21, "通过");
            this.imageList3.Images.SetKeyName(22, "修改");
            this.imageList3.Images.SetKeyName(23, "读取");
            this.imageList3.Images.SetKeyName(24, "退出");
            this.imageList3.Images.SetKeyName(25, "添加");
            this.imageList3.Images.SetKeyName(26, "插入");
            this.imageList3.Images.SetKeyName(27, "新单");
            this.imageList3.Images.SetKeyName(28, "收银");
            this.imageList3.Images.SetKeyName(29, "上传");
            this.imageList3.Images.SetKeyName(30, "转移");
            this.imageList3.Images.SetKeyName(31, "一并给药");
            this.imageList3.Images.SetKeyName(32, "入院登记");
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "保存");
            this.imageList1.Images.SetKeyName(1, "取消");
            this.imageList1.Images.SetKeyName(2, "查询");
            this.imageList1.Images.SetKeyName(3, "删除");
            this.imageList1.Images.SetKeyName(4, "导出");
            this.imageList1.Images.SetKeyName(5, "打印");
            this.imageList1.Images.SetKeyName(6, "关闭");
            this.imageList1.Images.SetKeyName(7, "复制");
            this.imageList1.Images.SetKeyName(8, "清空");
            this.imageList1.Images.SetKeyName(9, "刷新");
            this.imageList1.Images.SetKeyName(10, "上页");
            this.imageList1.Images.SetKeyName(11, "下页");
            this.imageList1.Images.SetKeyName(12, "结束");
            this.imageList1.Images.SetKeyName(13, "新增");
            this.imageList1.Images.SetKeyName(14, "反选");
            this.imageList1.Images.SetKeyName(15, "增加");
            this.imageList1.Images.SetKeyName(16, "作废");
            this.imageList1.Images.SetKeyName(17, "全选");
            this.imageList1.Images.SetKeyName(18, "重置");
            this.imageList1.Images.SetKeyName(19, "明细");
            this.imageList1.Images.SetKeyName(20, "拒绝");
            this.imageList1.Images.SetKeyName(21, "通过");
            this.imageList1.Images.SetKeyName(22, "修改");
            this.imageList1.Images.SetKeyName(23, "读取");
            this.imageList1.Images.SetKeyName(24, "退出");
            this.imageList1.Images.SetKeyName(25, "添加");
            this.imageList1.Images.SetKeyName(26, "插入");
            this.imageList1.Images.SetKeyName(27, "新单");
            this.imageList1.Images.SetKeyName(28, "收银");
            this.imageList1.Images.SetKeyName(29, "上传");
            this.imageList1.Images.SetKeyName(30, "转移");
            this.imageList1.Images.SetKeyName(31, "一并给药");
            this.imageList1.Images.SetKeyName(32, "入院登记");
            // 
            // imageList2
            // 
            this.imageList2.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList2.ImageStream")));
            this.imageList2.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList2.Images.SetKeyName(0, "保存");
            this.imageList2.Images.SetKeyName(1, "取消");
            this.imageList2.Images.SetKeyName(2, "查询");
            this.imageList2.Images.SetKeyName(3, "删除");
            this.imageList2.Images.SetKeyName(4, "导出");
            this.imageList2.Images.SetKeyName(5, "打印");
            this.imageList2.Images.SetKeyName(6, "关闭");
            this.imageList2.Images.SetKeyName(7, "复制");
            this.imageList2.Images.SetKeyName(8, "清空");
            this.imageList2.Images.SetKeyName(9, "刷新");
            this.imageList2.Images.SetKeyName(10, "上页");
            this.imageList2.Images.SetKeyName(11, "下页");
            this.imageList2.Images.SetKeyName(12, "结束");
            this.imageList2.Images.SetKeyName(13, "新增");
            this.imageList2.Images.SetKeyName(14, "反选");
            this.imageList2.Images.SetKeyName(15, "增加");
            this.imageList2.Images.SetKeyName(16, "作废");
            this.imageList2.Images.SetKeyName(17, "全选");
            this.imageList2.Images.SetKeyName(18, "重置");
            this.imageList2.Images.SetKeyName(19, "明细");
            this.imageList2.Images.SetKeyName(20, "拒绝");
            this.imageList2.Images.SetKeyName(21, "通过");
            this.imageList2.Images.SetKeyName(22, "修改");
            this.imageList2.Images.SetKeyName(23, "读取");
            this.imageList2.Images.SetKeyName(24, "退出");
            this.imageList2.Images.SetKeyName(25, "添加");
            this.imageList2.Images.SetKeyName(26, "插入");
            this.imageList2.Images.SetKeyName(27, "新单");
            this.imageList2.Images.SetKeyName(28, "收银");
            this.imageList2.Images.SetKeyName(29, "上传");
            this.imageList2.Images.SetKeyName(30, "转移");
            this.imageList2.Images.SetKeyName(31, "一并给药");
            this.imageList2.Images.SetKeyName(32, "入院登记");
            // 
            // MyButton
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.c1Button1);
            this.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "MyButton";
            this.Size = new System.Drawing.Size(70, 35);
            ((System.ComponentModel.ISupportInitialize)(this.c1Button1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private C1.Win.C1Input.C1Button c1Button1;
        private System.Windows.Forms.ImageList imageList1;
        private System.Windows.Forms.ImageList imageList2;
        private System.Windows.Forms.ImageList imageList3;
    }
}
