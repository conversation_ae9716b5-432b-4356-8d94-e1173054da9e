﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Win32;
using System.Diagnostics;
namespace Common
{
    /// <summary>
    /// 注册表基项静态域
    /// 
    /// 主要包括：
    /// 1.Registry.ClassesRoot     对应于HKEY_CLASSES_ROOT主键
    /// 2.Registry.CurrentUser     对应于HKEY_CURRENT_USER主键
    /// 3.Registry.LocalMachine    对应于 HKEY_LOCAL_MACHINE主键
    /// 4.Registry.User            对应于 HKEY_USER主键
    /// 5.Registry.CurrentConfig   对应于HEKY_CURRENT_CONFIG主键
    /// 6.Registry.DynDa           对应于HKEY_DYN_DATA主键
    /// 7.Registry.PerformanceData 对应于HKEY_PERFORMANCE_DATA主键
    /// 
    /// 版本:1.0
    /// </summary>
    public enum RegDomain
    {
        /// <summary>
        /// 对应于HKEY_CLASSES_ROOT主键
        /// </summary>
        HKEY_CLASSES_ROOT = 0,
        /// <summary>
        /// 对应于HKEY_CURRENT_USER主键
        /// </summary>
        HKEY_CURRENT_USER = 1,
        /// <summary>
        /// 对应于 HKEY_LOCAL_MACHINE主键
        /// </summary>
        HKEY_LOCAL_MACHINE = 2,
        /// <summary>
        /// 对应于 HKEY_USER主键
        /// </summary>
        HKEY_USER = 3,
        /// <summary>
        /// 对应于HEKY_CURRENT_CONFIG主键
        /// </summary>
        HEKY_CURRENT_CONFIG = 4,
        /// <summary>
        /// 对应于HKEY_DYN_DATA主键
        /// </summary>
        HKEY_DYN_DATA = 5,
        /// <summary>
        /// 对应于HKEY_PERFORMANCE_DATA主键
        /// </summary>
        HKEY_PERFORMANCE_DATA = 6,
    }
    //2.指定在注册表中存储值时所用的数据类型，或标识注册表中某个值的数据类型
    /// <summary>
    /// 指定在注册表中存储值时所用的数据类型，或标识注册表中某个值的数据类型
    /// 
    /// 主要包括：
    /// 1.RegistryValueKind.Unknown
    /// 2.RegistryValueKind.String
    /// 3.RegistryValueKind.ExpandString
    /// 4.RegistryValueKind.Binary
    /// 5.RegistryValueKind.DWord
    /// 6.RegistryValueKind.MultiString
    /// 7.RegistryValueKind.QWord
    /// 
    /// 版本:1.0
    /// </summary>
    public enum RegValueKind
    {
        /// <summary>
        /// 指示一个不受支持的注册表数据类型。例如，不支持 Microsoft Win32 API 注册表数据类型 REG_RESOURCE_LIST。使用此值指定
        /// </summary>
        Unknown = 0,
        /// <summary>
        /// 指定一个以 Null 结尾的字符串。此值与 Win32 API 注册表数据类型 REG_SZ 等效。
        /// </summary>
        String = 1,
        /// <summary>
        /// 指定一个以 NULL 结尾的字符串，该字符串中包含对环境变量（如 %PATH%，当值被检索时，就会展开）的未展开的引用。
        /// 此值与 Win32 API注册表数据类型 REG_EXPAND_SZ 等效。
        /// </summary>
        ExpandString = 2,
        /// <summary>
        /// 指定任意格式的二进制数据。此值与 Win32 API 注册表数据类型 REG_BINARY 等效。
        /// </summary>
        Binary = 3,
        /// <summary>
        /// 指定一个 32 位二进制数。此值与 Win32 API 注册表数据类型 REG_DWORD 等效。
        /// </summary>
        DWord = 4,
        /// <summary>
        /// 指定一个以 NULL 结尾的字符串数组，以两个空字符结束。此值与 Win32 API 注册表数据类型 REG_MULTI_SZ 等效。
        /// </summary>
        MultiString = 5,
        /// <summary>
        /// 指定一个 64 位二进制数。此值与 Win32 API 注册表数据类型 REG_QWORD 等效。
        /// </summary>
        QWord = 6,
    }
    public struct RegisterItem
    {
        public RegDomain Domain;
        public string Subkey;
        public string ValueKey;
        public string RealValue;//实际值
        public string ExpectedValue;//期望值
        public RegValueKind ValueKind;
        public string InnerCode;
        public string ExcelVer;
        public RegisterItem(RegDomain _domain, string _subKey, string _valueKey
            , string _realValue, string _expectedValue, RegValueKind _kind, string _innerCode)
        {
            Domain = _domain;
            Subkey = _subKey;
            ValueKey = _valueKey;
            RealValue = _realValue;
            ExpectedValue = _expectedValue;
            ValueKind = _kind;
            InnerCode = _innerCode;
            ExcelVer = string.Empty;
        }
        public RegisterItem Copy()
        {
            RegisterItem item = new RegisterItem();
            item.Domain = Domain;
            item.Subkey = Subkey;
            item.ValueKey = ValueKey;
            item.RealValue = RealValue;
            item.ExpectedValue = ExpectedValue;
            item.ValueKind = ValueKind;
            item.InnerCode = InnerCode;
            return item;
        }
    }
    // 3.注册表操作类
    /// <summary>
    /// 注册表操作类
    /// 
    /// 主要包括以下操作：
    /// 1.创建注册表项
    /// 2.读取注册表项
    /// 3.判断注册表项是否存在
    /// 4.删除注册表项
    /// 5.创建注册表键值
    /// 6.读取注册表键值
    /// 7.判断注册表键值是否存在
    /// 8.删除注册表键值
    /// 
    /// 版本:1.0
    /// </summary>
    public class Register
    {
        #region 字段定义
        /// <summary>
        /// 注册表项名称
        /// </summary>
        private string _subkey;
        /// <summary>
        /// 注册表基项域
        /// </summary>
        private RegDomain _domain;
        /// <summary>
        /// 注册表键值
        /// </summary>
        private string _regeditkey;
        #endregion
        #region 属性
        /// <summary>
        /// 设置注册表项名称
        /// </summary>
        public string SubKey
        {
            //get { return _subkey; }
            set { _subkey = value; }
        }
        /// <summary>
        /// 注册表基项域
        /// </summary>
        public RegDomain Domain
        {
            ///get { return _domain; }
            set { _domain = value; }
        }
        /// <summary>
        /// 注册表键值
        /// </summary>
        public string RegeditKey
        {
            ///get{return _regeditkey;}
            set { _regeditkey = value; }
        }
        #endregion
        #region 构造函数
        public Register()
        {
            ///默认注册表项名称
            _subkey = "software\\";
            ///默认注册表基项域
            _domain = RegDomain.HKEY_CURRENT_USER;
        }
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        public Register(string subKey, RegDomain regDomain)
        {
            ///设置注册表项名称
            _subkey = subKey;
            ///设置注册表基项域
            _domain = regDomain;
        }
        #endregion
        #region 公有方法
        #region 创建注册表项
        /// <summary>
        /// 创建注册表项，默认创建在注册表基项 HKEY_LOCAL_MACHINE下面（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// </summary>
        public virtual void CreateSubKey()
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (_subkey == string.Empty || _subkey == null)
            {
                return;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            ///要创建的注册表项的节点
            RegistryKey sKey;
            if (!IsSubKeyExist())
            {
                sKey = key.CreateSubKey(_subkey);
            }
            //sKey.Close();
            ///关闭对注册表项的更改
            key.Close();
        }
        /// <summary>
        /// 创建注册表项，默认创建在注册表基项 HKEY_LOCAL_MACHINE下面
        /// 虚方法，子类可进行重写
        /// 例子：如subkey是software\\higame\\，则将创建HKEY_LOCAL_MACHINE\\software\\higame\\注册表项
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        public virtual void CreateSubKey(string subKey)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            ///要创建的注册表项的节点
            RegistryKey sKey;
            if (!IsSubKeyExist(subKey))
            {
                sKey = key.CreateSubKey(subKey);
            }
            //sKey.Close();
            ///关闭对注册表项的更改
            key.Close();
        }
        /// <summary>
        /// 创建注册表项，默认创建在注册表基项 HKEY_LOCAL_MACHINE下面
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="regDomain">注册表基项域</param>
        public virtual void CreateSubKey(RegDomain regDomain)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (_subkey == string.Empty || _subkey == null)
            {
                return;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(regDomain);
            ///要创建的注册表项的节点
            RegistryKey sKey;
            if (!IsSubKeyExist(regDomain))
            {
                sKey = key.CreateSubKey(_subkey);
            }
            //sKey.Close();
            ///关闭对注册表项的更改
            key.Close();
        }
        /// <summary>
        /// 创建注册表项（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// 例子：如regDomain是HKEY_LOCAL_MACHINE，subkey是software\\higame\\，则将创建HKEY_LOCAL_MACHINE\\software\\higame\\注册表项
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        public virtual void CreateSubKey(string subKey, RegDomain regDomain)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(regDomain);
            ///要创建的注册表项的节点
            RegistryKey sKey;
            if (!IsSubKeyExist(subKey, regDomain))
            {
                sKey = key.CreateSubKey(subKey);
            }
            //sKey.Close();
            ///关闭对注册表项的更改
            key.Close();
        }
        #endregion
        public virtual string[] GetSubName(string subKey)
        {
            RegistryKey key = GetRegDomain(_domain);
            if (string.IsNullOrEmpty(subKey))
            {
                return key.GetSubKeyNames();
            }
            RegistryKey sKey = key.OpenSubKey(subKey);
            if (sKey == null)
            {
                return null;
            }
            return sKey.GetSubKeyNames();
        }
        /// <summary>
        /// 获得子键的名字
        /// </summary>
        /// <param name="subKey"></param>
        /// <returns></returns>
        public virtual string[] GetSubValueName(string subKey)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return null;
            }
            ///检索注册表子项
            ///如果sKey为null,说明没有该注册表项不存在，否则存在
            RegistryKey sKey = OpenSubKey(subKey);
            if (sKey == null)
            {
                return null;
            }
            return sKey.GetValueNames();
        }
        #region 判断注册表项是否存在
        /// <summary>
        /// 判断注册表项是否存在，默认是在注册表基项HKEY_LOCAL_MACHINE下判断（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// 例子：如果设置了Domain和SubKey属性，则判断Domain\\SubKey，否则默认判断HKEY_LOCAL_MACHINE\\software\\
        /// </summary>
        /// <returns>返回注册表项是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsSubKeyExist()
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (_subkey == string.Empty || _subkey == null)
            {
                return false;
            }
            ///检索注册表子项
            ///如果sKey为null,说明没有该注册表项不存在，否则存在
            RegistryKey sKey = OpenSubKey(_subkey, _domain);
            if (sKey == null)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 判断注册表项是否存在，默认是在注册表基项HKEY_LOCAL_MACHINE下判断
        /// 虚方法，子类可进行重写
        /// 例子：如subkey是software\\higame\\，则将判断HKEY_LOCAL_MACHINE\\software\\higame\\注册表项是否存在
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <returns>返回注册表项是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsSubKeyExist(string subKey)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return false;
            }
            ///检索注册表子项
            ///如果sKey为null,说明没有该注册表项不存在，否则存在
            RegistryKey sKey = OpenSubKey(subKey);
            if (sKey == null)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 判断注册表项是否存在
        /// 虚方法，子类可进行重写
        /// 例子：如regDomain是HKEY_CLASSES_ROOT，则将判断HKEY_CLASSES_ROOT\\SubKey注册表项是否存在
        /// </summary>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>返回注册表项是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsSubKeyExist(RegDomain regDomain)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (_subkey == string.Empty || _subkey == null)
            {
                return false;
            }
            ///检索注册表子项
            ///如果sKey为null,说明没有该注册表项不存在，否则存在
            RegistryKey sKey = OpenSubKey(_subkey, regDomain);
            if (sKey == null)
            {
                return false;
            }
            return true;
        }
        /// <summary>
        /// 判断注册表项是否存在（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// 例子：如regDomain是HKEY_CLASSES_ROOT，subkey是software\\higame\\，则将判断HKEY_CLASSES_ROOT\\software\\higame\\注册表项是否存在
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>返回注册表项是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsSubKeyExist(string subKey, RegDomain regDomain)
        {
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return false;
            }
            ///检索注册表子项
            ///如果sKey为null,说明没有该注册表项不存在，否则存在
            RegistryKey sKey = OpenSubKey(subKey, regDomain);
            if (sKey == null)
            {
                return false;
            }
            return true;
        }
        #endregion
        #region 删除注册表项
        /// <summary>
        /// 删除注册表项（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <returns>如果删除成功，则返回true，否则为false</returns>
        public virtual bool DeleteSubKey()
        {
            ///返回删除是否成功
            bool result = false;
            ///判断注册表项名称是否为空，如果为空，返回false
            if (_subkey == string.Empty || _subkey == null)
            {
                return false;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            if (IsSubKeyExist())
            {
                try
                {
                    ///删除注册表项
                    key.DeleteSubKey(_subkey);
                    result = true;
                }
                catch
                {
                    result = false;
                }
            }
            ///关闭对注册表项的更改
            key.Close();
            return result;
        }
        /// <summary>
        /// 删除注册表项（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <returns>如果删除成功，则返回true，否则为false</returns>
        public virtual bool DeleteSubKey(string subKey)
        {
            ///返回删除是否成功
            bool result = false;
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return result;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            //if (IsSubKeyExist())
            //{
            try
            {
                if (key.OpenSubKey(subKey) == null)
                {
                    result = true;
                }
                else
                {
                    key.DeleteSubKeyTree(subKey);
                    if (key.OpenSubKey(subKey) == null)
                    {
                        result = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.LogHelper.Error(ex.Message);
                Log.LogHelper.Error(ex.StackTrace);
                result = false;
            }
            //}
            ///关闭对注册表项的更改
            key.Close();
            Debug.WriteLine(result);
            return result;
        }
        /// <summary>
        /// 删除注册表项
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>如果删除成功，则返回true，否则为false</returns>
        public virtual bool DeleteSubKey(string subKey, RegDomain regDomain)
        {
            ///返回删除是否成功
            bool result = false;
            ///判断注册表项名称是否为空，如果为空，返回false
            if (subKey == string.Empty || subKey == null)
            {
                return false;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(regDomain);
            if (IsSubKeyExist(subKey, regDomain))
            {
                try
                {
                    ///删除注册表项
                    key.DeleteSubKey(subKey);
                    result = true;
                }
                catch (Exception ex)
                {
                    Log.LogHelper.Error(ex.Message);
                    Log.LogHelper.Error(ex.StackTrace);
                    result = false;
                }
            }
            ///关闭对注册表项的更改
            key.Close();
            return result;
        }
        #endregion
        #region 判断键值是否存在
        /// <summary>
        /// 判断键值是否存在（请先设置SubKey和RegeditKey属性）
        /// 虚方法，子类可进行重写
        /// 1.如果RegeditKey为空、null，则返回false
        /// 2.如果SubKey为空、null或者SubKey指定的注册表项不存在，返回false
        /// </summary>
        /// <returns>返回键值是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsRegeditKeyExist()
        {
            ///返回结果
            bool result = false;
            ///判断是否设置键值属性
            if (_regeditkey == string.Empty || _regeditkey == null)
            {
                return false;
            }
            ///判断注册表项是否存在
            if (IsSubKeyExist())
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey();
                ///键值集合
                string[] regeditKeyNames;
                ///获取键值集合
                regeditKeyNames = key.GetValueNames();
                ///遍历键值集合，如果存在键值，则退出遍历
                foreach (string regeditKey in regeditKeyNames)
                {
                    if (string.Compare(regeditKey, _regeditkey, true) == 0)
                    {
                        result = true;
                        break;
                    }
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        /// <summary>
        /// 判断键值是否存在（请先设置SubKey属性）
        /// 虚方法，子类可进行重写
        /// 如果SubKey为空、null或者SubKey指定的注册表项不存在，返回false
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <returns>返回键值是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsRegeditKeyExist(string name)
        {
            ///返回结果
            bool result = false;
            ///判断是否设置键值属性
            if (name == string.Empty || name == null)
            {
                name = "";
            }
            ///判断注册表项是否存在
            //if (IsSubKeyExist())
            //{
            ///打开注册表项
            RegistryKey key = OpenSubKey();
            ///键值集合
            string[] regeditKeyNames;
            ///获取键值集合
            regeditKeyNames = key.GetValueNames();
            ///遍历键值集合，如果存在键值，则退出遍历
            foreach (string regeditKey in regeditKeyNames)
            {
                if (regeditKey.StartsWith(name))
                {
                    result = true;
                    break;
                }
            }
            ///关闭对注册表项的更改
            key.Close();
            //}
            return result;
        }
        /// <summary>
        /// 判断键值是否存在
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="subKey">注册表项名称</param>
        /// <returns>返回键值是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsRegeditKeyExist(string name, string subKey)
        {
            ///返回结果
            bool result = false;
            ///判断是否设置键值属性
            if (name == string.Empty || name == null)
            {
                return false;
            }
            ///判断注册表项是否存在
            if (IsSubKeyExist())
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey(subKey);
                ///键值集合
                string[] regeditKeyNames;
                ///获取键值集合
                regeditKeyNames = key.GetValueNames();
                ///遍历键值集合，如果存在键值，则退出遍历
                foreach (string regeditKey in regeditKeyNames)
                {
                    if (string.Compare(regeditKey, name, true) == 0)
                    {
                        result = true;
                        break;
                    }
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        /// <summary>
        /// 判断键值是否存在
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>返回键值是否存在，存在返回true，否则返回false</returns>
        public virtual bool IsRegeditKeyExist(string name, string subKey, RegDomain regDomain)
        {
            ///返回结果
            bool result = false;
            ///判断是否设置键值属性
            if (name == string.Empty || name == null)
            {
                return false;
            }
            ///判断注册表项是否存在
            if (IsSubKeyExist())
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey(subKey, regDomain);
                ///键值集合
                string[] regeditKeyNames;
                ///获取键值集合
                regeditKeyNames = key.GetValueNames();
                ///遍历键值集合，如果存在键值，则退出遍历
                foreach (string regeditKey in regeditKeyNames)
                {
                    if (string.Compare(regeditKey, name, true) == 0)
                    {
                        result = true;
                        break;
                    }
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        #endregion
        #region 设置键值内容
        /// <summary>
        /// 设置指定的键值内容，不指定内容数据类型（请先设置RegeditKey和SubKey属性）
        /// 存在改键值则修改键值内容，不存在键值则先创建键值，再设置键值内容
        /// </summary>
        /// <param name="content">键值内容</param>
        /// <returns>键值内容设置成功，则返回true，否则返回false</returns>
        public virtual bool WriteRegeditKey(object content)
        {
            ///返回结果
            bool result = false;
            ///判断是否设置键值属性
            if (_regeditkey == string.Empty || _regeditkey == null)
            {
                return false;
            }
            ///判断注册表项是否存在，如果不存在，则直接创建
            if (!IsSubKeyExist(_subkey))
            {
                CreateSubKey(_subkey);
            }
            ///以可写方式打开注册表项
            RegistryKey key = OpenSubKey(true);
            ///如果注册表项打开失败，则返回false
            if (key == null)
            {
                return false;
            }
            try
            {
                key.SetValue(_regeditkey, content);
                result = true;
            }
            catch
            {
                result = false;
            }
            finally
            {
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        /// <summary>
        /// 设置指定的键值内容，不指定内容数据类型（请先设置SubKey属性）
        /// 存在改键值则修改键值内容，不存在键值则先创建键值，再设置键值内容
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="content">键值内容</param>
        /// <returns>键值内容设置成功，则返回true，否则返回false</returns>
        public virtual bool WriteRegeditKey(string name, object content)
        {
            ///返回结果
            bool result = false;
            ///判断键值是否存在
            if (name == string.Empty || name == null)
            {
                return false;
            }
            ///判断注册表项是否存在，如果不存在，则直接创建
            if (!IsSubKeyExist(_subkey))
            {
                CreateSubKey(_subkey);
            }
            ///以可写方式打开注册表项
            RegistryKey key = OpenSubKey(true);
            ///如果注册表项打开失败，则返回false
            if (key == null)
            {
                return false;
            }
            try
            {
                key.SetValue(name, content);
                result = true;
            }
            catch (Exception ex)
            {
                result = false;
            }
            finally
            {
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        /// <summary>
        /// 设置指定的键值内容，指定内容数据类型（请先设置SubKey属性）
        /// 存在改键值则修改键值内容，不存在键值则先创建键值，再设置键值内容
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="content">键值内容</param>
        /// <returns>键值内容设置成功，则返回true，否则返回false</returns>
        public virtual bool WriteRegeditKey(string name, object content, RegValueKind regValueKind)
        {
            ///返回结果
            bool result = false;
            ///判断键值是否存在
            if (name == string.Empty || name == null)
            {
                name = "";
            }
            ///判断注册表项是否存在，如果不存在，则直接创建
            if (!IsSubKeyExist(_subkey))
            {
                CreateSubKey(_subkey);
            }
            ///以可写方式打开注册表项
            RegistryKey key = OpenSubKey(true);
            ///如果注册表项打开失败，则返回false
            if (key == null)
            {
                return false;
            }
            try
            {
                key.SetValue(name, content, GetRegValueKind(regValueKind));
                result = true;
            }
            catch
            {
                result = false;
            }
            finally
            {
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        #endregion
        #region 读取键值内容
        /// <summary>
        /// 读取键值内容（请先设置RegeditKey和SubKey属性）
        /// 1.如果RegeditKey为空、null或者RegeditKey指示的键值不存在，返回null
        /// 2.如果SubKey为空、null或者SubKey指示的注册表项不存在，返回null
        /// 3.反之，则返回键值内容
        /// </summary>
        /// <returns>返回键值内容</returns>
        public virtual object ReadRegeditKey()
        {
            ///键值内容结果
            object obj = null;
            ///判断是否设置键值属性
            if (_regeditkey == string.Empty || _regeditkey == null)
            {
                return null;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(_regeditkey))
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey();
                if (key != null)
                {
                    obj = key.GetValue(_regeditkey);
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return obj;
        }
        /// <summary>
        /// 读取键值内容（请先设置SubKey属性）
        /// 1.如果SubKey为空、null或者SubKey指示的注册表项不存在，返回null
        /// 2.反之，则返回键值内容
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <returns>返回键值内容</returns>
        public virtual object ReadRegeditKey(string name)
        {
            ///键值内容结果
            object obj = null;
            ///判断是否设置键值属性
            if (name == string.Empty || name == null)
            {
                name = "";
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(name))
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey();
                if (key != null)
                {
                    obj = key.GetValue(name);
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return obj;
        }
        /// <summary>
        /// 读取键值内容
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="subKey">注册表项名称</param>
        /// <returns>返回键值内容</returns>
        public virtual object ReadRegeditKey(string name, string subKey)
        {
            ///键值内容结果
            object obj = null;
            ///判断是否设置键值属性
            if (name == string.Empty || name == null)
            {
                return null;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(name))
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey(subKey);
                if (key != null)
                {
                    obj = key.GetValue(name);
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return obj;
        }
        /// <summary>
        /// 读取键值内容
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>返回键值内容</returns>
        public virtual object ReadRegeditKey(string name, string subKey, RegDomain regDomain)
        {
            ///键值内容结果
            object obj = null;
            ///判断是否设置键值属性
            if (name == string.Empty || name == null)
            {
                return null;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(name))
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey(subKey, regDomain);
                if (key != null)
                {
                    obj = key.GetValue(name);
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return obj;
        }
        #endregion
        #region 删除键值
        /// <summary>
        /// 删除键值（请先设置RegeditKey和SubKey属性）
        /// 1.如果RegeditKey为空、null或者RegeditKey指示的键值不存在，返回false
        /// 2.如果SubKey为空、null或者SubKey指示的注册表项不存在，返回false
        /// </summary>
        /// <returns>如果删除成功，返回true，否则返回false</returns>
        public virtual bool DeleteRegeditKey()
        {
            ///删除结果
            bool result = false;
            ///判断是否设置键值属性，如果没有设置，则返回false
            if (_regeditkey == string.Empty || _regeditkey == null)
            {
                return false;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(_regeditkey))
            {
                ///以可写方式打开注册表项
                RegistryKey key = OpenSubKey(true);
                if (key != null)
                {
                    try
                    {
                        ///删除键值
                        key.DeleteValue(_regeditkey);
                        result = true;
                    }
                    catch (Exception ex)
                    {
                        Log.LogHelper.Error(ex.Message);
                        Log.LogHelper.Error(ex.StackTrace);
                        result = false;
                    }
                    finally
                    {
                        ///关闭对注册表项的更改
                        key.Close();
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 删除键值（请先设置SubKey属性）
        /// 如果SubKey为空、null或者SubKey指示的注册表项不存在，返回false
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <returns>如果删除成功，返回true，否则返回false</returns>
        public virtual bool DeleteRegeditKey(string name)
        {
            ///删除结果
            bool result = false;
            ///判断键值名称是否为空，如果为空，则返回false
            if (name == string.Empty || name == null)
            {
                return false;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(name))
            {
                ///以可写方式打开注册表项
                RegistryKey key = OpenSubKey(true);
                if (key != null)
                {
                    try
                    {
                        ///删除键值
                        ///
                        //Log.WriteLine(_subkey + "\\" + name);
                        key.DeleteValue(name);
                        result = true;
                    }
                    catch (Exception ex)
                    {
                        Log.LogHelper.Error(ex.Message);
                        Log.LogHelper.Error(ex.StackTrace);
                        result = false;
                    }
                    finally
                    {
                        ///关闭对注册表项的更改
                        key.Close();
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 删除键值
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="subKey">注册表项名称</param>
        /// <returns>如果删除成功，返回true，否则返回false</returns>
        public virtual bool DeleteRegeditKey(string name, string subKey)
        {
            ///删除结果
            bool result = false;
            ///判断键值名称和注册表项名称是否为空，如果为空，则返回false
            if (name == string.Empty || name == null || subKey == string.Empty || subKey == null)
            {
                return false;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(name))
            {
                ///以可写方式打开注册表项
                RegistryKey key = OpenSubKey(subKey, true);
                if (key != null)
                {
                    try
                    {
                        ///删除键值
                        key.DeleteValue(name);
                        result = true;
                    }
                    catch (Exception ex)
                    {
                        Log.LogHelper.Error(ex.Message);
                        Log.LogHelper.Error(ex.StackTrace);
                        result = false;
                    }
                    finally
                    {
                        ///关闭对注册表项的更改
                        key.Close();
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 删除键值
        /// </summary>
        /// <param name="name">键值名称</param>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>如果删除成功，返回true，否则返回false</returns>
        public virtual bool DeleteRegeditKey(string name, string subKey, RegDomain regDomain)
        {
            ///删除结果
            bool result = false;
            ///判断键值名称和注册表项名称是否为空，如果为空，则返回false
            if (name == string.Empty || name == null || subKey == string.Empty || subKey == null)
            {
                return false;
            }
            ///判断键值是否存在
            if (IsRegeditKeyExist(name))
            {
                ///以可写方式打开注册表项
                RegistryKey key = OpenSubKey(subKey, regDomain, true);
                if (key != null)
                {
                    try
                    {
                        ///删除键值
                        key.DeleteValue(name);
                        result = true;
                    }
                    catch (Exception ex)
                    {
                        Log.LogHelper.Error(ex.Message);
                        Log.LogHelper.Error(ex.StackTrace);
                        result = false;
                    }
                    finally
                    {
                        ///关闭对注册表项的更改
                        key.Close();
                    }
                }
            }
            return result;
        }
        #endregion
        #endregion
        #region 受保护方法
        /// <summary>
        /// 获取注册表基项域对应顶级节点
        /// 例子：如regDomain是ClassesRoot，则返回Registry.ClassesRoot
        /// </summary>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>注册表基项域对应顶级节点</returns>
        protected RegistryKey GetRegDomain(RegDomain regDomain)
        {
            ///创建基于注册表基项的节点
            RegistryKey key;
            #region 判断注册表基项域
            switch (regDomain)
            {
                case RegDomain.HKEY_CLASSES_ROOT:
                    key = Registry.ClassesRoot; break;
                case RegDomain.HKEY_CURRENT_USER:
                    key = Registry.CurrentUser; break;
                case RegDomain.HKEY_LOCAL_MACHINE:
                    key = Registry.LocalMachine; break;
                case RegDomain.HKEY_USER:
                    key = Registry.Users; break;
                case RegDomain.HEKY_CURRENT_CONFIG:
                    key = Registry.CurrentConfig; break;
                case RegDomain.HKEY_DYN_DATA:
                    key = Registry.DynData; break;
                case RegDomain.HKEY_PERFORMANCE_DATA:
                    key = Registry.PerformanceData; break;
                default:
                    key = Registry.LocalMachine; break;
            }
            #endregion
            return key;
        }
        /// <summary>
        /// 获取在注册表中对应的值数据类型
        /// 例子：如regValueKind是DWord，则返回RegistryValueKind.DWord
        /// </summary>
        /// <param name="regValueKind">注册表数据类型</param>
        /// <returns>注册表中对应的数据类型</returns>
        protected RegistryValueKind GetRegValueKind(RegValueKind regValueKind)
        {
            RegistryValueKind regValueK;
            #region 判断注册表数据类型
            switch (regValueKind)
            {
                case RegValueKind.Unknown:
                    regValueK = RegistryValueKind.Unknown; break;
                case RegValueKind.String:
                    regValueK = RegistryValueKind.String; break;
                case RegValueKind.ExpandString:
                    regValueK = RegistryValueKind.ExpandString; break;
                case RegValueKind.Binary:
                    regValueK = RegistryValueKind.Binary; break;
                case RegValueKind.DWord:
                    regValueK = RegistryValueKind.DWord; break;
                case RegValueKind.MultiString:
                    regValueK = RegistryValueKind.MultiString; break;
                case RegValueKind.QWord:
                    regValueK = RegistryValueKind.QWord; break;
                default:
                    regValueK = RegistryValueKind.String; break;
            }
            #endregion
            return regValueK;
        }
        #region 打开注册表项
        /// <summary>
        /// 打开注册表项节点，以只读方式检索子项
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <returns>如果SubKey为空、null或者SubKey指示注册表项不存在，则返回null，否则返回注册表节点</returns>
        protected virtual RegistryKey OpenSubKey()
        {
            ///判断注册表项名称是否为空
            if (_subkey == string.Empty || _subkey == null)
            {
                return null;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            ///要打开的注册表项的节点
            RegistryKey sKey = null;
            ///打开注册表项
            sKey = key.OpenSubKey(_subkey);
            ///关闭对注册表项的更改
            key.Close();
            ///返回注册表节点
            return sKey;
        }
        /// <summary>
        /// 打开注册表项节点
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="writable">如果需要项的写访问权限，则设置为 true</param>
        /// <returns>如果SubKey为空、null或者SubKey指示注册表项不存在，则返回null，否则返回注册表节点</returns>
        protected virtual RegistryKey OpenSubKey(bool writable)
        {
            ///判断注册表项名称是否为空
            if (_subkey == string.Empty || _subkey == null)
            {
                return null;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            ///要打开的注册表项的节点
            RegistryKey sKey = null;
            ///打开注册表项
            sKey = key.OpenSubKey(_subkey, writable);
            ///关闭对注册表项的更改
            key.Close();
            ///返回注册表节点
            return sKey;
        }
        /// <summary>
        /// 打开注册表项节点，以只读方式检索子项
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <returns>如果SubKey为空、null或者SubKey指示注册表项不存在，则返回null，否则返回注册表节点</returns>
        protected virtual RegistryKey OpenSubKey(string subKey)
        {
            ///判断注册表项名称是否为空
            if (subKey == string.Empty || subKey == null)
            {
                return null;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            ///要打开的注册表项的节点
            RegistryKey sKey = null;
            ///打开注册表项
            sKey = key.OpenSubKey(subKey);
            ///关闭对注册表项的更改
            key.Close();
            ///返回注册表节点
            return sKey;
        }
        /// <summary>
        /// 打开注册表项节点，以只读方式检索子项
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="writable">如果需要项的写访问权限，则设置为 true</param>
        /// <returns>如果SubKey为空、null或者SubKey指示注册表项不存在，则返回null，否则返回注册表节点</returns>
        protected virtual RegistryKey OpenSubKey(string subKey, bool writable)
        {
            ///判断注册表项名称是否为空
            if (subKey == string.Empty || subKey == null)
            {
                return null;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(_domain);
            ///要打开的注册表项的节点
            RegistryKey sKey = null;
            ///打开注册表项
            sKey = key.OpenSubKey(subKey, writable);
            ///关闭对注册表项的更改
            key.Close();
            ///返回注册表节点
            return sKey;
        }
        /// <summary>
        /// 打开注册表项节点，以只读方式检索子项
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <returns>如果SubKey为空、null或者SubKey指示注册表项不存在，则返回null，否则返回注册表节点</returns>
        protected virtual RegistryKey OpenSubKey(string subKey, RegDomain regDomain)
        {
            ///判断注册表项名称是否为空
            if (subKey == string.Empty || subKey == null)
            {
                return null;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(regDomain);
            ///要打开的注册表项的节点
            RegistryKey sKey = null;
            ///打开注册表项
            sKey = key.OpenSubKey(subKey);
            ///关闭对注册表项的更改
            key.Close();
            ///返回注册表节点
            return sKey;
        }
        /// <summary>
        /// 打开注册表项节点
        /// 虚方法，子类可进行重写
        /// </summary>
        /// <param name="subKey">注册表项名称</param>
        /// <param name="regDomain">注册表基项域</param>
        /// <param name="writable">如果需要项的写访问权限，则设置为 true</param>
        /// <returns>如果SubKey为空、null或者SubKey指示注册表项不存在，则返回null，否则返回注册表节点</returns>
        protected virtual RegistryKey OpenSubKey(string subKey, RegDomain regDomain, bool writable)
        {
            ///判断注册表项名称是否为空
            if (subKey == string.Empty || subKey == null)
            {
                return null;
            }
            ///创建基于注册表基项的节点
            RegistryKey key = GetRegDomain(regDomain);
            ///要打开的注册表项的节点
            RegistryKey sKey = null;
            ///打开注册表项
            sKey = key.OpenSubKey(subKey, writable);
            ///关闭对注册表项的更改
            key.Close();
            ///返回注册表节点
            return sKey;
        }
        #endregion
        #endregion
        public virtual bool IsSameSubKey(string name, string strvalue)
        {
            bool result = false;
            ///判断注册表项名称是否为空，如果为空，返回false
            if (name == string.Empty || name == null)
            {
                return false;
            }
            if (IsSubKeyExist())
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey();
                ///键值集合
                string[] regeditKeyNames;
                ///获取键值集合
                regeditKeyNames = key.GetValueNames();
                ///遍历键值集合，如果存在键值，则退出遍历
                foreach (string regeditKey in regeditKeyNames)
                {
                    if (regeditKey.StartsWith(name))
                    {
                        object value = ReadRegeditKey(regeditKey);
                        if (value.ToString().Contains(strvalue))
                        {
                            result = true;
                            break;
                        }
                    }
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
        public virtual int GetNumber(string name)
        {
            int result = 0;
            ///判断注册表项名称是否为空，如果为空，返回false
            if (name == string.Empty || name == null)
            {
                return 0;
            }
            if (IsSubKeyExist())
            {
                ///打开注册表项
                RegistryKey key = OpenSubKey();
                ///键值集合
                string[] regeditKeyNames;
                ///获取键值集合
                regeditKeyNames = key.GetValueNames();
                ///遍历键值集合，如果存在键值，则退出遍历
                foreach (string regeditKey in regeditKeyNames)
                {
                    if (regeditKey.StartsWith(name))
                    {
                        result++;
                    }
                }
                ///关闭对注册表项的更改
                key.Close();
            }
            return result;
        }
    }
}