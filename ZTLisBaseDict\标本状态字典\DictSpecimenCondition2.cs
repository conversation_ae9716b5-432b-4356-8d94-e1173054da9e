using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;

namespace ZTLisBaseDict
{
    public partial class DictSpecimenCondition2 : Common.BaseForm.BaseDict2
    {
        BLL.BllDictSpecimenCondition _bllDictSpecimenCondition = new BllDictSpecimenCondition();
        Model.MdlDictSpecimenCondition _mdlDictSpecimenCondition = new MdlDictSpecimenCondition();

        public DictSpecimenCondition2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();

            base.Insert = insert;
            base.MyRow = row;
            base.MyTable = table;

            TxtName.GotFocus += new System.EventHandler(base.InputCn);
            TxtEnglishName.GotFocus += new System.EventHandler(base.InputEn);
            TxtDescription.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void DictSpecimenCondition2_Load(object sender, EventArgs e)
        {
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.MyRow);
        }

        #region 自定义函数
        private void FormInit()
        {
            TxtJc.Enabled = false;
            Panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllDictSpecimenCondition.MaxCode(4);
            TxtName.Text = "";
            TxtJc.Text = "";
            TxtEnglishName.Text = "";
            NumSequence.Value = null;
            TxtDescription.Text = "";
            TxtCode.Select();
        }

        protected override void DataShow(DataRow row)
        {
            base.Insert = false;
            base.MyRow = row;
            TxtCode.Text = row["SpecCondCode"] + "";
            TxtName.Text = row["SpecCondName"] + "";
            TxtJc.Text = row["PinYin"] + "";
            TxtEnglishName.Text = row["EnglishName"] + "";
            NumSequence.Value = row["SequenceNo"];
            TxtDescription.Text = row["Description"] + "";

            TxtName.Select();
        }

        private bool DataCheck()
        {
            if (string.IsNullOrEmpty(TxtName.Text))
            {
                MessageBox.Show("请填写标本状态名称！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Select();
                return false;
            }
            if (string.IsNullOrEmpty(TxtCode.Text))
            {
                MessageBox.Show("请填写标本状态编码！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtCode.Select();
                return false;
            }

            // 检查编码重复
            if (base.Insert == true)
            {
                if (CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            else
            {
                if (TxtCode.Text.Trim() != base.MyRow["SpecCondCode"].ToString().Trim() && CheckCodeExists(TxtCode.Text))
                {
                    MessageBox.Show("编码重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtCode.Select();
                    return false;
                }
                if (TxtName.Text.Trim() != base.MyRow["SpecCondName"].ToString().Trim() && CheckNameExists(TxtName.Text))
                {
                    MessageBox.Show("名称重复！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    TxtName.Select();
                    return false;
                }
            }
            return true;
        }

        private bool CheckCodeExists(string code)
        {
            int cnt = _bllDictSpecimenCondition.GetRecordCount($"SpecCondCode='{code}'");
            return cnt > 0;
        }
        private bool CheckNameExists(string name)
        {
            int cnt = _bllDictSpecimenCondition.GetRecordCount($"SpecCondName='{name}'");
            return cnt > 0;
        }

        private void DataAdd()
        {
            base.MyRow = base.MyTable.NewRow();

            _mdlDictSpecimenCondition.SpecCondCode = TxtCode.Text.Trim();
            _mdlDictSpecimenCondition.SpecCondName = TxtName.Text.Trim();
            _mdlDictSpecimenCondition.PinYin = TxtJc.Text.Trim();
            _mdlDictSpecimenCondition.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictSpecimenCondition.SequenceNo = NumSequence.Value == DBNull.Value ? null : (int?)NumSequence.Value;
            _mdlDictSpecimenCondition.Description = TxtDescription.Text.Trim();
            _mdlDictSpecimenCondition.Status = true;
            _mdlDictSpecimenCondition.CreatedAt = DateTime.Now;
            _mdlDictSpecimenCondition.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictSpecimenCondition, base.MyRow);

            //数据保存
            try
            {
                base.MyRow["SpecCondId"] = _bllDictSpecimenCondition.Add(_mdlDictSpecimenCondition);
                base.MyTable.Rows.Add(base.MyRow);
                base.MyRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("新增成功");
                TxtName.Focus();
            }

            DataClear();
        }

        private void DataEdit()
        {
            _mdlDictSpecimenCondition = _bllDictSpecimenCondition.GetModel(int.Parse(base.MyRow["SpecCondId"].ToString()));
            _mdlDictSpecimenCondition.SpecCondCode = TxtCode.Text.Trim();
            _mdlDictSpecimenCondition.SpecCondName = TxtName.Text.Trim();
            _mdlDictSpecimenCondition.PinYin = TxtJc.Text.Trim();
            _mdlDictSpecimenCondition.EnglishName = TxtEnglishName.Text.Trim();
            _mdlDictSpecimenCondition.SequenceNo = NumSequence.Value == DBNull.Value ? null : (int?)NumSequence.Value;
            _mdlDictSpecimenCondition.Description = TxtDescription.Text.Trim();
            _mdlDictSpecimenCondition.UpdatedAt = DateTime.Now;

            Common.DataTableToList.ToDataRow(_mdlDictSpecimenCondition, base.MyRow);

            //数据保存
            try
            {
                _bllDictSpecimenCondition.Update(_mdlDictSpecimenCondition);
                base.MyRow.AcceptChanges();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                this.Close();
            }
        }
        #endregion

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (DataCheck() == false)
                return;

            if (base.Insert == true)
            {
                this.DataAdd();
            }
            else
            {
                this.DataEdit();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void TxtName_Validated(object sender, EventArgs e)
        {
            TxtJc.Text = base.MyChs2Spell.GetPy(TxtName.Text);
        }

    }
}