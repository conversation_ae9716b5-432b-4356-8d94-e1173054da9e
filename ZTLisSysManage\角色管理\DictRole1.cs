﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using C1.Win.C1TrueDBGrid;
using C1.Win.TreeView;
using Model;

namespace ZTLisSysManage
{
    public partial class DictRole1 : Common.BaseForm.BaseDict21
    {
        BLL.BllSysRole _bllSysRole = new BllSysRole();
        BLL.BllSysModule _bllSysModule = new BllSysModule();

        BLL.BllSysRoleModule _bllSysRoleModule = new BllSysRoleModule();
        Model.MdlSysRoleModule _mdlSysRoleModule = new MdlSysRoleModule();


        BLL.BllSysRoleAuth _bllSysRoleAuth = new BllSysRoleAuth();
        Model.MdlSysRoleAuth _mdlSysRoleAuth = new MdlSysRoleAuth();

        private bool _isAuthInitFinish = false;

        private Thread invokeThread;

        public DictRole1()
        {
            InitializeComponent();
        }
        private void DictRole1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();
            this.TreeInit();
            this.TvwAuthInit();
            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }

        #region 控件动作

        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {

        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {

        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            string str = "";
            if (TxtFilter.Text.Trim() == "")
            {
                str = "";
            }
            else
            {
                str = "RoleName Like '*" + TxtFilter.Text.Trim() + "*' or RolePinYin Like '*" + TxtFilter.Text.Trim() + "*' or RoleWuBi like '*" + TxtFilter.Text.Trim() + "*'";
            }
            base.MyView.RowFilter = str;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void treeView1_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Text == "角色管理")
            {
                this.splitContainer1.Panel1Collapsed = false;
                this.splitContainer1.Panel2Collapsed = true;
                BtnEnable(true);
            }
            else
            {
                this.splitContainer1.Panel1Collapsed = true;
                this.splitContainer1.Panel2Collapsed = false;
                BtnEnable(false);

                if (invokeThread != null)
                {
                    invokeThread.Abort();
                }

                invokeThread = new Thread(TvwRoleAuthInit);
                invokeThread.IsBackground = true;
                invokeThread.Start(treeView1.SelectedNode.Name);
            }
        }

        private void TvwRoleAuth_CheckStateChanged(object sender, C1TreeViewEventArgs e)
        {
            if (_isAuthInitFinish == true)
            {
                if (e.Node.Level == 3)
                {
                    if (e.Node.Checked == true)
                    {
                        _mdlSysRoleModule.ModuleCode = e.Node[1].ToString();
                        _mdlSysRoleModule.RoleCode = treeView1.SelectedNode.Name;
                        _bllSysRoleModule.Add(_mdlSysRoleModule);
                    }
                    else
                    {
                        _bllSysRoleModule.Delete(treeView1.SelectedNode.Name, e.Node[1].ToString());
                    }

                }

                if (e.Node.Level == 4)
                {
                    if (e.Node.Checked == true)
                    {
                        _mdlSysRoleAuth.AuthCode = e.Node[1].ToString();
                        _mdlSysRoleAuth.RoleCode = treeView1.SelectedNode.Name;
                        _bllSysRoleAuth.Add(_mdlSysRoleAuth);
                    }
                    else
                    {
                        _bllSysRoleAuth.Delete(treeView1.SelectedNode.Name, e.Node[1].ToString());
                    }

                }

            }
        }

        #endregion

        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("编码", "RoleCode", 100, "中", "", false);
            myGrid1.Init_Column("名称", "RoleName", 200, "左", "", false);
            myGrid1.Init_Column("拼音简称", "RolePinYin", 120, "左", "", false);
            myGrid1.Init_Column("五笔简称", "RoleWuBi", 120, "左", "", false);
            myGrid1.Init_Column("备注", "RoleMemo", 200, "左", "", false);
            myGrid1.AllowSort = true;
            myGrid1.MultiSelect = MultiSelectEnum.None;
        }

        private void DataInit()
        {
            base.MyTable = _bllSysRole.GetAllList().Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["RoleCode"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
        }

        private void TreeInit()
        {
            TreeNode root = new TreeNode();
            root.Tag = "00";
            root.Text = "角色管理";
            root.ImageIndex = 0;

            foreach (DataRow row in base.MyTable.Rows)
            {
                TreeNode node = new TreeNode();
                node.Tag = row["RoleCode"].ToString();
                node.Name = row["RoleCode"].ToString();
                node.Text = row["RoleName"].ToString();
                node.ImageIndex = 1;
                node.SelectedImageIndex = 2;
                root.Nodes.Add(node);
            }
            treeView1.Nodes.Add(root);
            root.Expand();
            treeView1.SelectedNode = root;
            treeView1.Select();
        }

        private void TvwAuthInit()
        {
            TvwRoleAuth.Init();
            TvwRoleAuth.Columns[0].DisplayFieldName = "NodeName\\NodeName\\NodeName\\NodeName\\NodeName";
            TvwRoleAuth.Columns[1].DisplayFieldName = "ID";
            TvwRoleAuth.Columns[1].Visible = false;
            TvwRoleAuth.KeyField = "ID";
            TvwRoleAuth.ParentKeyField = "ParentID";
            TvwRoleAuth.DataTable = _bllSysModule.GetAllModule();
            TvwRoleAuth.CheckBoxes = true;
            TvwRoleAuth.ExpandAll();
        }

        protected override void TreeEdit(TreeNode node)
        {
            if (base.Insert == true)
            {
                node.ImageIndex = 1;
                node.SelectedImageIndex = 2;
                treeView1.SelectedNode.Nodes.Add(node);
            }
            else
            {
                foreach (TreeNode tNode in treeView1.TopNode.Nodes)
                {
                    if (tNode.Name == node.Name)
                    {
                        tNode.Text = node.Text;
                        break;
                    }
                }
            }
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }
            DictRole2 vform = new DictRole2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            vform.MyTransmitTreeNode = base.MyTransmitTreeNode;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }
        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除:角色=" + this.myGrid1.Columns["RoleName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            try
            {
                _bllSysRole.Delete(base.MyRow["RoleCode"].ToString());
                base.NodeDelete(treeView1.TopNode, base.MyRow["RoleCode"].ToString());
                myGrid1.Delete();
                base.MyRow.AcceptChanges();
                this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
            }
            catch
            {
                MessageBox.Show("请先删除该角色下的所有用户和权限！");
            }

        }
        private void DataRefresh()
        {
            DataInit();
        }


        private void TvwRoleAuthInit(object roleCode)
        {
            _isAuthInitFinish = false;
            foreach (C1TreeNode node in TvwRoleAuth.Nodes)
            {
                CheckAuth(node, roleCode.ToString());
            }
            _isAuthInitFinish = true;
        }

        private void CheckAuth(C1TreeNode node, string roleCode)
        {
            foreach (C1TreeNode childNode in node.Nodes)
            {
                CheckAuth(childNode, roleCode);
                if (childNode.Level == 3)
                {
                    if (_bllSysRoleModule.Exists(roleCode, childNode[1].ToString()))
                    {
                        treeView1.Invoke(new Action<bool>(p => { childNode.Checked = p; }), true);
                    }
                    else
                    {
                        treeView1.Invoke(new Action<bool>(p => { childNode.Checked = p; }), false);
                    }
                }
                if (childNode.Level == 4)
                {
                    if (_bllSysRoleAuth.Exists(roleCode, childNode[1].ToString()))
                    {
                        treeView1.Invoke(new Action<bool>(p => { childNode.Checked = p; }), true);
                    }
                    else
                    {
                        treeView1.Invoke(new Action<bool>(p => { childNode.Checked = p; }), false);
                    }
                }
            }
        }

        private void BtnEnable(bool flag)
        {
            CmdAdd.Enabled = flag;
            CmdDelete.Enabled = flag;
            CmdRefresh.Enabled = flag;
            CmdMoveUp.Enabled = flag;
            CmdMoveDown.Enabled = flag;
            CmdExport.Enabled = flag;
            CmdImport.Enabled = flag;
            CmdEnable.Enabled = flag;
            CmdDisable.Enabled = flag;
        }

        #endregion

    }
}
