﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Resources;
using System.Text;

namespace ZTLisResources
{
    public class C_Resources
    {
        private static ResourceManager rm;

        private static System.Reflection.Assembly myAssembly;
        public static Image GetImage32(string imageName)
        {
            return GetImage(imageName, 32);
        }
        public static Image GetImage24(string imageName)
        {
            return GetImage(imageName, 24);
        }
        public static Image GetImage20(string imageName)
        {
            return GetImage(imageName, 20);
        }
        public static Image GetImage16(string imageName)
        {
            return GetImage(imageName, 16);
        }

        private static Image GetImage(string imageName, int size)
        {
            myAssembly = System.Reflection.Assembly.Load("ZTLisResources");
            rm = new ResourceManager("ZTLisResources.Resource1", myAssembly);
            Image img = (Image)rm.GetObject(imageName);
            if (img == null)
            {
                return null;
            }
            else
            {
                Bitmap b = new Bitmap(img, size, size);
                return b;
            }
        }


        public static Image GetMenuImage(string imageName, int size)
        {
            myAssembly = System.Reflection.Assembly.Load("ZTLisResources");
            rm = new ResourceManager("ZTLisResources.MenuImgRes", myAssembly);
            Image img = (Image)rm.GetObject(imageName);
            if (img == null)
            {
                //Image img1 = (Image)rm.GetObject("无图片按钮");
                //Bitmap b = new Bitmap(img1, size, size);
                return img;
            }
            else
            {
                Bitmap b = new Bitmap(img, size, size);
                return b;
            }
        }


        public static Image GetInvoiceRes(string imageName)
        {
            myAssembly = System.Reflection.Assembly.Load("ZTLisResources");
            rm = new ResourceManager("ZTLisResources.InvoiceRes", myAssembly);
            Image img = (Image)rm.GetObject(imageName);
            if (img == null)
            {
                //Image img1 = (Image)rm.GetObject("无图片按钮");
                //Bitmap b = new Bitmap(img1, size, size);
                return img;
            }
            else
            {
                Bitmap b = new Bitmap(img);
                return b;
            }
        }

        public static Image GetStateRes(string imageName)
        {
            myAssembly = System.Reflection.Assembly.Load("ZTLisResources");
            rm = new ResourceManager("ZTLisResources.StateRes", myAssembly);
            Image img = (Image)rm.GetObject(imageName);
            if (img == null)
            {
                return img;
            }
            else
            {
                Bitmap b = new Bitmap(img);
                return b;
            }
        }
    }
}
