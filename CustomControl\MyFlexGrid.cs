﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using C1.Win.C1FlexGrid;

namespace CustomControl
{
    public partial class MyFlexGrid : C1FlexGrid
    {
        public MyFlexGrid()
        {
            InitializeComponent();
        }

        public MyFlexGrid(IContainer container)
        {
            container.Add(this);

            InitializeComponent();
        }

        public void Init()
        {
            Dock = DockStyle.Fill;
            Clear();
            VisualStyle = VisualStyle.Office2010Blue;
            AllowDelete = false;
            AllowEditing = true;
            AutoResize = false;
            AllowSorting = AllowSortingEnum.SingleColumn;
            AllowDragging = AllowDraggingEnum.Columns;
            AllowResizing = AllowResizingEnum.Both;
            AllowMerging = AllowMergingEnum.Free;
            BorderStyle = C1.Win.C1FlexGrid.Util.BaseControls.BorderStyleEnum.Light3D;
            Rows.MinSize = 22;
            Styles.Fixed.Font = new Font("宋体", (float)10.5, FontStyle.Regular);
            Styles.Alternate.BackColor=Color.AliceBlue;
            ExtendLastCol = false;
            FocusRect = FocusRectEnum.None;
            SelectionMode = SelectionModeEnum.Cell;
  
            CellStyle cs = Styles.Add("emp");
            cs.DataType = typeof(string);
            cs.ComboList = "|Tom|Dick|Harry";
            cs.ForeColor = Color.Navy;
            cs.Font = new Font(Font, FontStyle.Bold);

            cs = Styles.Add("date");
            cs.DataType = typeof(DateTime);
            cs.Format = "dd-MMM-yy";
            cs.ForeColor = Color.DarkGoldenrod;

            cs = Styles.Add("curr");
            cs.DataType = typeof(decimal);
            cs.Format = "c";
            cs.ForeColor = Color.DarkGreen;
            cs.Font = new Font(Font, FontStyle.Bold);

            cs = Styles.Add("bool");
            cs.DataType = typeof(bool);
            cs.ImageAlign = ImageAlignEnum.CenterCenter;

            cs = Styles.Add("string");
            cs.DataType = typeof(string);
  
            //this.Tree.Column = 0;
            //this.Tree.Indent = 20;
            //this.Tree.Style = TreeStyleFlags.CompleteLeaf;
            //this.Tree.LineColor = Color.DarkRed;
            //this.Tree.LineStyle = System.Drawing.Drawing2D.DashStyle.Solid;

           
            ////类型()

            
            Styles.Fixed.WordWrap = false;
            Styles.Fixed.Border.Style = C1.Win.C1FlexGrid.BorderStyleEnum.Raised;
            Styles.Fixed.TextAlign = TextAlignEnum.CenterCenter;
            
            Styles.Fixed.Margins.Top = 1;
            Styles.Fixed.Margins.Bottom = 0;
           
            //BackColor = Color.FromArgb(0, 78, 152);
            //ForeColor = Color.FromArgb(255, 255, 255);

            ////this.rec
            //RecordSelectorWidth = 10;

            //ForeColor = Color.FromArgb(255, 255, 255);
            //BackColor = Color.FromArgb(49, 106, 197);

            
            //this.Rows[0].Height = 20;
            //Redraw = true;
            //this.DataSource = My_View;
        }

        public void Init_Column(string V_标题, string V_字段, int V_长度, string V_水平, string V_格式, bool V_可编辑,bool V_可合并)
        {
            {
                
                Cols[V_字段].Caption = V_标题;
                Cols[V_字段].Width = V_长度;
                         
                //Cols[V_字段].AllowMerging = true;
                //Cols[V_字段].AllowDragging = true;
                if (V_水平 == "左")
                {
                    Cols[V_字段].TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter;
                }
                else if (V_水平 == "中")
                {
                    Cols[V_字段].TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter;
                }
                else if (V_水平 == "右")
                {
                    Cols[V_字段].TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.RightCenter;
                }
                if (V_格式.ToUpper() == "CHECK")
                {
                    Cols[V_字段].Style = Styles["bool"];
                }
                else
                {
                    Cols[V_字段].Format = V_格式; 
                }
       
                Cols[V_字段].Style.WordWrap = true;
               Cols[V_字段].Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort;
                Cols[V_字段].AllowMerging = V_可合并;
                Cols[V_字段].Style.Font = new Font("宋体", (float)10.5, FontStyle.Regular);
                            
                if (V_长度 == 0)
                {
                    Cols[V_字段].Visible = false;
                }
                else
                {
                    Cols[V_字段].Visible = true;
                }

                Cols[V_字段].AllowEditing = V_可编辑;
                if (V_可编辑 == true)
                {
                    Cols[V_字段].Style.BackColor = SystemColors.Info;
                }
            }
        }
    }
}
