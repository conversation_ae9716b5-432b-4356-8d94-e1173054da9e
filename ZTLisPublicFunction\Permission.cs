﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using C1.Win.C1Command;
using CustomControl;
using Model;

namespace ZTLisPublicFunction
{
    public class Permission
    {
        /// <summary>
        /// 获取用户权限
        /// </summary>
        public static List<Model.MdlSysMenu1> GetUserPermission(string roleCode)
        {
            BLL.BllSysMenu1 _bllSysMenu1 = new BLL.BllSysMenu1();
            BLL.BllSysMenu2 _bllSysMenu2 = new BLL.BllSysMenu2();
            BLL.BllSysModule _bllSysModule = new BLL.BllSysModule();
            BLL.BllSysModuleAuth _bllSysModuleAuth = new BLL.BllSysModuleAuth();
            List<Model.MdlSysMenu1> listMdlSysMenu1s = new List<Model.MdlSysMenu1>();

            //获取角色的一级菜单
            foreach (var mdlSysMenu1 in _bllSysMenu1.GetMenu1(ZTLisVar.Var.Role_Code))
            {
                mdlSysMenu1.Menu2Lists = new List<MdlSysMenu2>();
                //获取角色的二级菜单
                foreach (var mdlSysMenu2 in _bllSysMenu2.GetMenu2(ZTLisVar.Var.Role_Code, mdlSysMenu1.FrsCode))
                {
                    mdlSysMenu2.ModuleLists = new List<MdlSysModule>();
                    //获取角色的模块按钮
                    foreach (var mdlSysModule in _bllSysModule.GetModule(ZTLisVar.Var.Role_Code, mdlSysMenu2.ScndCode))
                    {
                        mdlSysModule.ModuleAuthLists = new List<MdlSysModuleAuth>();

                        foreach (var mdlSysModuleAuth in _bllSysModuleAuth.GetModuleAuth(ZTLisVar.Var.Role_Code, mdlSysModule.ModuleCode))
                        {
                            mdlSysModule.ModuleAuthLists.Add(mdlSysModuleAuth);
                        }

                        mdlSysMenu2.ModuleLists.Add(mdlSysModule);
                    }

                    mdlSysMenu1.Menu2Lists.Add(mdlSysMenu2);
                }

                listMdlSysMenu1s.Add(mdlSysMenu1);
            }

            return listMdlSysMenu1s;

        }

        public static void CheckBtnAuth(ref MyButton myButton, string authName, string moduleName)
        {
            var query1 = ZTLisVar.Var.UserPermission
                .Where(p1 => p1.Menu2Lists
                    .Any(p2 => p2.ModuleLists
                        .Any(p3 => p3.ModuleName == moduleName && p3.ModuleAuthLists.Any(p4 => p4.AuthName == authName))));

            if (query1.Count() == 0)
            {
                myButton.Enabled = false;
            }

        }
        public static void CheckBtnAuth(ref C1Command c1Command, string authName, string moduleName)
        {
            var query1 = ZTLisVar.Var.UserPermission
                .Where(p1 => p1.Menu2Lists
                    .Any(p2 => p2.ModuleLists
                        .Any(p3 => p3.ModuleName == moduleName && p3.ModuleAuthLists.Any(p4 => p4.AuthName == authName))));

            if (query1.Count() == 0)
            {
                c1Command.Enabled = false;
            }

        }
        public static bool CheckModule(string moduleName)
        {
            var query1 = ZTLisVar.Var.UserPermission
                .Where(p1 => p1.Menu2Lists
                    .Any(p2 => p2.ModuleLists
                        .Any(p3 => p3.ModuleName == moduleName)));

            if (query1.Count() == 0)
            {
                return false;
            }
            else
            {
                return true;
            }

        }

    }
}
