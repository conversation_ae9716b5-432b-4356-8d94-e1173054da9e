# LIS 系统数据库文档

## 表结构

### 1. LabInstrument 表

#### 1.1 表概述

**表名**：LabInstrument  
**说明**：仪器主档 / 连接参数表  
**用途**：统一登记院内所有检验设备的静态信息与通讯配置。其他子表（项目映射、通道、质控计划等）均以 InstrumentId 关联。

#### 1.2 字段定义

| 字段名              | 数据类型      | 允许NULL | 默认值        | 约束                        | 说明                                                                                                                           |
| ------------------- | ------------- | -------- | ------------- | --------------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| InstrumentId        | INT           | 否       | IDENTITY(1,1) | 主键                        | 仪器ID，自增主键                                                                                                               |
| InstrCode           | NVARCHAR(20)  | 否       | -             | UNIQUE                      | 设备唯一代号（英文/拼音），如：BS240、CS600等，方便代码层快速定位                                                              |
| InstrName           | NVARCHAR(100) | 否       | -             | -                           | 设备中文全称，如：迈瑞 BS-240 生化分析仪                                                                                       |
| Vendor              | NVARCHAR(60)  | 是       | -             | -                           | 厂商名称，如：Mindray / Roche等                                                                                                |
| Model               | NVARCHAR(60)  | 是       | -             | -                           | 设备型号，如：BS-240                                                                                                           |
| SerialNo            | NVARCHAR(60)  | 是       | -             | -                           | 出厂序列号                                                                                                                     |
| Location            | NVARCHAR(60)  | 是       | -             | -                           | 设备放置地点，如：临检实验室 1 号位                                                                                            |
| ConnType            | TINYINT       | 否       | -             | CHECK (0-5)                 | 通讯方式：<br>0=RS232 (串口)<br>1=TCP<br>2=文件共享<br>3=中间库<br>4=REST API<br>5=HL7 MLLP                                    |
| MsgFormatType       | TINYINT       | 否       | 0             | CHECK (0-1)                 | 消息格式类型：<br>0=字符串<br>1=字节流                                                                                         |
| ComPort             | NVARCHAR(10)  | 是       | -             | -                           | 串口号，如：COM3 / \\.\COM5                                                                                                    |
| BaudRate            | INT           | 是       | -             | -                           | 波特率，如：9600 / 115200                                                                                                      |
| Parity              | CHAR(1)       | 是       | -             | CHECK ('N','E','O','M','S') | 校验位：N=无校验, E=偶校验, O=奇校验, M=标记, S=空格                                                                           |
| DataBits            | TINYINT       | 是       | -             | CHECK (5,6,7,8)             | 数据位                                                                                                                         |
| StopBits            | VARCHAR(10)   | 是       | -             | CHECK ('1','1.5','2')       | 停止位                                                                                                                         |
| IpHost              | VARCHAR(255)  | 是       | -             | -                           | IP地址或域名，如：**********                                                                                                   |
| IpPort              | INT           | 是       | -             | -                           | IP端口，如：5001 / 5555                                                                                                        |
| KeepAliveSec        | SMALLINT      | 是       | -             | -                           | 心跳间隔(秒)                                                                                                                   |
| FilePath            | NVARCHAR(260) | 是       | -             | -                           | 文件共享路径，结果文件掉落目录                                                                                                 |
| DbType              | NVARCHAR(50)  | 是       |               |                             | 数据库类型                                                                                                                     |
| DbConnStr           | NVARCHAR(400) | 是       | -             | -                           | 厂商中间库连接字符串                                                                                                           |
| ApiBaseUrl          | NVARCHAR(200) | 是       | -             | -                           | REST 起始 URL                                                                                                                  |
| ApiAuthToken        | NVARCHAR(400) | 是       | -             | -                           | REST 认证令牌（加密存储推荐 Always Encrypted）                                                                                 |
| ProtocolType        | TINYINT       | 否       | -             | CHECK (0-4)                 | 协议类型：<br>0=ASTM<br>1=LIS2<br>2=POCT1-A<br>3=HL7<br>4=自定义                                                               |
| MsgHeaderFormatType | TINYINT       | 否       | 0             | CHECK (0-1)                 | 消息格式类型：<br>0=字符串<br>1=字节流                                                                                         |
| MsgHeader           | NVARCHAR(100) | 是       | -             | -                           | 消息头（ASCII码16进制，多个用空格分割）                                                                                        |
| MsgTail             | NVARCHAR(100) | 是       | -             | -                           | 消息尾（ASCII码16进制，多个用空格分割）                                                                                        |
| AckSuccess          | NVARCHAR(100) | 是       | -             | -                           | 接收成功应答（ASCII码16进制，多个用空格分割）                                                                                  |
| AckFailure          | NVARCHAR(100) | 是       | -             | -                           | 接收失败应答（ASCII码16进制，多个用空格分割）                                                                                  |
| CustomDriverClass   | NVARCHAR(255) | 是       |               |                             | 当ProtocolType为4(自定义)时，指定解析器类的完全限定名（包括程序集名称）。例如：MyProject.Parsers.MyCustomParser, MyProject.dll |
| DriverVersion       | NVARCHAR(20)  | 是       | -             | -                           | 驱动 DLL 版本                                                                                                                  |
| FirmwareVersion     | NVARCHAR(40)  | 是       | -             | -                           | 设备固件版本                                                                                                                   |
| Status              | BIT           | 否       | 1             | -                           | 状态：1=启用 0=停用                                                                                                            |
| Description         | NVARCHAR(200) | 是       | -             | -                           | 备注信息/停用原因                                                                                                              |
| CreatedAt           | DATETIME      | 否       | GETDATE()     | -                           | 创建时间                                                                                                                       |
| UpdatedAt           | DATETIME      | 否       | GETDATE()     | -                           | 更新时间                                                                                                                       |

#### 1.3 索引

| 索引名                | 字段          | 类型       | 说明                   |
| --------------------- | ------------- | ---------- | ---------------------- |
| PK_LabInstrument      | InstrumentId  | 主键       | 仪器ID主键索引         |
| -                     | InstrCode     | 唯一索引   | 设备唯一代号唯一索引   |
| IX_Instr_Vendor_Model | Vendor, Model | 非唯一索引 | 按厂商和型号查询的索引 |
| IX_Instr_ConnType     | ConnType      | 非唯一索引 | 按连接类型查询的索引   |

#### 1.4 约束

| 约束名                    | 类型    | 字段                | 说明                                 |
| ------------------------- | ------- | ------------------- | ------------------------------------ |
| PK_LabInstrument          | 主键    | InstrumentId        | 主键约束                             |
| -                         | 唯一    | InstrCode           | 唯一约束                             |
| CHK_Instr_ConnType        | CHECK   | ConnType            | 检查ConnType值在0-5范围内            |
| CHK_Instr_Protocol        | CHECK   | ProtocolType        | 检查ProtocolType值在0-4范围内        |
| CHK_Instr_MsgFormat       | CHECK   | MsgFormatType       | 检查MsgFormatType值在0-1范围内       |
| CHK_Instr_MsgHeaderFormat | CHECK   | MsgHeaderFormatType | 检查MsgHeaderFormatType值在0-1范围内 |
| -                         | CHECK   | Parity              | 检查Parity值为N,E,O,M,S之一          |
| -                         | CHECK   | DataBits            | 检查DataBits值为5,6,7,8之一          |
| -                         | CHECK   | StopBits            | 检查StopBits值为1,2之一              |
| DF_Instr_Status           | DEFAULT | Status              | 默认值为1(启用)                      |

#### 1.5 示例数据

##### 示例1：迈瑞 BS-240 串口 ASTM
```sql
INSERT INTO dbo.LabInstrument
       (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType, ComPort,
        BaudRate, Parity, DataBits, StopBits,
        ProtocolType, DriverVersion, Location)
VALUES ('BS240', N'迈瑞 BS-240 生化分析仪', N'Mindray', N'BS-240',
        0, 0, 'COM3',
        9600, 'N', 8, 1,
        0, '3.2.1', N'临检实验室 1 号机');
```

##### 示例2：罗氏 Cobas 8000 TCP连接 LIS2协议
```sql
INSERT INTO dbo.LabInstrument
       (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType,
        IpHost, IpPort, KeepAliveSec,
        ProtocolType, DriverVersion, Location)
VALUES ('COBAS8000', N'罗氏 Cobas 8000 生化免疫分析系统', N'Roche', N'Cobas 8000',
        1, 0, '*************', 5001, 60,
        1, '2.5.0', N'生化免疫实验室');
```

##### 示例3：希森美康 XN-9000 文件共享方式
```sql
INSERT INTO dbo.LabInstrument
       (InstrCode, InstrName, Vendor, Model, ConnType, MsgFormatType,
        FilePath, ProtocolType, DriverVersion, Location)
VALUES ('XN9000', N'希森美康 XN-9000 血液分析系统', N'Sysmex', N'XN-9000',
        2, 0, '\\\\fileserver\\share\\xn9000\\results',
        0, '1.8.3', N'血液学实验室');
```

### 2. LabInstrRawMsg 表

#### 2.1 表概述

**表名**：LabInstrRawMsg  
**说明**：仪器原始报文缓冲表  
**用途**：存储由各连接器（Connector）接收到的完整报文，解析器解析成功后将 ParsedFlag 置 1 并写入队列表。

#### 2.2 字段定义

| 字段名       | 数据类型      | 允许NULL | 默认值        | 约束 | 说明                         |
| ------------ | ------------- | -------- | ------------- | ---- | ---------------------------- |
| RawId        | BIGINT        | 否       | IDENTITY(1,1) | 主键 | 主键ID                       |
| InstrumentId | INT           | 否       | -             | 外键 | 关联仪器ID (LabInstrument表) |
| RawText      | NVARCHAR(MAX) | 否       | -             | -    | 完整原始报文文本             |
| AcquiredAt   | DATETIME      | 否       | GETDATE()     | -    | 接收时间                     |
| ParsedFlag   | BIT           | 否       | 0             | -    | 解析标志：0=未解析 1=已解析  |
| ParseError   | NVARCHAR(400) | 是       | -             | -    | 解析失败原因                 |

#### 2.3 索引

| 索引名                      | 字段                          | 类型       | 说明                         |
| --------------------------- | ----------------------------- | ---------- | ---------------------------- |
| PK_LabInstrRawMsg           | RawId                         | 主键       | 原始报文ID主键索引           |
| IX_LabInstrRawMsg_InstrTime | InstrumentId, AcquiredAt DESC | 非唯一索引 | 按仪器ID和时间倒序查询的索引 |

#### 2.4 约束

| 约束名                       | 类型    | 字段         | 说明                              |
| ---------------------------- | ------- | ------------ | --------------------------------- |
| PK_LabInstrRawMsg            | 主键    | RawId        | 主键约束                          |
| FK_LabInstrRawMsg_Instrument | 外键    | InstrumentId | 引用LabInstrument表的InstrumentId |
| DF_LabInstrRawMsg_Acq        | DEFAULT | AcquiredAt   | 默认值为GETDATE()                 |
| DF_LabInstrRawMsg_Flag       | DEFAULT | ParsedFlag   | 默认值为0(未解析)                 |

#### 2.5 示例数据

##### ASTM协议原始报文示例 (BS240仪器)
```sql
INSERT INTO dbo.LabInstrRawMsg (InstrumentId, RawText)
SELECT InstrumentId, 
N'H|\^&|||BS-240^03.02.00^1234567890||||||P|LIS2-A|20230515123045' + CHAR(13) + CHAR(10) +
N'P|1||123456789|^||19850101|M|||||||||||||||||||||||||||' + CHAR(13) + CHAR(10) +
N'O|1|SID12345||^^^ALT+^^^AST+^^^GGT+^^^ALP|R||20230515122000|||||A||||||||||||||||' + CHAR(13) + CHAR(10) +
N'R|1|^^^ALT|42|U/L|5-40|H||F||||20230515123045|' + CHAR(13) + CHAR(10) +
N'R|2|^^^AST|38|U/L|5-40||||||20230515123045|' + CHAR(13) + CHAR(10) +
N'R|3|^^^GGT|75|U/L|11-50|H||F||||20230515123045|' + CHAR(13) + CHAR(10) +
N'R|4|^^^ALP|88|U/L|45-125||||||20230515123045|' + CHAR(13) + CHAR(10) +
N'L|1|N'
FROM dbo.LabInstrument WHERE InstrCode = 'BS240';
```

##### HL7协议原始报文示例 (COBAS8000仪器)
```sql
INSERT INTO dbo.LabInstrRawMsg (InstrumentId, RawText)
SELECT InstrumentId, 
N'MSH|^~\&|COBAS8000|LAB|LIS|HOSPITAL|20230515123045||ORU^R01|20230515123045|P|2.3.1||||||UNICODE UTF-8' + CHAR(13) + 
N'PID|||987654321||DOE^JOHN||19750615|M|||123 MAIN ST^^ANYTOWN^CA^12345||5551234567|||123-45-6789||||' + CHAR(13) + 
N'OBR|1||LAB23456|CBC^COMPLETE BLOOD COUNT^L|||20230515120000|20230515120500|||||||20230515121000||DR. SMITH||||LAB|F||^^^' + CHAR(13) + 
N'OBX|1|NM|WBC^WHITE BLOOD CELL COUNT^L||6.8|10*3/uL|4.5-11.0||||F|||20230515123045||' + CHAR(13) + 
N'OBX|2|NM|RBC^RED BLOOD CELL COUNT^L||4.67|10*6/uL|4.50-5.90||||F|||20230515123045||' + CHAR(13) + 
N'OBX|3|NM|HGB^HEMOGLOBIN^L||14.2|g/dL|13.0-17.0||||F|||20230515123045||' + CHAR(13) + 
N'OBX|4|NM|HCT^HEMATOCRIT^L||42.1|%|40.0-52.0||||F|||20230515123045||' + CHAR(13) + 
N'OBX|5|NM|PLT^PLATELET COUNT^L||267|10*3/uL|150-450||||F|||20230515123045||'
FROM dbo.LabInstrument WHERE InstrCode = 'COBAS8000';
```

### 3. LabInstrResultQ 表

#### 3.1 表概述

**表名**：LabInstrResultQ  
**说明**：解析结果中间队列表  
**用途**：存放 Parser 已解析、Importer 待入库的单项结果记录。通过 ProcessedFlag 管理成功 / 失败 / 待处理状态。

#### 3.2 字段定义

| 字段名        | 数据类型      | 允许NULL | 默认值        | 约束               | 说明                                     |
| ------------- | ------------- | -------- | ------------- | ------------------ | ---------------------------------------- |
| QId           | BIGINT        | 否       | IDENTITY(1,1) | 主键               | 主键ID（IDENTITY）                       |
| RawId         | BIGINT        | 否       |               |                    | 关联LabInstrRawMsg的RawId                |
| InstrumentId  | INT           | 否       | -             | -                  | 关联仪器ID（LabInstrument.InstrumentId） |
| Barcode       | NVARCHAR(30)  | 否       | -             | -                  | 样本条码                                 |
| InstrItemCode | NVARCHAR(30)  | 否       | -             | -                  | 仪器项目代码（待映射到 LIS 项目）        |
| ResultValue   | NVARCHAR(60)  | 是       | -             | -                  | 结果值（字符或数字）                     |
| InstrFlag     | NVARCHAR(10)  | 是       | -             | -                  | 仪器标志（H/L/A等异常标记）              |
| ResultType    | TINYINT       | 否       | 0             |                    | 结果类型：0=检验结果, 1=标本参数         |
| MeasuredAt    | DATETIME      | 是       | -             | -                  | 仪器测定时间                             |
| ProcessError  | NVARCHAR(200) | 是       | -             | -                  | 入库阶段的错误信息（为空表示入库成功）   |
| ProcessedFlag | TINYINT       | 否       | 0             | CHECK (IN (0,1,2)) | 处理标记：0=待入库 1=入库成功 2=入库失败 |

#### 3.3 索引

| 索引名                     | 字段          | 类型       | 说明                 |
| -------------------------- | ------------- | ---------- | -------------------- |
| PK_LabInstrResultQ         | QId           | 主键       | 队列ID主键索引       |
| IX_LabInstrResultQ_Flag    | ProcessedFlag | 非唯一索引 | 按处理状态查询的索引 |
| IX_LabInstrResultQ_Barcode | Barcode       | 非唯一索引 | 按样本条码查询的索引 |

#### 3.4 约束

| 约束名                  | 类型    | 字段          | 说明                              |
| ----------------------- | ------- | ------------- | --------------------------------- |
| PK_LabInstrResultQ      | 主键    | QId           | 主键约束                          |
| FK_ResultQ_Instrument   | 外键    | InstrumentId  | 引用LabInstrument表的InstrumentId |
| FK_ResultQ_RawMsg       | 外键    | RawId         | 引用LabInstrRawMsg表的RawId       |
| DF_LabInstrResultQ_Flag | DEFAULT | ProcessedFlag | 默认值为0(待入库)                 |
| CK_LabInstrResultQ_Flag | CHECK   | ProcessedFlag | 检查ProcessedFlag值为0、1或2      |
| CK_ResultQ_ResultType   | CHECK   | ResultType    | 检查ResultType值为0或1            |

#### 3.5 示例数据

```sql
-- 待入库的结果记录
INSERT INTO dbo.LabInstrResultQ
       (InstrumentId, Barcode, InstrItemCode, ResultValue, InstrFlag, ResultType，MeasuredAt)
SELECT InstrumentId, '2023051500001', 'ALT', '42', 'H', 1, GETDATE()
FROM dbo.LabInstrument WHERE InstrCode = 'BS240';

-- 入库成功的记录
INSERT INTO dbo.LabInstrResultQ
       (InstrumentId, Barcode, InstrItemCode, ResultValue, InstrFlag, ResultType，MeasuredAt, ProcessedFlag)
SELECT InstrumentId, '2023051500002', 'AST', '38', '', 1, GETDATE(), 1
FROM dbo.LabInstrument WHERE InstrCode = 'BS240';

-- 入库失败的记录
INSERT INTO dbo.LabInstrResultQ
       (InstrumentId, Barcode, InstrItemCode, ResultValue, InstrFlag, ResultType，MeasuredAt, ProcessedFlag, ProcessError)
SELECT InstrumentId, '2023051500003', 'XXX', 'ERROR', 'L', 1, GETDATE(), 2, N'无法识别的项目代码'
FROM dbo.LabInstrument WHERE InstrCode = 'BS240';
```

### 4. LabResult 表

#### 4.1 表概述

**表名**：LabResult  
**说明**：检验结果主表  
**用途**：存放经过审核流程、可对外发布的最终检验结果。优化后的审核流程设计，支持多级审核和详细的执行记录。

#### 4.2 字段定义

| 字段名                | 数据类型      | 允许NULL | 默认值        | 约束 | 说明                                                               |
| --------------------- | ------------- | -------- | ------------- | ---- | ------------------------------------------------------------------ |
| ResultId              | BIGINT        | 否       | IDENTITY(1,1) | 主键 | 主键ID（自动增长）                                                 |
| OrderItemId           | BIGINT        | 否       | -             | 外键 | 检验医嘱明细ID（LabOrderItem表外键）                               |
| InstrumentId          | INT           | 否       | -             | 外键 | 仪器ID（LabInstrument表外键）                                      |
| InstrItemCode         | NVARCHAR(30)  | 否       | -             | -    | 仪器项目代码（冗余存储，便于追溯）                                 |
| ResultValue           | NVARCHAR(60)  | 是       | -             | -    | 结果值（字符或数字）                                               |
| Unit                  | NVARCHAR(20)  | 是       | -             | -    | 检验结果单位                                                       |
| AbnormalFlag          | CHAR(1)       | 是       | -             | -    | 异常标志：H=高 L=低 A=阳性 ...                                     |
| IsSystemGenerated     | BIT           | 否       | 0             | -    | 是否为系统生成结果：0=否(常规医嘱结果), 1=是(如仪器回报的标本参数) |
| MeasuredAt            | DATETIME      | 是       | -             | -    | 仪器测定时间                                                       |
| TestingTechnicianId   | INT           | 是       | -             | 外键 | 检验技师ID（实际执行检验的人，关联DictDoctor表）                   |
| TestingTechnicianName | NVARCHAR(100) | 是       | -             | -    | 检验技师姓名（冗余存储，便于查询）                                 |
| TestingCompletedAt    | DATETIME      | 是       | -             | -    | 检验完成时间                                                       |
| CurrentReviewLevel    | TINYINT       | 否       | 0             | -    | 当前审核级别：0=未审核 1=已初审 2=已复审 3=已三审...               |
| MaxReviewLevel        | TINYINT       | 否       | 2             | -    | 需要的最大审核级别（根据项目类型和业务规则确定）                   |
| FinalSignerId         | INT           | 是       | -             | 外键 | 最终签发医师ID（承担医疗责任的医师，关联DictDoctor表）             |
| FinalSignerName       | NVARCHAR(100) | 是       | -             | -    | 最终签发医师姓名（冗余存储）                                       |
| FinalSignedAt         | DATETIME      | 是       | -             | -    | 最终签发时间                                                       |
| ReviewStatus          | TINYINT       | 否       | 0             | -    | 审核状态：0=待审 1=审核中 2=审核完成                               |

#### 4.3 索引

| 索引名                         | 字段                             | 类型       | 说明                 |
| ------------------------------ | -------------------------------- | ---------- | -------------------- |
| PK_LabResult                   | ResultId                         | 主键       | 结果ID主键索引       |
| IX_LabResult_OrderItem         | OrderItemId                      | 非唯一索引 | 按医嘱明细查询的索引 |
| IX_LabResult_ReviewLevel       | CurrentReviewLevel               | 非唯一索引 | 按审核级别查询的索引 |
| IX_LabResult_TestingTechnician | TestingTechnicianId              | 非唯一索引 | 按检验技师查询的索引 |
| IX_LabResult_FinalSigner       | FinalSignerId                    | 非唯一索引 | 按签发医师查询的索引 |
| IX_LabResult_MeasuredAt        | MeasuredAt DESC                  | 非唯一索引 | 按检测时间倒序查询   |
| IX_LabResult_ReviewWorkList    | ReviewStatus, CurrentReviewLevel | 非唯一索引 | 审核工作列表查询索引 |

#### 4.4 约束

| 约束名                          | 类型    | 字段                               | 说明                              |
| ------------------------------- | ------- | ---------------------------------- | --------------------------------- |
| PK_LabResult                    | 主键    | ResultId                           | 主键约束                          |
| FK_LabResult_OrderItem          | 外键    | OrderItemId                        | 引用LabOrderItem表的OrderItemId   |
| FK_LabResult_Instrument         | 外键    | InstrumentId                       | 引用LabInstrument表的InstrumentId |
| FK_LabResult_TestingTechnician  | 外键    | TestingTechnicianId                | 引用DictDoctor表的DoctorId        |
| FK_LabResult_FinalSigner        | 外键    | FinalSignerId                      | 引用DictDoctor表的DoctorId        |
| CK_LabResult_ReviewLevel        | CHECK   | CurrentReviewLevel, MaxReviewLevel | 当前审核级别不能超过最大审核级别  |
| DF_LabResult_ReviewStatus       | DEFAULT | ReviewStatus                       | 默认值为0(待审)                   |
| DF_LabResult_CurrentReviewLevel | DEFAULT | CurrentReviewLevel                 | 默认值为0(未审核)                 |
| DF_LabResult_MaxReviewLevel     | DEFAULT | MaxReviewLevel                     | 默认值为2(需要复审)               |

#### 4.5 示例数据

```sql
-- 插入待审核的结果
INSERT INTO dbo.LabResult 
       (OrderItemId, InstrumentId, InstrItemCode, ResultValue, Unit, AbnormalFlag, 
        MeasuredAt, TestingTechnicianId, TestingTechnicianName, TestingCompletedAt)
VALUES (10001, 1, 'ALT', '42', 'U/L', 'H', GETDATE(), 101, '张技师', GETDATE());

-- 插入审核中的结果
INSERT INTO dbo.LabResult 
       (OrderItemId, InstrumentId, InstrItemCode, ResultValue, Unit, MeasuredAt,
        TestingTechnicianId, TestingTechnicianName, TestingCompletedAt,
        CurrentReviewLevel, ReviewStatus)
VALUES (10002, 1, 'AST', '38', 'U/L', GETDATE(),
        102, '李技师', GETDATE(), 1, 1);

-- 插入审核完成的结果
INSERT INTO dbo.LabResult 
       (OrderItemId, InstrumentId, InstrItemCode, ResultValue, Unit, MeasuredAt,
        TestingTechnicianId, TestingTechnicianName, TestingCompletedAt,
        CurrentReviewLevel, MaxReviewLevel, FinalSignerId, FinalSignerName, 
        FinalSignedAt, ReviewStatus)
VALUES (10003, 1, 'GGT', '75', 'U/L', GETDATE(),
        101, '张技师', DATEADD(MINUTE, -60, GETDATE()),
        2, 2, 201, '王主任', GETDATE(), 2);
```

### 5. LabResultReview 表

#### 5.1 表概述

**表名**：LabResultReview  
**说明**：检验结果审核记录表  
**用途**：记录检验结果的详细审核过程，支持多级审核，明确区分操作员和责任医师的概念。每一次审核操作都会在此表中产生一条记录。

#### 5.2 字段定义

| 字段名                 | 数据类型      | 允许NULL | 默认值        | 约束 | 说明                                                      |
| ---------------------- | ------------- | -------- | ------------- | ---- | --------------------------------------------------------- |
| ReviewId               | BIGINT        | 否       | IDENTITY(1,1) | 主键 | 审核记录ID，自增主键                                      |
| ResultId               | BIGINT        | 否       | -             | 外键 | 关联的检验结果ID（LabResult表外键）                       |
| ReviewLevel            | TINYINT       | 否       | -             | -    | 审核级别：1=初审 2=复审 3=三审...                         |
| ReviewAction           | TINYINT       | 否       | -             | -    | 审核动作：1=通过 2=退回 3=修改                            |
| OperatorId             | INT           | 否       | -             | 外键 | 操作员ID（实际登录系统执行审核操作的账号，关联SysUser表） |
| OperatorName           | NVARCHAR(100) | 否       | -             | -    | 操作员姓名（冗余存储，便于查询）                          |
| ResponsibleDoctorId    | INT           | 是       | -             | 外键 | 责任医师ID（承担医疗责任的医师，关联DictDoctor表）        |
| ResponsibleDoctorName  | NVARCHAR(100) | 是       | -             | -    | 责任医师姓名（冗余存储）                                  |
| ResponsibleDoctorTitle | NVARCHAR(50)  | 是       | -             | -    | 责任医师职称（如：主任医师、副主任医师、主治医师等）      |
| ReviewComment          | NVARCHAR(500) | 是       | -             | -    | 审核意见/备注                                             |
| ReviewedAt             | DATETIME      | 否       | GETDATE()     | -    | 审核时间                                                  |

#### 5.3 索引

| 索引名                      | 字段                                 | 类型       | 说明                   |
| --------------------------- | ------------------------------------ | ---------- | ---------------------- |
| PK_LabResultReview          | ReviewId                             | 主键       | 审核记录ID主键索引     |
| IX_LabResultReview_Result   | ResultId, ReviewLevel                | 非唯一索引 | 按结果和审核级别查询   |
| IX_LabResultReview_Operator | OperatorId, ReviewedAt DESC          | 非唯一索引 | 按操作员查询审核记录   |
| IX_LabResultReview_Doctor   | ResponsibleDoctorId, ReviewedAt DESC | 非唯一索引 | 按责任医师查询审核记录 |
| IX_LabResultReview_Time     | ReviewedAt DESC                      | 非唯一索引 | 按审核时间倒序查询     |

#### 5.4 约束

| 约束名                      | 类型  | 字段                                          | 说明                       |
| --------------------------- | ----- | --------------------------------------------- | -------------------------- |
| PK_LabResultReview          | 主键  | ReviewId                                      | 主键约束                   |
| FK_LabResultReview_Result   | 外键  | ResultId                                      | 引用LabResult表的ResultId  |
| FK_LabResultReview_Operator | 外键  | OperatorId                                    | 引用SysUser表的UserId      |
| FK_LabResultReview_Doctor   | 外键  | ResponsibleDoctorId                           | 引用DictDoctor表的DoctorId |
| CK_LabResultReview_Level    | CHECK | ReviewLevel                                   | 审核级别必须大于0          |
| CK_LabResultReview_Action   | CHECK | ReviewAction                                  | 审核动作必须在1-3范围内    |
| UQ_LabResultReview_Unique   | 唯一  | ResultId, ReviewLevel, OperatorId, ReviewedAt | 防止重复提交审核记录       |

#### 5.5 示例数据

```sql
-- 插入初审记录
INSERT INTO dbo.LabResultReview 
       (ResultId, ReviewLevel, ReviewAction, OperatorId, OperatorName, 
        ResponsibleDoctorId, ResponsibleDoctorName, ResponsibleDoctorTitle, ReviewComment)
VALUES (10001, 1, 1, 301, '技师小张', 201, '李主治', '主治医师', '结果正常，初审通过');

-- 插入复审记录
INSERT INTO dbo.LabResultReview 
       (ResultId, ReviewLevel, ReviewAction, OperatorId, OperatorName, 
        ResponsibleDoctorId, ResponsibleDoctorName, ResponsibleDoctorTitle, ReviewComment)
VALUES (10001, 2, 1, 302, '医师小王', 202, '张主任', '主任医师', '复审确认，准予发报告');

-- 插入退回记录
INSERT INTO dbo.LabResultReview 
       (ResultId, ReviewLevel, ReviewAction, OperatorId, OperatorName, 
        ResponsibleDoctorId, ResponsibleDoctorName, ResponsibleDoctorTitle, ReviewComment)
VALUES (10002, 1, 2, 301, '技师小张', 201, '李主治', '主治医师', '结果偏高，建议复查确认');
```

### 6. LabInstrItemMap 表

#### 6.1 表概述

**表名**：LabInstrItemMap  
**说明**：仪器-项目映射表  
**用途**：建立"仪器项目 ↔ LIS 项目"一一对应关系，解析器解析结果时用 InstrItemCode 找到 LisItemCode，便于不同仪器共用统一项目字典。

#### 5.2 字段定义

| 字段名        | 数据类型      | 允许NULL | 默认值        | 约束                           | 说明                                   |
| ------------- | ------------- | -------- | ------------- | ------------------------------ | -------------------------------------- |
| MapId         | BIGINT        | 否       | IDENTITY(1,1) | 主键                           | 主键ID（IDENTITY）                     |
| InstrumentId  | INT           | 否       | -             | 外键                           | 仪器ID（LabInstrument.InstrumentId）   |
| InstrItemCode | NVARCHAR(30)  | 否       | -             | -                              | 仪器项目代码（Raw报文中的项目标识）    |
| InstrItemName | NVARCHAR(100) | 否       | -             | -                              | 仪器项目名称                           |
| LabItemId     | INT           | 否       | -             | 外键                           | LIS项目ID（DictLabItem表外键）         |
| LisItemName   | NVARCHAR(100) | **是**   | -             | -                              | LIS项目名称（冗余存储，优化UI查询）    |
| ResultFormat  | CHAR(3)       | 否       | -             | CHECK (IN ('NUM','TXT','NEG')) | 结果格式：NUM=数值 TXT=文本 NEG=阴阳性 |
| Decimals      | TINYINT       | 是       | -             | -                              | 数值结果保留的小数位数（仅NUM时有效）  |
| CreatedAt     | DATETIME      | 否       | GETDATE()     | -                              | 创建时间                               |
| UpdatedAt     | DATETIME      | 否       | GETDATE()     | -                              | 更新时间                               |

#### 5.3 索引

| 索引名                  | 字段                        | 类型       | 说明                   |
| ----------------------- | --------------------------- | ---------- | ---------------------- |
| PK_LabInstrItemMap      | MapId                       | 主键       | 映射ID主键索引         |
| UQ_LabItemMap           | InstrumentId, InstrItemCode | 唯一索引   | 同一仪器项目唯一性约束 |
| IX_LabItemMap_LabItemId | LabItemId                   | 非唯一索引 | 按LIS项目ID查询的索引  |

#### 5.4 约束

| 约束名                   | 类型    | 字段                        | 说明                              |
| ------------------------ | ------- | --------------------------- | --------------------------------- |
| PK_LabInstrItemMap       | 主键    | MapId                       | 主键约束                          |
| FK_LabItemMap_Instrument | 外键    | InstrumentId                | 引用LabInstrument表的InstrumentId |
| FK_LabItemMap_LabItem    | 外键    | LabItemId                   | 引用DictLabItem表的LabItemId      |
| UQ_LabItemMap            | 唯一    | InstrumentId, InstrItemCode | 同一仪器下的项目代码必须唯一      |
| CK_LabItemMap_Format     | CHECK   | ResultFormat                | 检查ResultFormat值为NUM、TXT或NEG |
| DF_LabItemMap_CreatedAt  | DEFAULT | CreatedAt                   | 默认值为GETDATE()                 |
| DF_LabItemMap_UpdatedAt  | DEFAULT | UpdatedAt                   | 默认值为GETDATE()                 |

#### 5.5 示例数据

```sql
-- 迈瑞BS-240仪器项目映射示例
INSERT INTO dbo.LabInstrItemMap
       (InstrumentId, InstrItemCode, InstrItemName, LabItemId, LisItemName, ResultFormat, Decimals)
SELECT i.InstrumentId, 'ALT', N'丙氨酸氨基转移酶', l.LabItemId, l.LabItemName, 'NUM', 2
FROM dbo.LabInstrument i, dbo.DictLabItem l
WHERE i.InstrCode = 'BS240' AND l.LabItemCode = 'BIO_ALT';

INSERT INTO dbo.LabInstrItemMap
       (InstrumentId, InstrItemCode, InstrItemName, LabItemId, LisItemName, ResultFormat, Decimals)
SELECT i.InstrumentId, 'AST', N'天门冬氨酸氨基转移酶', l.LabItemId, l.LabItemName, 'NUM', 2
FROM dbo.LabInstrument i, dbo.DictLabItem l
WHERE i.InstrCode = 'BS240' AND l.LabItemCode = 'BIO_AST';

-- 罗氏Cobas 8000仪器项目映射示例
INSERT INTO dbo.LabInstrItemMap
       (InstrumentId, InstrItemCode, InstrItemName, LabItemId, LisItemName, ResultFormat, Decimals)
SELECT i.InstrumentId, 'ALT', N'ALT', l.LabItemId, l.LabItemName, 'NUM', 1
FROM dbo.LabInstrument i, dbo.DictLabItem l
WHERE i.InstrCode = 'COBAS8000' AND l.LabItemCode = 'BIO_ALT';

-- 阴阳性结果示例
INSERT INTO dbo.LabInstrItemMap
       (InstrumentId, InstrItemCode, InstrItemName, LabItemId, LisItemName, ResultFormat, Decimals)
SELECT i.InstrumentId, 'RF', N'类风湿因子', l.LabItemId, l.LabItemName, 'NEG', NULL
FROM dbo.LabInstrument i, dbo.DictLabItem l
WHERE i.InstrCode = 'COBAS8000' AND l.LabItemCode = 'IMM_RF';
```

### 7. DictSampleType 表

#### 7.1 表概述

**表名**：DictSampleType  
**说明**：标本类型字典表  
**用途**：用于维护样本类型基础信息并在 LIS、HIS、仪器之间保持一致。使用 Status 取代 IsActive 作为状态标识。

#### 7.2 字段定义

| 字段名          | 数据类型      | 允许NULL | 默认值        | 约束   | 说明                                         |
| --------------- | ------------- | -------- | ------------- | ------ | -------------------------------------------- |
| SampleTypeId    | INT           | 否       | IDENTITY(1,1) | 主键   | 自增主键                                     |
| SampleTypeCode  | NVARCHAR(20)  | 否       | -             | UNIQUE | 业务唯一编码（如 SR01 = Serum）              |
| SampleTypeName  | NVARCHAR(100) | 否       | -             | -      | 中文名称（血清、全血等）                     |
| PinYin          | NVARCHAR(100) | 是       | -             | -      | 中文名称对应拼音或首字母，方便检索           |
| EnglishName     | NVARCHAR(100) | 是       | -             | -      | 英文名称                                     |
| ColorHex        | CHAR(6)       | 是       | -             | -      | 采样管帽颜色（HEX 码，例 FF0000 = 红帽）     |
| ContainerType   | NVARCHAR(50)  | 是       | -             | -      | 容器/试管类型                                |
| DefaultVolumeMl | DECIMAL(5,2)  | 是       | -             | -      | 建议采样量（毫升）                           |
| PreTreatment    | NVARCHAR(200) | 是       | -             | -      | 预处理要求（如 离心 3000g×10min）            |
| Status          | BIT           | 否       | 1             | -      | 状态：1 = Active (启用)，0 = Inactive (停用) |
| CreatedAt       | DATETIME      | 否       | GETDATE()     | -      | 记录创建时间                                 |
| UpdatedAt       | DATETIME      | 否       | GETDATE()     | -      | 最近更新时间（应用层维护）                   |

#### 7.3 索引

| 索引名                   | 字段           | 类型       | 说明                 |
| ------------------------ | -------------- | ---------- | -------------------- |
| PK_DictSampleType        | SampleTypeId   | 主键       | 标本类型ID主键索引   |
| -                        | SampleTypeCode | 唯一索引   | 标本类型代码唯一索引 |
| IX_DictSampleType_Status | Status         | 非唯一索引 | 按状态查询的索引     |
| IX_DictSampleType_PinYin | PinYin         | 非唯一索引 | 按拼音查询的索引     |

#### 6.4 约束

| 约束名            | 类型    | 字段           | 说明              |
| ----------------- | ------- | -------------- | ----------------- |
| PK_DictSampleType | 主键    | SampleTypeId   | 主键约束          |
| -                 | 唯一    | SampleTypeCode | 唯一约束          |
| -                 | DEFAULT | Status         | 默认值为1(启用)   |
| -                 | DEFAULT | CreatedAt      | 默认值为GETDATE() |
| -                 | DEFAULT | UpdatedAt      | 默认值为GETDATE() |

#### 6.5 示例数据

```sql
-- 插入常见标本类型示例数据
INSERT INTO dbo.DictSampleType 
       (SampleTypeCode, SampleTypeName, PinYin, EnglishName, ColorHex, ContainerType, DefaultVolumeMl, PreTreatment)
VALUES  ('SR01', N'血清', N'XQ', N'Serum', 'FF0000', N'红帽真空管', 5.00, N'离心 3000g×10min'),
       ('PL01', N'血浆', N'XJ', N'Plasma', '8A2BE2', N'紫帽真空管(EDTA)', 3.00, N'离心 2500g×8min'),
       ('WB01', N'全血', N'QX', N'Whole Blood', '8A2BE2', N'紫帽真空管(EDTA)', 2.00, NULL),
       ('UR01', N'尿液', N'NY', N'Urine', 'FFFF00', N'尿杯', 10.00, N'离心 1500g×5min'),
       ('CSF01', N'脑脊液', N'NJY', N'Cerebrospinal Fluid', 'FFFFFF', N'无菌管', 2.00, NULL),
       ('ST01', N'粪便', N'FB', N'Stool', 'A52A2A', N'粪便采集盒', NULL, NULL);
```

### 8. DictLabItemCategory 表

#### 8.1 表概述

**表名**：DictLabItemCategory  
**说明**：检验项目类别字典表  
**用途**：用于维护检验项目类别信息，例如 生化、血液、免疫、微生物等。

#### 7.2 字段定义

| 字段名           | 数据类型      | 允许NULL | 默认值        | 约束   | 说明                                         |
| ---------------- | ------------- | -------- | ------------- | ------ | -------------------------------------------- |
| ItemCategoryId   | INT           | 否       | IDENTITY(1,1) | 主键   | 自增主键                                     |
| ItemCategoryCode | NVARCHAR(20)  | 否       | -             | UNIQUE | 业务唯一编码（如 SR01 = Serum）              |
| ItemCategoryName | NVARCHAR(100) | 否       | -             | -      | 中文名称（血清、全血等）                     |
| PinYin           | NVARCHAR(100) | 是       | -             | -      | 中文名称对应拼音或首字母，方便检索           |
| EnglishName      | NVARCHAR(100) | 是       | -             | -      | 英文名称                                     |
| Description      | NVARCHAR(200) | 是       | -             | -      | 类别描述                                     |
| Status           | BIT           | 否       | 1             | -      | 状态：1 = Active (启用)，0 = Inactive (停用) |
| CreatedAt        | DATETIME      | 否       | GETDATE()     | -      | 记录创建时间                                 |
| UpdatedAt        | DATETIME      | 否       | GETDATE()     | -      | 最近更新时间（应用层维护）                   |

#### 7.3 索引

| 索引名                        | 字段         | 类型       | 说明                 |
| ----------------------------- | ------------ | ---------- | -------------------- |
| PK_DictLabItemCategory        | CategoryId   | 主键       | 项目类别ID主键索引   |
| UQ_DictLabItemCategory_Code   | CategoryCode | 唯一索引   | 项目类别代码唯一索引 |
| IX_DictLabItemCategory_Status | Status       | 非唯一索引 | 按状态查询的索引     |
| IX_DictLabItemCategory_PinYin | PinYin       | 非唯一索引 | 按拼音查询的索引     |

#### 7.4 约束

| 约束名                        | 类型    | 字段         | 说明              |
| ----------------------------- | ------- | ------------ | ----------------- |
| PK_DictLabItemCategory        | 主键    | CategoryId   | 主键约束          |
| UQ_DictLabItemCategory_Code   | 唯一    | CategoryCode | 唯一约束          |
| DF_DictLabItemCategory_Status | DEFAULT | Status       | 默认值为1(启用)   |
| DF_DictLabItemCategory_Create | DEFAULT | CreatedAt    | 默认值为GETDATE() |
| DF_DictLabItemCategory_Update | DEFAULT | UpdatedAt    | 默认值为GETDATE() |

#### 7.5 示例数据

```sql
-- 插入常见项目类别示例数据
INSERT INTO dbo.DictLabItemCategory 
       (CategoryCode, CategoryName, PinYin, EnglishName, Description)
VALUES  ('BIO', N'临床化学', N'LCHX', N'Clinical Chemistry', N'俗称生化检验'),
        ('HEM', N'血液学', N'XYX', N'Hematology', N'血液细胞分析等'),
        ('IMM', N'临床免疫学', N'LCMY', N'Clinical Immunology', N'血清学、免疫功能检测等'),
        ('MIC', N'微生物学', N'WSWX', N'Microbiology', N'细菌培养、鉴定、药敏等'),
        ('MOL', N'分子诊断', N'FZZD', N'Molecular Diagnostics', N'PCR、基因测序等');
```

### 9. DictMethodCategory 表

#### 9.1 表概述

**表名**：DictMethodCategory  
**说明**：方法学类别字典表  
**用途**：用于维护检验项目的方法学类别信息，例如 生化法、免疫法、分子法等。

#### 8.2 字段定义

| 字段名             | 数据类型      | 允许NULL | 默认值        | 约束   | 说明           |
| ------------------ | ------------- | -------- | ------------- | ------ | -------------- |
| MethodCategoryId   | INT           | 否       | IDENTITY(1,1) | 主键   | 自增主键       |
| MethodCategoryCode | NVARCHAR(20)  | 否       |               | UNIQUE | 方法学类别编码 |
| MethodCategoryName | NVARCHAR(100) | 否       |               |        | 中文名称       |
| EnglishName        | NVARCHAR(100) | 是       |               |        | 英文名称       |
| PinYin             | NVARCHAR(100) | 是       |               |        | 拼音/首字母    |
| Description        | NVARCHAR(200) | 是       |               |        | 备注           |
| Status             | BIT           | 否       | 1             |        | 1=启用,0=停用  |
| CreatedAt          | DATETIME      | 否       | GETDATE()     |        | 创建时间       |
| UpdatedAt          | DATETIME      | 否       | GETDATE()     |        | 更新时间       |

#### 8.3 索引

| 索引名                       | 字段               | 类型       | 说明               |
| ---------------------------- | ------------------ | ---------- | ------------------ |
| PK_DictMethodCategory        | MethodCategoryId   | 主键       | 方法学类别ID主键   |
| UQ_DictMethodCategory_Code   | MethodCategoryCode | 唯一索引   | 方法学类别编码唯一 |
| IX_DictMethodCategory_Status | Status             | 非唯一索引 | 按状态查询         |
| IX_DictMethodCategory_PinYin | PinYin             | 非唯一索引 | 按拼音查询         |

#### 8.4 约束

| 约束名                        | 类型    | 字段               | 说明              |
| ----------------------------- | ------- | ------------------ | ----------------- |
| PK_DictMethodCategory         | 主键    | MethodCategoryId   | 主键约束          |
| UQ_DictMethodCategory_Code    | 唯一    | MethodCategoryCode | 唯一约束          |
| DF_DictMethodCategory_Status  | DEFAULT | Status             | 默认值为1(启用)   |
| DF_DictMethodCategory_Created | DEFAULT | CreatedAt          | 默认值为GETDATE() |
| DF_DictMethodCategory_Updated | DEFAULT | UpdatedAt          | 默认值为GETDATE() |

#### 8.5 示例数据

```sql
INSERT INTO dbo.DictMethodCategory
        (MethodCategoryCode, MethodCategoryName, EnglishName, PinYin, Description)
VALUES  ('BIO', N'生化法',   'Biochemical',   'SHF', N'干式/湿式生化分析方法'),
        ('IMM', N'免疫法',   'Immunological', 'MMF', N'化学发光、ELISA 等免疫学检测方法'),
        ('MOL', N'分子法',   'Molecular',     'FZF', N'PCR、测序等分子生物学方法');
```

### 10. DictMethod 表

#### 10.1 表概述

**表名**：DictMethod  
**说明**：检验方法学字典表  
**用途**：用于记录具体的检验方法，并将其归类到相应的`DictMethodCategory`方法学类别中。

#### 9.2 字段定义

| 字段名           | 数据类型      | 允许NULL | 默认值        | 约束                                          | 说明                    |
| ---------------- | ------------- | -------- | ------------- | --------------------------------------------- | ----------------------- |
| MethodId         | INT           | 否       | IDENTITY(1,1) | 主键                                          | 自增主键                |
| MethodCode       | NVARCHAR(20)  | 否       |               | UNIQUE                                        | 方法编码 (如 CLIA、PCR) |
| MethodName       | NVARCHAR(100) | 否       |               |                                               | 中文名称                |
| EnglishName      | NVARCHAR(100) | 是       |               |                                               | 英文名称                |
| PinYin           | NVARCHAR(100) | 是       |               |                                               | 拼音/首字母             |
| MethodCategoryId | INT           | 否       |               | 外键 (-> DictMethodCategory.MethodCategoryId) | 方法学类别ID            |
| Description      | NVARCHAR(200) | 是       |               |                                               | 备注                    |
| Status           | BIT           | 否       | 1             |                                               | 1=启用,0=停用           |
| CreatedAt        | DATETIME      | 否       | GETDATE()     |                                               | 创建时间                |
| UpdatedAt        | DATETIME      | 否       | GETDATE()     |                                               | 更新时间                |

#### 9.3 索引

| 索引名                       | 字段             | 类型       | 说明             |
| ---------------------------- | ---------------- | ---------- | ---------------- |
| PK_DictMethod                | MethodId         | 主键       | 方法ID主键       |
| UQ_DictMethod_Code           | MethodCode       | 唯一索引   | 方法编码唯一     |
| IX_DictMethod_Status         | Status           | 非唯一索引 | 按状态查询       |
| IX_DictMethod_PinYin         | PinYin           | 非唯一索引 | 按拼音查询       |
| IX_DictMethod_MethodCategory | MethodCategoryId | 非唯一索引 | 按方法学类别查询 |

#### 9.4 约束

| 约束名                  | 类型    | 字段             | 说明                                       |
| ----------------------- | ------- | ---------------- | ------------------------------------------ |
| PK_DictMethod           | 主键    | MethodId         | 主键约束                                   |
| UQ_DictMethod_Code      | 唯一    | MethodCode       | 唯一约束                                   |
| FK_DictMethod_Category  | 外键    | MethodCategoryId | 引用DictMethodCategory表的MethodCategoryId |
| DF_DictMethod_Status    | DEFAULT | Status           | 默认值为1(启用)                            |
| DF_DictMethod_CreatedAt | DEFAULT | CreatedAt        | 默认值为GETDATE()                          |
| DF_DictMethod_UpdatedAt | DEFAULT | UpdatedAt        | 默认值为GETDATE()                          |

#### 9.5 示例数据

```sql
INSERT INTO dbo.DictMethod (MethodCode, MethodName, EnglishName, PinYin, MethodCategoryId, Description)
SELECT 'Enz',  N'酶速率法',              'Enzymatic Rate',                     'MSLF', MethodCategoryId, N'计速法测定酶活'
FROM dbo.DictMethodCategory WHERE MethodCategoryCode = 'BIO'
UNION ALL
SELECT 'CLIA', N'化学发光免疫分析',      'Chemiluminescent Immunoassay',       'HXFG', MethodCategoryId, N'全自动化学发光'
FROM dbo.DictMethodCategory WHERE MethodCategoryCode = 'IMM'
UNION ALL
SELECT 'ELISA',N'酶联免疫吸附测定',      'Enzyme-linked Immunosorbent Assay',  'ELISA', MethodCategoryId, N'板式/条式 ELISA'
FROM dbo.DictMethodCategory WHERE MethodCategoryCode = 'IMM'
UNION ALL
SELECT 'PCR',  N'聚合酶链式反应',        'Polymerase Chain Reaction',          'PCR',  MethodCategoryId, N'荧光定量 PCR'
FROM dbo.DictMethodCategory WHERE MethodCategoryCode = 'MOL'
UNION ALL
SELECT 'NGS',  N'高通量测序',            'Next-Generation Sequencing',         'NGS',  MethodCategoryId, N'Illumina / Ion 平台测序'
FROM dbo.DictMethodCategory WHERE MethodCategoryCode = 'MOL';
```

### 11. DictLabItem 表

#### 11.1 表概述

**表名**：DictLabItem  
**说明**：检验项目字典表  
**用途**：统一管理LIS系统中的所有检验项目的基础信息，包括项目编码、名称、所属类别、默认标本类型、默认方法学、LOINC编码等核心属性。

#### 10.2 字段定义

| 字段名          | 数据类型      | 允许NULL | 默认值        | 约束                                     | 说明                        |
| --------------- | ------------- | -------- | ------------- | ---------------------------------------- | --------------------------- |
| LabItemId       | INT           | 否       | IDENTITY(1,1) | 主键                                     | 自增主键                    |
| LabItemCode     | NVARCHAR(30)  | 否       |               | UNIQUE                                   | 项目编码 (院内统一)         |
| LabItemName     | NVARCHAR(100) | 否       |               |                                          | 中文名称                    |
| EnglishName     | NVARCHAR(100) | 是       |               |                                          | 英文名称                    |
| PinYin          | NVARCHAR(100) | 是       |               |                                          | 拼音/首字母                 |
| ReportName      | NVARCHAR(100) | 是       |               |                                          | 报表名称                    |
| ItemCategoryId  | INT           | 否       |               | 外键 (-> DictLabItemCategory.CategoryId) | 项目类别ID                  |
| SampleTypeId    | INT           | 否       |               | 外键 (-> DictSampleType.SampleTypeId)    | 默认标本类型ID              |
| DefaultUnit     | NVARCHAR(20)  | 是       |               |                                          | 默认单位                    |
| Precision       | TINYINT       | 是       |               |                                          | 默认小数位                  |
| DefaultMethodId | INT           | 是       |               | 外键 (-> DictMethod.MethodId)            | 默认方法学ID                |
| LoincCode       | NVARCHAR(20)  | 是       |               |                                          | LOINC 码                    |
| IsCalculated    | BIT           | 否       | 0             |                                          | 是否为计算项目 (1=是, 0=否) |
| Status          | BIT           | 否       | 1             |                                          | 1=启用,0=停用               |
| CreatedAt       | DATETIME      | 否       | GETDATE()     |                                          | 创建时间                    |
| UpdatedAt       | DATETIME      | 否       | GETDATE()     |                                          | 更新时间                    |

#### 10.3 索引

| 索引名                    | 字段            | 类型       | 说明           |
| ------------------------- | --------------- | ---------- | -------------- |
| PK_DictLabItem            | LabItemId       | 主键       | 项目ID主键     |
| UQ_DictLabItem_Code       | LabItemCode     | 唯一索引   | 项目编码唯一   |
| IX_DictLabItem_Status     | Status          | 非唯一索引 | 按状态查询     |
| IX_DictLabItem_PinYin     | PinYin          | 非唯一索引 | 按拼音查询     |
| IX_DictLabItem_Category   | ItemCategoryId  | 非唯一索引 | 按项目类别查询 |
| IX_DictLabItem_SampleType | SampleTypeId    | 非唯一索引 | 按标本类型查询 |
| IX_DictLabItem_Method     | DefaultMethodId | 非唯一索引 | 按默认方法查询 |

#### 10.4 约束

| 约束名                   | 类型    | 字段            | 说明                                  |
| ------------------------ | ------- | --------------- | ------------------------------------- |
| PK_DictLabItem           | 主键    | LabItemId       | 主键约束                              |
| UQ_DictLabItem_Code      | 唯一    | LabItemCode     | 唯一约束                              |
| FK_LabItem_Category      | 外键    | ItemCategoryId  | 引用DictLabItemCategory表的CategoryId |
| FK_LabItem_SampleType    | 外键    | SampleTypeId    | 引用DictSampleType表的SampleTypeId    |
| FK_LabItem_Method        | 外键    | DefaultMethodId | 引用DictMethod表的MethodId            |
| DF_DictLabItem_Status    | DEFAULT | Status          | 默认值为1(启用)                       |
| DF_DictLabItem_CreatedAt | DEFAULT | CreatedAt       | 默认值为GETDATE()                     |
| DF_DictLabItem_UpdatedAt | DEFAULT | UpdatedAt       | 默认值为GETDATE()                     |

#### 10.5 示例数据

```sql
INSERT INTO dbo.DictLabItem
        (LabItemCode, LabItemName, EnglishName, PinYin,
         ItemCategoryId, SampleTypeId, DefaultUnit, Precision,
         DefaultMethodId, LoincCode)
SELECT 'BIO_ALT', N'谷丙转氨酶', 'ALT', 'GBZGA', 
       c.CategoryId, s.SampleTypeId, 'U/L', 0, m.MethodId, '1742-6'
FROM dbo.DictLabItemCategory c, dbo.DictSampleType s, dbo.DictMethod m
WHERE c.CategoryCode = 'BIO' AND s.SampleTypeCode = 'SR01' AND m.MethodCode = 'Enz'
UNION ALL
SELECT 'HEM_HGB', N'血红蛋白', 'HGB', 'XHDB', 
       c.CategoryId, s.SampleTypeId, 'g/L', 1, NULL, '718-7'
FROM dbo.DictLabItemCategory c, dbo.DictSampleType s
WHERE c.CategoryCode = 'HEM' AND s.SampleTypeCode = 'WB01'
UNION ALL
SELECT 'IMM_TSH', N'促甲状腺激素', 'TSH', 'CJZXJS', 
       c.CategoryId, s.SampleTypeId, 'mIU/L', 2, m.MethodId, '3016-3'
FROM dbo.DictLabItemCategory c, dbo.DictSampleType s, dbo.DictMethod m
WHERE c.CategoryCode = 'IMM' AND s.SampleTypeCode = 'SR01' AND m.MethodCode = 'CLIA';
```

### 12. DictLabItemRefRange 表

#### 12.1 表概述

**表名**：DictLabItemRefRange  
**说明**：检验项目参考区间表  
**用途**：按项目、方法学、性别、年龄段、时间段维护参考值上下限，支持多维度精确定义各检验项目的正常参考范围。

#### 11.2 字段定义

| 字段名          | 数据类型      | 允许NULL | 默认值        | 约束 | 说明                            |
| --------------- | ------------- | -------- | ------------- | ---- | ------------------------------- |
| RefRangeId      | BIGINT        | 否       | IDENTITY(1,1) | 主键 | 自增主键                        |
| LabItemId       | INT           | 否       | -             | 外键 | 检验项目ID (DictLabItem表外键)  |
| MethodId        | INT           | 是       | -             | 外键 | 方法学ID (NULL=通用)            |
| Sex             | NVARCHAR(2)   | 是       | -             | -    | 性别：M=男 F=女 N=不区分        |
| AgeLower        | DECIMAL(5,2)  | 是       | -             | -    | 年龄下限 (岁)                   |
| AgeUpper        | DECIMAL(5,2)  | 是       | -             | -    | 年龄上限 (岁)                   |
| AgeUnit         | NVARCHAR(2)   | 是       |               |      | 年龄单位年月周天                |
| LowerLimit      | NVARCHAR(20)  | 是       | -             | -    | 参考值下限                      |
| UpperLimit      | NVARCHAR(20)  | 是       | -             | -    | 参考值上限                      |
| TextualRefValue | NVARCHAR(100) | **是**   | -             | -    | 文本/定性结果参考值 (如 "阴性") |
| AlertLow        | NVARCHAR(20)  | **是**   | -             | -    | 警示值下限                      |
| AlertHigh       | NVARCHAR(20)  | **是**   | -             | -    | 警示值上限                      |
| CriticalLow     | NVARCHAR(20)  | **是**   | -             | -    | 危机值下限                      |
| CriticalHigh    | NVARCHAR(20)  | **是**   | -             | -    | 危机值上限                      |
| Unit            | NVARCHAR(20)  | 是       | -             | -    | 单位                            |
| Status          | BIT           | 否       | 1             | -    | 状态：1=启用 0=停用             |
| EffectiveFrom   | DATETIME      | 否       | GETDATE()     | -    | 生效时间                        |
| EffectiveTo     | DATETIME      | 是       | -             | -    | 失效时间 (NULL=永久有效)        |
| CreatedAt       | DATETIME      | 否       | GETDATE()     | -    | 创建时间                        |
| UpdatedAt       | DATETIME      | 否       | GETDATE()     | -    | 更新时间                        |

#### 11.3 索引

| 索引名                 | 字段                                                               | 类型       | 说明                     |
| ---------------------- | ------------------------------------------------------------------ | ---------- | ------------------------ |
| PK_DictLabItemRefRange | RefRangeId                                                         | 主键       | 主键索引                 |
| IX_RefRange_Match      | LabItemId, MethodId, Sex, AgeUnit, EffectiveFrom DESC, EffectiveTo | 非唯一索引 | 多条件匹配查询的复合索引 |

#### 11.4 约束

| 约束名                 | 类型    | 字段          | 说明                         |
| ---------------------- | ------- | ------------- | ---------------------------- |
| PK_DictLabItemRefRange | 主键    | RefRangeId    | 主键约束                     |
| FK_RefRange_LabItem    | 外键    | LabItemId     | 引用DictLabItem表的LabItemId |
| FK_RefRange_Method     | 外键    | MethodId      | 引用DictMethod表的MethodId   |
| DF_RefRange_Status     | DEFAULT | Status        | 默认值为1(启用)              |
| DF_RefRange_EffFrom    | DEFAULT | EffectiveFrom | 默认值为GETDATE()            |
| DF_RefRange_CreatedAt  | DEFAULT | CreatedAt     | 默认值为GETDATE()            |
| DF_RefRange_UpdatedAt  | DEFAULT | UpdatedAt     | 默认值为GETDATE()            |

#### 11.5 示例数据

```sql
-- ALT — 酶速率法专用区间（成人通用）
INSERT dbo.DictLabItemRefRange
      (LabItemId, MethodId, Sex, AgeLower, AgeUpper,
       LowerLimit, UpperLimit, Unit)
SELECT l.LabItemId, m.MethodId, 'N', 0, 150, '7', '40', 'U/L'
FROM dbo.DictLabItem l, dbo.DictMethod m
WHERE l.LabItemCode = 'BIO_ALT' AND m.MethodCode = 'Enz';

-- ALT — 比色法专用区间（成人通用）
INSERT dbo.DictLabItemRefRange
      (LabItemId, MethodId, Sex, AgeLower, AgeUpper,
       LowerLimit, UpperLimit, Unit)
SELECT l.LabItemId, m.MethodId, 'N', 0, 150, '5', '35', 'U/L'
FROM dbo.DictLabItem l, dbo.DictMethod m
WHERE l.LabItemCode = 'BIO_ALT' AND m.MethodCode = 'Color';

-- HGB — 男女有差异的参考区间（方法ID为NULL表示通用）
INSERT dbo.DictLabItemRefRange
      (LabItemId, MethodId, Sex, AgeLower, AgeUpper,
       LowerLimit, UpperLimit, Unit)
SELECT l.LabItemId, NULL, 'M', 18, 60, '130', '175', 'g/L'
FROM dbo.DictLabItem l
WHERE l.LabItemCode = 'HEM_HGB'
UNION ALL
SELECT l.LabItemId, NULL, 'F', 18, 60, '115', '150', 'g/L'
FROM dbo.DictLabItem l
WHERE l.LabItemCode = 'HEM_HGB';
```

### 13. DictVisitType 表

#### 13.1 表概述

**表名**：DictVisitType  
**说明**：就诊类型字典表  
**用途**：统一管理LIS系统中的就诊类型信息，如门诊、住院、急诊、体检等，并与HIS系统保持编码映射关系。

#### 12.2 字段定义

| 字段名           | 数据类型      | 允许NULL | 默认值        | 约束   | 说明                         |
| ---------------- | ------------- | -------- | ------------- | ------ | ---------------------------- |
| VisitTypeId      | INT           | 否       | IDENTITY(1,1) | 主键   | 自增主键                     |
| VisitTypeCode    | NVARCHAR(20)  | 否       | -             | UNIQUE | LIS内部就诊类型编码          |
| VisitTypeName    | NVARCHAR(100) | 否       | -             | -      | 中文名称（门诊/住院/急诊等） |
| EnglishName      | NVARCHAR(100) | 是       | -             | -      | 英文名称                     |
| HisVisitTypeCode | NVARCHAR(200) | 是       | -             | UNIQUE | HIS系统就诊类型编码          |
| PinYin           | NVARCHAR(100) | 是       | -             | -      | 拼音或首字母，用于快速检索   |
| Description      | NVARCHAR(200) | 是       | -             | -      | 备注说明                     |
| Status           | BIT           | 否       | 1             | -      | 状态：1=启用 0=停用          |
| CreatedAt        | DATETIME      | 否       | GETDATE()     | -      | 记录创建时间                 |
| UpdatedAt        | DATETIME      | 否       | GETDATE()     | -      | 最近更新时间，需在应用层维护 |

#### 12.3 索引

| 索引名                   | 字段             | 类型       | 说明                 |
| ------------------------ | ---------------- | ---------- | -------------------- |
| PK_DictVisitType         | VisitTypeId      | 主键       | 就诊类型ID主键索引   |
| UQ_DictVisitType_Code    | VisitTypeCode    | 唯一索引   | 就诊类型编码唯一索引 |
| IX_DictVisitType_Status  | Status           | 非唯一索引 | 按状态查询的索引     |
| IX_DictVisitType_HisCode | HisVisitTypeCode | 非唯一索引 | 按HIS编码查询的索引  |

#### 12.4 约束

| 约束名                     | 类型    | 字段          | 说明              |
| -------------------------- | ------- | ------------- | ----------------- |
| PK_DictVisitType           | 主键    | VisitTypeId   | 主键约束          |
| UQ_DictVisitType_Code      | 唯一    | VisitTypeCode | 唯一约束          |
| DF_DictVisitType_Status    | DEFAULT | Status        | 默认值为1(启用)   |
| DF_DictVisitType_CreatedAt | DEFAULT | CreatedAt     | 默认值为GETDATE() |
| DF_DictVisitType_UpdatedAt | DEFAULT | UpdatedAt     | 默认值为GETDATE() |

#### 12.5 示例数据

```sql
INSERT INTO dbo.DictVisitType 
       (VisitTypeCode, VisitTypeName, EnglishName, HisVisitTypeCode, PinYin, Description)
VALUES  ('OP', N'门诊', N'Outpatient', 'OP001', 'MZ', N'门诊患者'),
       ('IP', N'住院', N'Inpatient', 'IP001', 'ZY', N'住院患者'),
       ('ER', N'急诊', N'Emergency', 'ER001', 'JZ', N'急诊患者'),
       ('HC', N'体检', N'Health Check', 'HC001', 'TJ', N'健康体检');
```

### 14. DictDepartment 表

#### 14.1 表概述

**表名**：DictDepartment  
**说明**：科室字典表  
**用途**：统一管理医院科室信息，支持科室层级结构，并与HIS系统保持编码映射关系。

#### 13.2 字段定义

| 字段名       | 数据类型      | 允许NULL | 默认值        | 约束                            | 说明                       |
| ------------ | ------------- | -------- | ------------- | ------------------------------- | -------------------------- |
| DeptId       | INT           | 否       | IDENTITY(1,1) | 主键                            | 自增主键                   |
| DeptCode     | NVARCHAR(20)  | 否       | -             | UNIQUE                          | LIS科室编码                |
| DeptName     | NVARCHAR(100) | 否       | -             | -                               | 科室中文名称               |
| HisDeptCode  | NVARCHAR(200) | 是       | -             | UNIQUE                          | HIS科室编码                |
| PinYin       | NVARCHAR(100) | 是       | -             | -                               | 拼音/首字母                |
| ParentDeptId | INT           | 是       | -             | 外键 (-> DictDepartment.DeptId) | 上级科室ID（递归）         |
| DeptType     | NVARCHAR(20)  | 是       | -             | -                               | 科室类型（临床/医技/行政） |
| Description  | NVARCHAR(200) | 是       | -             | -                               | 备注                       |
| Status       | BIT           | 否       | 1             | -                               | 状态：1=启用 0=停用        |
| CreatedAt    | DATETIME      | 否       | GETDATE()     | -                               | 创建时间                   |
| UpdatedAt    | DATETIME      | 否       | GETDATE()     | -                               | 最近更新时间               |

#### 13.3 索引

| 索引名                     | 字段         | 类型       | 说明                |
| -------------------------- | ------------ | ---------- | ------------------- |
| PK_DictDepartment          | DeptId       | 主键       | 科室ID主键索引      |
| UQ_DictDepartment_Code     | DeptCode     | 唯一索引   | 科室编码唯一索引    |
| IX_DictDepartment_Status   | Status       | 非唯一索引 | 按状态查询的索引    |
| IX_DictDepartment_ParentId | ParentDeptId | 非唯一索引 | 按上级科室查询      |
| IX_DictDepartment_HisCode  | HisDeptCode  | 非唯一索引 | 按HIS编码查询的索引 |

#### 13.4 约束

| 约束名                      | 类型    | 字段         | 说明                         |
| --------------------------- | ------- | ------------ | ---------------------------- |
| PK_DictDepartment           | 主键    | DeptId       | 主键约束                     |
| UQ_DictDepartment_Code      | 唯一    | DeptCode     | 唯一约束                     |
| FK_Department_Parent        | 外键    | ParentDeptId | 引用DictDepartment表的DeptId |
| DF_DictDepartment_Status    | DEFAULT | Status       | 默认值为1(启用)              |
| DF_DictDepartment_CreatedAt | DEFAULT | CreatedAt    | 默认值为GETDATE()            |
| DF_DictDepartment_UpdatedAt | DEFAULT | UpdatedAt    | 默认值为GETDATE()            |

#### 13.5 示例数据

```sql
INSERT INTO dbo.DictDepartment 
       (DeptCode, DeptName, HisDeptCode, PinYin, DeptType, Description)
VALUES  ('LAB', N'检验科', 'LAB001', 'JYK', N'医技', N'临床检验科'),
       ('ICU', N'重症医学科', 'ICU001', 'ZSYXK', N'临床', N'重症监护病房'),
       ('CARD', N'心内科', 'CARD001', 'XNK', N'临床', N'心血管内科'),
       ('ORTH', N'骨科', 'ORTH001', 'GK', N'临床', N'骨科'),
       ('ADMIN', N'行政办公室', 'ADMIN001', 'XZBGS', N'行政', N'医院行政管理');
```

### 15. DictDoctor 表

#### 15.1 表概述

**表名**：DictDoctor  
**说明**：医生/检验人员字典表  
**用途**：统一管理医院医生和检验人员信息，包括基本信息、所属科室、职称等，并与HIS系统保持编码映射关系。

#### 14.2 字段定义

| 字段名        | 数据类型      | 允许NULL | 默认值        | 约束                            | 说明                |
| ------------- | ------------- | -------- | ------------- | ------------------------------- | ------------------- |
| DoctorId      | INT           | 否       | IDENTITY(1,1) | 主键                            | 自增主键            |
| DoctorCode    | NVARCHAR(20)  | 否       | -             | UNIQUE                          | LIS医生编码/工号    |
| DoctorName    | NVARCHAR(100) | 否       | -             | -                               | 中文姓名            |
| HisDoctorCode | NVARCHAR(200) | 是       | -             | UNIQUE                          | HIS医生编码         |
| PinYin        | NVARCHAR(100) | 是       | -             | -                               | 拼音/首字母         |
| DeptId        | INT           | 否       | -             | 外键 (-> DictDepartment.DeptId) | 所属科室ID          |
| Title         | NVARCHAR(50)  | 是       | -             | -                               | 职称                |
| LicenseNo     | NVARCHAR(50)  | 是       | -             | -                               | 执业证书号          |
| Mobile        | NVARCHAR(20)  | 是       | -             | -                               | 联系方式            |
| Status        | BIT           | 否       | 1             | -                               | 状态：1=启用 0=停用 |
| CreatedAt     | DATETIME      | 否       | GETDATE()     | -                               | 创建时间            |
| UpdatedAt     | DATETIME      | 否       | GETDATE()     | -                               | 最近更新时间        |

#### 14.3 索引

| 索引名                | 字段          | 类型       | 说明                |
| --------------------- | ------------- | ---------- | ------------------- |
| PK_DictDoctor         | DoctorId      | 主键       | 医生ID主键索引      |
| UQ_DictDoctor_Code    | DoctorCode    | 唯一索引   | 医生编码唯一索引    |
| IX_DictDoctor_Status  | Status        | 非唯一索引 | 按状态查询的索引    |
| IX_DictDoctor_DeptId  | DeptId        | 非唯一索引 | 按科室查询的索引    |
| IX_DictDoctor_HisCode | HisDoctorCode | 非唯一索引 | 按HIS编码查询的索引 |

#### 14.4 约束

| 约束名                  | 类型    | 字段       | 说明                         |
| ----------------------- | ------- | ---------- | ---------------------------- |
| PK_DictDoctor           | 主键    | DoctorId   | 主键约束                     |
| UQ_DictDoctor_Code      | 唯一    | DoctorCode | 唯一约束                     |
| FK_Doctor_Department    | 外键    | DeptId     | 引用DictDepartment表的DeptId |
| DF_DictDoctor_Status    | DEFAULT | Status     | 默认值为1(启用)              |
| DF_DictDoctor_CreatedAt | DEFAULT | CreatedAt  | 默认值为GETDATE()            |
| DF_DictDoctor_UpdatedAt | DEFAULT | UpdatedAt  | 默认值为GETDATE()            |

#### 14.5 示例数据

```sql
INSERT INTO dbo.DictDoctor 
       (DoctorCode, DoctorName, HisDoctorCode, PinYin, DeptId, Title, LicenseNo, Mobile)
SELECT 'DOC001', N'张三', 'HIS_DOC001', 'ZS', DeptId, N'主任技师', 'LIC001', '13800138001'
FROM dbo.DictDepartment WHERE DeptCode = 'LAB'
UNION ALL
SELECT 'DOC002', N'李四', 'HIS_DOC002', 'LS', DeptId, N'主任医师', 'LIC002', '13800138002'
FROM dbo.DictDepartment WHERE DeptCode = 'ICU'
UNION ALL
SELECT 'DOC003', N'王五', 'HIS_DOC003', 'WW', DeptId, N'副主任医师', 'LIC003', '13800138003'
FROM dbo.DictDepartment WHERE DeptCode = 'CARD'
UNION ALL
SELECT 'DOC004', N'赵六', 'HIS_DOC004', 'ZL', DeptId, N'主治医师', 'LIC004', '13800138004'
FROM dbo.DictDepartment WHERE DeptCode = 'ORTH';
```

### 16. DictSpecimenCondition 表

#### 16.1 表概述

**表名**：DictSpecimenCondition  
**说明**：标本状态字典表  
**用途**：统一管理标本质量和外观异常状态，如正常、溶血、脂血、黄疸等，为标本质量评估和结果审核提供标准化依据。

#### 15.2 字段定义

| 字段名       | 数据类型      | 允许NULL | 默认值        | 约束   | 说明                |
| ------------ | ------------- | -------- | ------------- | ------ | ------------------- |
| SpecCondId   | INT           | 否       | IDENTITY(1,1) | 主键   | 自增主键            |
| SpecCondCode | NVARCHAR(20)  | 否       | -             | UNIQUE | LIS内部编码         |
| SpecCondName | NVARCHAR(100) | 否       | -             | -      | 中文名称            |
| EnglishName  | NVARCHAR(100) | 是       | -             | -      | 英文名称            |
| PinYin       | NVARCHAR(100) | 是       | -             | -      | 拼音/首字母         |
| SequenceNo   | INT           | 是       | -             | -      | 显示顺序            |
| Description  | NVARCHAR(200) | 是       | -             | -      | 备注说明            |
| Status       | BIT           | 否       | 1             | -      | 状态：1=启用 0=停用 |
| CreatedAt    | DATETIME      | 否       | GETDATE()     | -      | 创建时间            |
| UpdatedAt    | DATETIME      | 否       | GETDATE()     | -      | 最近更新时间        |

#### 15.3 索引

| 索引名                          | 字段         | 类型       | 说明                 |
| ------------------------------- | ------------ | ---------- | -------------------- |
| PK_DictSpecimenCondition        | SpecCondId   | 主键       | 标本状态ID主键索引   |
| UQ_DictSpecimenCondition_Code   | SpecCondCode | 唯一索引   | 标本状态编码唯一索引 |
| IX_DictSpecimenCondition_Status | Status       | 非唯一索引 | 按状态查询的索引     |
| IX_DictSpecimenCondition_PinYin | PinYin       | 非唯一索引 | 按拼音查询的索引     |

#### 15.4 约束

| 约束名                             | 类型    | 字段         | 说明              |
| ---------------------------------- | ------- | ------------ | ----------------- |
| PK_DictSpecimenCondition           | 主键    | SpecCondId   | 主键约束          |
| UQ_DictSpecimenCondition_Code      | 唯一    | SpecCondCode | 唯一约束          |
| DF_DictSpecimenCondition_Status    | DEFAULT | Status       | 默认值为1(启用)   |
| DF_DictSpecimenCondition_CreatedAt | DEFAULT | CreatedAt    | 默认值为GETDATE() |
| DF_DictSpecimenCondition_UpdatedAt | DEFAULT | UpdatedAt    | 默认值为GETDATE() |

#### 15.5 示例数据

```sql
INSERT INTO dbo.DictSpecimenCondition
       (SpecCondCode, SpecCondName, EnglishName, PinYin, SequenceNo, Description)
VALUES ('NORMAL', N'正常', 'Normal', 'ZC', 1, N'标本外观正常，无异常'),
       ('HEMOLYSIS', N'溶血', 'Hemolysis', 'RX', 2, N'标本出现溶血现象'),
       ('LIPEMIA', N'脂血', 'Lipemia', 'ZX', 3, N'标本呈乳白色，脂质含量高'),
       ('ICTERUS', N'黄疸', 'Icterus', 'HD', 4, N'标本呈黄色，胆红素含量高'),
       ('CLOT', N'凝块', 'Clot', 'NK', 5, N'标本中有凝血块'),
       ('INSUFFICIENT', N'标本量不足', 'Insufficient Volume', 'BBLBZ', 6, N'采集的标本量不够检测要求');
```

### 17. DictSpecimenParam 表

#### 17.1 表概述

**表名**：DictSpecimenParam
**说明**：标本参数字典表
**用途**：统一管理仪器可能回报的标本固有参数或质量指标，如血细胞比容(HCT)、溶血指数(H)、脂血指数(L)、黄疸指数(I)等，为标本质量自动化评估提供标准化数据基础。

#### 16.2 字段定义

| 字段名      | 数据类型      | 允许NULL | 默认值        | 约束   | 说明                                   |
| ----------- | ------------- | -------- | ------------- | ------ | -------------------------------------- |
| ParamId     | INT           | 否       | IDENTITY(1,1) | 主键   | 自增主键                               |
| ParamCode   | NVARCHAR(20)  | 否       | -             | UNIQUE | LIS内部参数编码                        |
| ParamName   | NVARCHAR(100) | 否       | -             | -      | 中文名称                               |
| EnglishName | NVARCHAR(100) | 是       | -             | -      | 英文名称                               |
| PinYin      | NVARCHAR(100) | 是       | -             | -      | 拼音/首字母                            |
| ParamType   | INT           | 否       | 0             | -      | 参数类型：0=常规参数 1=质量指数 2=其他 |
| Unit        | NVARCHAR(20)  | 是       | -             | -      | 参数单位（如 %、Index等）              |
| Description | NVARCHAR(200) | 是       | -             | -      | 备注说明                               |
| Status      | BIT           | 否       | 1             | -      | 状态：1=启用 0=停用                    |
| CreatedAt   | DATETIME      | 否       | GETDATE()     | -      | 创建时间                               |
| UpdatedAt   | DATETIME      | 否       | GETDATE()     | -      | 最近更新时间                           |

#### 16.3 索引

| 索引名                      | 字段      | 类型       | 说明             |
| --------------------------- | --------- | ---------- | ---------------- |
| PK_DictSpecimenParam        | ParamId   | 主键       | 参数ID主键索引   |
| UQ_DictSpecimenParam_Code   | ParamCode | 唯一索引   | 参数编码唯一索引 |
| IX_DictSpecimenParam_Status | Status    | 非唯一索引 | 按状态查询的索引 |
| IX_DictSpecimenParam_PinYin | PinYin    | 非唯一索引 | 按拼音查询的索引 |

#### 16.4 约束

| 约束名                         | 类型    | 字段      | 说明                |
| ------------------------------ | ------- | --------- | ------------------- |
| PK_DictSpecimenParam           | 主键    | ParamId   | 主键约束            |
| UQ_DictSpecimenParam_Code      | 唯一    | ParamCode | 唯一约束            |
| DF_DictSpecimenParam_ParamType | DEFAULT | ParamType | 默认值为0(常规参数) |
| DF_DictSpecimenParam_Status    | DEFAULT | Status    | 默认值为1(启用)     |
| DF_DictSpecimenParam_CreatedAt | DEFAULT | CreatedAt | 默认值为GETDATE()   |
| DF_DictSpecimenParam_UpdatedAt | DEFAULT | UpdatedAt | 默认值为GETDATE()   |

#### 16.5 示例数据

```sql
INSERT INTO dbo.DictSpecimenParam 
       (ParamCode, ParamName, EnglishName, PinYin, ParamType, Unit, Description)
VALUES ('HCT', N'血细胞比容', 'Hematocrit', 'XBBCT', 0, '%', N'由特定仪器在检测全血标本时回报'),
       ('H_INDEX', N'溶血指数', 'Hemolysis Index', 'RXZS', 1, 'Index', N'标本质量指标，反映红细胞破坏程度'),
       ('L_INDEX', N'脂血指数', 'Lipemia Index', 'ZXZS', 1, 'Index', N'标本质量指标，反映样本中甘油三酯浓度'),
       ('I_INDEX', N'黄疸指数', 'Icterus Index', 'HDZS', 1, 'Index', N'标本质量指标，反映样本中胆红素浓度');
```

### 18. LabInstrSpecimenParamMap 表

#### 18.1 表概述

**表名**：LabInstrSpecimenParamMap
**说明**：仪器-标本参数映射表
**用途**：建立"仪器标本参数代码 ↔ LIS标本参数字典"的对应关系。例如，将QR-100仪器回报的参数类型 0x21 映射到LIS中的 HCT 参数。

#### 17.2 字段定义

| 字段名           | 数据类型      | 允许NULL | 默认值        | 约束                        | 说明                              |
| ---------------- | ------------- | -------- | ------------- | --------------------------- | --------------------------------- |
| MapId            | BIGINT        | 否       | IDENTITY(1,1) | 主键                        | 主键ID                            |
| InstrumentId     | INT           | 否       | -             | 外键 (-> LabInstrument)     | 仪器ID                            |
| InstrParamCode   | NVARCHAR(30)  | 否       | -             | -                           | 仪器端参数代码 (如QR-100的0x21)   |
| InstrParamName   | NVARCHAR(100) | 是       | -             | -                           | 仪器端参数名称 (冗余，便于UI显示) |
| ParamId          | INT           | 否       | -             | 外键 (-> DictSpecimenParam) | LIS标本参数ID                     |
| ConversionFactor | DECIMAL(10,4) | 是       | 1.0           | -                           | 转换系数（仪器值×系数=标准值）    |
| ConversionOffset | DECIMAL(10,4) | 是       | 0.0           | -                           | 转换偏移量（转换后+偏移量）       |
| Status           | BIT           | 否       | 1             | -                           | 状态：1=启用 0=停用               |
| CreatedAt        | DATETIME      | 否       | GETDATE()     | -                           | 创建时间                          |
| UpdatedAt        | DATETIME      | 否       | GETDATE()     | -                           | 更新时间                          |

#### 17.3 索引

| 索引名                      | 字段                         | 类型       | 说明                         |
| --------------------------- | ---------------------------- | ---------- | ---------------------------- |
| PK_LabInstrSpecimenParamMap | MapId                        | 主键       | 映射ID主键索引               |
| UQ_LabSpecimenParamMap      | InstrumentId, InstrParamCode | 唯一索引   | 同一仪器下的参数代码必须唯一 |
| IX_SpecimenParamMap_ParamId | ParamId                      | 非唯一索引 | 按LIS参数ID查询的索引        |

#### 17.4 约束

| 约束名                         | 类型 | 字段                         | 说明                              |
| ------------------------------ | ---- | ---------------------------- | --------------------------------- |
| PK_LabInstrSpecimenParamMap    | 主键 | MapId                        | 主键约束                          |
| UQ_LabSpecimenParamMap         | 唯一 | InstrumentId, InstrParamCode | 同一仪器下的参数代码必须唯一      |
| FK_SpecimenParamMap_Instrument | 外键 | InstrumentId                 | 引用LabInstrument表的InstrumentId |
| FK_SpecimenParamMap_Param      | 外键 | ParamId                      | 引用DictSpecimenParam表的ParamId  |

#### 17.5 示例数据

```sql
-- 示例：为汇松科技QR-100仪器(假设其InstrumentId=15)配置标本参数映射
-- 将其协议中的"标本参数值类型字节"0x21映射为LIS中的HCT参数(假设其ParamId=1)
INSERT INTO dbo.LabInstrSpecimenParamMap 
       (InstrumentId, InstrParamCode, InstrParamName, ParamId, LisParamName)
SELECT i.InstrumentId, '21', N'HCT', p.ParamId, p.ParamName
FROM dbo.LabInstrument i, dbo.DictSpecimenParam p
WHERE i.InstrCode = 'QR100' AND p.ParamCode = 'HCT';
```

### 19. LabOrder 表

#### 19.1 表概述

**表名**：LabOrder
**说明**：检验申请主表
**用途**：记录由HIS或手工创建的检验申请的头信息。一张申请单（如生化全项+血常规）对应一条记录，它可能包含一个或多个标本。

#### 18.2 字段定义

| 字段名             | 数据类型      | 允许NULL | 默认值        | 约束                                | 说明                                                         |
| ------------------ | ------------- | -------- | ------------- | ----------------------------------- | ------------------------------------------------------------ |
| OrderId            | BIGINT        | 否       | IDENTITY(1,1) | 主键                                | LIS内部申请单ID，自增主键                                    |
| HisOrderCode       | NVARCHAR(50)  | 是       | -             | UNIQUE                              | 对应的HIS系统医嘱号/申请单号，用于系统间对接，手工单可为空   |
| VisitId            | NVARCHAR(50)  | 否       | -             | -                                   | 患者就诊ID（门诊号/住院号等）                                |
| PatientId          | NVARCHAR(50)  | 否       | -             | -                                   | 患者唯一标识（如患者ID/身份证号）                            |
| PatientName        | NVARCHAR(100) | 否       | -             | -                                   | 患者姓名（冗余存储，便于查询显示）                           |
| PatientSex         | CHAR(2)       | 是       | -             |                                     | 患者性别：M=男, F=女, U=未知。匹配参考范围的核心字段，应在创建时冗余。 |
| PatientBirthDate   | DATE          | 是       | -             | -                                   | 患者出生日期：用于精确计算年龄，匹配参考范围的核心字段，应在创建时冗余。 |
| PatientAge         | INT           | 是       |               |                                     | 患者年龄（冗余字段便于查询）                                 |
| PatientAgeUnit     | CHAR(2)       | 是       |               |                                     | 年龄单位                                                     |
| BedNo              | NVARCHAR(20)  | 是       | -             | -                                   | 床号：住院患者的关键信息，用于标本采集和报告分发。           |
| VisitTypeId        | INT           | 否       | -             | 外键 (-> DictVisitType.VisitTypeId) | 就诊类型ID（门诊/住院/急诊/体检）                            |
| RequestingDeptId   | INT           | 否       | -             | 外键 (-> DictDepartment.DeptId)     | 申请科室ID                                                   |
| RequestingDoctorId | INT           | 否       | -             | 外键 (-> DictDoctor.DoctorId)       | 申请医生ID                                                   |
| OrderStatus        | TINYINT       | 否       | 0             | CHECK (0,1,2,3,4,9)                 | 申请单状态：0=已开立 1=部分采集 2=全部采集 3=部分完成 4=全部完成 9=已取消 |
| Priority           | TINYINT       | 否       | 0             | CHECK (0,1)                         | 优先级：0=常规(Normal) 1=紧急(STAT)                          |
| ClinicalDiagnosis  | NVARCHAR(500) | 是       | -             | -                                   | 临床诊断信息                                                 |
| ReportComment      | NVARCHAR(500) | 是       | -             | -                                   | 报告评价/总结：由审核医生填写的对本次检验的综合性结论或建议，显示在报告单上。 |
| OrderDateTime      | DATETIME      | 否       | GETDATE()     | -                                   | 医嘱开立时间                                                 |
| CreatedAt          | DATETIME      | 否       | GETDATE()     | -                                   | 记录创建时间                                                 |
| CreatedById        | INT           | 是       | -             | 外键 (-> SysUser)                   | 创建人ID                                                     |
| CreatedByName      | NVARCHAR(100) | 是       | -             | -                                   | 创建人姓名（冗余）                                           |
| UpdatedAt          | DATETIME      | 否       | GETDATE()     | -                                   | 记录更新时间                                                 |
| UpdatedById        | INT           | 是       | -             | 外键 (-> SysUser)                   | 最后更新人ID。                                               |
| UpdatedByName      | NVARCHAR(100) | 是       | -             | -                                   | 最后更新人姓名（冗余）。                                     |

#### 18.3 索引

| 索引名                  | 字段                       | 类型       | 说明                           |
| ----------------------- | -------------------------- | ---------- | ------------------------------ |
| PK_LabOrder             | OrderId                    | 主键       | 申请单ID主键索引               |
| IX_LabOrder_VisitId     | VisitId                    | 非唯一索引 | 按就诊ID查询                   |
| IX_LabOrder_Patient     | PatientId, PatientName     | 非唯一索引 | 按患者信息查询                 |
| IX_LabOrder_Status_Time | OrderStatus, OrderDateTime | 非唯一索引 | 按状态和时间查询，用于工作列表 |
| IX_LabOrder_HisCode     | HisOrderCode               | 非唯一索引 | 按HIS编码查询的索引            |

#### 18.4 约束

| 约束名             | 类型    | 字段               | 说明                             |
| ------------------ | ------- | ------------------ | -------------------------------- |
| PK_LabOrder        | 主键    | OrderId            | 主键约束                         |
| FK_Order_VisitType | 外键    | VisitTypeId        | 引用DictVisitType表的VisitTypeId |
| FK_Order_Dept      | 外键    | RequestingDeptId   | 引用DictDepartment表的DeptId     |
| FK_Order_Doctor    | 外键    | RequestingDoctorId | 引用DictDoctor表的DoctorId       |
| CK_Order_Status    | CHECK   | OrderStatus        | 检查OrderStatus值的有效性        |
| DF_Order_Status    | DEFAULT | OrderStatus        | 默认值为0 (已开立)               |
| CK_Order_Priority  | CHECK   | Priority           | 检查Priority值的有效性           |
| DF_Order_Priority  | DEFAULT | Priority           | 默认值为0 (常规)                 |

#### 18.5 示例数据

```sql
INSERT INTO dbo.LabOrder
       (HisOrderCode, VisitId, PatientId, PatientName, VisitTypeId, 
        RequestingDeptId, RequestingDoctorId, ClinicalDiagnosis)
SELECT 'REQ20240521001', 'IP123456', 'PAT009876', N'王大锤', vt.VisitTypeId,
       d.DeptId, dr.DoctorId, N'疑似肝功能异常'
FROM dbo.DictVisitType vt, dbo.DictDepartment d, dbo.DictDoctor dr
WHERE vt.VisitTypeCode = 'IP' AND d.DeptCode = 'CARD' AND dr.DoctorCode = 'DOC003';
```

### 20. LabSpecimen 表

#### 20.1 表概述

**表名**：LabSpecimen
**说明**：检验标本信息表
**用途**：管理与检验申请关联的物理标本。核心是**条码**，追踪标本从采集到废弃的全生命周期状态。一个LabOrder可以对应多个LabSpecimen（如血清管+全血管）。

#### 19.2 字段定义

| 字段名          | 数据类型      | 允许NULL | 默认值        | 约束                                  | 说明                                                                                       |
| --------------- | ------------- | -------- | ------------- | ------------------------------------- | ------------------------------------------------------------------------------------------ |
| SpecimenId      | BIGINT        | 否       | IDENTITY(1,1) | 主键                                  | LIS内部标本ID，自增主键                                                                    |
| OrderId         | BIGINT        | 否       | -             | 外键 (-> LabOrder.OrderId)            | 关联的申请单ID                                                                             |
| Barcode         | NVARCHAR(30)  | 否       | -             | UNIQUE                                | 标本唯一条码                                                                               |
| SeqPrefix       | NVARCHAR(20)  | 是       | -             | -                                     | **样本号前缀**。可选字段。用于标识样本批次（如 'A' 批）、类型（如 'U' 代表尿液）或机架号。 |
| DailySeqNo      | INT           | 是       | -             | -                                     | **当日流水号**。与 SpecimenDate 和可选的 SeqPrefix 组合，构成完整的样本号。                |
| SpecimenDate    | DATE          | 是       | -             | -                                     | **标本日期**。与 SeqPrefix 和 DailySeqNo 配合使用，确保复合样本号在特定日期内的唯一性。    |
| SampleTypeId    | INT           | 否       | -             | 外键 (-> DictSampleType.SampleTypeId) | 标本类型ID（血清/全血/尿液等）                                                             |
| SpecimenStatus  | TINYINT       | 否       | 10            | CHECK (10,20,30,40,90,99)             | 标本状态：10=待采集 20=已采集 30=已接收 40=检测中/完成 90=已废弃 99=已作废/拒收            |
| CollectedById   | INT           | 是       | -             | 外键 (-> SysUser)                     | 采集人ID                                                                                   |
| CollectedByName | NVARCHAR(100) | 是       | -             | -                                     | 采集人姓名（冗余）                                                                         |
| CollectedAt     | DATETIME      | 是       | -             | -                                     | 采集时间                                                                                   |
| ReceivedById    | INT           | 是       | -             | 外键 (-> SysUser)                     | 接收人ID                                                                                   |
| ReceivedByName  | NVARCHAR(100) | 是       | -             | -                                     | 接收人姓名（冗余）                                                                         |
| ReceivedAt      | DATETIME      | 是       | -             | -                                     | 接收时间                                                                                   |
| SpecCondId      | INT           | 是       | -             | 外键 (-> DictSpecimenCondition)       | 标本状态ID（溶血/脂血等）                                                                  |
| RejectReason    | NVARCHAR(200) | 是       | -             | -                                     | 拒收原因（当SpecimenStatus=99时填写）                                                      |
| Remarks         | NVARCHAR(500) | 是       | -             | -                                     | 标本备注：用于记录非拒收原因的标本相关信息，如"轻微溶血"、"乳糜血"等。                     |
| CreatedAt       | DATETIME      | 否       | GETDATE()     | -                                     | 记录创建时间                                                                               |
| CreatedById     | INT           | 是       | -             | 外键 (-> SysUser)                     | 创建人ID                                                                                   |
| CreatedByName   | NVARCHAR(100) | 是       | -             | -                                     | 创建人姓名（冗余）                                                                         |
| UpdatedAt       | DATETIME      | 否       | GETDATE()     | -                                     | 记录更新时间                                                                               |
| UpdatedById     | INT           | 是       | -             | 外键 (-> SysUser)                     | 最后更新人ID。                                                                             |
| UpdatedByName   | NVARCHAR(100) | 是       | -             | -                                     | 最后更新人姓名（冗余）。                                                                   |

#### 19.3 索引

| 索引名                     | 字段                                | 类型       | 说明                           |
| -------------------------- | ----------------------------------- | ---------- | ------------------------------ |
| PK_LabSpecimen             | SpecimenId                          | 主键       | 标本ID主键索引                 |
| UQ_LabSpecimen_Barcode     | Barcode                             | 唯一索引   | 标本条码唯一，核心查询字段     |
| UQ_LabSpecimen_DailySeq    | SpecimenDate, SeqPrefix, DailySeqNo | 唯一索引   | 确保"当日复合样本号"的唯一性。 |
| IX_LabSpecimen_OrderId     | OrderId                             | 非唯一索引 | 按申请单ID查询其所有标本       |
| IX_LabSpecimen_Status_Time | SpecimenStatus, ReceivedAt          | 非唯一索引 | 按状态和时间查询，用于工作列表 |

#### 19.4 约束

| 约束名                 | 类型    | 字段           | 说明                                  |
| ---------------------- | ------- | -------------- | ------------------------------------- |
| PK_LabSpecimen         | 主键    | SpecimenId     | 主键约束                              |
| FK_Specimen_Order      | 外键    | OrderId        | 引用LabOrder表的OrderId               |
| FK_Specimen_SampleType | 外键    | SampleTypeId   | 引用DictSampleType表的SampleTypeId    |
| FK_Specimen_Condition  | 外键    | SpecCondId     | 引用DictSpecimenCondition的SpecCondId |
| CK_Specimen_Status     | CHECK   | SpecimenStatus | 检查SpecimenStatus值的有效性          |
| DF_Specimen_Status     | DEFAULT | SpecimenStatus | 默认值为10 (待采集)                   |

#### 19.5 示例数据

```sql
-- 为上面的申请单创建一个血清标本
INSERT INTO dbo.LabSpecimen (OrderId, Barcode, SampleTypeId)
SELECT o.OrderId, 'S202405210001', st.SampleTypeId
FROM dbo.LabOrder o, dbo.DictSampleType st
WHERE o.HisOrderCode = 'REQ20240521001' AND st.SampleTypeCode = 'SR01';

-- 为上面的申请单再创建一个全血标本
INSERT INTO dbo.LabSpecimen (OrderId, Barcode, SampleTypeId)
SELECT o.OrderId, 'S202405210002', st.SampleTypeId
FROM dbo.LabOrder o, dbo.DictSampleType st
WHERE o.HisOrderCode = 'REQ20240521001' AND st.SampleTypeCode = 'WB01';
```

### 21. LabOrderItem 表

#### 21.1 表概述

**表名**：LabOrderItem
**说明**：检验医嘱明细表
**用途**：记录一个标本上具体要做的检验项目。**这是 LabResult 表中 OrderItemId 字段的外键关联目标**，将最终结果与具体的检验申请项目挂钩。

#### 20.2 字段定义

| 字段名                 | 数据类型      | 允许NULL | 默认值        | 约束                               | 说明                                                                                       |
| ---------------------- | ------------- | -------- | ------------- | ---------------------------------- | ------------------------------------------------------------------------------------------ |
| OrderItemId            | BIGINT        | 否       | IDENTITY(1,1) | 主键                               | 检验项目明细ID，自增主键                                                                   |
| SpecimenId             | BIGINT        | 否       | -             | 外键 (-> LabSpecimen.SpecimenId)   | 关联的标本ID                                                                               |
| LabItemId              | INT           | 否       | -             | 外键 (-> DictLabItem.LabItemId)    | 关联的LIS项目ID                                                                            |
| ProfileId              | INT           | 是       | -             | 外键 (-> DictLabProfile.ProfileId) | 关联检验组合ID                                                                             |
| AssignedTechnicianId   | INT           | 是       | -             | 外键 (-> DictDoctor.DoctorId)      | 分配的检验技师ID                                                                           |
| AssignedTechnicianName | NVARCHAR(100) | 是       | -             | -                                  | 分配的检验技师姓名（冗余存储）                                                             |
| ExecutedById           | INT           | 是       | -             | 外键 (-> DictDoctor.DoctorId)      | 实际执行检验的人员ID                                                                       |
| ExecutedByName         | NVARCHAR(100) | 是       | -             | -                                  | 实际执行检验的人员姓名（冗余存储）                                                         |
| ExecutedAt             | DATETIME      | 是       | -             | -                                  | 检验执行时间                                                                               |
| ItemStatus             | TINYINT       | 否       | 0             | CHECK (0,1,2,3,5,6,7,9)            | 项目状态：0=已申请 1=已上机/检测中 2=已出结果 3=已审核 5=质控中 6=重检 7=标本异常 9=已取消 |
| CreatedAt              | DATETIME      | 否       | GETDATE()     | -                                  | 记录创建时间 (UTC)                                                                         |
| UpdatedAt              | DATETIME      | 否       | GETDATE()     | -                                  | 记录更新时间 (UTC)                                                                         |

#### 20.3 索引

| 索引名                       | 字段                 | 类型       | 说明                         |
| ---------------------------- | -------------------- | ---------- | ---------------------------- |
| PK_LabOrderItem              | OrderItemId          | 主键       | 项目明细ID主键索引           |
| IX_LabOrderItem_Specimen     | SpecimenId           | 非唯一索引 | 按标本ID查询其所有项目       |
| IX_LabOrderItem_LabItem      | LabItemId            | 非唯一索引 | 按LIS项目ID查询              |
| IX_LabOrderItem_Status       | ItemStatus           | 非唯一索引 | 按项目状态查询，用于工作列表 |
| IX_LabOrderItem_AssignedTech | AssignedTechnicianId | 非唯一索引 | 按分配技师查询               |
| IX_LabOrderItem_ExecutedBy   | ExecutedById         | 非唯一索引 | 按执行人员查询               |
| IX_LabOrderItem_ExecutedAt   | ExecutedAt DESC      | 非唯一索引 | 按执行时间倒序查询           |

#### 20.4 约束

| 约束名                       | 类型    | 字段                 | 说明                                      |
| ---------------------------- | ------- | -------------------- | ----------------------------------------- |
| PK_LabOrderItem              | 主键    | OrderItemId          | 主键约束                                  |
| FK_OrderItem_Specimen        | 外键    | SpecimenId           | 引用LabSpecimen表的SpecimenId             |
| FK_OrderItem_LabItem         | 外键    | LabItemId            | 引用DictLabItem表的LabItemId              |
| FK_OrderItem_AssignedTech    | 外键    | AssignedTechnicianId | 引用DictDoctor表的DoctorId                |
| FK_OrderItem_ExecutedBy      | 外键    | ExecutedById         | 引用DictDoctor表的DoctorId                |
| CK_OrderItem_Status_Enhanced | CHECK   | ItemStatus           | 检查ItemStatus值的有效性(0,1,2,3,5,6,7,9) |
| DF_OrderItem_Status          | DEFAULT | ItemStatus           | 默认值为0 (已申请)                        |

#### 20.5 示例数据

```sql
-- 在血清标本(S202405210001)上添加'谷丙转氨酶'和'促甲状腺激素'两个项目
INSERT INTO dbo.LabOrderItem (SpecimenId, LabItemId, AssignedTechnicianId, AssignedTechnicianName)
SELECT sp.SpecimenId, li.LabItemId, 101, '张技师'
FROM dbo.LabSpecimen sp, dbo.DictLabItem li
WHERE sp.Barcode = 'S202405210001' AND li.LabItemCode IN ('BIO_ALT', 'IMM_TSH');

-- 在全血标本(S202405210002)上添加'血红蛋白'项目，并记录执行信息
INSERT INTO dbo.LabOrderItem (SpecimenId, LabItemId, AssignedTechnicianId, AssignedTechnicianName, 
                              ExecutedById, ExecutedByName, ExecutedAt, ItemStatus)
SELECT sp.SpecimenId, li.LabItemId, 102, '李技师', 102, '李技师', GETDATE(), 2
FROM dbo.LabSpecimen sp, dbo.DictLabItem li
WHERE sp.Barcode = 'S202405210002' AND li.LabItemCode = 'HEM_HGB';

-- 更新项目执行状态
UPDATE dbo.LabOrderItem 
SET ExecutedById = 101, 
    ExecutedByName = '张技师',
    ExecutedAt = GETDATE(),
    ItemStatus = 2
WHERE OrderItemId = 10001;
```

### 22. DictLabItemCalculation 表

#### 22.1 表概述

**表名**：DictLabItemCalculation
**说明**：检验项目计算公式表
**用途**：为 DictLabItem 表中 IsCalculated = 1 的项目定义其计算公式。

#### 21.2 字段定义

| 字段名            | 数据类型      | 允许NULL | 默认值        | 约束                       | 说明                                                     |
| ----------------- | ------------- | -------- | ------------- | -------------------------- | -------------------------------------------------------- |
| CalculationId     | INT           | 否       | IDENTITY(1,1) | 主键                       | 计算规则ID                                               |
| LabItemId         | INT           | 否       | -             | 外键/唯一 (-> DictLabItem) | 关联的计算项目ID (一个项目只有一个公式)                  |
| FormulaExpression | NVARCHAR(500) | 否       | -             |                            | **公式表达式字符串**，如: ([BIO_TP]-[BIO_ALB])/[BIO_ALB] |
| Description       | NVARCHAR(200) | 是       | -             |                            | 公式说明/备注                                            |
| Status            | BIT           | 否       | 1             |                            | 状态：1=启用 0=停用                                      |
| CreatedAt         | DATETIME      | 否       | GETDATE()     |                            | 创建时间                                                 |
| UpdatedAt         | DATETIME      | 否       | GETDATE()     |                            | 最近更新时间                                             |

#### 21.3 索引

| 索引名                            | 字段          | 类型       | 说明               |
| --------------------------------- | ------------- | ---------- | ------------------ |
| PK_DictLabItemCalculation         | CalculationId | 主键       | 计算规则ID主键索引 |
| UQ_DictLabItemCalculation_LabItem | LabItemId     | 唯一索引   | 计算项目ID唯一索引 |
| IX_DictLabItemCalculation_Status  | Status        | 非唯一索引 | 按状态查询的索引   |

#### 21.4 约束

| 约束名                              | 类型    | 字段          | 说明                               |
| ----------------------------------- | ------- | ------------- | ---------------------------------- |
| PK_DictLabItemCalculation           | 主键    | CalculationId | 主键约束                           |
| UQ_DictLabItemCalculation_LabItem   | 唯一    | LabItemId     | 确保一个计算项目只对应一条公式记录 |
| FK_DictLabItemCalculation_LabItem   | 外键    | LabItemId     | 引用DictLabItem表的LabItemId       |
| DF_DictLabItemCalculation_Status    | DEFAULT | Status        | 默认值为1(启用)                    |
| DF_DictLabItemCalculation_CreatedAt | DEFAULT | CreatedAt     | 默认值为GETDATE()                  |
| DF_DictLabItemCalculation_UpdatedAt | DEFAULT | UpdatedAt     | 默认值为GETDATE()                  |

#### 21.5 示例数据

```sql
-- 示例1：白蛋白/球蛋白比值 (A/G)
INSERT INTO dbo.DictLabItemCalculation 
       (LabItemId, FormulaExpression, Description)
SELECT l.LabItemId, '[BIO_ALB]/([BIO_TP]-[BIO_ALB])', N'白蛋白与球蛋白比值计算：白蛋白/(总蛋白-白蛋白)'
FROM dbo.DictLabItem l
WHERE l.LabItemCode = 'BIO_AG_RATIO' AND l.IsCalculated = 1;

-- 示例2：肌酐清除率
INSERT INTO dbo.DictLabItemCalculation 
       (LabItemId, FormulaExpression, Description)
SELECT l.LabItemId, '([URINE_CREA]*[URINE_VOL]*1440)/([SERUM_CREA]*体重*1.73)', N'肌酐清除率计算公式'
FROM dbo.DictLabItem l
WHERE l.LabItemCode = 'BIO_CCR' AND l.IsCalculated = 1;

-- 示例3：LDL胆固醇计算 (Friedewald公式)
INSERT INTO dbo.DictLabItemCalculation 
       (LabItemId, FormulaExpression, Description)
SELECT l.LabItemId, '[BIO_TC]-[BIO_HDL]-([BIO_TG]/2.2)', N'LDL胆固醇计算：总胆固醇-高密度脂蛋白胆固醇-甘油三酯/2.2'
FROM dbo.DictLabItem l
WHERE l.LabItemCode = 'BIO_LDL_CALC' AND l.IsCalculated = 1;

-- 示例4：血清渗透压计算
INSERT INTO dbo.DictLabItemCalculation 
       (LabItemId, FormulaExpression, Description)
SELECT l.LabItemId, '2*([BIO_NA]+[BIO_K])+[BIO_GLU]/18+[BIO_UREA]/2.8', N'血清渗透压计算：2×(Na+K)+葡萄糖/18+尿素/2.8'
FROM dbo.DictLabItem l
WHERE l.LabItemCode = 'BIO_OSMO_CALC' AND l.IsCalculated = 1;
```

**公式表达式约定**：

- 应用层负责解析此字符串。
- 约定用 [项目编码] 的格式引用其他项目的值。例如 [BIO_TP] 代表总蛋白的结果值。
- 支持 +, -, *, /, (, ) 等标准数学运算符。

好的，遵照您的要求，我将完全按照您提供的文档规范来输出新增的`DictUnit`表的设计说明。

---

### 23. DictUnit 表

#### 23.1 表概述

**表名**：DictUnit  
**说明**：检验结果单位字典表  
**用途**：统一管理 LIS 系统中所有检验项目可能用到的计量单位，确保单位的标准化和唯一性。

#### 22.2 字段定义

| 字段名    | 数据类型     | 允许NULL | 默认值        | 约束   | 说明                                         |
| --------- | ------------ | -------- | ------------- | ------ | -------------------------------------------- |
| UnitId    | INT          | 否       | IDENTITY(1,1) | 主键   | 单位ID，自增主键，系统内部关联使用           |
| UnitName  | NVARCHAR(30) | 否       | -             | UNIQUE | 单位名称，作为业务唯一标识，如 g/L, U/L 等   |
| Status    | BIT          | 否       | 1             | -      | 状态：1 = Active (启用)，0 = Inactive (停用) |
| CreatedAt | DATETIME     | 否       | GETDATE()     | -      | 记录创建时间                                 |
| UpdatedAt | DATETIME     | 否       | GETDATE()     | -      | 最近更新时间                                 |

#### 22.3 索引

| 索引名             | 字段     | 类型       | 说明                             |
| ------------------ | -------- | ---------- | -------------------------------- |
| PK_DictUnit        | UnitId   | 主键       | 单位ID主键索引                   |
| UQ_DictUnit_Name   | UnitName | 唯一索引   | 单位名称唯一索引，确保业务唯一性 |
| IX_DictUnit_Status | Status   | 非唯一索引 | 按状态查询的索引                 |

#### 22.4 约束

| 约束名                | 类型    | 字段      | 说明              |
| --------------------- | ------- | --------- | ----------------- |
| PK_DictUnit           | 主键    | UnitId    | 主键约束          |
| UQ_DictUnit_Name      | 唯一    | UnitName  | 唯一约束          |
| DF_DictUnit_Status    | DEFAULT | Status    | 默认值为1(启用)   |
| DF_DictUnit_CreatedAt | DEFAULT | CreatedAt | 默认值为GETDATE() |
| DF_DictUnit_UpdatedAt | DEFAULT | UpdatedAt | 默认值为GETDATE() |

#### 22.5 示例数据

```sql
INSERT INTO dbo.DictUnit (UnitName, Status)
VALUES  ('g/L', 1),
        ('U/L', 1),
        ('mmol/L', 1),
        ('umol/L', 1),
        ('10*9/L', 1),
        ('10*12/L', 1),
        ('%', 1),
        ('fL', 1),
        ('pg', 1),
        ('s', 1),
        ('mIU/L', 1);
```

### 24. DictLabProfile 表

#### 24.1 表概述

**表名**：DictLabProfile
**说明**：检验组合字典表
**用途**：定义临床可开立的检验组合或套餐，如"血常规"、"肝功能全项"等。这是与 HIS 系统对接时医嘱项目的主要实体。

#### 23.2 字段定义

| 字段名         | 数据类型      | 允许NULL | 默认值        | 约束   | 说明                                         |
| -------------- | ------------- | -------- | ------------- | ------ | -------------------------------------------- |
| ProfileId      | INT           | 否       | IDENTITY(1,1) | 主键   | 组合ID，自增主键                             |
| ProfileCode    | NVARCHAR(30)  | 否       | -             | UNIQUE | 组合编码 (院内统一，如 CBC, LFP)             |
| ProfileName    | NVARCHAR(100) | 否       | -             | -      | 组合中文名称（如：血常规，肝功能全项）       |
| HisProfileCode | NVARCHAR(200) | 是       | -             | UNIQUE | HIS系统组合/医嘱编码，允许NULL，用于系统集成 |
| ReportTitle    | NVARCHAR(100) | 是       | -             | -      | 报告单标题，如果为空则默认使用ProfileName    |
| EnglishName    | NVARCHAR(100) | 是       | -             | -      | 英文名称                                     |
| PinYin         | NVARCHAR(100) | 是       | -             | -      | 拼音/首字母                                  |
| Description    | NVARCHAR(200) | 是       | -             | -      | 组合描述，如临床意义、注意事项等             |
| Status         | BIT           | 否       | 1             | -      | 状态：1 = Active (启用)，0 = Inactive (停用) |
| CreatedAt      | DATETIME      | 否       | GETDATE()     | -      | 记录创建时间                                 |
| UpdatedAt      | DATETIME      | 否       | GETDATE()     | -      | 最近更新时间                                 |

#### 23.3 索引

| 索引名                    | 字段           | 类型       | 说明                |
| ------------------------- | -------------- | ---------- | ------------------- |
| PK_DictLabProfile         | ProfileId      | 主键       | 组合ID主键索引      |
| UQ_DictLabProfile_Code    | ProfileCode    | 唯一索引   | 组合编码唯一索引    |
| IX_DictLabProfile_Status  | Status         | 非唯一索引 | 按状态查询的索引    |
| IX_DictLabProfile_PinYin  | PinYin         | 非唯一索引 | 按拼音查询的索引    |
| IX_DictLabProfile_HisCode | HisProfileCode | 非唯一索引 | 按HIS编码查询的索引 |

#### 23.4 约束

| 约束名                   | 类型    | 字段        | 说明            |
| ------------------------ | ------- | ----------- | --------------- |
| PK_DictLabProfile        | 主键    | ProfileId   | 主键约束        |
| UQ_DictLabProfile_Code   | 唯一    | ProfileCode | 唯一约束        |
| DF_DictLabProfile_Status | DEFAULT | Status      | 默认值为1(启用) |

#### 23.5 示例数据

```sql
INSERT INTO dbo.DictLabProfile (ProfileCode, ProfileName, HisProfileCode, PinYin)
VALUES ('CBC', N'血常规', 'HIS_CBC', 'XCG'),
       ('LFP', N'肝功能全项', 'HIS_LFP', 'GGNQX'),
       ('RFP', N'肾功能三项', 'HIS_RFP', 'SGNJX'),
       ('CHO', N'总胆固醇测定', 'HIS_CHO', 'ZDGC'); -- 注意：单个项目也可以作为一个Profile被开立
```

------



### 25. DictLabProfileItemMap 表

#### 25.1 表概述

**表名**：DictLabProfileItemMap
**说明**：检验组合-项目映射表
**用途**：定义一个检验组合（DictLabProfile）具体包含哪些检验项目（DictLabItem），建立它们之间的多对多关系。

#### 24.2 字段定义

| 字段名     | 数据类型 | 允许NULL | 默认值        | 约束                               | 说明                                                               |
| ---------- | -------- | -------- | ------------- | ---------------------------------- | ------------------------------------------------------------------ |
| MapId      | BIGINT   | 否       | IDENTITY(1,1) | 主键                               | 映射ID，自增主键                                                   |
| ProfileId  | INT      | 否       | -             | 外键 (-> DictLabProfile.ProfileId) | 关联的组合ID                                                       |
| LabItemId  | INT      | 否       | -             | 外键 (-> DictLabItem.LabItemId)    | 关联的检验项目ID                                                   |
| SequenceNo | INT      | 是       | 0             | -                                  | 报告显示顺序，决定项目在报告单中的排列位置                         |
| IsDefault  | BIT      | 否       | 1             | -                                  | 是否为默认项目：1=是(常规开立包含此项), 0=否(可选项目，需额外勾选) |
| CreatedAt  | DATETIME | 否       | GETDATE()     | -                                  | 记录创建时间                                                       |
| UpdatedAt  | DATETIME | 否       | GETDATE()     | -                                  | 记录更新时间                                                       |

#### 24.3 索引

| 索引名                           | 字段                 | 类型       | 说明                       |
| -------------------------------- | -------------------- | ---------- | -------------------------- |
| PK_DictLabProfileItemMap         | MapId                | 主键       | 映射ID主键索引             |
| UQ_DictLabProfileItemMap_Map     | ProfileId, LabItemId | 唯一索引   | 确保一个组合内项目不重复   |
| IX_DictLabProfileItemMap_Profile | ProfileId            | 非唯一索引 | 按组合ID快速查找其所有项目 |
| IX_DictLabProfileItemMap_Item    | LabItemId            | 非唯一索引 | 按项目ID反查它属于哪些组合 |

#### 24.4 约束

| 约束名                       | 类型    | 字段                 | 说明                            |
| ---------------------------- | ------- | -------------------- | ------------------------------- |
| PK_DictLabProfileItemMap     | 主键    | MapId                | 主键约束                        |
| UQ_DictLabProfileItemMap_Map | 唯一    | ProfileId, LabItemId | 唯一约束                        |
| FK_Map_Profile               | 外键    | ProfileId            | 引用DictLabProfile表的ProfileId |
| FK_Map_Item                  | 外键    | LabItemId            | 引用DictLabItem表的LabItemId    |
| DF_Map_IsDefault             | DEFAULT | IsDefault            | 默认值为1(默认项目)             |

#### 24.5 示例数据

```sql
-- 示例1：定义"肝功能全项"包含的项目
INSERT INTO dbo.DictLabProfileItemMap (ProfileId, LabItemId, SequenceNo)
SELECT p.ProfileId, i.LabItemId, 1 FROM dbo.DictLabProfile p, dbo.DictLabItem i WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_ALT';

INSERT INTO dbo.DictLabProfileItemMap (ProfileId, LabItemId, SequenceNo)
SELECT p.ProfileId, i.LabItemId, 2 FROM dbo.DictLabProfile p, dbo.DictLabItem i WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_AST';

INSERT INTO dbo.DictLabProfileItemMap (ProfileId, LabItemId, SequenceNo)
SELECT p.ProfileId, i.LabItemId, 3 FROM dbo.DictLabProfile p, dbo.DictLabItem i WHERE p.ProfileCode = 'LFP' AND i.LabItemCode = 'BIO_TP';

-- 示例2：定义"总胆固醇测定"这个组合只包含它自己一个项目
INSERT INTO dbo.DictLabProfileItemMap (ProfileId, LabItemId, SequenceNo)
SELECT p.ProfileId, i.LabItemId, 1 FROM dbo.DictLabProfile p, dbo.DictLabItem i WHERE p.ProfileCode = 'CHO' AND i.LabItemCode = 'BIO_CHO';
```

### 26. SysUser (系统用户/操作员表)

#### 26.1 表概述

**表名**：SysUser
**说明**：系统用户/操作员表
**用途**：存储所有可以登录和操作系统的人员信息，是系统身份认证和权限管理的基础。

#### 26.2 字段定义

| 字段名       | 数据类型      | 允许NULL | 默认值        | 约束                 | 说明                                                         |
| ------------ | ------------- | -------- | ------------- | -------------------- | ------------------------------------------------------------ |
| UserId       | INT           | 否       | IDENTITY(1,1) | 主键                 | 用户ID，自增主键。                                           |
| LoginName    | NVARCHAR(30)  | 否       | -             | UNIQUE               | **登录账号**，系统内唯一。                                   |
| PasswordHash | NVARCHAR(255) | 否       | -             | -                    | **密码哈希值**。严禁明文存储密码，必须使用强哈希算法（如Argon2, bcrypt）加盐后存储。 |
| UserName     | NVARCHAR(100) | 否       | -             | -                    | **用户真实姓名**。                                           |
| DoctorId     | INT           | 是       | -             | 外键 (-> DictDoctor) | 关联的医生/技师ID。便于获取职称、科室等信息，实现单点登录或信息同步。 |
| RoleCode     | CHAR(2)       | 否       |               | 外键 (-> SysRole)    |                                                              |
| IsLockedOut  | BIT           | 否       | 0             | -                    | 是否被锁定。因密码错误次数过多等原因被锁定。                 |
| LastLoginAt  | DATETIME      | 是       | -             | -                    | 最后登录时间。                                               |
| Status       | BIT           | 否       | 1             | -                    | 状态：1=启用, 0=停用。                                       |
| CreatedAt    | DATETIME      | 否       | GETDATE()     | -                    | 创建时间。                                                   |
| UpdatedAt    | DATETIME      | 否       | GETDATE()     | -                    | 更新时间。                                                   |
| CreatedBy    | INT           | 是       | -             | 外键 (-> SysUser)    | 创建此用户的操作员ID。                                       |
| UpdatedBy    | INT           | 是       | -             | 外键 (-> SysUser)    | 最后更新此用户的操作员ID。                                   |

## 数据库设计说明

### 1. LabInstrument 表设计说明

1. 表设计采用了分组字段的方式，根据不同的连接方式（ConnType）使用对应的参数组：
   - 串口连接：使用ComPort, BaudRate, Parity, DataBits, StopBits
   - TCP连接：使用IpHost, IpPort, KeepAliveSec
   - 文件共享：使用FilePath
   - 中间库：使用DbConnStr
   - REST API：使用ApiBaseUrl, ApiAuthToken
   
2. 敏感信息处理：
   - ApiAuthToken 字段存储API认证令牌，建议使用SQL Server的Always Encrypted特性进行加密存储
   - DbConnStr 字段存储数据库连接字符串，可能包含密码，也建议加密

3. 扩展性考虑：
   - ProtocolType 字段支持多种协议类型，便于扩展不同厂商的通讯协议
   - 表结构设计兼顾了不同类型设备的连接需求，可适应实验室多样化的仪器设备

4. 关联设计：
   - 该表作为主表，其他相关子表（如项目映射、通道、质控计划等）均通过InstrumentId进行关联

### 2. LabInstrRawMsg 表设计说明

1. 表设计目的：
   - 存储仪器发送的原始报文，保留完整的通讯记录以便追溯和调试
   - 通过ParsedFlag字段标记报文处理状态，形成处理流水线

2. 性能考虑：
   - 使用BIGINT类型作为RawId主键，支持大量报文存储
   - 建立了按仪器ID和时间倒序的复合索引，优化查询性能
   - RawText字段使用NVARCHAR(MAX)类型，支持存储大型报文

3. 关联设计：
   - 通过外键约束与LabInstrument表关联，确保数据完整性
   - 支持按仪器筛选和查询报文历史

4. 报文处理流程：
   - 连接器接收报文后写入该表，ParsedFlag初始为0
   - 解析器读取未解析报文，处理后更新ParsedFlag为1
   - 解析失败时，在ParseError字段记录失败原因

### 3. LabInstrResultQ 表设计说明

1. 表设计目的：
   - 作为仪器原始报文解析后的中间结果存储，形成处理流水线的中间环节
   - 通过ProcessedFlag状态管理，跟踪每个结果的处理状态

2. 性能考虑：
   - 使用BIGINT类型作为QId主键，支持大量结果记录
   - 建立了按处理状态和样本条码的索引，优化查询性能
   - 结果值使用NVARCHAR(60)类型，支持存储各种类型的结果（数值、文本、特殊符号等）

3. 关联设计：
   - 通过InstrumentId字段与LabInstrument表关联
   - 通过Barcode字段可以与LIS系统的样本表关联

4. 处理流程：
   - 解析器从LabInstrRawMsg表读取原始报文，解析后写入该表
   - 导入服务读取ProcessedFlag=0的记录，进行项目映射和结果处理
   - 处理成功后将ProcessedFlag更新为1，失败则更新为2并记录错误信息

### 4. LabResult 表设计说明

1. 表设计目的：
   - 存储经过审核流程的最终检验结果，作为对外发布的权威数据
   - 保留完整的初审/复审痕迹，满足医疗记录追溯需求
   - 与医嘱子表关联，实现检验结果与医嘱的完整关联

2. 审核流程设计：
   - 通过ReviewStatus字段实现三态审核流程：待审核→初审通过→复审通过
   - 记录每个审核环节的审核人和审核时间，完整留痕
   - 审核通过的结果才允许对外发布（如打印报告、传输HIS）

3. 关联设计：
   - 通过OrderItemId与医嘱子表关联，实现结果与患者信息的间接关联
   - 通过InstrumentId与仪器表关联，保留结果来源信息
   - 冗余存储InstrItemCode，便于结果溯源和问题排查

4. 结果呈现：
   - 存储原始结果值、单位及异常标志，便于结果展示
   - 通过AbnormalFlag标识结果异常情况，支持结果审核和报警

### 5. LabResultReview 表设计说明

1. **表设计目的**：
   - **审核流程记录**：为每一次审核操作创建独立的记录，形成完整的审核轨迹，满足医疗质量管理和监管要求。
   - **责任区分**：明确区分实际操作员（登录系统执行操作的人）和责任医师（承担医疗责任的医师），解决实际工作中代他人操作的场景。
   - **多级审核支持**：通过ReviewLevel字段支持初审、复审、三审等多级审核流程，适应不同项目和医院的审核要求。
   - **审核决策记录**：详细记录每次审核的动作（通过/退回/修改）和审核意见，为质量改进和争议处理提供依据。

2. **关联设计**：
   - **与LabResult表关联**：通过ResultId外键建立一对多关系，一个检验结果可以有多条审核记录。
   - **操作员关联**：通过OperatorId与SysUser表关联，记录实际的登录操作人员。
   - **责任医师关联**：通过ResponsibleDoctorId与DictDoctor表关联，明确承担医疗责任的医师。
   - **审核级别管控**：通过ReviewLevel与LabResult表的CurrentReviewLevel和MaxReviewLevel配合，实现审核流程控制。

3. **审核流程设计**：
   - **审核级别**：支持1=初审、2=复审、3=三审等多级审核，可根据项目风险等级配置不同的审核要求。
   - **审核动作**：1=通过（结果符合要求，可进入下一级或完成）、2=退回（需要重新检测）、3=修改（允许修改结果值）。
   - **职称记录**：记录责任医师的职称信息，用于报告签名和权限验证。
   - **意见记录**：支持审核意见的详细记录，便于沟通和追溯。

4. **业务应用场景**：
   - **审核工作流**：系统根据ReviewLevel和MaxReviewLevel确定下一个审核环节，推送给相应级别的审核医师。
   - **质量控制**：通过审核记录统计分析，监控不同医师的审核质量和效率。
   - **争议处理**：当出现医疗争议时，可通过审核记录快速定位责任人和决策过程。
   - **权限管理**：根据医师职称和审核级别进行权限控制，确保审核权限与职责匹配。
   - **审核统计**：按医师、科室、时间段等维度统计审核工作量和质量指标。

5. **数据完整性设计**：
   - **唯一性约束**：通过复合唯一约束防止同一结果在相同级别被重复审核。
   - **外键约束**：确保关联的结果、操作员和责任医师都是有效的。
   - **时间戳管理**：自动记录审核时间，为审核效率分析提供数据。
   - **冗余字段设计**：操作员姓名和责任医师信息采用冗余存储，提高查询性能并防止人员信息变更对历史记录的影响。

### 6. LabInstrItemMap 表设计说明

1. 表设计目的：
   - 建立仪器项目与LIS统一项目字典之间的映射关系
   - 实现不同仪器间项目代码和名称的标准化转换
   - 支持结果处理中的项目自动匹配和格式转换

2. 映射关系设计：
   - 采用一对一映射模式，确保每个仪器项目只对应到一个LIS项目
   - 通过唯一约束确保单个仪器内部项目不会重复映射
   - 项目名称同时保存仪器原始名称和LIS标准名称，便于溯源和调试

3. 结果格式管理：
   - 通过ResultFormat字段区分不同类型的结果处理方式
   - 对于数值型结果，提供小数位数控制，确保结果精度一致性
   - 支持文本型和阴阳性结果的处理，满足不同检验项目需求

4. 业务使用场景：
   - 解析仪器原始报文时，根据InstrItemCode找到对应的LIS项目信息
   - 在质控和统计分析时，可以按LIS统一项目进行跨仪器的数据聚合
   - 便于配置新的仪器接入，只需建立新的项目映射即可

### 7. DictSampleType 表设计说明

1. 表设计目的：
   - 标准化维护所有标本类型的基础信息
   - 确保LIS系统、HIS系统和检验仪器间标本类型代码的一致性
   - 为采集、标签打印和结果处理提供标本相关参考信息

2. 状态管理：
   - 采用布尔型Status字段（1=启用, 0=停用）取代传统的IsActive字段
   - 保持与系统其他表的状态字段命名和逻辑一致性

3. 检索优化：
   - 添加PinYin字段以支持拼音检索
   - 为常用查询条件（Status、PinYin）创建了索引以提高查询性能

4. 业务应用：
   - 通过ColorHex字段可在界面上直观展示试管颜色
   - 通过DefaultVolumeMl可为采集提供参考
   - 通过PreTreatment字段提供标本预处理方法提示

### 8. DictLabItemCategory 表设计说明

1. 表设计目的：
   - 统一维护检验项目的类别信息，实现项目分类标准化，便于管理和统计分析
   - 支持LIS、HIS等系统间项目类别的对接和映射

2. 字段设计：
   - ItemCategoryCode 作为主键，保证类别唯一性，便于代码层引用
   - ItemCategoryName、EnglishName、PinYin 提供多语言和多方式检索支持
   - Description 字段可补充类别的详细说明
   - Status 字段用于启用/停用管理，CreatedAt/UpdatedAt 记录维护痕迹

3. 索引与性能：
   - 对Status、PinYin等常用查询字段建立索引，提升检索效率

4. 业务应用场景：
   - 检验项目维护时可选择类别，便于项目归类
   - 统计报表、数据分析时可按类别聚合
   - 支持多院区、多系统间的项目类别标准化对接

### 9. DictMethodCategory 表设计说明

1. 表设计目的：
   - 标准化管理检验项目所使用的方法学类别，如生化法、免疫法、分子生物学法等。
   - 为项目配置、结果解释、数据统计分析提供方法学分类依据。

2. 字段设计：
   - `MethodCategoryCode`：方法学类别唯一编码，作为主键，方便程序调用和关联。
   - `MethodCategoryName`：方法学类别中文名称，主要显示用。
   - `EnglishName` 和 `PinYin`：提供英文名称和拼音首字母，方便不同用户检索和国际化需求。
   - `Description`：对方法学类别的补充说明。
   - `Status`：控制该类别是否启用。
   - `CreatedAt`, `UpdatedAt`：记录创建和更新时间，用于审计和追踪。

3. 索引设计：
   - 主键索引：`MethodCategoryCode`。
   - 辅助索引：为 `Status` 和 `PinYin` 创建索引，提高按状态和拼音检索的效率。

4. 业务应用场景：
   - **项目字典配置**：在定义检验项目时，关联其所属的方法学类别。
   - **结果报告**：某些情况下可能需要在报告中展示项目的方法学。
   - **统计分析**：可以按方法学类别对检验工作量、成本、阳性率等进行统计。
   - **质量控制**：某些质控规则或评估可能与方法学相关。
   - 支持多院区、多系统间的项目类别标准化对接

### 10. DictMethod 表设计说明

1.  **表设计目的**：
    *   详细记录每一种具体的检验分析方法，例如化学发光法、酶速率法、PCR等。
    *   通过 `MethodCategoryCode` 与 `DictMethodCategory` 表关联，实现具体方法到方法学大类的归属。
    *   为检验项目配置提供精确的方法学选项。

2.  **字段设计**：
    *   `MethodCode`：主键，具体方法的唯一编码，简短易记，方便系统内部使用。
    *   `MethodName`：方法中文名，主要用于界面显示和报告。
    *   `EnglishName`, `PinYin`：支持英文和拼音检索，满足不同用户需求。
    *   `MethodCategoryId`：外键，关联到 `DictMethodCategory` 表，指明该方法所属的类别。
    *   `Description`：对方法的详细描述或备注信息。
    *   `Status`, `CreatedAt`, `UpdatedAt`：标准状态和时间戳字段。

3.  **索引与约束**：
    *   主键索引：`MethodCode`。
    *   外键约束：`MethodCategoryId` 关联到 `DictMethodCategory.MethodCategoryId`，保证数据一致性。
    *   辅助索引：为 `Status`, `PinYin`, `MethodCategoryId` 创建索引，优化查询性能。

4.  **业务应用场景**：
    *   **检验项目维护**：为每个检验项目指定其采用的具体检验方法。
    *   **仪器接口**：不同仪器可能支持不同的检验方法，此表可用于方法匹配和转换。
    *   **结果解释与报告**：检验结果报告中可能需要列出所使用的方法学。
    *   **方法学比对与验证**：进行新旧方法比对或方法学验证时，可精确记录所用方法。
    *   **成本核算与统计**：不同方法的成本可能不同，可基于此进行精细化统计。

### 11. DictLabItem 表设计说明

1.  **表设计目的**：
    *   作为LIS系统的核心字典之一，统一定义和管理所有检验项目的基础属性。
    *   确保项目信息在整个系统中的一致性、准确性和标准化。
    *   支持项目相关的业务逻辑，如开单、标本采集、结果录入、报告生成、统计分析等。

2.  **字段设计**：
    *   `LabItemCode`：主键，检验项目的院内唯一编码，是系统内其他表关联此表的主要依据。
    *   `LabItemName`, `EnglishName`, `PinYin`：提供多语言及拼音检索支持。
    *   `ItemCategoryId`：外键，关联 `DictLabItemCategory`，定义项目所属的检验大类（如生化、免疫）。
    *   `SampleTypeId`：外键，关联 `DictSampleType`，定义该项目默认使用的标本类型。
    *   `DefaultUnit`：项目结果的默认单位。
    *   `Precision`：数值型结果默认的小数位数。
    *   `DefaultMethodId`：外键，关联 `DictMethod`，定义该项目默认采用的检验方法。
    *   `LoincCode`：国际通用的LOINC编码，用于数据标准化和交换。
    *   `Status`, `CreatedAt`, `UpdatedAt`：标准状态和时间戳字段。

3.  **索引与约束**：
    *   主键索引：`LabItemCode`。
    *   外键约束：确保 `ItemCategoryId`, `SampleTypeId`, `DefaultMethodId` 的有效性。
    *   辅助索引：为 `Status`, `PinYin`, `ItemCategoryId` 等常用查询字段创建索引，提高查询效率。

4.  **业务应用场景**：
    *   **医嘱开立**：临床医生开立检验申请时，选择此表中的检验项目。
    *   **标本采集与接收**：根据项目的默认标本类型提示采集要求。
    *   **仪器项目映射**：`LabInstrItemMap` 表中的 `LabItemId` 通常会引用此表的 `LabItemId`。
    *   **结果录入与审核**：显示项目名称、单位、参考范围（需另外关联参考范围表）等。
    *   **报告生成**：报告中显示标准的项目名称、单位等信息。
    *   **数据统计与分析**：按项目、项目类别等维度进行统计分析。
    *   **与其他系统接口**：通过 `LoincCode` 或院内码与其他系统（如HIS、EMR）进行项目对接。

### 12. DictLabItemRefRange 表设计说明

1. **表设计目的**：
   * 为检验项目提供多维度、精细化的参考区间定义，支持临床结果判读。
   * 实现按方法学、性别、年龄段、时间段等多条件的参考范围管理。
   * 提供历史参考范围的追溯能力，支持参考范围的版本管理。

2. **多维度设计**：
   * `MethodId`：同一项目在不同方法学下可以有不同参考范围，NULL表示适用于所有方法。
   * `Sex`：支持男女分开设置不同参考范围，'N'表示不区分性别。
   * `AgeLower/AgeUpper`：按年龄段定义参考范围，覆盖新生儿到老年人各阶段。
   * `EffectiveFrom/EffectiveTo`：时间段维度，支持参考范围随时间调整并保留历史记录。

3. **业务应用场景**：
   * **结果自动审核**：系统可基于此表中的参考范围自动判断结果是否异常，辅助技师审核。
   * **报告显示**：在检验报告上显示项目对应的参考范围。
   * **趋势分析**：患者检验结果趋势图上的正常区间显示。
   * **版本管理**：通过时间段控制，可实现参考范围的版本控制，在调整参考范围后保留历史记录。
   * **方法学差异**：当实验室采用新方法或仪器时，可设置针对特定方法的参考范围。

4. **查询优化**：
   * 创建了包含关键匹配条件的复合索引`IX_RefRange_Match`，优化按项目、方法、性别和有效期查找参考范围的性能。
   * 时间字段上使用了`EffectiveFrom DESC`降序设计，使查询最新生效的参考范围更高效。

### 13. DictVisitType 表设计说明

1. **表设计目的**：
   - 标准化管理医院就诊类型信息，确保LIS系统与HIS系统间就诊类型编码的一致性
   - 为检验申请单、样本标签、报告生成等业务提供就诊类型基础数据支持

2. **编码映射设计**：
   - `VisitTypeCode`：LIS内部统一编码，简洁易用
   - `HisVisitTypeCode`：与HIS系统对接的编码，支持长编码格式
   - 通过唯一约束确保HIS编码不重复，保证系统间数据一致性

3. **检索优化**：
   - 添加`PinYin`字段支持拼音检索，提高用户操作效率
   - 为`Status`字段创建索引，优化按状态筛选的查询性能

4. **业务应用场景**：
   - **检验申请**：区分不同就诊类型的检验申请流程和优先级
   - **费用计算**：不同就诊类型可能有不同的收费标准
   - **报告分发**：按就诊类型确定报告的分发方式和时效要求
   - **统计分析**：按就诊类型进行检验工作量和收入统计

### 14. DictDepartment 表设计说明

1. **表设计目的**：
   - 统一管理医院科室信息，支持科室层级结构管理
   - 为医生归属、检验申请、报告分发等业务提供科室基础数据
   - 与HIS系统保持科室编码的映射关系

2. **层级结构设计**：
   - 通过`ParentDeptId`自引用外键实现科室层级结构
   - 支持多级科室管理，如：内科 -> 心内科 -> 心内科一病区
   - 递归查询可实现科室树形结构的展示和管理

3. **科室分类**：
   - `DeptType`字段区分科室类型：临床科室、医技科室、行政科室
   - 便于按科室类型进行不同的业务处理和权限控制

4. **业务应用场景**：
   - **医生管理**：医生归属科室，权限控制和工作量统计
   - **检验申请**：申请科室信息，用于报告分发和统计
   - **报告分发**：按科室分发检验报告
   - **统计分析**：按科室统计检验工作量、收入等指标

### 15. DictDoctor 表设计说明

1. **表设计目的**：
   - 统一管理医院医生和检验人员信息，包括基本信息和职业资质
   - 为检验申请、结果审核、报告签发等业务提供人员基础数据
   - 与HIS系统保持医生编码的映射关系

2. **关联设计**：
   - 通过`DeptId`外键与科室表关联，确保医生归属科室的有效性
   - 支持医生科室调动时的数据一致性维护

3. **职业信息管理**：
   - `Title`字段记录职称信息，用于权限控制和签名显示
   - `LicenseNo`字段记录执业证书号，满足医疗监管要求
   - `Mobile`字段便于紧急情况下的联系沟通

4. **业务应用场景**：
   - **检验申请**：记录申请医生信息，用于报告分发和沟通
   - **结果审核**：检验技师的审核权限控制和审核记录
   - **报告签发**：医生签名和职称显示
   - **权限管理**：基于科室和职称的功能权限控制
   - **统计分析**：按医生统计检验申请量和工作效率

### 16. DictSpecimenCondition 表设计说明

1. **表设计目的**：
   - 标准化管理标本质量和外观异常状态，为标本质量评估提供统一标准
   - 支持标本接收、处理、检测各环节的质量控制和异常记录
   - 为结果审核和报告发布提供标本状态依据
2. **状态分类设计**：
   - 涵盖常见标本异常状态：正常、溶血、脂血、黄疸、凝块、标本量不足等
   - 通过`SequenceNo`字段控制状态显示顺序，便于用户选择
   - 支持中英文双语显示，满足不同用户需求
3. **检索优化**：
   - 添加`PinYin`字段支持拼音检索，提高操作效率
   - 为`Status`和`PinYin`字段创建索引，优化查询性能
4. **业务应用场景**：
   - **标本接收**：记录标本接收时的外观状态，及时发现异常
   - **质量控制**：统计标本异常率，分析标本质量趋势
   - **结果审核**：根据标本状态判断结果可靠性，决定是否需要重新采样
   - **报告备注**：在检验报告中标注标本异常状态，提醒临床医生
   - **数据分析**：按标本状态统计分析，优化标本采集和运输流程
   - **仪器接口**：某些仪器可自动检测标本异常，通过此表标准化异常状态编码

### 17. DictSpecimenParam 表设计说明

1. **表设计目的**：
   - **标准化**：为系统中所有可能遇到的标本参数（如HCT）和标本质量指数（HIL）提供一个统一、标准的定义，避免在代码或配置中硬编码。
   - **解耦与扩展**：将标本参数的定义与仪器本身解耦。当新的仪器引入了新的质量参数时，只需在此字典表中增加记录，无需修改程序或数据库结构，提高了系统的可扩展性。
   - **信息中心**：集中管理参数的中英文名称、拼音、描述等信息，为UI显示、报告备注和数据分析提供一致的数据源。
2. **字段设计**：
   - ParamCode 作为业务唯一键，确保了每个参数在系统内部有唯一的、可读的标识符，便于程序逻辑引用。
   - ParamType 字段用于区分参数类型：0=常规参数（如血细胞比容HCT）、1=质量指数（如HIL指数）、2=其他特殊参数，便于系统进行分类处理。
   - Unit 字段存储参数的计量单位，如百分比(%)、指数(Index)等，为结果显示和报告生成提供单位信息。
   - 包含 Status, CreatedAt, UpdatedAt 等标准字段，便于进行参数的启停管理和审计追溯。
3. **业务应用场景**：
   - **标本质量控制**：系统可根据仪器回报的溶血、脂血、黄疸指数，结合此字典表的定义，自动对标本质量进行评估和报警。通过ParamType字段区分质量指数类参数，进行专门的质控处理。
   - **结果辅助判读**：在结果审核界面，可将这些标本参数值展示给检验技师，作为判断检验结果是否受干扰的重要依据。Unit字段确保参数值显示时带有正确的单位。
   - **报告备注**：对于超出特定阈值的标本质量参数，可在检验报告上自动生成提示性备注，单位信息来源于Unit字段。
   - **参数分类管理**：通过ParamType字段实现参数的分类展示和管理，如在界面上将常规参数和质量指数分开显示。
   - **数据统计分析**：可按参数类型进行统计分析，如专门统计质量指数类参数的异常率。

### 18. LabInstrSpecimenParamMap 表设计说明

1. **表设计目的**：
   - **建立连接**：作为连接"仪器原始数据"和"LIS标准字典"的关键桥梁。它解决了不同厂商、不同型号仪器对同一标本参数使用不同编码的问题（如QR-100用0x21代表HCT，其他仪器可能用HCT字符串）。
   - **解析逻辑驱动**：为数据解析服务（Parser/Importer）提供映射规则。解析服务根据 InstrumentId 和仪器原始报文中的 InstrParamCode，查询此表，找到其在LIS中对应的标准 ParamId。
   - **配置灵活性**：使新仪器的接入更为便捷。对于支持回报标本参数的新仪器，实施工程师只需在此表中配置相应的映射关系，即可完成对接，极大降低了开发工作量。
2. **约束与索引设计**：
   - UQ_LabSpecimenParamMap 唯一约束是设计的核心，它保证了对于同一台仪器，其任何一个参数代码只能映射到一个LIS标准参数，确保了映射关系的唯一性和准确性。
   - 通过外键约束关联 LabInstrument 和 DictSpecimenParam 表，保证了数据引用的完整性和有效性。
3. **处理流程中的角色**：
   - 仪器连接器接收到包含标本参数的报文。
   - 解析器识别出标本参数段，提取出仪器端的参数代码（如0x21）和值。
   - 解析器查询 LabInstrSpecimenParamMap 表，将 0x21 转换为 LIS 的 ParamId。
   - 解析器将转换后的结果（包括LIS的ParamId、样本条码、参数值等）写入 LabInstrResultQ 队列，并设置 ResultType 为 1 (标本参数)。
   - 后续的入库服务根据 ResultType 进行相应的逻辑处理。

### 19. LabOrder 表设计说明

1. **表设计目的**：
   - **申请单管理中心**：作为检验申请的核心主表，统一管理所有检验申请的头信息。
   - **HIS系统对接**：通过HisOrderCode字段实现与HIS系统的医嘱对接，支持双向数据同步。
   - **患者信息关联**：建立患者、就诊、申请科室、申请医生之间的完整关联关系。

2. **状态管理设计**：
   - **全生命周期状态**：从申请开立到最终完成的完整状态流转管理。
   - **状态枚举**：0=已开立、1=部分采集、2=全部采集、3=部分完成、4=全部完成、9=已取消。
   - **状态驱动业务**：不同状态对应不同的业务处理逻辑和界面展示。

3. **优先级管理**：
   - **紧急处理**：通过Priority字段区分常规和紧急申请，影响处理优先级。
   - **工作流优化**：紧急申请在各个环节都享有优先处理权。

4. **业务应用场景**：
   - **申请单创建**：从HIS接收医嘱或手工创建检验申请。
   - **工作列表**：为采集、接收、检测、审核等各环节提供工作列表数据源。
   - **进度跟踪**：通过状态变化跟踪申请单的处理进度。
   - **统计分析**：按科室、医生、时间等维度进行申请量统计。

### 20. LabSpecimen 表设计说明

1. **表设计目的**：
   - **标本生命周期管理**：追踪标本从采集到废弃的全生命周期状态变化。
   - **条码管理中心**：以条码为核心，实现标本的唯一标识和快速定位。
   - **质量控制基础**：记录标本状态和异常信息，为质量控制提供数据支撑。

2. **状态流转设计**：
   - **状态枚举**：10=待采集、20=已采集、30=已接收、40=检测中/完成、90=已废弃、99=已作废/拒收。
   - **状态驱动**：不同状态决定标本可执行的操作和显示的界面。
   - **异常处理**：支持拒收和废弃等异常状态的处理。

3. **关联设计**：
   - **申请单关联**：通过OrderId与申请单建立关联，一个申请单可对应多个标本。
   - **标本类型关联**：通过SampleTypeId确定标本的采集和处理要求。
   - **标本状态关联**：通过SpecCondId记录标本的质量状态。

4. **业务应用场景**：
   - **标本采集**：记录采集人和采集时间，更新标本状态。
   - **标本接收**：扫码接收标本，检查标本质量，记录异常。
   - **标本分拣**：按标本类型和检测项目进行标本分拣。
   - **条码查询**：通过条码快速查找标本信息和检测项目。

### 21. LabOrderItem 表设计说明

1. **表设计目的**：
   - **项目明细管理**：将申请单的检验项目进行明细化管理，实现精确的项目级别控制。
   - **结果关联桥梁**：作为LabResult表的外键关联目标，建立结果与申请项目的准确对应。
   - **项目级状态管理**：支持单个检验项目的独立状态管理和进度跟踪。

2. **关联设计**：
   - **标本关联**：通过SpecimenId与标本表关联，确定项目在哪个标本上检测。
   - **项目字典关联**：通过LabItemId与项目字典关联，获取项目的标准信息。
   - **结果关联**：LabResult表通过OrderItemId字段关联到此表。

3. **状态管理**：
   - **项目级状态**：0=已申请、1=已上机/检测中、2=已出结果、3=已审核、9=已取消。
   - **独立状态**：每个项目可以有独立的处理状态，支持部分项目先完成的场景。
   - **状态聚合**：申请单和标本的状态可以根据项目状态进行聚合计算。

4. **业务应用场景**：
   - **项目分配**：将申请单的项目分配到相应的标本上。
   - **上机管理**：记录项目的上机状态，避免重复检测。
   - **结果录入**：为每个项目录入检测结果。
   - **进度跟踪**：跟踪每个项目的检测进度和完成情况。
   - **费用核算**：按项目进行费用计算和统计。

### 22. DictLabItemCalculation 表设计说明

1. **表设计目的**：
   - **公式管理中心**：为LIS系统中所有计算型检验项目提供统一的公式定义和管理。
   - **业务逻辑分离**：将复杂的计算逻辑从程序代码中分离出来，存储在数据库中，便于维护和调整。
   - **标准化计算**：确保同一计算项目在系统中使用统一的计算公式，避免不同模块间计算结果不一致。

2. **公式表达式设计**：
   - **项目引用格式**：使用`[项目编码]`格式引用其他检验项目的结果值，如`[BIO_TP]`代表总蛋白的结果。
   - **运算符支持**：支持标准数学运算符：`+`(加)、`-`(减)、`*`(乘)、`/`(除)、`(`、`)`(括号)。
   - **扩展性**：公式表达式采用字符串存储，便于后期扩展支持更复杂的函数和运算。

3. **约束设计**：
   - **唯一性保证**：通过`UQ_DictLabItemCalculation_LabItem`唯一约束确保每个计算项目只有一个有效公式。
   - **数据完整性**：通过外键约束确保`LabItemId`必须是`DictLabItem`表中已存在且`IsCalculated=1`的项目。
   - **状态管理**：通过`Status`字段控制公式的启用/停用，支持公式版本管理。

4. **业务应用场景**：
   - **自动计算**：当相关基础项目有结果时，系统自动根据公式计算出计算项目的结果。
   - **结果验证**：在结果审核阶段，可以重新计算验证计算项目结果的正确性。
   - **公式维护**：当计算方法发生变化时，可以更新公式表达式，历史数据通过时间戳字段追溯。
   - **质量控制**：通过比对手工计算和系统自动计算的结果，验证公式的正确性。

5. **计算引擎集成**：
   - **解析器**：应用层需要实现公式解析器，将字符串表达式转换为可执行的计算逻辑。
   - **依赖检测**：系统需要检测公式中引用的项目是否都有结果，确保计算的完整性。
   - **错误处理**：当公式计算出现错误时（如除零、项目结果缺失），需要有相应的错误处理机制。
   
### 23. DictUnit 表设计说明

   1. **表设计目的**：
      - **标准化管理**：建立一个全系统统一、权威的检验结果单位字典，从源头上杜绝同一单位出现多种写法（如 "g/L", "G/L", "克/升"）所导致的数据混乱问题。
      - **数据完整性**：通过外键关联，确保所有检验项目定义、参考范围和最终结果所使用的单位都必须是预先定义的、有效的记录，保证了数据的规范性和准确性。
      - **提升可维护性**：将单位的定义与使用解耦。未来新增、停用或修改单位时，仅需在此表中操作，无需改动其他表的结构或数据，简化了系统维护工作。
   2. **字段设计**：
      - UnitId (INT): 作为系统内部关联的代理主键。使用整数类型进行表连接（JOIN）可以获得比字符串更高的性能，尤其是在 LabResult 这种海量数据表中，性能优势更为明显。
      - UnitName (NVARCHAR(30)): 作为单位的业务标识和显示名称。通过设置唯一约束（UNIQUE），强制保证了系统中每个单位名称的唯一性，是实现单位标准化的核心。
      - Status (BIT): 采用标准的"软删除"设计。允许将不再使用的单位设置为"停用"状态，而非物理删除，从而保证了历史数据的引用完整性。
   3. **索引与约束设计**：
      - UQ_DictUnit_Name 唯一约束是此表的关键，它在数据库层面强制执行了"单位名称不可重复"的业务规则，是数据质量的第一道防线。
      - 主键 PK_DictUnit 和状态索引 IX_DictUnit_Status 是标准配置，分别用于高效的记录定位和快速筛选出所有"启用"状态的单位，以供前端界面（如项目配置页的下拉列表）调用。
   4. **业务应用场景**：
      - **项目字典配置**：在 DictLabItem 表中定义检验项目时，为其指定默认单位（DefaultUnitId），该单位从本字典表中选取。
      - **参考范围定义**：在 DictLabItemRefRange 表中，为不同性别、年龄段的参考范围指定其对应的单位。
      - **结果存储与报告**：在 LabResult 表中记录每条检验结果的单位ID，确保在检验报告单上显示标准、一致的单位名称。
      - **系统间数据交换**：当与 HIS、EMR 或其他外部系统进行数据交互时，使用此表定义的标准化单位，是保证信息准确传递的基础。

### 24. DictLabProfile 表设计说明

1. **表设计目的**：
   - **检验组合管理**：统一管理医院可开立的检验组合或套餐，如"血常规"、"肝功能全项"等，是临床医生开立检验申请的主要实体。
   - **HIS系统对接**：通过HisProfileCode字段实现与HIS系统医嘱项目的对接，支持医嘱同步和费用结算。
   - **业务标准化**：建立标准化的检验组合定义，确保同一组合在不同科室、不同医生间的一致性。
   - **报告管理**：为检验报告的生成和标题显示提供标准化的组合信息。

2. **字段设计**：
   - **ProfileCode**：院内统一的组合编码，简洁易记，便于程序逻辑引用和用户快速定位。
   - **ProfileName**：组合的中文名称，是临床医生选择和患者理解的主要标识。
   - **HisProfileCode**：与HIS系统对接的编码，支持长编码格式，处理系统间编码差异。
   - **ReportTitle**：专门用于报告单标题显示，如果为空则使用ProfileName，提供了报告显示的灵活性。
   - **PinYin**：支持拼音检索，提高医生开单效率，特别是在组合数量较多的情况下。

3. **业务应用场景**：
   - **医嘱开立**：临床医生在HIS或LIS系统中选择检验组合进行开单申请。
   - **自动分解**：系统根据组合定义自动分解为具体的检验项目，生成相应的LabOrderItem记录。
   - **费用计算**：按组合进行费用计算和医保结算，简化计费流程。
   - **报告生成**：使用ReportTitle字段生成标准化的检验报告标题。
   - **统计分析**：按组合统计检验工作量、收入和申请频次等指标。

4. **扩展性设计**：
   - **状态管理**：通过Status字段控制组合的启用/停用，支持组合的生命周期管理。
   - **描述信息**：Description字段可记录组合的临床意义、注意事项等，为医生提供参考信息。
   - **多语言支持**：通过EnglishName字段支持国际化需求或与国外系统对接。

### 25. DictLabProfileItemMap 表设计说明

1. **表设计目的**：
   - **组合分解管理**：定义检验组合与具体检验项目之间的多对多关系，实现组合的灵活配置。
   - **自动化处理**：为系统自动分解检验组合为具体项目提供映射规则，减少手工操作。
   - **个性化配置**：支持同一组合在不同情况下包含不同的项目，如可选项目的处理。
   - **报告布局**：通过SequenceNo控制项目在报告中的显示顺序，保证报告的专业性和可读性。

2. **关联设计**：
   - **一对多关系**：一个检验组合可以包含多个检验项目，通过ProfileId关联DictLabProfile表。
   - **多对一关系**：一个检验项目可以属于多个不同的组合，通过LabItemId关联DictLabItem表。
   - **唯一性约束**：确保同一组合内不会重复包含相同的检验项目，避免数据冗余。

3. **业务功能设计**：
   - **默认项目标识**：通过IsDefault字段区分必选项目和可选项目，支持灵活的组合配置。
   - **显示顺序控制**：SequenceNo字段确保报告中项目按照专业逻辑顺序排列，如生化项目按器官系统分组。
   - **版本管理**：通过CreatedAt和UpdatedAt字段追踪组合配置的变更历史。

4. **业务应用场景**：
   - **组合分解**：当医生开立检验组合时，系统根据此表自动生成对应的LabOrderItem记录。
   - **报告生成**：按照SequenceNo顺序在报告中显示各项目结果，保证报告的专业性。
   - **组合维护**：检验科可以根据临床需要调整组合包含的项目，如增加新项目或移除过时项目。
   - **成本控制**：通过IsDefault字段实现组合的基础版本和完整版本，满足不同的临床需求和费用控制要求。
   - **质量管理**：确保相关联的项目能够在同一标本上检测，提高检验效率和质量。

### 26. SysUser 表设计说明

1. **表设计目的**：
   - **身份认证中心**：作为系统身份认证和访问控制的核心表，管理所有可以登录系统的用户账号。
   - **操作审计基础**：为系统操作日志、审核记录等提供用户身份信息，支持完整的操作追溯。
   - **权限管理基础**：与角色权限系统结合，实现基于用户身份的功能权限控制和数据访问控制。
   - **人员信息整合**：通过与DictDoctor表的关联，实现登录账号与医生信息的统一管理。

2. **安全性设计**：
   - **密码安全**：PasswordHash字段强制使用哈希加密存储，严禁明文密码，建议使用Argon2或bcrypt等强哈希算法。
   - **账号锁定**：IsLockedOut字段支持因密码错误等原因的账号锁定机制，防止暴力破解攻击。
   - **登录追踪**：LastLoginAt字段记录最后登录时间，便于安全审计和异常检测。
   - **状态控制**：Status字段支持账号的启用/停用管理，便于人员离职或角色变更时的权限控制。

3. **关联设计**：
   - **医生信息关联**：通过DoctorId字段可选关联DictDoctor表，获取职称、科室等专业信息。
   - **角色权限关联**：通过RoleCode字段关联角色权限系统，实现基于角色的访问控制(RBAC)。
   - **操作审计关联**：作为各种操作记录表(如LabResultReview)的操作员外键，实现操作追溯。
   - **层级管理**：通过CreatedBy和UpdatedBy字段实现用户管理的责任追踪。

4. **业务应用场景**：
   - **用户登录**：验证用户身份，建立用户会话，记录登录时间和状态。
   - **权限控制**：根据用户角色和状态控制系统功能访问权限和数据查看范围。
   - **操作记录**：在审核、修改、创建等操作中记录具体的操作人员信息。
   - **安全审计**：通过登录记录、锁定状态等信息进行安全事件分析和风险评估。
   - **人员管理**：与医生信息结合，实现统一的人员账号管理和权限分配。

5. **数据完整性设计**：
   - **唯一性保证**：LoginName字段的唯一约束确保账号的唯一性，防止账号冲突。
   - **外键约束**：与DictDoctor和SysRole的外键关联保证数据引用的有效性。
   - **审计字段**：CreatedBy和UpdatedBy形成用户管理的责任链，便于账号变更的追溯。
   - **软删除支持**：通过Status字段实现软删除，保证历史操作记录的完整性。

## 数据库架构优化升级说明

### 优化背景
基于对LIS系统核心业务表的深度分析，针对检验医师和审核医师管理的业务需求，对数据库结构进行了重要优化升级。

### 主要优化内容

#### 1. LabResult表重构
**优化目标**：简化审核流程设计，支持多级审核，明确检验执行和审核责任

**主要变更**：
- 移除了混乱的初审/复审字段设计
- 新增检验技师相关字段：`TestingTechnicianId`, `TestingTechnicianName`, `TestingCompletedAt`
- 简化审核状态管理：`CurrentReviewLevel`, `MaxReviewLevel`  
- 明确最终签发责任：`FinalSignerId`, `FinalSignerName`, `FinalSignedAt`
- 优化索引设计，提升查询性能

#### 2. 新增LabResultReview表
**设计目标**：独立管理审核过程，支持多级审核，区分操作员和责任医师

**核心功能**：
- 每次审核操作都产生独立记录
- 明确区分实际操作员（`OperatorId`）和责任医师（`ResponsibleDoctorId`）
- 支持审核意见和职称信息记录
- 提供完整的审核轨迹追踪

#### 3. LabOrderItem表增强
**优化目标**：在项目级别记录执行人员信息

**主要增加**：
- 分配信息：`AssignedTechnicianId`, `AssignedTechnicianName`
- 执行信息：`ExecutedById`, `ExecutedByName`, `ExecutedAt`
- 扩展状态：支持质控中、重检、标本异常等更精细的状态管理

### 业务流程优化

#### 新的数据流转设计
```
申请下达 → LabOrderItem记录分配技师 
        ↓
检验执行 → LabOrderItem记录执行信息
        ↓  
结果生成 → LabResult记录检验技师和结果
        ↓
审核流程 → LabResultReview记录每级审核
        ↓
最终签发 → LabResult记录签发医师
```

#### 关键优势
1. **职责清晰**：明确区分检验执行、审核操作、医疗责任
2. **流程灵活**：支持多级审核，适应不同项目的审核要求
3. **追溯完整**：详细记录每个环节的人员和时间信息
4. **查询高效**：优化的索引设计支持各种统计查询

### 数据迁移建议

#### SQL脚本示例
```sql
-- 建议修改LabResult表：
ALTER TABLE LabResult 
-- 删除现有混乱字段
DROP COLUMN FirstReviewedById, FirstReviewerName, FirstReviewedAt, FirstSignerId, FirstSignerName,
           FinalReviewedById, FinalReviewerName, FinalReviewedAt, FinalSignerId, FinalSignerName;

-- 增加明确的字段
ALTER TABLE LabResult ADD 
    -- 检验医师（实际执行检验的人）
    TestingTechnicianId INT,                    -- 检验技师ID
    TestingTechnicianName NVARCHAR(100),        -- 检验技师姓名
    TestingCompletedAt DATETIME,                -- 检验完成时间
    
    -- 当前审核状态
    CurrentReviewLevel TINYINT DEFAULT 0,       -- 当前审核级别
    MaxReviewLevel TINYINT DEFAULT 2,           -- 需要的最大审核级别
    FinalSignerId INT,                          -- 最终签发医师ID
    FinalSignerName NVARCHAR(100),              -- 最终签发医师姓名
    FinalSignedAt DATETIME;                     -- 最终签发时间

-- 创建详细的审核记录表
CREATE TABLE LabResultReview (
    ReviewId BIGINT IDENTITY(1,1) PRIMARY KEY,
    ResultId BIGINT NOT NULL,
    ReviewLevel TINYINT NOT NULL,               -- 1=初审,2=复审,3=三审...
    ReviewAction TINYINT NOT NULL,              -- 1=通过,2=退回,3=修改
    OperatorId INT NOT NULL,                    -- 操作员ID (关联SysUser)
    OperatorName NVARCHAR(100) NOT NULL,        -- 操作员姓名
    ResponsibleDoctorId INT,                    -- 责任医师ID (关联DictDoctor)  
    ResponsibleDoctorName NVARCHAR(100),        -- 责任医师姓名
    ResponsibleDoctorTitle NVARCHAR(50),        -- 责任医师职称
    ReviewComment NVARCHAR(500),                -- 审核意见
    ReviewedAt DATETIME DEFAULT GETDATE(),      -- 审核时间
    FOREIGN KEY (ResultId) REFERENCES LabResult(ResultId),
    FOREIGN KEY (OperatorId) REFERENCES SysUser(UserId),
    FOREIGN KEY (ResponsibleDoctorId) REFERENCES DictDoctor(DoctorId)
);

-- 在医嘱明细表中记录具体执行人员
ALTER TABLE LabOrderItem ADD 
    AssignedTechnicianId INT,                   -- 分配的检验技师
    AssignedTechnicianName NVARCHAR(100),       -- 技师姓名
    ExecutedById INT,                           -- 实际执行人ID
    ExecutedByName NVARCHAR(100),               -- 实际执行人姓名
    ExecutedAt DATETIME,                        -- 执行时间
    CONSTRAINT FK_OrderItem_AssignedTech 
    FOREIGN KEY (AssignedTechnicianId) REFERENCES DictDoctor(DoctorId),
    CONSTRAINT FK_OrderItem_ExecutedBy 
    FOREIGN KEY (ExecutedById) REFERENCES DictDoctor(DoctorId);
```

## 总结

本文档定义了LIS系统的完整数据库表结构，包括：
- **仪器管理体系**：仪器主档、项目映射、质控管理等
- **业务流程支撑**：从检验申请到结果发布的完整流程，经过优化的审核管理机制
- **基础字典维护**：检验项目、标本类型、科室医生等核心字典
- **人员责任管理**：明确的检验医师和审核医师管理体系
- **数据标准化**：统一的编码规范和命名约定
- **性能优化**：针对高频查询场景的索引设计

设计遵循以下原则：
- **业务完整性**：覆盖LIS系统核心业务场景
- **数据一致性**：通过外键约束保证数据关联的完整性
- **查询性能**：基于实际查询场景设计索引
- **扩展灵活性**：预留充足的扩展空间，支持未来业务发展
- **运维友好**：合理的字段命名和注释，便于维护
- **责任明确**：清晰的人员职责划分和权限管理

该设计可作为LIS系统数据库实施的标准规范，特别是优化后的审核流程设计，更好地满足了实际业务需求。