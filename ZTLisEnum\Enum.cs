﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZTLisEnum
{
    /// <summary>
    /// 通讯方式枚举
    /// </summary>
    public enum ConnType
    {
        /// <summary>
        /// RS232 (串口)
        /// </summary>
        串口,

        /// <summary>
        /// TCP
        /// </summary>
        TCP,

        /// <summary>
        /// 文件共享
        /// </summary>
        文件共享,

        /// <summary>
        /// 中间库
        /// </summary>
        中间库,

        /// <summary>
        /// REST API
        /// </summary>
        REST接口,

        /// <summary>
        /// HL7 MLLP
        /// </summary>
        HL7_MLLP
    }
    /// <summary>
    /// 科室类型枚举
    /// </summary>
    public enum DeptType
    {
        医技,
        临床,
        行政
    }
    /// <summary>
    /// 协议类型枚举
    /// </summary>
    public enum ProtocolType
    {
        /// <summary>
        /// ASTM
        /// </summary>
        ASTM = 0,

        /// <summary>
        /// LIS2
        /// </summary>
        LIS2 = 1,

        /// <summary>
        /// POCT1-A
        /// </summary>
        POCT1_A = 2,

        /// <summary>
        /// HL7
        /// </summary>
        HL7 = 3,

        /// <summary>
        /// 自定义
        /// </summary>
        自定义 = 4
    }

    /// <summary>
    /// 校验位枚举
    /// </summary>
    public enum Parity
    {
        [Description("无校验")]
        N,
        [Description("偶校验")]
        E,
        [Description("奇校验")]
        O,
        [Description("标记")]
        M,
        [Description("空格")]
        S
    }

    /// <summary>
    /// 异常标志枚举
    /// </summary>
    public enum AbnormalFlag
    {
        /// <summary>
        /// 正常
        /// </summary>
        正常 = 'N',

        /// <summary>
        /// 高
        /// </summary>
        高 = 'H',

        /// <summary>
        /// 低
        /// </summary>
        低 = 'L',

        /// <summary>
        /// 阳性
        /// </summary>
        阳性 = 'A'
    }

    /// <summary>
    /// 结果格式枚举
    /// </summary>
    public enum ResultFormat
    {
        /// <summary>
        /// 数值
        /// </summary>
        数值,

        /// <summary>
        /// 文本
        /// </summary>
        文本,

        /// <summary>
        /// 阴阳性
        /// </summary>
        阴阳性
    }
    /// <summary>
    /// 消息格式类型
    /// </summary>
    public enum MsgFormatType
    {
        /// <summary>
        /// 字符串格式
        /// </summary>
        字符串 = 0,

        /// <summary>
        /// 字节流格式
        /// </summary>
        字节流 = 1
    }
    /// <summary>
    /// 性别编码
    /// </summary>
    public enum Sex_Code
    {
        男,
        女
    }

    /// <summary>
    /// 标本参数类型枚举
    /// </summary>
    public enum ParamType
    {
        /// <summary>
        /// 常规参数（如血细胞比容HCT等）
        /// </summary>
        常规参数 = 0,

        /// <summary>
        /// 质量指数（如溶血指数、脂血指数、黄疸指数等）
        /// </summary>
        质量指数 = 1,

        /// <summary>
        /// 其他特殊参数
        /// </summary>
        其他 = 2
    }

    /// <summary>
    /// 年龄单位枚举
    /// </summary>
    public enum AgeUnit
    {
        岁,
        月,
        天
    }

    /// <summary>
    /// 标本状态枚举
    /// </summary>
    public enum SpecimenStatus
    {
        /// <summary>
        /// 待采集
        /// </summary>
        待采集 = 10,

        /// <summary>
        /// 已采集
        /// </summary>
        已采集 = 20,

        /// <summary>
        /// 已送检
        /// </summary>
        已送检 = 30,

        /// <summary>
        /// 检验中
        /// </summary>
        检验中 = 40,

        /// <summary>
        /// 已完成
        /// </summary>
        已完成 = 50,

        /// <summary>
        /// 已审核
        /// </summary>
        已审核 = 60,

        /// <summary>
        /// 已作废
        /// </summary>
        已作废 = 99
    }

    /// <summary>
    /// 连接器错误码枚举
    /// </summary>
    public enum ConnectorErrorCode
    {
        /// <summary>
        /// 连接器启动错误
        /// </summary>
        启动错误 = 1001,

        /// <summary>
        /// 连接器停止错误
        /// </summary>
        停止错误 = 1002,

        /// <summary>
        /// 发送数据失败
        /// </summary>
        发送失败 = 1003,

        /// <summary>
        /// 初始化配置失败
        /// </summary>
        初始化失败 = 1004,

        /// <summary>
        /// 数据处理错误
        /// </summary>
        数据处理错误 = 1005,

        /// <summary>
        /// 接收数据错误
        /// </summary>
        接收错误 = 1006,

        /// <summary>
        /// 硬件错误事件
        /// </summary>
        硬件错误 = 1007,

        /// <summary>
        /// 发送文本命令失败
        /// </summary>
        发送文本失败 = 1008,

        /// <summary>
        /// 默认数据处理错误代码
        /// </summary>
        默认_数据处理错误 = 9999
    }
    /// <summary>
    /// 枚举扩展方法类
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// 获取ResultFormat的字符串表示
        /// </summary>
        /// <param name="format">结果格式</param>
        /// <returns>字符串表示</returns>
        public static string ToFormatString(this ResultFormat format)
        {
            switch (format)
            {
                case ResultFormat.数值:
                    return "NUM";
                case ResultFormat.文本:
                    return "TXT";
                case ResultFormat.阴阳性:
                    return "NEG";
                default:
                    throw new ArgumentOutOfRangeException(nameof(format));
            }
        }

        /// <summary>
        /// 从字符串解析ResultFormat
        /// </summary>
        /// <param name="formatString">格式字符串</param>
        /// <returns>ResultFormat枚举值</returns>
        public static ResultFormat ParseResultFormat(string formatString)
        {
            switch (formatString?.ToUpper())
            {
                case "NUM":
                    return ResultFormat.数值;
                case "TXT":
                    return ResultFormat.文本;
                case "NEG":
                    return ResultFormat.阴阳性;
                default:
                    throw new ArgumentException($"无效的结果格式: {formatString}");
            }
        }

    }
}
