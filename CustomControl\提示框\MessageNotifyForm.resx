﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="TimerUp.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="TimerStay.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>107, 17</value>
  </metadata>
  <metadata name="TimerDown.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>197, 17</value>
  </metadata>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>317, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACe
        IAAAAk1TRnQBSQFMAgEBAwEAAQgBAAEIAQABGAEAARgBAAT/ARkBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEYBgABGyEAA/8D+wLqAe4BxwHIAdkBkAGVAcEBdgF9Ab0BXAFlAb4BUgFbAbwBbwF2
        AbsBiQGOAcEBvAG+AdQC4wHpAvgB+QP/DwAB8QLyAYkBqgHKAUABiwHUATgBhwHVATgBhgHRATYBhQHQ
        ATYBhQHOATUBgwHKATUBggHIATUBfwHHATQBfgHGATMBfAHDATMBfAHCATQBfgHFATUBfgHGATUBgQHI
        ATUBggHIATYBhAHMATYBhQHQATYBhgHQATgBhwHUAT0BiAHVAXgBogHMAeEB5AHoEgAD/wP+A/sD9wz1
        A/cD+gP9A/5mAAP9AecB6AHqAaQBpwHLAWABaAHEAT4BTAHNATUBRQHXATYBRQHcATcBRwHdATcBRwHd
        ATYBRgHdATUBRAHZATkBSgHQAVMBXwHEAZMBmAHJAt0B4gP7DAABkgGoAcUBPQGcAewBBQGNAfwBAgGM
        Af0BAgGMAf0BAgGOAf0BAgGQAfwBAgGQAfwBAgGRAfwBAgGRAfwBAgGRAfwBAgGSAfwBAgGSAfsBAgGS
        AfwBAgGRAfwBAgGRAfwBAgGRAfwBAgGPAfwBAgGOAf0BAgGNAf0BAgGMAf0BAgGMAf0BJwGXAfABhAGq
        Ac0MAAP/A/4D+gPxAdsB2QHXAcEBqwGYAcEBlwFzAb8BlQFxAb8BlQFxAcEBlwF0AcIBpQGNAdIBzQHK
        Ae8C7gP4A/0D/10AA/4B0gHTAdoBXgFnAb4BMwFCAdUBOgFJAeMBOwFLAegBOwFLAecBOwFLAecBOwFL
        AecBOwFLAecBOwFLAecBOwFLAecBOwFLAegBOgFKAeUBNQFEAdgBPQFMAcIBuQG6AcwC+AH5CQABeAGa
        AcQBWgGxAfUBDAGXAf8BAAGRAf8BAAGTAf8BAAGVAf8BAAGYAf8BAAGZAf8BAAGbAf8BAAGcAf8BAAGf
        Af8BAAGhAf8BAAGhAf8BAAGfAf8BAAGcAf8BAAGbAf8BAAGaAf8BAAGYAf8BAAGWAf8BAAGTAf8BAAGR
        Af8BBAGUAf8BQgGqAfgBfAGlAc0MAAP9A/QB3ALbAaoBcQE+AbcBZgEfAb8BcgErAcQBegEvAcIBdwEs
        AcABdAEnAb4BcQEkAboBawEfAbYBZAEbAa4BZwEtAcoBwQG5A/ED+wP/VwAD+wHAAcIB0QFVAWEBxgE5
        AUkB3QE7AUsB5wE7AUsB6AE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7
        AUsB5wE7AUsB5wE7AUsB5wE5AUkB4gFGAVMBzgGoAasBygP4BgAB1wHaAdwBZAGnAeYBLQGnAf0BAQGX
        Af8BAAGaAf8BAAGcAf8BAAGfAf8BAAGhAf8BAAGjAf8BAAGmAf8BGgGvAf4BMwGoAfQBLwGfAe8BKgG0
        Af4BAgGnAf8BAAGkAf8BAAGiAf8BAAGgAf8BAAGdAf8BAAGaAf8BAQGYAf8BGgGhAf8BYwGvAeoBuwHI
        AdUGAAP/A/0B7AHrAekBvgGlAZIBugFsASYB0wGTAVEB4QGrAW8B6gG8AYQB7wHHAZEB8AHHAZEB8AHG
        AZAB7wHFAY4B6QG6AX8B2wGjAWQBywGIAUIBuQFpASEBrwGMAW0B4wHeAdoD+gP/UQAD/QHMAc4B2wFT
        AV0ByQE6AUoB4QE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7
        AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE6AUkB4wFLAVgBzQGwAbIByQP5AwAD/AGH
        AasBzgFQAbEB9AEMAaEB/wEAAaAB/wEAAaMB/wEAAaYB/wEAAacB/wEAAakB/wEAAa0B/wEfAbQB/AEl
        AXQB0AEUAVIBvAEzAboB/AEDAa8B/wEAAaoB/wEAAagB/wEAAaYB/wEAAaQB/wEAAaAB/wEKAaEB/wFE
        AbEB+wGCAa4B1AHpAewB7gMAA/8D/QPxAb0BlQFzAcYBhAFCAegBtwF/Ae0BwgGLAecBuAF+AeMBsgF1
        AeEBrwFwAeABrwFvAeABrwFwAeABrwFwAeIBsgFzAeUBtQF5AegBuQF+AeABqAFrAb4BbwEmAbYBfQFL
        AeMB3gHaA/sD/04AAu0B8AFwAXgBxQE6AUkB5AE7AUsB5wE7AUsB5wE7AUsB5wE7AUoB5gE6AUoB5gE7
        AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE6AUoB5gE5AUkB5QE7AUsB5wE7AUsB5wE7
        AUsB5wE6AUoB5AFNAVsBywHRAdIB2wP+AwAB0QHZAeABbQGxAecBKAGuAf4BAwGlAf8BAAGnAf8BAAGq
        Af8BAAGtAf8BAAGvAf8BAAGyAf8BDwG4Af0BDwGTAeQBBgGAAdkBGQG7Af4BAQG0Af8BAAGwAf8BAAGt
        Af8BAAGrAf8BAAGoAf8BAgGmAf8BIQGsAf4BbAG6AfQBrAG/AdID/AMAA/4D9gGpAW4BPgHOAYsBSQHx
        AckBkwHrAb8BhwHeAaoBaQHfAawBawHfAawBawHfAasBagHfAaoBaQHfAaoBaQHfAasBagHfAawBawHf
        AawBbAHfAawBawHlAbUBeAHzAcgBlAHBAXYBLQGtAW0BMwHxAvAD/UsAA/4BsAGxAdABRQFUAdkBOwFL
        AecBOwFLAecBOwFLAeYBOQFIAd8BPwFMAdEBPQFLAdIBOQFIAeIBOwFLAecBOwFLAecBOwFLAecBOwFL
        AecBOQFJAeMBOwFJAdUBQQFOAc8BOQFJAdsBOgFKAeYBOwFLAecBOwFLAecBPAFMAeEBigGRAcoC9AH1
        AwAC9AH1AZIBsgHSAVUBugH5ARMBrwH/AQABrAH/AQABrwH/AQABsgH/AQABtAH/AQABtwH/ARQBvwH/
        ASoBuwH4AScBtgH1ASEBwgH/AQIBuQH/AQABtQH/AQABsgH/AQABsAH/AQABrQH/AQwBrQH/AUQBtwH6
        AXoBqQHRAeMB5QHoBgAD+gHJAbcBqgHGAX4BOwHsAb8BiAHqAb0BhQHbAaYBZgHdAagBZwHdAagBZwHb
        AaUBYwHmAcEBkwHmAcABkQHmAcABkQHmAcEBkgHfAa8BcwHdAagBZwHdAagBaAHcAagBZgHiAbABcQHl
        AbIBdgHAAXQBLAGvAYwBcAP2A/5IAAP2AWsBdAHIAT4BSwHiATsBSwHnATsBSwHnATsBSwHlAVQBYQHb
        AaABpgHcAZYBnAHZAT8BTQHQATcBRgHfATsBSwHnATsBSwHnATkBSQHjATwBSQHTAYcBjgHXAa0BsQHd
        AWgBcwHbATwBTAHlATsBSwHnATsBSwHnATsBSwHnAVkBZQHRAdoB2wHkAwAD/gHEAc8B2QFxAbMB6AEw
        AbkB/QEFAbIB/wEAAbMB/wEAAbYB/wEAAbgB/wEAAbwB/wEgAcIB/QEoAYAB1AEZAV8BwgE0AcYB/QED
        Ab4B/wEAAbkB/wEAAbYB/wEAAbQB/wEAAbEB/wEiAbcB/gFiAbQB6gGjAbQBxAP8AwAD/wHqAecB5AG9
        AYgBWAHpAbgBgAHuAcYBkAHcAaUBZAHbAaUBYwHbAaUBYwHbAaUBYgHZAaEBXQHtAdEBrwb/AfYB6AHY
        Ad0BqgFrAdsBpQFiAdsBpQFjAdsBpQFjAdsBpAFiAeMBsQF0AdoBoAFgAbcBZgEdAdYBywHCA/1IAALJ
        AdEBQwFTAdoBOwFLAecBOwFLAecBOwFLAecBRgFVAegBnQGkAeoB7AHrAewB6gHpAeoBqAGsAdwBQAFN
        Ac4BOAFIAeABOwFLAecBOAFHAdIBiQGQAdYC5wHpAfAB7wHsAbUBugHrAU4BXAHoAToBSgHnATsBSwHn
        ATsBSwHnAUIBUAHgAaMBpwHLBgAD9AGPAaoBxgFVAb0B8QESAbgB/wEAAbcB/wEAAboB/wEAAbwB/wEA
        AcAB/wEfAcQB/AEfAW0ByQEMAUIBsgEzAcgB/AEDAcIB/wEAAb0B/wEAAboB/wEAAbcB/wEDAbUB/wFG
        Ab8B+gF/AawB0APsBgAD/QHaAc4BwgHUAZQBVAH/AeMBtgHaAaQBYQHZAaEBXgHZAaIBXwHZAaIBXwHZ
        AaIBXwHYAaABXAHiAboBiAb/AfEB3QHEAdcBnQFXAdkBogFfAdkBogFfAdkBogFfAdkBogFfAdkBogFf
        AesBvQGFAcMBeQEvAcUBnQF6A/pIAAGfAaIBwgE+AU0B4AE7AUsB5wE7AUsB5wE7AUsB5wE8AUwB5wFV
        AWIB6AGqAbAB6gHoAecB7APrAagBrQHcAUwBWAHQATgBRgHQAYwBkgHWAuAB6AHwAe8B7AHBAcUB6gFq
        AXYB6AE8AUwB5wE7AUsB5wE7AUsB5wE7AUsB5wE+AU0B4QGHAY0ByAkAAdAB0wHXAXcBswHbATEBwAH8
        AQABugH/AQABvQH/AQABwAH/AQABxAH/AR8ByAH8AR8BbQHJAQwBQgGyATMBywH8AQMBxgH/AQABwQH/
        AQABvgH/AQABuwH/ASMBwgH/AWUBuAHoAbEBwQHPA/8GAAP7AbsBngGCAeYBtAF7Ae0BxwGQAdcBnAFX
        AdgBnwFbAdgBnwFbAdgBnwFbAdgBnwFbAdcBnQFYAeIBuAGGBv8B8QHcAcMB1gGaAVMB2AGfAVsB2AGf
        AVsB2AGfAVsB2AGfAVsB1wGeAVkB4gGwAXMB2wGiAWIBtgGAAVAB6wHpAehIAAGAAYcBxQE/AUwB4gE7
        AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wFVAWMB6AGiAakB6gHtAuwC6QHqAbYBuQHfAZwBoQHa
        AeMB5AHoAe8B7gHsAbwBwAHrAWMBcAHoAT8BTgHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnATwBTAHj
        AW0BdQHMCQAD+gGhAbkBywFWAcEB8AEOAcEB/wEAAcAB/wEAAcIB/wEAAccB/wEfAcoB/AEfAW4ByQEM
        AUIBsgEzAc0B/AEDAcgB/wEAAcMB/wEAAcAB/wEKAcEB/wFMAckB/AGPAbUB0AHsAe4B7wkAA/oBvAF6
        AT8B9AHQAZ0B2wGnAWUB1QGaAVUB1gGcAVcB1gGcAVcB1gGcAVcB1gGcAVcB1QGaAVQB4QG2AYMG/wHv
        AdoBwQHUAZcBTwHWAZwBVwHWAZwBVwHWAZwBVwHWAZwBVwHWAZsBVgHZAaEBXgHwAcQBjwG4AW8BLAHl
        AdwB1EgAAWoBcQHMAT4BTAHjATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnAUwBWwHo
        AaEBqAHqAe0B7AHrAfMB8gHsAfMB8gHsAfAB7wHsAboBvgHqAVIBYAHoATsBSwHnATsBSwHnATsBSwHn
        ATsBSwHnATsBSwHnATsBSwHnAT0BTAHlAVwBZwHSDAAB0wHZAd0BdAG2Ad4BOQHPAf8BAAHDAf8BAAHF
        Af8BAAHKAf8BHwHLAfwBHwFuAckBDAFCAbIBMwHPAfwBAwHLAf8BAAHGAf8BAAHDAf8BKwHLAf8BYwG9
        AfABuwHHAdEC+gH7CQAD+gHCAXgBNQH5AdsBrAHQAZEBSgHTAZcBUgHUAZgBUwHUAZgBUwHUAZgBUwHU
        AZgBUwHTAZYBUAHfAbMBgAb/Ae8B2QHAAdIBkwFLAdQBmAFTAdQBmAFTAdQBmAFTAdQBmAFTAdQBmAFT
        AdIBlQFPAfgB0gGhAb4BdAErAeQB2QHQSAABbwF3AcwBPQFMAeMBOwFLAecBOwFLAecBOwFLAecBOwFL
        AecBOwFLAecBOwFLAeYBOQFIAeABVAFfAdQBxAHHAeIB8QHwAewB8QHwAewB2wHcAecBcQF6AdcBOQFI
        Ad0BOgFKAeYBOwFLAecBOwFLAecBOwFLAecBOwFLAecBOwFLAecBPQFMAeUBYQFqAdMMAAH1AvYBpAG7
        Ac0BVwHJAfcBFwHMAf8BAAHJAf8BAAHNAf8BHwHPAfwBHwFvAckBDAFCAbIBMwHSAfwBAwHPAf8BAAHK
        Af8BDAHKAf8BSAHKAfkBiAGvAcsB6gHrAewD/wkAA/oBwgF4ATcB+gHdAa8BzgGOAUUB0QGUAU0B0gGV
        AU4B0gGVAU4B0gGVAU4B0gGVAU4B0QGTAUkB3QGwAXoG/wHvAdgBvgHQAZABRgHSAZUBTgHSAZUBTgHS
        AZUBTgHSAZUBTgHSAZUBTgHQAZIBSgH5AdMBowHBAXYBLgHkAdkB0EgAAY0BkwHFAT4BTQHiATsBSwHn
        ATsBSwHnATsBSwHnATsBSwHnATsBSwHnATgBSAHeAU4BWgHTAagBrAHcAukB6gHxAfAB7AHwAe8B7AHu
        Ae0B6wG8Ab8B4AFWAWIB0QE4AUcB3AE6AUoB5gE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE/AU0B5AF2
        AX4BzQwAA/0B1gHaAd4BdAG1AdsBOAHRAf0BBQHNAf8BAAHPAf8BHwHRAfwBHwFwAckBDAFCAbIBMwHU
        AfwBAwHRAf8BAAHMAf8BJgHQAf4BZwG9AeQBwgHJAdAD+wwAA/oBxAF6ATgB+wHgAbMBywGKAUABzwGR
        AUkB0AGSAUoB0AGSAUoB0AGSAUoB0AGRAUkBzgGNAUIB8wHkAdIG/wHuAdgBvgHPAY4BQwHQAZIBSgHQ
        AZIBSgHQAZIBSgHQAZIBSgHQAZIBSQHOAY4BRQH6AdUBpQHDAXkBMgHkAdkB0EgAAaQBpwHGAT8BTgHh
        ATsBSwHnATsBSwHnATsBSwHnATsBSwHnATcBRwHgAUoBVgHRAagBrAHcAfIB8QHsAu0B7AG6Ab4B6wGg
        AacB6gPsAfMB8gHsAcgBygHiAVgBYwHQATgBRwHbATsBSwHnATsBSwHnATsBSwHnATsBSwHnAT8BTQHh
        AYwBkgHJDwAD9AGeAbIBxAFUAcoB9QEVAdQB/wEAAdMB/wEhAdkB/gFBAcUB8gE8AboB7AE1AdwB/gED
        AdQB/wEAAdEB/wFNAdIB+QGPAbUBzAPtDwAD+wHDAXsBOAH6Ad8BsQHQAZMBTAHOAY0BRAHPAY8BRgHP
        AY8BRgHPAY8BRgHPAY8BRQHOAYwBQQHLAYYBNwHKAYMBMgHKAYIBMQHMAYgBOgHOAY4BQwHPAY8BRgHP
        AY8BRgHPAY8BRgHPAY8BRgHPAY4BRQHQAZEBSAH4AdMBoQHDAXoBNAHmAdwB00gAAtoB3gFOAVwB2wE7
        AUsB5gE7AUsB5wE7AUsB5wE7AUsB5QFRAV4B2gGoAa0B3gLpAeoC6wHsAbABtQHqAVUBYgHoAT8BTwHn
        AZkBoAHqAuIB7AHzAfIB7AG/AcIB4QFlAXAB2gE8AUwB5AE7AUsB5wE7AUsB5wE7AUsB5wFIAVUB3AG2
        AbgB1A8AA/4B2QHaAdwBegG4AdcBNQHXAfwBAAHWAf8BBwHZAf8BDwHcAf8BDwHcAf8BCwHaAf8BAQHW
        Af8BJgHbAf8BbAHBAeUBxgHPAdYD/Q8AA/4BvQGbAXkB8QHKAZgB5gG4AXwBygGHAT0BzQGLAUIBzQGL
        AUIBzQGLAUIBzQGLAUIBzQGKAUEBzQGNAUUB2AGlAWwB2gGrAXUB0QGUAVEBzAGKAT8BzQGLAUIBzQGL
        AUIBzQGLAUIBzQGLAUIBywGKAUAB2QGhAV4B6wG7AYIBvAGGAVQB7QHrAehIAAP6AX0BgwHOAT8BTAHj
        ATsBSwHnATsBSwHnAUQBUwHnAZABmAHoAeMB5AHrAucB6wGqAbAB6gFUAWEB5wE8AUwB5wE7AUsB5wFJ
        AVcB5wGSAZsB6QLkAewD6wGnAa0B6QFJAVcB5wE6AUoB5wE7AUsB5wE7AUsB5wFlAW4B0gHiAeMB6RIA
        A/cBqQG/Ac0BXQHOAe8BDQHbAf8BAAHaAf8BAAHcAf8BAAHcAf8BAAHaAf8BCQHaAf8BWQHeAfwBjwG4
        AdIB7AHtAe4SAAP/AdsBzQHBAeUBsgF6AfoB5QG5AcoBhwE8AcsBiAE8AcsBiQE+AcsBiQE+AcsBiQE+
        AcsBhwE8AdcBowFqAfwB+QH1A/8B5wHIAaYBygGEATgBywGJAT4BywGJAT4BywGJAT4BywGJAT4BygGH
        ATwB5wG4AX0B3AGjAWIBxwGeAXoD+ksAAcQBxgHZAUsBWQHcATsBSwHnATsBSwHnAToBSgHnAVIBYAHo
        AakBrgHqAZ8BpQHqAUIBUgHnAToBSgHnATsBSwHnATsBSwHnATsBSwHnAT8BTwHnAYwBlQHpAboBvgHr
        AWsBdwHpATsBSwHnATsBSwHnATsBSwHnAT4BTgHjAZoBoAHRAvgB+RUAAdoB3QHgAXgBuwHbAUAB5QH/
        AQAB3QH/AQAB3gH/AQAB3gH/AQAB3gH/AS4B4wH/AWEBwgHuAcYBzQHUA/wVAAHsAecB4wHLAZYBZQH/
        AeEBtAHlAbgBfAHGAYABMwHJAYUBOQHJAYUBOQHJAYUBOQHIAYMBNgHYAaUBbwb/AesB0wG4AccBgAEx
        AckBhQE5AckBhQE5AckBhQE5AcgBgwE2AdMBlQFQAewBwAGJAckBgwFAAdABvwGvA/9LAALzAfUBjgGV
        AdABPwFNAeMBOwFLAecBOwFLAecBOwFLAecBRQFUAecBQwFSAecBOgFKAecBOwFLAecBOwFLAecBOwFL
        AecBOwFLAecBOgFKAecBPwFPAecBSAFXAegBPgFOAecBOwFLAecBOwFLAecBPwFNAeQBagF1Ac8B4AHh
        AecD/hUAA/UBqAG/Ac8BYgHYAfcBFgHiAf8BAAHhAf8BAAHhAf8BCwHhAf8BUAHcAfkBhgGsAcoB7QLu
        A/4VAAP9Ac0BtQGfAecBsgF5AfgB3AGrAdUBnAFZAcUBfgEwAccBgQE0AcgBggE1AcgBgQE0AcwBjAFG
        AeUBxAGgAesB0AG0AdMBmwFfAccBgAExAcgBggE1AcgBgQE0AcYBfwEyAcsBiAE9AeoBvQGEAdsBoAFj
        AbEBfAFOA/tOAAP+AeEB4gHoAW8BeAHRATwBTAHjATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHn
        ATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnAToBSgHnATsBSwHnATsBSwHnATwBTAHl
        AWEBbAHUAc4BzwHZA/0YAAP9AdoB3QHgAWoBtAHdAT8B5wH+AQMB5QH/AQAB5AH/AScB5gH+AWUBvwHk
        AcsBzwHUA/sbAAL2AfUBsgF2AUEB7wHDAY4B9wHcAawB1AGaAVQBwgF4ASkBxAF8AS8BxgF9ATABxQF8
        AS4BxAF5ASoBwwF5ASkBxAF7AS0BxgF9ATABxQF9ATABxAF6ASwBzQGMAUMB8QHKAZUB5QGyAXkBuwF8
        AUQB8QHvAe4D/1EAA/8C3AHfAWsBdQHPATwBTQHkATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHn
        ATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnAVcBYwHWAcUBxwHW
        A/4eAAP3AacBtgHEAVkB1gH1ARgB6wH/AQQB6QH/AU8B4gH5AZQBuQHMA/QhAAP+AboBgQFPAfMBxgGS
        Af8B7gHFAeUBuAF8AcEBdwEmAcEBdQEmAcIBeAEpAcMBeQErAcMBeQErAcMBeQEqAcEBdwEnAb8BcwEi
        AdQBlwFSAfcB1gGlAekBtwF9AcABgwFIAewB5QHgWgAD/wHlAeYB6gGUAZsB0QFPAVwB3QE9AU0B5QE7
        AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5wE7AUsB5gFKAVcB3gF8
        AYMB0gHVAdYB4AP7IQAD/gHiAeMB5AF6AboB2gFeAewB/AFQAfEB/wF4Ac0B5wHMAdUB3AP9JAAB8wHw
        Ae4BxAGdAXsB4QGoAW8B+AHZAaoB+AHaAagB5QG4AXwB1gGgAV4B0wGZAVUB0wGYAVUB1QGbAVgB4AGt
        AW8B8AHHAZIB9QHQAZ4B2wGiAWUBuwGLAV8B5AHZAdAD/10AA/4C9QH2AdQB1QHiAY4BlAHPAVQBYQHZ
        AT4BTgHkATsBSwHnATsBSwHnATsBSwHnATsBSwHnATsBSwHnAUkBVwHbAXsBhAHQAcUBxwHbAvEB8wP9
        JwAD+gGuAcEB0QF/AdMB7QGMAeYB9QGJAbUB1QLxAfIqAAH3AvYB4QHVAcoBvwGMAVwB2AGeAWIB7QHB
        AYwB+wHZAakB/AHaAaoB+gHXAagB+AHTAaIB7QG+AYkB2QGfAWMBwgGCAUkB1AHAAa0B8gHxAfAD/2kA
        A/wC6gHrAboBuwHLAZkBnAHGAXoBggHJAXABeAHMAZcBmwHGAbEBswHJAeIB4wHkA/oD/zAAAe4C7wGQ
        AaoBxQFqAZcBxAHfAeEB4wP+MAAD/gHlAeMB4gGvAYkBaQGsAVYBDgGrAVUBDQGrAVUBDQGsAVYBDgGt
        AXkBTgHSAc8BzAP9XQABQgFNAT4HAAE+AwABKAMAAWADAAEYAwABAQEAAQEFAAEgAQEWAAP/AQAB+AEA
        AR8DAAH8AQABPwMAAfABAAEPAwAB8AEAAQ8DAAHgAQABBwMAAfABAAEHAwABwAEAAQMDAAHAAQABAwMA
        AYABAAEBAwABgAEAAQEDAAGAAgABgAIAAYABAAEBBgABgAEAAQEBgAgAAYABAAEBCQABwAEAAQMJAAHg
        AQABAwkAAeABAAEHCQAB8AEAAQcJAAHwAQABBwkAAfABAAEPCQAB+AEAAR8JAAH4AQABHwkAAfwBAAE/
        BgABgAIAAf4BAAE/AYAFAAGAAgAB/gEAAT8BgAEAAQEDAAGAAQABAQH+AQABfwHAAQABAQMAAcABAAED
        Af8BAAH/AeABAAEHAwAB4AEAAQcB/wEAAf8B8AEAAQcDAAHwAQABDwH/AYEB/wH4AQABDwMAAf4BAAE/
        Af8BwQH/Af4BAAF/AwAL
</value>
  </data>
</root>