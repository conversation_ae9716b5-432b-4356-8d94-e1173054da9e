﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using log4net;

namespace Common.Log
{
    public class LogHelper
    {

        public static void Info(object message)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Info(message);
        }

        public static void Info(object message, Exception ex)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Info(message, ex);
        }

        public static void Warn(object message)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Warn(message);
        }

        public static void Warn(object message, Exception ex)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Warn(message, ex);
        }

        public static void Debug(object message)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Debug(message);
        }

        public static void Debug(object message, Exception ex)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Debug(message, ex);
        }

        public static void Error(object message)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Error(message);
        }

        public static void Error(object message, Exception exception)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Error(message, exception);
        }

        public static void Fatal(object message)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Fatal(message);
        }

        public static void Fatal(object message, Exception exception)
        {
            LogManager.GetLogger(GetCurrentMethodFullName()).Fatal(message, exception);
        }

        private static string GetCurrentMethodFullName()
        {
            try
            {
                StackFrame frame;
                string str2;
                int num = 2;
                StackTrace trace = new StackTrace();
                int length = trace.GetFrames().Length;
                do
                {
                    frame = trace.GetFrame(num++);
                    str2 = frame.GetMethod().DeclaringType.ToString();
                }
                while (str2.EndsWith("Exception") && (num < length));
                string name = frame.GetMethod().Name;
                return (str2 + "." + name);
            }
            catch
            {
                return null;
            }
        }

        public static void DeletLogs(int day)
        {
            if (DirFileHelper.IsExistDirectory("log"))
            {
                string[] logFiles = Directory.GetDirectories("log\\");
                foreach (string logFile in logFiles)
                {
                    DirectoryInfo fileInfo = new DirectoryInfo(logFile);
                    if (fileInfo.CreationTime < DateTime.Now.AddDays(-day))
                    {
                        fileInfo.Delete(true);
                    }
                }
            }

        }

    }
}
