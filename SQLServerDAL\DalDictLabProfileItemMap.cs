﻿/**  版本信息模板在安装目录下，可自行修改。
* DalDictLabProfileItemMap.cs
*
* 功 能： N/A
* 类 名： DalDictLabProfileItemMap
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/7 15:46:35   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalDictLabProfileItemMap
	/// </summary>
	public partial class DalDictLabProfileItemMap : IDalDictLabProfileItemMap
	{
		public DalDictLabProfileItemMap()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long MapId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from DictLabProfileItemMap");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlDictLabProfileItemMap model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into DictLabProfileItemMap(");
			strSql.Append("ProfileId,LabItemId,SequenceNo,IsDefault,CreatedAt,UpdatedAt)");
			strSql.Append(" values (");
			strSql.Append("@ProfileId,@LabItemId,@SequenceNo,@IsDefault,@CreatedAt,@UpdatedAt)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@ProfileId", SqlDbType.Int,4),
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@SequenceNo", SqlDbType.Int,4),
					new SqlParameter("@IsDefault", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime)};
			parameters[0].Value = model.ProfileId;
			parameters[1].Value = model.LabItemId;
			parameters[2].Value = model.SequenceNo;
			parameters[3].Value = model.IsDefault;
			parameters[4].Value = model.CreatedAt;
			parameters[5].Value = model.UpdatedAt;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt64(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlDictLabProfileItemMap model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update DictLabProfileItemMap set ");
			strSql.Append("ProfileId=@ProfileId,");
			strSql.Append("LabItemId=@LabItemId,");
			strSql.Append("SequenceNo=@SequenceNo,");
			strSql.Append("IsDefault=@IsDefault,");
			strSql.Append("CreatedAt=@CreatedAt,");
			strSql.Append("UpdatedAt=@UpdatedAt");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@ProfileId", SqlDbType.Int,4),
					new SqlParameter("@LabItemId", SqlDbType.Int,4),
					new SqlParameter("@SequenceNo", SqlDbType.Int,4),
					new SqlParameter("@IsDefault", SqlDbType.Bit,1),
					new SqlParameter("@CreatedAt", SqlDbType.DateTime),
					new SqlParameter("@UpdatedAt", SqlDbType.DateTime),
					new SqlParameter("@MapId", SqlDbType.BigInt,8)};
			parameters[0].Value = model.ProfileId;
			parameters[1].Value = model.LabItemId;
			parameters[2].Value = model.SequenceNo;
			parameters[3].Value = model.IsDefault;
			parameters[4].Value = model.CreatedAt;
			parameters[5].Value = model.UpdatedAt;
			parameters[6].Value = model.MapId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long MapId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictLabProfileItemMap ");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string MapIdlist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from DictLabProfileItemMap ");
			strSql.Append(" where MapId in (" + MapIdlist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabProfileItemMap GetModel(long MapId)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 MapId,ProfileId,LabItemId,SequenceNo,IsDefault,CreatedAt,UpdatedAt from DictLabProfileItemMap ");
			strSql.Append(" where MapId=@MapId");
			SqlParameter[] parameters = {
					new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parameters[0].Value = MapId;

			Model.MdlDictLabProfileItemMap model = new Model.MdlDictLabProfileItemMap();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlDictLabProfileItemMap DataRowToModel(DataRow row)
		{
			Model.MdlDictLabProfileItemMap model = new Model.MdlDictLabProfileItemMap();
			if (row != null)
			{
				if (row["MapId"] != null && row["MapId"].ToString() != "")
				{
					model.MapId = long.Parse(row["MapId"].ToString());
				}
				if (row["ProfileId"] != null && row["ProfileId"].ToString() != "")
				{
					model.ProfileId = int.Parse(row["ProfileId"].ToString());
				}
				if (row["LabItemId"] != null && row["LabItemId"].ToString() != "")
				{
					model.LabItemId = int.Parse(row["LabItemId"].ToString());
				}
				if (row["SequenceNo"] != null && row["SequenceNo"].ToString() != "")
				{
					model.SequenceNo = int.Parse(row["SequenceNo"].ToString());
				}
				if (row["IsDefault"] != null && row["IsDefault"].ToString() != "")
				{
					if ((row["IsDefault"].ToString() == "1") || (row["IsDefault"].ToString().ToLower() == "true"))
					{
						model.IsDefault = true;
					}
					else
					{
						model.IsDefault = false;
					}
				}
				if (row["CreatedAt"] != null && row["CreatedAt"].ToString() != "")
				{
					model.CreatedAt = DateTime.Parse(row["CreatedAt"].ToString());
				}
				if (row["UpdatedAt"] != null && row["UpdatedAt"].ToString() != "")
				{
					model.UpdatedAt = DateTime.Parse(row["UpdatedAt"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select DictLabProfileItemMap.* ");
			strSql.Append(",DictLabItem.LabItemCode,DictLabItem.LabItemName,DictLabItem.PinYin ");
			strSql.Append(" FROM DictLabProfileItemMap ");
			strSql.Append(" INNER JOIN DictLabItem ON DictLabProfileItemMap.LabItemId = DictLabItem.LabItemId ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" MapId,ProfileId,LabItemId,SequenceNo,IsDefault,CreatedAt,UpdatedAt ");
			strSql.Append(" FROM DictLabProfileItemMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM DictLabProfileItemMap ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.MapId desc");
			}
			strSql.Append(")AS Row, T.*  from DictLabProfileItemMap T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DictLabProfileItemMap";
			parameters[1].Value = "MapId";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 获取指定组合的下一个序号（最大值+1）
		/// </summary>
		/// <param name="profileId">组合ID</param>
		/// <returns>下一个序号</returns>
		public int GetNextSequenceNo(int profileId)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT ISNULL(MAX(SequenceNo), 0) + 1 FROM DictLabProfileItemMap WHERE ProfileId = @ProfileId");
			SqlParameter[] parameters = {
				new SqlParameter("@ProfileId", SqlDbType.Int)
			};
			parameters[0].Value = profileId;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString(), parameters);
			if (obj == null)
			{
				return 1;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

		/// <summary>
		/// 删除项目并自动调整序号
		/// </summary>
		/// <param name="mapId">映射ID</param>
		/// <param name="profileId">组合ID</param>
		/// <returns>是否成功</returns>
		public bool DeleteWithSequenceUpdate(long mapId, int profileId)
		{
			// 先获取要删除项目的序号
			StringBuilder strSqlGet = new StringBuilder();
			strSqlGet.Append("SELECT SequenceNo FROM DictLabProfileItemMap WHERE MapId = @MapId");
			SqlParameter[] parametersGet = {
				new SqlParameter("@MapId", SqlDbType.BigInt)
			};
			parametersGet[0].Value = mapId;

			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSqlGet.ToString(), parametersGet);
			if (obj == null)
			{
				return false; // 记录不存在
			}

			int deletedSequenceNo = Convert.ToInt32(obj);

			// 删除记录
			bool deleteResult = Delete(mapId);
			if (!deleteResult)
			{
				return false;
			}

			// 更新后续序号（将删除项目后面的序号减1）
			StringBuilder strSqlUpdate = new StringBuilder();
			strSqlUpdate.Append("UPDATE DictLabProfileItemMap SET SequenceNo = SequenceNo - 1, UpdatedAt = GETDATE() ");
			strSqlUpdate.Append("WHERE ProfileId = @ProfileId AND SequenceNo > @DeletedSequenceNo");
			SqlParameter[] parametersUpdate = {
				new SqlParameter("@ProfileId", SqlDbType.Int),
				new SqlParameter("@DeletedSequenceNo", SqlDbType.Int)
			};
			parametersUpdate[0].Value = profileId;
			parametersUpdate[1].Value = deletedSequenceNo;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSqlUpdate.ToString(), parametersUpdate);
			return rows >= 0; // 返回true表示执行成功，即使没有更新任何行也是成功的
		}

		#endregion  ExtensionMethod
	}
}


