﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CustomControl
{
    public partial class MessageNotifyForm : SkinForm
    {
        //提示框高度
        private int _HeightMax;
        /// 提示框高度
        public int HeightMax
        {
            set { _HeightMax = value; }
        }

        /// 提示框停留时间（单位：秒）
        public int StayTime
        {
            set { this.TimerStay.Interval = value; }
        }


        /// 显示窗体
        public void ShowMessage(string text, MessageBoxIcon messageBoxIcon = MessageBoxIcon.Information)
        {
            ShowMessage(text,"", messageBoxIcon);
        }
        /// 显示窗体

        public void ShowMessage(string text, string caption, MessageBoxIcon messageBoxIcon = MessageBoxIcon.Information)
        {
            if (caption!="") this.Text = caption;
            switch (messageBoxIcon)
            {
                case MessageBoxIcon.Error:
                    pictureBox1.Image = imageList1.Images["错误.png"];
                    break;
                case MessageBoxIcon.Information:
                    pictureBox1.Image = imageList1.Images["提示.png"];
                    break;
                case MessageBoxIcon.Warning:
                    pictureBox1.Image = imageList1.Images["警告.png"];
                    break;
                default:
                    pictureBox1.Image = imageList1.Images["提示.png"];
                   break;
            }
            this.lblContent.Text = text;
            this.TimerUp.Enabled = true;
            this.Height = 0;
            this.Show();
        }

        /// 向上移动窗体
        private void MoveUp()
        {
            if (this.Height < _HeightMax)
            {
                this.Height += 3;
                this.Location = new Point(Location.X, Location.Y - 3);
            }
            else
            {
                this.TimerUp.Enabled = false;
                this.TimerStay.Enabled = true;
            }
        }

        /// 向下移动窗体
        private void MoveDown()
        {
            if (this.Height > 0)
            {
                this.Height -= 3;
                this.Location = new Point(Location.X, Location.Y + 3);
            }
            else
            {
                this.TimerDown.Enabled = false;
                this.Close();
            }
        }
        public MessageNotifyForm()
        {
            InitializeComponent();
        }
        private void MessageNotifyForm_Load(object sender, EventArgs e)
        {
            //根据不同的屏幕分辨率来确定窗体的初始位置
            Screen[] screens = Screen.AllScreens;
            Screen screen = screens[0];
            this.Location = new Point(screen.WorkingArea.Width - this.Width - 2, screen.WorkingArea.Height - 30);
        }
        private void TimerUp_Tick(object sender, EventArgs e)
        {
            this.MoveUp();
        }

        private void TimerStay_Tick(object sender, EventArgs e)
        {
            this.TimerStay.Enabled = false;
            this.TimerDown.Enabled = true;
        }

        private void TimerDown_Tick(object sender, EventArgs e)
        {
            this.MoveDown();
        }

    }
}
