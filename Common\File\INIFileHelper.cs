﻿/*********************************************************************************** 
*        文 件 名 :INIFileHelper 
*        功能描述 :INI文件读写类
*        生成日期 :2017/2/7 11:53:42 
*        作   者 :董铮
*        机 器 名 :2013-20160817SN
*        All rights reserved 
************************************************************************************/
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace Common
{ /// <summary>
  /// INI文件读写类。
  /// </summary>
    public class INIFileHelper
    {
        public string path;

        public INIFileHelper(string INIPath)
        {
            path = INIPath;
        }

        [DllImport("kernel32")]
        private static extern long WritePrivateProfileString(string section, string key, string val, string filePath);

        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string section, string key, string def, StringBuilder retVal, int size, string filePath);


        [DllImport("kernel32")]
        private static extern int GetPrivateProfileString(string section, string key, string defVal, Byte[] retVal, int size, string filePath);


        /// <summary>
        /// 写INI文件
        /// </summary>
        /// <param name="Section"></param>
        /// <param name="Key"></param>
        /// <param name="Value"></param>
        public void IniWriteValue(string Section, string Key, string Value)
        {
            WritePrivateProfileString(Section, Key, Value, this.path);
        }

        /// <summary>
        /// 读取INI文件
        /// </summary>
        /// <param name="Section"></param>
        /// <param name="Key"></param>
        /// <returns></returns>
        public string IniReadValue(string Section, string Key)
        {
            StringBuilder temp = new StringBuilder(255);
            int i = GetPrivateProfileString(Section, Key, "", temp, 255, this.path);
            return temp.ToString();
        }
        public byte[] IniReadValues(string section, string key)
        {
            byte[] temp = new byte[255];
            int i = GetPrivateProfileString(section, key, "", temp, 255, this.path);
            return temp;

        }


        /// <summary>
        /// 删除ini文件下所有段落
        /// </summary>
        public void ClearAllSection()
        {
            IniWriteValue(null, null, null);
        }
        /// <summary>
        /// 删除ini文件下personal段落下的所有键
        /// </summary>
        /// <param name="Section"></param>
        public void ClearSection(string Section)
        {
            IniWriteValue(Section, null, null);
        }

        /// <summary>
        /// 读取ini文件所有段落
        /// </summary>
        /// <returns></returns>
        public List<string> ReadSections()
        {
            List<string> result = new List<string>();
            Byte[] buf = new Byte[65536];
            int len = GetPrivateProfileString(null, null, null, buf, buf.Length, this.path);
            int j = 0;
            for (int i = 0; i < len; i++)
                if (buf[i] == 0)
                {
                    result.Add(Encoding.Default.GetString(buf, j, i - j));
                    j = i + 1;
                }
            return result;
        }

        /// <summary>
        /// 读取ini某一段落所有键值
        /// </summary>
        /// <param name="SectionName">段落名</param>
        /// <returns></returns>
        public  List<string> ReadKeys(String SectionName)
        {
            List<string> result = new List<string>();
            Byte[] buf = new Byte[65536];
            int len = GetPrivateProfileString(SectionName, null, null, buf, buf.Length, this.path);
            int j = 0;
            for (int i = 0; i < len; i++)
                if (buf[i] == 0)
                {
                    result.Add(Encoding.Default.GetString(buf, j, i - j));
                    j = i + 1;
                }
            return result;
        }



    }
}
