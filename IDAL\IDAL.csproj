﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8DEE3CA3-DDE2-40B7-8E77-BC5D0FCE94C1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>IDAL</RootNamespace>
    <AssemblyName>IDAL</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IDalDictDepartment.cs" />
    <Compile Include="IDalDictDoctor.cs" />
    <Compile Include="IDalDictLabItem.cs" />
    <Compile Include="IDalDictLabItemCalculation.cs" />
    <Compile Include="IDalDictLabItemCategory.cs" />
    <Compile Include="IDalDictLabItemRefRange.cs" />
    <Compile Include="IDalDictLabProfile.cs" />
    <Compile Include="IDalDictLabProfileItemMap.cs" />
    <Compile Include="IDalDictMethod.cs" />
    <Compile Include="IDalDictMethodCategory.cs" />
    <Compile Include="IDalDictSampleType.cs" />
    <Compile Include="IDalDictSpecimenCondition.cs" />
    <Compile Include="IDalDictSpecimenParam.cs" />
    <Compile Include="IDalDictUnit.cs" />
    <Compile Include="IDalDictVisitType.cs" />
    <Compile Include="IDalLabInstrItemMap.cs" />
    <Compile Include="IDalLabInstrRawMsg.cs" />
    <Compile Include="IDalLabInstrResultQ.cs" />
    <Compile Include="IDalLabInstrSpecimenParamMap.cs" />
    <Compile Include="IDalLabInstrument.cs" />
    <Compile Include="IDalLabOrder.cs" />
    <Compile Include="IDalLabOrderItem.cs" />
    <Compile Include="IDalLabResult.cs" />
    <Compile Include="IDalLabResultReview.cs" />
    <Compile Include="IDalLabSpecimen.cs" />
    <Compile Include="IDalSysMenu1.cs" />
    <Compile Include="IDalSysMenu2.cs" />
    <Compile Include="IDalSysModule.cs" />
    <Compile Include="IDalSysModuleAuth.cs" />
    <Compile Include="IDalSysPara.cs" />
    <Compile Include="IDalSysRole.cs" />
    <Compile Include="IDalSysRoleAuth.cs" />
    <Compile Include="IDalSysRoleModule.cs" />
    <Compile Include="IDalSysRpt.cs" />
    <Compile Include="IDalSysRpt_Class.cs" />
    <Compile Include="IDalSysRpt_Class_Level.cs" />
    <Compile Include="IDalSysUser.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Model\MODEL.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>MODEL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>