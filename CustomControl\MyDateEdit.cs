﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1Input;

namespace CustomControl
{
    public partial class MyDateEdit : UserControl
    {
        public MyDateEdit()
        {
            InitializeComponent();
            InitDateEdit();
        }
        #region 私有函数

        private void InitDateEdit()
        {
            c1DateEdit1.DateTimeInput = true;
            //显示日期的开关

            c1DateEdit1.AutoChangePosition = false;
            c1DateEdit1.CaseSensitive = true;
            c1DateEdit1.AcceptsTab = true;
            c1DateEdit1.EmptyAsNull = true;
            c1DateEdit1.ValueIsDbNull = true;
            c1DateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom;
            c1DateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart;
            c1DateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            c1DateEdit1.CustomFormat = "yyyy-MM-dd HH:mm";
            c1DateEdit1.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat;
            c1DateEdit1.EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat;
            c1DateEdit1.EditFormat.CustomFormat = "yyyy-MM-dd";
            c1DateEdit1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
            //.EditMask = "9999-09-09"
            c1DateEdit1.DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat;
            c1DateEdit1.DisplayFormat.CustomFormat = "yyyy-MM-dd";
            c1DateEdit1.MaskInfo.AutoTabWhenFilled = true;

            c1DateEdit1.Calendar.ClearText = "清空(&C)";
            c1DateEdit1.Calendar.TodayText = "今日(&T)";
            c1DateEdit1.Calendar.TitleHeight = 25;
            c1DateEdit1.Calendar.DayNameLength = 1;
            c1DateEdit1.Calendar.FirstDayOfWeek = System.Windows.Forms.Day.Monday;

            c1DateEdit1.ErrorInfo.BeepOnError = true;
            c1DateEdit1.ErrorInfo.CanLoseFocus = false;
            c1DateEdit1.ErrorInfo.ShowErrorMessage = true;
            c1DateEdit1.ErrorInfo.ErrorAction = C1.Win.C1Input.ErrorActionEnum.None;
            c1DateEdit1.ErrorInfo.ErrorMessageCaption = "提示";
            c1DateEdit1.ErrorInfo.ErrorMessage = "日期格式不正确!";

            c1DateEdit1.Value = DateTime.Now;
        }

        private void c1DateEdit1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (EnterToTab == true)
            {
                if (e.KeyChar != (char)(Keys.Return))
                    return;
                e.Handled = true;
                System.Windows.Forms.SendKeys.Send("{Tab}");
            }
        }
        #endregion

        #region 公开属性

        [Description("标题")]
        public string Captain
        {
            get { return label1.Text; }
            set
            {
                label1.Text = value;
                if (value == "")
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        float _CaptainWidth = 70;
        [Description("标题宽度")]
        public float CaptainWidth
        {
            get { return _CaptainWidth; }
            set
            {
                _CaptainWidth = value;
                tableLayoutPanel1.ColumnStyles[0].Width = _CaptainWidth;
                if (value == 0)
                {
                    label1.Visible = false;
                }
                else
                {
                    label1.Visible = true;
                }
                Invalidate();
            }
        }

        [Description("标题颜色")]
        public Color CaptainForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                label1.ForeColor = value;
                Invalidate();
            }
        }

        private bool _entertotab = true;
        [Description("回车键走Tab键"), Browsable(true), DefaultValue(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public bool EnterToTab
        {
            get { return _entertotab; }
            set
            {
                _entertotab = value;
            }
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public C1.Win.C1Input.VisualStyle VisualStyle
        {
            get { return c1DateEdit1.VisualStyle; }
            set
            {
                c1DateEdit1.VisualStyle = value;
                Invalidate();
            }
        }

        [Description("控件值"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public object Value
        {
            get { return c1DateEdit1.Value; }
            set
            {
                c1DateEdit1.Value = value;
                Invalidate();
            }
        }
        public string Text
        {
            get { return c1DateEdit1.Text; }
            set
            {
                c1DateEdit1.Text = value;
                Invalidate();
            }
        }

        public bool ReadOnly
        {
            get { return c1DateEdit1.ReadOnly; }
            set
            {
                if (value == true)
                {
                    c1DateEdit1.BackColor = Color.MistyRose;
                    c1DateEdit1.DisabledForeColor = Color.Black;
                }
                else
                {
                    c1DateEdit1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;

                }
                c1DateEdit1.ReadOnly = value;
                Invalidate();
            }
        }
        [Description("控件是否启用"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public new bool Enabled
        {
            get { return c1DateEdit1.Enabled; }
            set
            {
                c1DateEdit1.Enabled = value;
                if (value == true)
                {
                    c1DateEdit1.TabStop = true;
                    this.TabStop = true;
                    c1DateEdit1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue;
                    c1DateEdit1.BackColor = SystemColors.Window;
                }
                else
                {
                    c1DateEdit1.TabStop = false;
                    this.TabStop = false;
                    c1DateEdit1.BackColor = SystemColors.Info;
                    c1DateEdit1.DisabledForeColor = Color.Black;
                }


                Invalidate();
            }
        }

        [Description("自定义格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string CustomFormat
        {
            get { return c1DateEdit1.CustomFormat; }
            set
            {
                c1DateEdit1.CustomFormat = value;
                Invalidate();
            }
        }

        [Description("显示格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string DisplayFormat
        {
            get { return c1DateEdit1.DisplayFormat.CustomFormat; }
            set
            {
                c1DateEdit1.DisplayFormat.CustomFormat = value;
                Invalidate();
            }
        }

        [Description("编辑格式"), DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string EditFormat
        {
            get { return c1DateEdit1.EditFormat.CustomFormat; }
            set
            {
                c1DateEdit1.EditFormat.CustomFormat = value;
                Invalidate();
            }
        }
        [Description("垂直对齐方式"), Browsable(true), DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public C1.Win.C1Input.VerticalAlignEnum VerticalAlign
        {
            get { return c1DateEdit1.VerticalAlign; }
            set
            {
                c1DateEdit1.VerticalAlign = value;
                Invalidate();
            }
        }
        [Description("按钮可见")]
        public C1.Win.C1Input.DropDownControlButtonFlags VisibleButtons
        {
            get { return c1DateEdit1.VisibleButtons; }
            set
            {
                c1DateEdit1.VisibleButtons = value;
                Invalidate();
            }
        }

        [Description("字体")]
        public new Font Font
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                c1DateEdit1.Font = value;
                Invalidate();
            }
        }



        [Description("标题字体")]
        public Font CaptainFont
        {
            get { return label1.Font; }
            set
            {
                label1.Font = value;
                Invalidate();
            }
        }
        [Description("文本字体")]
        public Font TextFont
        {
            get { return c1DateEdit1.Font; }
            set
            {
                c1DateEdit1.Font = value;
                Invalidate();
            }
        }

        public C1.Win.C1Input.InitialSelectionEnum InitialSelection
        {
            get { return c1DateEdit1.InitialSelection; }
            set
            {
                c1DateEdit1.InitialSelection = value;
                Invalidate();
            }
        }

        public bool ValueIsDbNull
        {
            get { return c1DateEdit1.ValueIsDbNull; }
            set
            {
                c1DateEdit1.ValueIsDbNull = value;
                Invalidate();
            }
        }
        #endregion

        #region 公开方法
        public new void Select()
        {
            c1DateEdit1.Select();
        }
        #endregion

        #region 公开事件
        [Description("当控件获得光标焦点时"), Browsable(true)]
        public new event EventHandler GotFocus
        {
            add
            {
                c1DateEdit1.GotFocus += value;
            }
            remove
            {
                c1DateEdit1.GotFocus -= value;
            }
        }
        [Description("当值改变时时触发"), Browsable(true)]
        public event EventHandler ValueChanged
        {
            add
            {
                c1DateEdit1.ValueChanged += value;
            }
            remove
            {
                c1DateEdit1.ValueChanged -= value;
            }
        }

        public new event KeyPressEventHandler KeyPress
        {
            add
            {
                c1DateEdit1.KeyPress += value;
            }
            remove
            {
                c1DateEdit1.KeyPress -= value;
            }
        }

        #endregion


    }
}
