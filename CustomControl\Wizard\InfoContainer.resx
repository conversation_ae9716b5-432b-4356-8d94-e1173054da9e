<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 1.3
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">1.3</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1">this is my long string</data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        [base64 mime encoded serialized .NET Framework object]
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        [base64 mime encoded string representing a byte array form of the .NET Framework object]
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used forserialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="picImage.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="picImage.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="picImage.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="picImage.Image" type="System.Drawing.Bitmap, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhpACDAfIAAAAAAP///wAAgMDAwICAgP///wAAAAAAACH5BAEAAAUALAAAAACkAIMBAAP/CKK8
        3vDJSKet+OrMt++g9Y1hSZ5miq6l6rLwK8d0Vd9zju860PPAnzAoIQ6PxmQKyVQ6m5GnFDrlUa9V7CzL
        1XY33vBXHCWPz1qzGi1lr99GuNy9o8/vMbzefuJrA4CBgoOEhYaHiIRLflKJjo+QiSuMV5GWl4+Le02Y
        nZ6Ck5tKgj6lpqeoqaqrq6QoopyBm6AmlE+krKYDAwS5vr6uLbaxgCGCvFC0fbBGuLkEAb3QAci/1qXB
        JMxI2RjQD9PIQcohw6OyqdS9ANCo4evXrN0f20TzCoIEAwEX7wy9MciNMMfE2Sl1Bt0B4rUvWjxssl4R
        tBcxwj4F06JtyLhr/2EKgR0mHklYql3JhfBSEYDXMGWue2BEAoEJjYCEjDZDNBwAAqQHmUJIYnPpw5+1
        fURRwcRQb8hSahpwkoDKwWcGoDyE+rjIyigrrvIqasOqY+m3DlIzONxgVQNZHFrZOfzlVWlSU0svNJ0p
        NgI1E2klrMXQ1sbeHnEB/H1Y12TYYhIP38h7EcY0gHI/FBYhOUdixw99zJULrC+9tzPy1qRRGcA+zYFC
        oQ6IrtXdX2DBtjLNofNk3g+oWg7A4HXV2LV8y0jsmnjooVt5vgTOdPaLvHI1uggEbQBsQLKVs2DOrmXo
        hh6nQxYmXgV2B4FBoNfOFvky6+Nr0zVPN1/p9f9jtbfCexHEp0E+3/GTHH4f6XfKPtIpBMhtojmoCoEP
        COgecPPxNAE13GmAXk/2laMhClr9pU965TmEk0oWpkOdXgwOCNxfC6wIYojN8QjfjAxsVkaNJaS4WFHm
        zYUefxU6px5xkRE5Ak2BeIcRVdqhJ0BaQC4gZBEnFhljSTs1B12Pc+m4YoSPQcmelB28lxGOiuU0H45z
        engceAuGmaA1K8IzIU4vtsjmbgCC4KcxXTZAZ56YVZkfn/ctWl8x10yYy3yhYciApXE2+uOE/QwyqYKV
        wnmpk9fsRBSErsbj6QKgejBrA+jltCWfOtK3J6omqvrrc6XEOs2DKP3nZoD/wh4oagZLSoorkF820Oyw
        xCJLCJvhtKnJtRbc6oGr0u60anjgQkBeti3uOJqMiZ5WawXiqjDnshRU60C6F6zLLpmHwouvvPw6+mwW
        +ipQ8Idj/svurfM6+8nElqAbcZAUZ5xJlBc7oPHHiFhcMMgkK8LxwhGjjHLHLO/bMssvL6wyzDPzW/PF
        MXd8c7o529zzvD+DuzPQQzdbNKhB13q0sEkb3fSiT6u6NNRTS1l1mFH7eTWRWVvdtYZf17g12GPjV3Z7
        YQt4tnVpm922ciEJIPfca8PxE914v43GVXj3LbfeYrjl9+CAZ2HD4Ij/XXcbUSTueOFQNO745Is3UQTl
        /5hDHoS1j2Oed+V1KIy45J5rngPnffPtOehbiJ763aubzoLrn5ezOuuyEd7H7bIrOjoMt+MeEu10vxB8
        7zTqvgXvwoP5+/KxN//p83UwL/u+fg9xvPDEz2259Ytj/zoS2wPeveJUlF829emD//X5X6gfNftVyL80
        /V7Y3zP+XOhfM/+Bc5/KOmc3AbaMgHTwn9Iyl0ADDqR25igdHxQoAgCyIXgTdCDplOcH+CUODxSM3hwu
        J8ILOpCCYdgg5UAIPgx674KHK+EatufC4u1NcDI0YQ1raIYH5rCHO+RhF2z3wzPsEHUSxMIkNNg/DKpw
        cmlYYhGByMAYJjEJNEBh/f9WCLsrHgF6XtRh9nY3xSzqQItKhGAtmOiCH6DxLW8cHhDiKBM6Vkd7bJyN
        HZ1HvjxKZo/T+14Z44a+Bg6SM23wIwlfOEJFBjKRh0Ti+AwZRsM08ZBQzGAkqVhJ8XGxkVV0ywxb6ERN
        fpJGo4xeEBmRyauAcpWd5CQHKcBCWDoSkmO0ZC1LCUgs5hKHu8xhL/E4yQ3uoYjDrN4sF8lISn6wi6EM
        4DI9eEwLSvKU0lQjM4spRhuuMZJEmGY0rXbLM/IvlmYrZxZ/eU0Eak2dxmMnrUppqWQSUp6eHOc3WQlP
        MrLzie70YTNfqU9l/rKC4PQgPxMqRTWqDp0KLSRBsXn/g2cq7KEFjag2UwnRho6PiGG0Zzg7uk9vShSH
        VzyiKS06x1yqwJYMTSNLzTlJ4ME0pk5opUElCsabztSZG7VpTXtKSpwKMqgv/edOKSpSN8oznkNd6k/b
        GdBscjOpxezjVLepU1kOdJ1RJeZTUZpRXJoUqg7N6VdBWtajXtWjzcRKU/15VqGmdS9zZeta4VpIPfaz
        pEgV6FnFk1eyBvaeJhXbXwV7WMPyVLFGpWtjATqkExVWoWgdrNQWa8W3YvSrQiMpX8F6V6OJFrAnRSxo
        dVbVio5VtY9l7VaJmlpornaA1hxtZge6v9yi1q513Zk4d1tb2E4gaMMF7l4/e1Lh//qWscW1bXN761nd
        Kre50V1gdX+L1aBOFrKvlWx2HYvQumr3u8btrnlnS87tQpe0wWWv2p7L3OuSF71ypW9515teriaXsPql
        rHUBGuCJyJe8xN1vW/P7X+nal5b5pKhvDtzZ5Tq4svRMp3s/m2AIE7jAwbRwfXerS/9S2BYNrvB4VYzK
        +4K4myJG8ID56OIUrzTGLO7wccXb2hCvWMbcRaReu4ri8I74wTtWr4SB+uMcz9haCT6xVXGs4Cb718Pw
        XbJXrSzgIGM4yi/WKn6pqmMIiHXB7dswkHlcYpqe1pdq7vKT5ylmNI80zldG8pdbGlnaZpnLAJTpm/1c
        5jlf1P+sWr4zlZ3s5SRvsc/vLXSjoXzJQbP5z3p29JSJ7Gb+XjjTlN5yj0Ft6E9jmaN27q+SAQ2/NhuR
        s2Eu8HCryWlSDxnTri5grScNV0lnKIKxJrOtGetKYNv4yKvGdYuZcew181rVQjb2mKus7GTf0cBGNvWw
        kR1tUUiZ2r6+9bWZnW1uW7vUh65Hs+X87EgvW9qehva5251ubOOZ3Zeet6bhPe1WVzvf3eY3qyM88HUT
        PCZ4LTe4sxxul9VR4YxGt7P3Te57H7zh8qZxxSG+TUJL3AcM7veLDe7vXAt82+6mN50fbvGSf3zioWb5
        tEl+8Y//UdYcF7a+97zxeC8c4zD///XNLU7zoocXjjmPtcH/O+GkG73l2UZ6s58ucqZLfeZJd7m4f9J0
        PFPd5xE/tczVTHOtp1yUVyd71kdu5K6Puuw4d69fd812qFvd7USG+9Kjjnemrv3rIgYwDddu9ozz3N4+
        Bbq5KT52WBJV8QPXNUwhj2+xJ/ymlP852vuu0szrHOFpdzzKF394dU/+31vvjYZFv3OAm9z0tvS4yh/J
        eSG2PvWbD33nR6/5ceve9i8Pe8CHHkTZu/7dxF+l5/NcbMEXn/fCt3zIlQ995nN99buffdDNDF4XGh/3
        zZ/785MN6BRbdvz2Bftzuw/836p9w+fP/miTa+P2tp/H7DU///uT6X3rh9/5/Ud+fkd28yV/7idB9ad+
        tHZ/txZ7dAWA/cRB1GdcvxdZEsiA95V8EThWAehgIpc/GAh+B/dtiTZRFhh1ogVrbmVpn8diXgdpK3iC
        L3hiIxhsigZpU0dfVPVtaZZh2ldhfVKDcOdU/Pd3i7ZyO2iDnaZOQwg89cWDfBaCvWc4HqiE6adITfhS
        DWiFW6hFWTg7+MeF72WANRc4AviFFIiBaHgyXUh4YTiBZdg/fgaFBwiHa/gmYHaHQjhGgwd1rSNVo5aH
        fMh6QxSFqfaG2gSH+VNnJYiI3ESGVxCDgeiIFnZZqcKIdJeG9KeCYNiDh/hh7zeJlgOCMs3YiBoVeTWw
        aaJoYk4nhkFIim/2iTWoBqhmiovlhxUleQsmi7BggpPIiyjmY60FjJuwgAVFjKzkbQKUiWTRc6eEjCAE
        ewi4ik1xeYnnNdOHfmSjgR1oWRBoibSIfZw4QvE3jm7wTjCYjJsFjR1kWsz4RylDjSIhW254TAf0gcMQ
        MwooV8jTi9ITQf2Yj//obQFZjAMZjAepBwWJkAt5Bw2pjg8pBxE5QRMpkQkJkRdpkRUJQxlpBxtpNx95
        QyHZQyM5BiVJkh2pkSnJBgkAADs=
</value>
  </data>
  <data name="lblTitle.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="lblTitle.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="lblTitle.Modifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="$this.Name">
    <value>InfoContainer</value>
  </data>
  <data name="$this.TrayLargeIcon" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.Locked" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.SnapToGrid" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </data>
  <data name="$this.DrawGrid" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </data>
  <data name="$this.TrayHeight" type="System.Int32, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>80</value>
  </data>
  <data name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>(Default)</value>
  </data>
  <data name="$this.Localizable" type="System.Boolean, mscorlib, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </data>
  <data name="$this.DefaultModifiers" type="System.CodeDom.MemberAttributes, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Private</value>
  </data>
  <data name="$this.GridSize" type="System.Drawing.Size, System.Drawing, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>4, 4</value>
  </data>
</root>