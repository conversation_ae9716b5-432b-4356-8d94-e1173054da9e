﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace Common
{
    public class EnCode
    {


        private static string MyMiyao;
        private struct MiYaoData
        {

            ////元素值
            public string myChar;
            ////对照字母，产生顺序号
            public int Num;

        }

        public static string F_Encode(string Value)
        {
            string result;
            MyMiyao = "Dongzheng";

            int max;
            ////密钥长度

            MiYaoData[] p = new MiYaoData[26];
            ////密钥数据

            int[] t = new int[26];
            ////转换密钥数据的中间变量

            string[,] MingWen = new string[10, 26];
            ////明文

            int i;
            int j;

            int MinAsc;
            int Mindex = 0;
            ////当前最小字符ASCII值和其下标

            string s;
            string ss;

            s = MyMiyao.Trim();
            max = s.Length;
            ////获取密钥长度

            ////逐个获取密钥字母（不应重复）
            for (i = 0; i <= max - 1; i++)
            {
                p[i].myChar = s.Substring(i, 1);
                p[i].Num = max;
            }

            ////对应英文字母表，逐个填写密钥字母的顺序，为生成密文输出作准备
            for (i = 0; i <= max - 1; i++)
            {
                ////查找当前小的密钥字符所在下标
                MinAsc = StringPlus.ASCII("z") + 10;
                ////将一个大数作为当前最小值


                for (j = 0; j <= max - 1; j++)
                {
                    if (StringPlus.ASCII(p[j].myChar) < MinAsc & p[j].Num == max)
                    {
                        MinAsc = StringPlus.ASCII(p[j].myChar);
                        ////更改最小值
                        Mindex = j;
                        ////登记其下标
                    }

                }

                ////当前最小值找到，修改数据（下标）
                p[Mindex].Num = i;
                ////登记其下标
            }

            ////将"p[i].num=j"的格式转换成"p[j].num=i"
            ////防止数据覆盖，转换结果另存
            for (i = 0; i <= max - 1; i++)
            {
                t[p[i].Num] = i;
            }

            ////将另存结果，写入原始数据中
            for (i = 0; i <= max - 1; i++)
            {
                p[i].Num = t[i];
            }

            ////去除明文中的空格
            s = Value;
            //s = Replace(s, " ", "")

            ////明文依次写入二维数组
            for (i = 0; i <= s.Length - 1; i++)
            {
                MingWen[(i / max), (i % max)] = s.Substring(i, 1);
            }

            ////最后一行上的数据未满，用a,b,……填充
            for (j = (i - 1) % max + 1; j <= max - 1; j++)
            {
                MingWen[(i - 1) / max, j] = StringPlus.Character(96 - (i - 1) % max + j);
            }

            result = "";

            ////按密钥字母的顺序，转置明文，生成密文
            ////初始明文矩阵的列范围
            for (j = 0; j <= max - 1; j++)
            {
                ss = "";

                ////初始明文矩阵的行范围
                for (i = 0; i <= (s.Length + max - 1) / max - 1; i++)
                {
                    ss = ss + MingWen[i, p[j].Num].ToUpper();
                    ////转换成大写字母
                }

                result = result + ss;
                ////显示密文
            }

            return result;
        }



    }
}
