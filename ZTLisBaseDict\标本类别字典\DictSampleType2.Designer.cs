﻿namespace ZTLisBaseDict
{
    partial class DictSampleType2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.TableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.TxtPreTreatment = new CustomControl.MyTextBox();
            this.TxtCode = new CustomControl.MyTextBox();
            this.TxtName = new CustomControl.MyTextBox();
            this.TxtJc = new CustomControl.MyTextBox();
            this.TxtEnglishName = new CustomControl.MyTextBox();
            this.TxtContainerType = new CustomControl.MyTextBox();
            this.NumVolume = new CustomControl.MyNumericEdit();
            this.Panel1 = new System.Windows.Forms.Panel();
            this.BtnSave = new CustomControl.MyButton();
            this.BtnCancel = new CustomControl.MyButton();
            this.TableLayoutPanel1.SuspendLayout();
            this.Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // TableLayoutPanel1
            // 
            this.TableLayoutPanel1.ColumnCount = 4;
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250F));
            this.TableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.TableLayoutPanel1.Controls.Add(this.TxtPreTreatment, 1, 4);
            this.TableLayoutPanel1.Controls.Add(this.TxtCode, 1, 0);
            this.TableLayoutPanel1.Controls.Add(this.TxtName, 1, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtJc, 2, 1);
            this.TableLayoutPanel1.Controls.Add(this.TxtEnglishName, 1, 2);
            this.TableLayoutPanel1.Controls.Add(this.TxtContainerType, 2, 2);
            this.TableLayoutPanel1.Controls.Add(this.NumVolume, 1, 3);
            this.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.TableLayoutPanel1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.TableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.TableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.TableLayoutPanel1.Name = "TableLayoutPanel1";
            this.TableLayoutPanel1.RowCount = 5;
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 120F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.TableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.TableLayoutPanel1.Size = new System.Drawing.Size(520, 242);
            this.TableLayoutPanel1.TabIndex = 8;
            // 
            // TxtPreTreatment
            // 
            this.TxtPreTreatment.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtPreTreatment.Captain = "预处理";
            this.TxtPreTreatment.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtPreTreatment.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtPreTreatment.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtPreTreatment.CaptainWidth = 70F;
            this.TableLayoutPanel1.SetColumnSpan(this.TxtPreTreatment, 2);
            this.TxtPreTreatment.ContentForeColor = System.Drawing.Color.Black;
            this.TxtPreTreatment.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtPreTreatment.EditMask = null;
            this.TxtPreTreatment.Location = new System.Drawing.Point(13, 119);
            this.TxtPreTreatment.Multiline = true;
            this.TxtPreTreatment.Name = "TxtPreTreatment";
            this.TxtPreTreatment.PasswordChar = '\0';
            this.TxtPreTreatment.ReadOnly = false;
            this.TxtPreTreatment.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.TxtPreTreatment.SelectionStart = 0;
            this.TxtPreTreatment.SelectStart = 0;
            this.TxtPreTreatment.Size = new System.Drawing.Size(494, 120);
            this.TxtPreTreatment.TabIndex = 6;
            this.TxtPreTreatment.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtPreTreatment.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtPreTreatment.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top;
            this.TxtPreTreatment.Watermark = null;
            // 
            // TxtCode
            // 
            this.TxtCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtCode.Captain = "编    码";
            this.TxtCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtCode.CaptainWidth = 70F;
            this.TxtCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtCode.EditMask = null;
            this.TxtCode.Location = new System.Drawing.Point(13, 3);
            this.TxtCode.Multiline = false;
            this.TxtCode.Name = "TxtCode";
            this.TxtCode.PasswordChar = '\0';
            this.TxtCode.ReadOnly = false;
            this.TxtCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtCode.SelectionStart = 0;
            this.TxtCode.SelectStart = 0;
            this.TxtCode.Size = new System.Drawing.Size(244, 23);
            this.TxtCode.TabIndex = 0;
            this.TxtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtCode.Watermark = null;
            // 
            // TxtName
            // 
            this.TxtName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtName.Captain = "名    称";
            this.TxtName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtName.CaptainWidth = 70F;
            this.TxtName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtName.EditMask = null;
            this.TxtName.Location = new System.Drawing.Point(13, 32);
            this.TxtName.Multiline = false;
            this.TxtName.Name = "TxtName";
            this.TxtName.PasswordChar = '\0';
            this.TxtName.ReadOnly = false;
            this.TxtName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtName.SelectionStart = 0;
            this.TxtName.SelectStart = 0;
            this.TxtName.Size = new System.Drawing.Size(244, 23);
            this.TxtName.TabIndex = 1;
            this.TxtName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtName.Watermark = null;
            this.TxtName.Validated += new System.EventHandler(this.TxtName_Validated);
            // 
            // TxtJc
            // 
            this.TxtJc.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtJc.Captain = "简    称";
            this.TxtJc.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtJc.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtJc.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtJc.CaptainWidth = 70F;
            this.TxtJc.ContentForeColor = System.Drawing.Color.Black;
            this.TxtJc.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtJc.EditMask = null;
            this.TxtJc.Location = new System.Drawing.Point(263, 32);
            this.TxtJc.Multiline = false;
            this.TxtJc.Name = "TxtJc";
            this.TxtJc.PasswordChar = '\0';
            this.TxtJc.ReadOnly = false;
            this.TxtJc.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtJc.SelectionStart = 0;
            this.TxtJc.SelectStart = 0;
            this.TxtJc.Size = new System.Drawing.Size(244, 23);
            this.TxtJc.TabIndex = 2;
            this.TxtJc.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtJc.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtJc.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtJc.Watermark = null;
            // 
            // TxtEnglishName
            // 
            this.TxtEnglishName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtEnglishName.Captain = "英文名称";
            this.TxtEnglishName.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtEnglishName.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtEnglishName.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtEnglishName.CaptainWidth = 70F;
            this.TxtEnglishName.ContentForeColor = System.Drawing.Color.Black;
            this.TxtEnglishName.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtEnglishName.EditMask = null;
            this.TxtEnglishName.Location = new System.Drawing.Point(13, 61);
            this.TxtEnglishName.Multiline = false;
            this.TxtEnglishName.Name = "TxtEnglishName";
            this.TxtEnglishName.PasswordChar = '\0';
            this.TxtEnglishName.ReadOnly = false;
            this.TxtEnglishName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtEnglishName.SelectionStart = 0;
            this.TxtEnglishName.SelectStart = 0;
            this.TxtEnglishName.Size = new System.Drawing.Size(244, 23);
            this.TxtEnglishName.TabIndex = 3;
            this.TxtEnglishName.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtEnglishName.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtEnglishName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtEnglishName.Watermark = null;
            // 
            // TxtContainerType
            // 
            this.TxtContainerType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtContainerType.Captain = "容器类型";
            this.TxtContainerType.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtContainerType.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtContainerType.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtContainerType.CaptainWidth = 70F;
            this.TxtContainerType.ContentForeColor = System.Drawing.Color.Black;
            this.TxtContainerType.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtContainerType.EditMask = null;
            this.TxtContainerType.Location = new System.Drawing.Point(263, 61);
            this.TxtContainerType.Multiline = false;
            this.TxtContainerType.Name = "TxtContainerType";
            this.TxtContainerType.PasswordChar = '\0';
            this.TxtContainerType.ReadOnly = false;
            this.TxtContainerType.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtContainerType.SelectionStart = 0;
            this.TxtContainerType.SelectStart = 0;
            this.TxtContainerType.Size = new System.Drawing.Size(244, 23);
            this.TxtContainerType.TabIndex = 4;
            this.TxtContainerType.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtContainerType.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtContainerType.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtContainerType.Watermark = null;
            // 
            // NumVolume
            // 
            this.NumVolume.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.NumVolume.Captain = "采样量(mL)";
            this.NumVolume.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumVolume.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.NumVolume.CaptainWidth = 70F;
            this.NumVolume.Location = new System.Drawing.Point(13, 90);
            this.NumVolume.MaximumSize = new System.Drawing.Size(100000, 80);
            this.NumVolume.MinimumSize = new System.Drawing.Size(0, 20);
            this.NumVolume.Name = "NumVolume";
            this.NumVolume.NumericInputKeys = ((C1.Win.C1Input.NumericInputKeyFlags)(((((C1.Win.C1Input.NumericInputKeyFlags.F9 | C1.Win.C1Input.NumericInputKeyFlags.Minus)
            | C1.Win.C1Input.NumericInputKeyFlags.Plus)
            | C1.Win.C1Input.NumericInputKeyFlags.Decimal)
            | C1.Win.C1Input.NumericInputKeyFlags.X)));
            this.NumVolume.NumFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.NumVolume.ReadOnly = false;
            this.NumVolume.Size = new System.Drawing.Size(244, 23);
            this.NumVolume.TabIndex = 5;
            this.NumVolume.ValueIsDbNull = false;
            // 
            // Panel1
            // 
            this.Panel1.Controls.Add(this.BtnSave);
            this.Panel1.Controls.Add(this.BtnCancel);
            this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.Panel1.Location = new System.Drawing.Point(0, 245);
            this.Panel1.Margin = new System.Windows.Forms.Padding(0);
            this.Panel1.Name = "Panel1";
            this.Panel1.Size = new System.Drawing.Size(520, 38);
            this.Panel1.TabIndex = 9;
            // 
            // BtnSave
            // 
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(371, 0);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // BtnCancel
            // 
            this.BtnCancel.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.BtnCancel.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnCancel.Location = new System.Drawing.Point(447, 0);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(70, 35);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            // 
            // DictSampleType2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(520, 283);
            this.Controls.Add(this.TableLayoutPanel1);
            this.Controls.Add(this.Panel1);
            this.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "DictSampleType2";
            this.Text = "标本类型字典";
            this.Load += new System.EventHandler(this.DictSampleType2_Load);
            this.TableLayoutPanel1.ResumeLayout(false);
            this.Panel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        internal System.Windows.Forms.Panel Panel1;
        internal CustomControl.MyButton BtnSave;
        internal CustomControl.MyButton BtnCancel;
        internal CustomControl.MyTextBox TxtJc;
        internal CustomControl.MyTextBox TxtName;
        internal CustomControl.MyTextBox TxtPreTreatment;
        internal System.Windows.Forms.TableLayoutPanel TableLayoutPanel1;
        internal CustomControl.MyTextBox TxtCode;
        internal CustomControl.MyTextBox TxtEnglishName;
        internal CustomControl.MyTextBox TxtContainerType;
        private CustomControl.MyNumericEdit NumVolume;
    }
}