﻿namespace Common.BaseForm
{
    partial class MainBase
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainBase));
            this.MainTab = new C1.Win.C1Command.C1DockingTab();
            this.ContextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.ToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.c1Ribbon1 = new C1.Win.C1Ribbon.C1Ribbon();
            this.ribbonApplicationMenu1 = new C1.Win.C1Ribbon.RibbonApplicationMenu();
            this.ribbonBottomToolBar1 = new C1.Win.C1Ribbon.RibbonBottomToolBar();
            this.ribbonConfigToolBar1 = new C1.Win.C1Ribbon.RibbonConfigToolBar();
            this.ribbonQat1 = new C1.Win.C1Ribbon.RibbonQat();
            this.ribbonTab1 = new C1.Win.C1Ribbon.RibbonTab();
            this.QuickMenuGroup = new C1.Win.C1Ribbon.RibbonGroup();
            this.PersonGroup = new C1.Win.C1Ribbon.RibbonGroup();
            this.BtnPerson = new C1.Win.C1Ribbon.RibbonButton();
            this.BtnHelp = new C1.Win.C1Ribbon.RibbonButton();
            this.BtnUpdate = new C1.Win.C1Ribbon.RibbonButton();
            this.BtnExit = new C1.Win.C1Ribbon.RibbonButton();
            this.ribbonTopToolBar1 = new C1.Win.C1Ribbon.RibbonTopToolBar();
            this.c1CommandDock1 = new C1.Win.C1Command.C1CommandDock();
            this.LeftMenuTab = new C1.Win.C1Command.C1DockingTab();
            this.LeftMenuPage = new C1.Win.C1Command.C1DockingTabPage();
            this.LeftMenuOutBar = new C1.Win.C1Command.C1OutBar();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            this.c1StatusBar1 = new C1.Win.C1Ribbon.C1StatusBar();
            this.RblblDate = new C1.Win.C1Ribbon.RibbonLabel();
            this.ribbonSeparator1 = new C1.Win.C1Ribbon.RibbonSeparator();
            this.ribbonLabel1 = new C1.Win.C1Ribbon.RibbonLabel();
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.c1CommandDock2 = new C1.Win.C1Command.C1CommandDock();
            this.RightMenuTab = new C1.Win.C1Command.C1DockingTab();
            this.RightMenuPage = new C1.Win.C1Command.C1DockingTabPage();
            ((System.ComponentModel.ISupportInitialize)(this.MainTab)).BeginInit();
            this.ContextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1Ribbon1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock1)).BeginInit();
            this.c1CommandDock1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.LeftMenuTab)).BeginInit();
            this.LeftMenuTab.SuspendLayout();
            this.LeftMenuPage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.LeftMenuOutBar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1StatusBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock2)).BeginInit();
            this.c1CommandDock2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.RightMenuTab)).BeginInit();
            this.RightMenuTab.SuspendLayout();
            this.SuspendLayout();
            // 
            // MainTab
            // 
            this.MainTab.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.MainTab.CanCloseTabs = true;
            this.MainTab.CanMoveTabs = true;
            this.MainTab.CloseBox = C1.Win.C1Command.CloseBoxPositionEnum.AllPages;
            this.MainTab.Dock = System.Windows.Forms.DockStyle.Fill;
            this.MainTab.Location = new System.Drawing.Point(218, 153);
            this.MainTab.Name = "MainTab";
            this.MainTab.Size = new System.Drawing.Size(580, 654);
            this.MainTab.TabIndex = 2;
            this.MainTab.TabsSpacing = 5;
            this.MainTab.Visible = false;
            this.MainTab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue;
            this.MainTab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            this.MainTab.TabPageClosing += new C1.Win.C1Command.TabPageCancelEventHandler(this.MainTab_TabPageClosing);
            this.MainTab.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.MainTab_MouseDoubleClick);
            this.MainTab.MouseDown += new System.Windows.Forms.MouseEventHandler(this.MainTab_MouseDown);
            this.MainTab.MouseLeave += new System.EventHandler(this.MainTab_MouseLeave);
            // 
            // ContextMenuStrip1
            // 
            this.ContextMenuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.ContextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItem1,
            this.ToolStripMenuItem2,
            this.ToolStripMenuItem3,
            this.ToolStripMenuItem4,
            this.ToolStripMenuItem5});
            this.ContextMenuStrip1.Name = "ContextMenuStrip1";
            this.ContextMenuStrip1.Size = new System.Drawing.Size(189, 134);
            // 
            // ToolStripMenuItem1
            // 
            this.ToolStripMenuItem1.Image = ((System.Drawing.Image)(resources.GetObject("ToolStripMenuItem1.Image")));
            this.ToolStripMenuItem1.Name = "ToolStripMenuItem1";
            this.ToolStripMenuItem1.Size = new System.Drawing.Size(188, 26);
            this.ToolStripMenuItem1.Text = "关闭";
            this.ToolStripMenuItem1.Click += new System.EventHandler(this.ToolStripMenuItem1_Click);
            // 
            // ToolStripMenuItem2
            // 
            this.ToolStripMenuItem2.Name = "ToolStripMenuItem2";
            this.ToolStripMenuItem2.Size = new System.Drawing.Size(188, 26);
            this.ToolStripMenuItem2.Text = "关闭其他窗体";
            this.ToolStripMenuItem2.Click += new System.EventHandler(this.ToolStripMenuItem2_Click);
            // 
            // ToolStripMenuItem3
            // 
            this.ToolStripMenuItem3.Name = "ToolStripMenuItem3";
            this.ToolStripMenuItem3.Size = new System.Drawing.Size(188, 26);
            this.ToolStripMenuItem3.Text = "关闭左边的所有窗体";
            this.ToolStripMenuItem3.Click += new System.EventHandler(this.ToolStripMenuItem3_Click);
            // 
            // ToolStripMenuItem4
            // 
            this.ToolStripMenuItem4.Name = "ToolStripMenuItem4";
            this.ToolStripMenuItem4.Size = new System.Drawing.Size(188, 26);
            this.ToolStripMenuItem4.Text = "关闭右边的所有窗体";
            this.ToolStripMenuItem4.Click += new System.EventHandler(this.ToolStripMenuItem4_Click);
            // 
            // ToolStripMenuItem5
            // 
            this.ToolStripMenuItem5.Image = ((System.Drawing.Image)(resources.GetObject("ToolStripMenuItem5.Image")));
            this.ToolStripMenuItem5.Name = "ToolStripMenuItem5";
            this.ToolStripMenuItem5.Size = new System.Drawing.Size(188, 26);
            this.ToolStripMenuItem5.Text = "关闭所有窗体";
            this.ToolStripMenuItem5.Click += new System.EventHandler(this.ToolStripMenuItem5_Click);
            // 
            // c1Ribbon1
            // 
            this.c1Ribbon1.ApplicationMenuHolder = this.ribbonApplicationMenu1;
            this.c1Ribbon1.BottomToolBarHolder = this.ribbonBottomToolBar1;
            this.c1Ribbon1.ConfigToolBarHolder = this.ribbonConfigToolBar1;
            this.c1Ribbon1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.c1Ribbon1.Location = new System.Drawing.Point(0, 0);
            this.c1Ribbon1.Name = "c1Ribbon1";
            this.c1Ribbon1.QatHolder = this.ribbonQat1;
            this.c1Ribbon1.Size = new System.Drawing.Size(1066, 153);
            this.c1Ribbon1.Tabs.Add(this.ribbonTab1);
            this.c1Ribbon1.TopToolBarHolder = this.ribbonTopToolBar1;
            // 
            // ribbonApplicationMenu1
            // 
            this.ribbonApplicationMenu1.Name = "ribbonApplicationMenu1";
            this.ribbonApplicationMenu1.Visible = false;
            // 
            // ribbonBottomToolBar1
            // 
            this.ribbonBottomToolBar1.Name = "ribbonBottomToolBar1";
            // 
            // ribbonConfigToolBar1
            // 
            this.ribbonConfigToolBar1.Name = "ribbonConfigToolBar1";
            // 
            // ribbonQat1
            // 
            this.ribbonQat1.Name = "ribbonQat1";
            this.ribbonQat1.Visible = false;
            // 
            // ribbonTab1
            // 
            this.ribbonTab1.Groups.Add(this.QuickMenuGroup);
            this.ribbonTab1.Groups.Add(this.PersonGroup);
            this.ribbonTab1.Name = "ribbonTab1";
            this.ribbonTab1.Text = "快速启动菜单";
            // 
            // QuickMenuGroup
            // 
            this.QuickMenuGroup.Name = "QuickMenuGroup";
            this.QuickMenuGroup.Text = "常用菜单";
            // 
            // PersonGroup
            // 
            this.PersonGroup.Items.Add(this.BtnPerson);
            this.PersonGroup.Items.Add(this.BtnHelp);
            this.PersonGroup.Items.Add(this.BtnUpdate);
            this.PersonGroup.Items.Add(this.BtnExit);
            this.PersonGroup.Name = "PersonGroup";
            this.PersonGroup.Text = "个人菜单";
            // 
            // BtnPerson
            // 
            this.BtnPerson.LargeImage = ((System.Drawing.Image)(resources.GetObject("BtnPerson.LargeImage")));
            this.BtnPerson.Name = "BtnPerson";
            this.BtnPerson.SmallImage = ((System.Drawing.Image)(resources.GetObject("BtnPerson.SmallImage")));
            this.BtnPerson.Text = "个人设置";
            this.BtnPerson.TextImageRelation = C1.Win.C1Ribbon.TextImageRelation.ImageAboveText;
            // 
            // BtnHelp
            // 
            this.BtnHelp.LargeImage = ((System.Drawing.Image)(resources.GetObject("BtnHelp.LargeImage")));
            this.BtnHelp.Name = "BtnHelp";
            this.BtnHelp.SmallImage = ((System.Drawing.Image)(resources.GetObject("BtnHelp.SmallImage")));
            this.BtnHelp.Text = "帮助文档";
            this.BtnHelp.TextImageRelation = C1.Win.C1Ribbon.TextImageRelation.ImageAboveText;
            // 
            // BtnUpdate
            // 
            this.BtnUpdate.LargeImage = ((System.Drawing.Image)(resources.GetObject("BtnUpdate.LargeImage")));
            this.BtnUpdate.Name = "BtnUpdate";
            this.BtnUpdate.SmallImage = ((System.Drawing.Image)(resources.GetObject("BtnUpdate.SmallImage")));
            this.BtnUpdate.Text = "更新说明";
            this.BtnUpdate.TextImageRelation = C1.Win.C1Ribbon.TextImageRelation.ImageAboveText;
            // 
            // BtnExit
            // 
            this.BtnExit.LargeImage = ((System.Drawing.Image)(resources.GetObject("BtnExit.LargeImage")));
            this.BtnExit.Name = "BtnExit";
            this.BtnExit.SmallImage = ((System.Drawing.Image)(resources.GetObject("BtnExit.SmallImage")));
            this.BtnExit.Text = "退出程序";
            this.BtnExit.TextImageRelation = C1.Win.C1Ribbon.TextImageRelation.ImageAboveText;
            this.BtnExit.Click += new System.EventHandler(this.ribbonButton4_Click);
            // 
            // ribbonTopToolBar1
            // 
            this.ribbonTopToolBar1.Name = "ribbonTopToolBar1";
            // 
            // c1CommandDock1
            // 
            this.c1CommandDock1.Controls.Add(this.LeftMenuTab);
            this.c1CommandDock1.Dock = System.Windows.Forms.DockStyle.Left;
            this.c1CommandDock1.Id = 1;
            this.c1CommandDock1.Location = new System.Drawing.Point(0, 153);
            this.c1CommandDock1.Name = "c1CommandDock1";
            this.c1CommandDock1.Size = new System.Drawing.Size(218, 654);
            this.c1CommandDock1.Resize += new System.EventHandler(this.c1CommandDock1_Resize);
            // 
            // LeftMenuTab
            // 
            this.LeftMenuTab.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.LeftMenuTab.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.LeftMenuTab.CanAutoHide = true;
            this.LeftMenuTab.Controls.Add(this.LeftMenuPage);
            this.LeftMenuTab.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LeftMenuTab.Location = new System.Drawing.Point(0, 0);
            this.LeftMenuTab.Name = "LeftMenuTab";
            this.LeftMenuTab.ShowCaption = true;
            this.LeftMenuTab.ShowSingleTab = false;
            this.LeftMenuTab.Size = new System.Drawing.Size(218, 654);
            this.LeftMenuTab.TabIndex = 0;
            this.LeftMenuTab.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit;
            this.LeftMenuTab.TabsSpacing = 5;
            this.LeftMenuTab.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue;
            this.LeftMenuTab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            // 
            // LeftMenuPage
            // 
            this.LeftMenuPage.CaptionVisible = true;
            this.LeftMenuPage.Controls.Add(this.LeftMenuOutBar);
            this.LeftMenuPage.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LeftMenuPage.Location = new System.Drawing.Point(0, 0);
            this.LeftMenuPage.Name = "LeftMenuPage";
            this.LeftMenuPage.Size = new System.Drawing.Size(215, 653);
            this.LeftMenuPage.TabIndex = 0;
            this.LeftMenuPage.Text = "菜单栏";
            // 
            // LeftMenuOutBar
            // 
            this.LeftMenuOutBar.Dock = System.Windows.Forms.DockStyle.Fill;
            this.LeftMenuOutBar.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LeftMenuOutBar.Location = new System.Drawing.Point(0, 28);
            this.LeftMenuOutBar.Name = "LeftMenuOutBar";
            this.LeftMenuOutBar.PageTitleHeight = 30;
            this.LeftMenuOutBar.ShowScrollButtons = false;
            this.LeftMenuOutBar.Size = new System.Drawing.Size(215, 625);
            this.LeftMenuOutBar.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue;
            this.LeftMenuOutBar.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Owner = this;
            // 
            // c1StatusBar1
            // 
            this.c1StatusBar1.Location = new System.Drawing.Point(0, 807);
            this.c1StatusBar1.Name = "c1StatusBar1";
            this.c1StatusBar1.RightPaneItems.Add(this.RblblDate);
            this.c1StatusBar1.RightPaneItems.Add(this.ribbonSeparator1);
            this.c1StatusBar1.RightPaneItems.Add(this.ribbonLabel1);
            this.c1StatusBar1.Size = new System.Drawing.Size(1066, 23);
            this.c1StatusBar1.SmallImageList = this.imageList1;
            // 
            // RblblDate
            // 
            this.RblblDate.Name = "RblblDate";
            this.RblblDate.SmallImageIndex = 5;
            this.RblblDate.Text = "状态";
            // 
            // ribbonSeparator1
            // 
            this.ribbonSeparator1.Name = "ribbonSeparator1";
            // 
            // ribbonLabel1
            // 
            this.ribbonLabel1.Name = "ribbonLabel1";
            this.ribbonLabel1.Text = "状态:";
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "OK.png");
            this.imageList1.Images.SetKeyName(1, "6695e1917ae885f818ec773078128291.gif");
            this.imageList1.Images.SetKeyName(2, "人员.png");
            this.imageList1.Images.SetKeyName(3, "医院.png");
            this.imageList1.Images.SetKeyName(4, "科室.png");
            this.imageList1.Images.SetKeyName(5, "日期.png");
            this.imageList1.Images.SetKeyName(6, "超管.png");
            // 
            // c1CommandDock2
            // 
            this.c1CommandDock2.Controls.Add(this.RightMenuTab);
            this.c1CommandDock2.Dock = System.Windows.Forms.DockStyle.Right;
            this.c1CommandDock2.Id = 2;
            this.c1CommandDock2.Location = new System.Drawing.Point(798, 153);
            this.c1CommandDock2.Name = "c1CommandDock2";
            this.c1CommandDock2.Size = new System.Drawing.Size(268, 654);
            // 
            // RightMenuTab
            // 
            this.RightMenuTab.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.RightMenuTab.AutoHiding = true;
            this.RightMenuTab.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.RightMenuTab.CanAutoHide = true;
            this.RightMenuTab.Controls.Add(this.RightMenuPage);
            this.RightMenuTab.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.RightMenuTab.KeepClosedPages = false;
            this.RightMenuTab.Location = new System.Drawing.Point(0, 0);
            this.RightMenuTab.Name = "RightMenuTab";
            this.RightMenuTab.ShowCaption = true;
            this.RightMenuTab.ShowSingleTab = false;
            this.RightMenuTab.Size = new System.Drawing.Size(268, 654);
            this.RightMenuTab.TabIndex = 0;
            this.RightMenuTab.TabLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.RightMenuTab.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit;
            this.RightMenuTab.TabsSpacing = 5;
            this.RightMenuTab.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            // 
            // RightMenuPage
            // 
            this.RightMenuPage.CaptionVisible = true;
            this.RightMenuPage.Image = ((System.Drawing.Image)(resources.GetObject("RightMenuPage.Image")));
            this.RightMenuPage.Location = new System.Drawing.Point(3, 0);
            this.RightMenuPage.Name = "RightMenuPage";
            this.RightMenuPage.Size = new System.Drawing.Size(265, 653);
            this.RightMenuPage.TabIndex = 0;
            this.RightMenuPage.Text = "消息";
            // 
            // MainBase
            // 
            this.ClientSize = new System.Drawing.Size(1066, 830);
            this.Controls.Add(this.MainTab);
            this.Controls.Add(this.c1CommandDock2);
            this.Controls.Add(this.c1CommandDock1);
            this.Controls.Add(this.c1Ribbon1);
            this.Controls.Add(this.c1StatusBar1);
            this.IsMdiContainer = true;
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "MainBase";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.MainBase_Load);
            ((System.ComponentModel.ISupportInitialize)(this.MainTab)).EndInit();
            this.ContextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1Ribbon1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock1)).EndInit();
            this.c1CommandDock1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.LeftMenuTab)).EndInit();
            this.LeftMenuTab.ResumeLayout(false);
            this.LeftMenuPage.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.LeftMenuOutBar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1StatusBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock2)).EndInit();
            this.c1CommandDock2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.RightMenuTab)).EndInit();
            this.RightMenuTab.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        internal System.Windows.Forms.ContextMenuStrip ContextMenuStrip1;
        internal System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem1;
        internal System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem2;
        internal System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem3;
        internal System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem4;
        internal System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem5;
        private C1.Win.C1Ribbon.RibbonApplicationMenu ribbonApplicationMenu1;
        private C1.Win.C1Command.C1CommandDock c1CommandDock1;
        private C1.Win.C1Command.C1DockingTab LeftMenuTab;
        private C1.Win.C1Ribbon.RibbonTab ribbonTab1;
        private C1.Win.C1Ribbon.RibbonGroup PersonGroup;
        private C1.Win.C1Ribbon.RibbonQat ribbonQat1;
        private C1.Win.C1Ribbon.RibbonButton BtnExit;
        protected C1.Win.C1Command.C1DockingTab MainTab;
        protected C1.Win.C1Ribbon.C1Ribbon c1Ribbon1;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1DockingTabPage LeftMenuPage;
        protected C1.Win.C1Command.C1OutBar LeftMenuOutBar;
        protected C1.Win.C1Ribbon.RibbonGroup QuickMenuGroup;
        private C1.Win.C1Ribbon.RibbonBottomToolBar ribbonBottomToolBar1;
        private C1.Win.C1Ribbon.RibbonTopToolBar ribbonTopToolBar1;
        protected C1.Win.C1Ribbon.RibbonButton BtnPerson;
        protected C1.Win.C1Ribbon.RibbonButton BtnHelp;
        protected C1.Win.C1Ribbon.RibbonButton BtnUpdate;
        private System.Windows.Forms.ImageList imageList1;
        private C1.Win.C1Ribbon.RibbonSeparator ribbonSeparator1;
        protected C1.Win.C1Ribbon.RibbonLabel RblblDate;
        protected C1.Win.C1Ribbon.RibbonLabel ribbonLabel1;
        public C1.Win.C1Ribbon.C1StatusBar c1StatusBar1;
        private C1.Win.C1Command.C1CommandDock c1CommandDock2;
        private C1.Win.C1Command.C1DockingTab RightMenuTab;
        private C1.Win.C1Command.C1DockingTabPage RightMenuPage;
        protected C1.Win.C1Ribbon.RibbonConfigToolBar ribbonConfigToolBar1;
    }
}
