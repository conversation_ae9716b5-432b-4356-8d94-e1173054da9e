using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using CustomControl;

namespace ZTLisControl
{
    /// <summary>
    /// 就诊类型
    /// </summary>
    public partial class ComboVisitType : MyDtComobo
    {
        public ComboVisitType()
        {
            InitializeComponent();
        }

        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strWhere">查询条件</param>
        public void Init(string strWhere = " 1=1")
        {
            if (string.IsNullOrEmpty(strWhere))
            {
                strWhere = " 1=1";
            }
            strWhere += " and Status=1";
            BLL.BllDictVisitType _bllDictVisitType = new BllDictVisitType();
            this.DataView = _bllDictVisitType.GetList(strWhere).Tables[0].DefaultView;
            this.Init_Colum("VisitTypeName", "名称", 130, "左");
            this.Init_Colum("VisitTypeCode", "编码", 0, "左");
            this.Init_Colum("PinYin", "拼音", 0, "左");
            this.Init_Colum("Description", "描述", 0, "左");
            this.DisplayMember = "VisitTypeName";
            this.ValueMember = "VisitTypeId";
            int width = 230;
            if (this.Width - (int)this.CaptainWidth > width) width = this.Width - (int)this.CaptainWidth;
            DroupDownWidth = width;
            this.MaxDropDownItems = 15;
            this.SelectedIndex = -1;
            this.RowFilterTextNull = "";
            this.ItemHeight = 20;
            this.RowFilterNotTextNull = "VisitTypeCode+isnull(PinYin,'')+VisitTypeName";
        }
    }
}