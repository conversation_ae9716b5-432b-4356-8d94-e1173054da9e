﻿/**  版本信息模板在安装目录下，可自行修改。
* BllLabResultReview.cs
*
* 功 能： N/A
* 类 名： BllLabResultReview
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/7/21 15:50:55   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using Model;
using DALFactory;
using IDAL;
namespace BLL
{
	/// <summary>
	/// 检验结果审核记录表
	/// </summary>
	public partial class BllLabResultReview
	{
		private readonly IDalLabResultReview dal=DataAccess.CreateDalLabResultReview();
		public BllLabResultReview()
		{}
		#region  BasicMethod
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long ReviewId)
		{
			return dal.Exists(ReviewId);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public long Add(Model.MdlLabResultReview model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlLabResultReview model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long ReviewId)
		{
			
			return dal.Delete(ReviewId);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string ReviewIdlist )
		{
			return dal.DeleteList(ReviewIdlist );
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlLabResultReview GetModel(long ReviewId)
		{
			
			return dal.GetModel(ReviewId);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
		public Model.MdlLabResultReview GetModelByCache(long ReviewId)
		{
			
			string CacheKey = "MdlLabResultReviewModel-" + ReviewId;
			object objModel = Common.DataCache.GetCache(CacheKey);
			if (objModel == null)
			{
				try
				{
					objModel = dal.GetModel(ReviewId);
					if (objModel != null)
					{
						int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
						Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
					}
				}
				catch{}
			}
			return (Model.MdlLabResultReview)objModel;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlLabResultReview> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.MdlLabResultReview> DataTableToList(DataTable dt)
		{
			List<Model.MdlLabResultReview> modelList = new List<Model.MdlLabResultReview>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				Model.MdlLabResultReview model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

