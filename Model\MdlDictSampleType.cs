﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlDictSampleType.cs
*
* 功 能： N/A
* 类 名： MdlDictSampleType
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/5/23 15:35:45   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 标本类型字典表，用于维护样本类型基础信息并在 LIS、HIS、仪器之间保持一致
	/// </summary>
	[Serializable]
	public partial class MdlDictSampleType
	{
		public MdlDictSampleType()
		{ }
		#region Model
		private int _sampletypeid;
		private string _sampletypecode;
		private string _sampletypename;
		private string _pinyin;
		private string _englishname;
		private string _colorhex;
		private string _containertype;
		private decimal? _defaultvolumeml;
		private string _pretreatment;
		private bool _status = true;
		private DateTime _createdat = DateTime.Now;
		private DateTime _updatedat = DateTime.Now;
		/// <summary>
		/// 自增主键
		/// </summary>
		public int SampleTypeId
		{
			set { _sampletypeid = value; }
			get { return _sampletypeid; }
		}
		/// <summary>
		/// 业务唯一编码（如 SR01 = Serum）
		/// </summary>
		public string SampleTypeCode
		{
			set { _sampletypecode = value; }
			get { return _sampletypecode; }
		}
		/// <summary>
		/// 中文名称（血清、全血等）
		/// </summary>
		public string SampleTypeName
		{
			set { _sampletypename = value; }
			get { return _sampletypename; }
		}
		/// <summary>
		/// 中文名称对应拼音或首字母，方便检索
		/// </summary>
		public string PinYin
		{
			set { _pinyin = value; }
			get { return _pinyin; }
		}
		/// <summary>
		/// 英文名称
		/// </summary>
		public string EnglishName
		{
			set { _englishname = value; }
			get { return _englishname; }
		}
		/// <summary>
		/// 采样管帽颜色（HEX 码，例 FF0000 = 红帽）
		/// </summary>
		public string ColorHex
		{
			set { _colorhex = value; }
			get { return _colorhex; }
		}
		/// <summary>
		/// 容器/试管类型
		/// </summary>
		public string ContainerType
		{
			set { _containertype = value; }
			get { return _containertype; }
		}
		/// <summary>
		/// 建议采样量（毫升）
		/// </summary>
		public decimal? DefaultVolumeMl
		{
			set { _defaultvolumeml = value; }
			get { return _defaultvolumeml; }
		}
		/// <summary>
		/// 预处理要求（如 离心 3000g×10min）
		/// </summary>
		public string PreTreatment
		{
			set { _pretreatment = value; }
			get { return _pretreatment; }
		}
		/// <summary>
		/// 状态：1 = Active (启用)，0 = Inactive (停用)
		/// </summary>
		public bool Status
		{
			set { _status = value; }
			get { return _status; }
		}
		/// <summary>
		/// 记录创建时间 (DATETIME, 默认 GETDATE())
		/// </summary>
		public DateTime CreatedAt
		{
			set { _createdat = value; }
			get { return _createdat; }
		}
		/// <summary>
		/// 最近更新时间 (DATETIME, 默认 GETDATE())；需在应用层维护
		/// </summary>
		public DateTime UpdatedAt
		{
			set { _updatedat = value; }
			get { return _updatedat; }
		}
		#endregion Model

	}
}

