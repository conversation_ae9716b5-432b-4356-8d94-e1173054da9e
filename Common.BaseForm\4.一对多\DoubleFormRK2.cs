﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace Common.BaseForm
{
    public partial class DoubleFormRK2 : Common.BaseForm.BaseChild
    {
        #region 基础变量

        private Common.Delegate.TransmitTxt _transmitTxt;
        private Common.Delegate.TransmitDataRow _transmitDataRow;

        protected DataTable MyTable { get; set; }
        protected DataRow SubItemRow { get; set; }
        protected bool Insert { get; set; }

        public Common.Delegate.TransmitTxt MyTransmitTxt
        {
            get { return _transmitTxt; }
            set
            {
                _transmitTxt = value;
            }

        }
        public Common.Delegate.TransmitDataRow MyTransmitDataRow
        {
            get { return _transmitDataRow; }
            set
            {
                _transmitDataRow = value;
                this.MyTransmitDataRow.SetDataRow += DataShow;
            }

        }

        #endregion
        public DoubleFormRK2()
        {
            InitializeComponent();
        }

        private void RK2_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (this.MyTransmitDataRow != null)
                this.MyTransmitDataRow.SetDataRow -= DataShow;
        }


        #region 自定义函数
        protected virtual void DataShow(DataRow _row)
        {

        }
        #endregion
    }
}
