﻿/**  版本信息模板在安装目录下，可自行修改。
* DalSysRoleAuth.cs
*
* 功 能： N/A
* 类 名： DalSysRoleAuth
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/7/6 11:01:35   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalSysRoleAuth
	/// </summary>
	public partial class DalSysRoleAuth:IDalSysRoleAuth
	{
		public DalSysRoleAuth()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string RoleCode,string AuthCode)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from SysRoleAuth");
			strSql.Append(" where RoleCode=@RoleCode and AuthCode=@AuthCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@AuthCode", SqlDbType.Char,6)			};
			parameters[0].Value = RoleCode;
			parameters[1].Value = AuthCode;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlSysRoleAuth model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into SysRoleAuth(");
			strSql.Append("RoleCode,AuthCode)");
			strSql.Append(" values (");
			strSql.Append("@RoleCode,@AuthCode)");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@AuthCode", SqlDbType.Char,6)};
			parameters[0].Value = model.RoleCode;
			parameters[1].Value = model.AuthCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlSysRoleAuth model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update SysRoleAuth set ");
			strSql.Append("RoleCode=@RoleCode,");
			strSql.Append("AuthCode=@AuthCode");
			strSql.Append(" where RoleCode=@RoleCode and AuthCode=@AuthCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@AuthCode", SqlDbType.Char,6)};
			parameters[0].Value = model.RoleCode;
			parameters[1].Value = model.AuthCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string RoleCode,string AuthCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from SysRoleAuth ");
			strSql.Append(" where RoleCode=@RoleCode and AuthCode=@AuthCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@AuthCode", SqlDbType.Char,6)			};
			parameters[0].Value = RoleCode;
			parameters[1].Value = AuthCode;

			int rows=Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRoleAuth GetModel(string RoleCode,string AuthCode)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 RoleCode,AuthCode from SysRoleAuth ");
			strSql.Append(" where RoleCode=@RoleCode and AuthCode=@AuthCode ");
			SqlParameter[] parameters = {
					new SqlParameter("@RoleCode", SqlDbType.Char,2),
					new SqlParameter("@AuthCode", SqlDbType.Char,6)			};
			parameters[0].Value = RoleCode;
			parameters[1].Value = AuthCode;

			Model.MdlSysRoleAuth model=new Model.MdlSysRoleAuth();
			DataSet ds=Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlSysRoleAuth DataRowToModel(DataRow row)
		{
			Model.MdlSysRoleAuth model=new Model.MdlSysRoleAuth();
			if (row != null)
			{
				if(row["RoleCode"]!=null)
				{
					model.RoleCode=row["RoleCode"].ToString();
				}
				if(row["AuthCode"]!=null)
				{
					model.AuthCode=row["AuthCode"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select RoleCode,AuthCode ");
			strSql.Append(" FROM SysRoleAuth ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" RoleCode,AuthCode ");
			strSql.Append(" FROM SysRoleAuth ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM SysRoleAuth ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.AuthCode desc");
			}
			strSql.Append(")AS Row, T.*  from SysRoleAuth T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "SysRoleAuth";
			parameters[1].Value = "AuthCode";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

