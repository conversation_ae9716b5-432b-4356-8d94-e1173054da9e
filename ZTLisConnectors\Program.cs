﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DbProviderFactory;
using ZTLisConnectors.Properties;

namespace ZTLisConnectors
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            PersistenceProperty pp = new PersistenceProperty();

            switch (Settings.Default.DAL)
            {
                case "SQLServerDAL":
                    pp.DatabaseType = DatabaseType.MSSQLServer;
                    pp.ConnectionString = "Data Source=" + Settings.Default.DbIP + ";Initial Catalog=" + Settings.Default.DbName + ";Persist Security Info=True;User Id=sa;Pwd=" + Settings.Default.DbPwd + "";

                    break;
                default:
                    pp.DatabaseType = DatabaseType.MSSQLServer;
                    pp.ConnectionString = "";
                    break;
            }
            Common.WinFormVar.Var.DbHelper = DataAccessFactory.CreateDataAccess(pp);
            Common.WinFormVar.Var.DALPath = Settings.Default.DAL;

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainConnecter());
        }
    }
}
