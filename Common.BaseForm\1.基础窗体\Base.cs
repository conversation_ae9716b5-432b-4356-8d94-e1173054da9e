﻿using System;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using C1.Win.C1Command;
using C1.Win.C1Ribbon;
using Common.WinFormVar;
namespace Common.BaseForm
{
    public partial class Base : C1RibbonForm
    {
        public Base()
        {
            InitializeComponent();
        }

        private void Base_FormClosed(object sender, System.Windows.Forms.FormClosedEventArgs e)
        {
            RemoveTap(this);
        }

        #region 属性
        private bool _canDockFill;
        /// <summary>
        /// 是否允许最大化
        /// </summary>
        public bool CanDockFill
        {
            set { _canDockFill = value; }
            get { return _canDockFill; }
        }

        private bool _allowShow = true;
        /// <summary>
        /// 窗体是否允许显示
        /// </summary>
        public bool AllowShow
        {
            set { _allowShow = value; }
            get { return _allowShow; }
        }
        #endregion

        #region 选项卡函数
        /// <summary>
        /// 添加一个选项卡窗体
        /// </summary>
        /// <param name="objFrm">窗体实例</param>
        /// <param name="captain">名称</param>
        /// <param name="img">选项卡图片</param>
        protected void AddTabControl(Base objFrm, string captain, Image img)
        {
            this.Cursor = Cursors.WaitCursor;
            PublicFunc.AddTabControl(objFrm, captain, img);
            this.Cursor = Cursors.Default;
        }


        /// <summary>
        /// 移除一个选项卡
        /// </summary>
        /// <param name="objFrm">要被移除到选项卡的用户控件对象</param>
        private void RemoveTap(System.Windows.Forms.Form objFrm)
        {
            if (WinFormVar.Var.MainTab != null)
            {
                foreach (C1DockingTabPage con in WinFormVar.Var.MainTab.TabPages)
                {
                    C1DockingTabPage tab = (C1DockingTabPage)con;
                    foreach (object _ctrl in tab.Controls)
                    {
                        if (object.ReferenceEquals(_ctrl, objFrm))
                        {
                            WinFormVar.Var.MainTab.Invoke(new Action(() => { WinFormVar.Var.MainTab.Close(tab); }));
                        }
                    }
                }
            }
        }


        protected bool CheckOwnForm(BaseFather Parentform, BaseChild ChildForm)
        {
            foreach (var V_Form in Parentform.OwnedForms)
            {
                if (V_Form.Name == ChildForm.Name && V_Form.Text == ChildForm.Text)
                {
                    V_Form.Activate();
                    return true;
                }
            }
            return false;
        }

        #endregion

        #region 输入法函数

        protected void InputCn(System.Object sender, System.EventArgs e)
        {
            InputLanguage.CurrentInputLanguage = Common.WinFormVar.Var.InputMethod;
            //((Control)sender).ImeMode = ImeMode.Hangul;
        }

        protected void InputEn(System.Object sender, System.EventArgs e)
        {
            InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages[0];
            // ((Control)sender).ImeMode = ImeMode.Disable;
        }

        #endregion

        public bool IsDesignMode()
        {
            bool returnFlag = false;


#if DEBUG
            if (LicenseManager.UsageMode == LicenseUsageMode.Designtime)
            {
                returnFlag = true;
            }
            else if (Process.GetCurrentProcess().ProcessName == "devenv")
            {
                returnFlag = true;
            }
#endif


            return returnFlag;
        }
    }
}