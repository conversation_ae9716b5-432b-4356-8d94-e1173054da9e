using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using ZTLisPublicFunction;

namespace ZTLisBaseDict
{
    public partial class DictLabProfile1 : Common.BaseForm.BaseDict1
    {
        BLL.BllDictLabProfile _bllDictLabProfile = new BllDictLabProfile();
        BLL.BllDictLabProfileItemMap _bllDictLabProfileItemMap = new BllDictLabProfileItemMap();

        // 项目映射相关变量
        private int _profileId;
        private DataTable _profileItemTable;
        private CurrencyManager _profileItemCm;
        private Common.Delegate.TransmitTxt _profileItemTransmitTxt = new Common.Delegate.TransmitTxt();
        private Common.Delegate.TransmitDataRow _profileItemTransmitDataRow = new Common.Delegate.TransmitDataRow();

        public DictLabProfile1()
        {
            InitializeComponent();
        }

        private void DictLabProfile1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            this.FormInit();
            this.DataInit();
            this.ProfileItemFormInit();
            this.ProfileItemDataInit(_profileId);

            TxtFilter.GotFocus += new System.EventHandler(base.InputEn);
        }

        private void DictLabProfile1_FormClosed(object sender, FormClosedEventArgs e)
        {
            this._profileItemTransmitTxt.SetText -= GridMove;
        }

        #region 自定义函数

        private void FormInit()
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("启用", "Status", 60, "中", "", false);
            myGrid1.Init_Column("编码", "ProfileCode", 120, "中", "", false);
            myGrid1.Init_Column("组合名称", "ProfileName", 160, "左", "", false);
            myGrid1.Init_Column("拼音码", "PinYin", 120, "左", "", false);
            myGrid1.Init_Column("英文名称", "EnglishName", 150, "左", "", false);
            myGrid1.Init_Column("HIS编码", "HisProfileCode", 120, "左", "", false);
            myGrid1.Init_Column("报告标题", "ReportTitle", 150, "左", "", false);
            myGrid1.Init_Column("描述", "Description", 200, "左", "", false);
            myGrid1.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Splits[0].DisplayColumns["Status"].FetchStyle = true;
            myGrid1.FetchCellStyle += GridFunction.IsEnable_FetchCellStyle;
            myGrid1.AllowSort = true;
        }

        private void DataInit()
        {
            base.MyTable = _bllDictLabProfile.GetList("").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["ProfileCode"] };
            base.MyTable.Columns["ProfileId"].ReadOnly = false;
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            this.myGrid1.DataTable = base.MyTable;
            this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString();
            base.MyView = (DataView)base.MyCm.List;
            if (base.MyTable.Rows.Count > 0)
            {
                _profileId = int.Parse(base.MyTable.Rows[0]["ProfileId"].ToString());
            }
        }

        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            DictLabProfile2 vform = new DictLabProfile2(base.Insert, base.MyRow, base.MyTable);
            vform.MyTransmitTxt = base.MyTransmitTxt;
            vform.MyTransmitDataRow = base.MyTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        protected override void DataDelete()
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除检验组合：" + this.myGrid1.Columns["ProfileName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;

            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            _bllDictLabProfile.Delete(int.Parse(base.MyRow["ProfileId"].ToString()));
            myGrid1.Delete();
            base.MyTable.AcceptChanges();
            this.LblTotal.Text = "∑=" + myGrid1.Splits[0].Rows.Count.ToString();
        }

        private void DataRefresh()
        {
            this.DataInit();
        }

        private void DataExport()
        {
            Dictionary<string, Common.MdlExcel> colconfig = new Dictionary<string, Common.MdlExcel>();
            colconfig.Add("ProfileCode", new Common.MdlExcel(10, "", "编码", true));
            colconfig.Add("ProfileName", new Common.MdlExcel(15, "", "组合名称", true));
            colconfig.Add("PinYin", new Common.MdlExcel(10, "", "拼音码", true));
            colconfig.Add("EnglishName", new Common.MdlExcel(15, "", "英文名称", true));
            colconfig.Add("HisProfileCode", new Common.MdlExcel(12, "", "HIS编码", true));
            colconfig.Add("ReportTitle", new Common.MdlExcel(15, "", "报告标题", true));
            colconfig.Add("Description", new Common.MdlExcel(20, "", "描述", true));
            Common.ExcelHelper.Export(base.MyTable, "检验组合字典", colconfig);
        }

        private void DataImport()
        {
            string fileName;
            int tableColCnt;
            System.Windows.Forms.OpenFileDialog folderBrowserDialog = new System.Windows.Forms.OpenFileDialog();
            {
                folderBrowserDialog.InitialDirectory = Environment.SpecialFolder.Desktop.ToString();
                folderBrowserDialog.Filter = "xls文件|*.xls";
                folderBrowserDialog.Title = "选择文件";
            }
            System.Windows.Forms.DialogResult result = folderBrowserDialog.ShowDialog();
            if (result == System.Windows.Forms.DialogResult.OK)
            {
                fileName = folderBrowserDialog.FileName;
                DataTable dt = new DataTable();
                dt = Common.ExcelHelper.RenderDataTableFromExcel(fileName, 0, 1);
                tableColCnt = dt.Columns.Count;
                int i = 1;
                int successCnt = 0;
                int failCnt = 0;
                string[] UpdateData = new string[dt.Rows.Count];
                foreach (DataRow row in dt.Rows)
                {
                    Model.MdlDictLabProfile mdlDictLabProfile = new MdlDictLabProfile();
                    if (row["组合名称"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "组合名称为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    if (row["编码"].ToString().Trim() + "" == "")
                    {
                        UpdateData[i - 1] = "编码为空";
                        i = i + 1;
                        failCnt++;
                        continue;
                    }
                    mdlDictLabProfile.ProfileCode = row["编码"] + "";
                    mdlDictLabProfile.ProfileName = row["组合名称"] + "";
                    mdlDictLabProfile.PinYin = base.MyChs2Spell.GetPy(row["组合名称"].ToString());
                    mdlDictLabProfile.EnglishName = row["英文名称"] + "";
                    mdlDictLabProfile.HisProfileCode = row["HIS编码"] + "";
                    mdlDictLabProfile.ReportTitle = row["报告标题"] + "";
                    mdlDictLabProfile.Description = row["描述"] + "";
                    mdlDictLabProfile.Status = true;
                    mdlDictLabProfile.CreatedAt = DateTime.Now;
                    mdlDictLabProfile.UpdatedAt = DateTime.Now;

                    try
                    {
                        _bllDictLabProfile.Add(mdlDictLabProfile);
                        UpdateData[i - 1] = "导入成功";
                        successCnt++;
                    }
                    catch (Exception ex)
                    {
                        UpdateData[i - 1] = ex.Message;
                        failCnt++;
                    }
                    i = i + 1;
                }
                MessageBox.Show("导入结果：成功 " + successCnt + " 条，失败 " + failCnt + " 条。", "提示");
                this.DataRefresh();
            }
        }

        private void DataEnable(bool Status)
        {
            if (this.myGrid1.RowCount == 0)
            {
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            // 启用禁用功能已移除
            base.MyRow["Status"] = Status;
            base.MyRow["UpdatedAt"] = DateTime.Now;
        }

        #endregion

        #region 事件
        private void CmdAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEdit(true);
        }

        private void CmdDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataDelete();
        }

        private void CmdImport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataImport();
        }

        private void CmdExport_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataExport();
        }

        private void CmdEnable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(true);
        }

        private void CmdDisable_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataEnable(false);
        }

        private void CmdRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.DataRefresh();
        }

        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            base.DataFilter("ProfileName+ProfileCode+PinYin+EnglishName+HisProfileCode+ReportTitle+Description", TxtFilter.Text.Trim());
        }
        #endregion

        #region 项目映射管理
        private void ProfileItemFormInit()
        {
            myGrid2.Init_Grid();
            myGrid2.Init_Column("项目编码", "LabItemCode", 120, "中", "", false);
            myGrid2.Init_Column("项目名称", "LabItemName", 160, "左", "", false);
            myGrid2.Init_Column("拼音码", "PinYin", 120, "左", "", false);
            myGrid2.Init_Column("显示顺序", "SequenceNo", 80, "右", "", false);
            myGrid2.Init_Column("默认项目", "IsDefault", 80, "中", "", false);
            myGrid2.Init_Column("创建时间", "CreatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid2.Init_Column("更新时间", "UpdatedAt", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid2.Splits[0].DisplayColumns["IsDefault"].FetchStyle = true;
            myGrid2.AllowSort = true;

            _profileItemTransmitTxt.SetText += GridMove;
        }

        private void ProfileItemDataInit(int profileId)
        {
            if (profileId <= 0) return;

            // 使用标准查询方法获取项目映射数据
            string strWhere = $"ProfileId={profileId}";
            _profileItemTable = _bllDictLabProfileItemMap.GetList(strWhere).Tables[0];
            _profileItemTable.PrimaryKey = new DataColumn[] { _profileItemTable.Columns["MapId"] };
            _profileItemTable.Columns["MapId"].ReadOnly = false;
            _profileItemCm = (CurrencyManager)BindingContext[_profileItemTable, ""];
            this.myGrid2.DataTable = _profileItemTable;
        }

        private void GridMove(string moveType)
        {
            if (myGrid2.RowCount == 0)
                return;
            switch (moveType)
            {
                case "最前":
                    myGrid2.MoveFirst();
                    break;
                case "上移":
                    myGrid2.MovePrevious();
                    break;
                case "下移":
                    myGrid2.MoveNext();
                    break;
                case "最后":
                    myGrid2.MoveLast();
                    break;
                default:
                    int index;
                    if (int.TryParse(moveType, out index))
                    {
                        myGrid2.Row = index;
                    }
                    break;
            }
        }

        private void ProfileItemEdit(bool insert)
        {
            if (_profileId <= 0)
            {
                MessageBox.Show("请先选择检验组合！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            DataRow myProfileItemRow;
            if (insert == true)
            {
                myProfileItemRow = _profileItemTable.NewRow();
            }
            else
            {
                if (this.myGrid2.RowCount == 0)
                {
                    return;
                }
                myProfileItemRow = ((DataRowView)_profileItemCm.List[myGrid2.Row]).Row;
            }

            DictLabProfile3 vform = new DictLabProfile3(insert, myProfileItemRow, _profileItemTable, _profileId);
            vform.MyTransmitTxt = _profileItemTransmitTxt;
            vform.MyTransmitDataRow = _profileItemTransmitDataRow;
            if (base.CheckOwnForm(this, vform) == false)
            {
                vform.Owner = this;
                vform.Show();
            }
        }

        private void ProfileItemDelete()
        {
            if (this.myGrid2.RowCount == 0)
            {
                return;
            }

            if (MessageBox.Show("是否删除项目映射：" + this.myGrid2.Columns["LabItemName"].Value, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.Cancel)
                return;

            DataRow myProfileItemRow = ((DataRowView)_profileItemCm.List[myGrid2.Row]).Row;
            long mapId = long.Parse(myProfileItemRow["MapId"].ToString());

            try
            {
                // 使用新的删除方法，自动调整序号
                _bllDictLabProfileItemMap.DeleteWithSequenceUpdate(mapId, _profileId);
                myGrid2.Delete();
                _profileItemTable.AcceptChanges();

                // 重新加载数据以显示更新后的序号
                this.ProfileItemRefresh();

                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("删除成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show("删除失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ProfileItemRefresh()
        {
            this.ProfileItemDataInit(_profileId);
        }

        #region 项目映射按钮事件
        private void myGrid1_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            if (myGrid1.RowCount == 0) return;
            try
            {
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                _profileId = int.Parse(base.MyRow["ProfileId"].ToString());
                this.ProfileItemDataInit(_profileId);
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载项目映射数据失败：" + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void myGrid2_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
                this.ProfileItemEdit(false);
        }

        private void myGrid2_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            if ((myGrid2.Row + 1) > myGrid2.RowCount)
            {

            }
            else
            {
                MyRow = ((DataRowView)_profileItemCm.List[myGrid2.Row]).Row;
                this._profileItemTransmitDataRow.OnSetDataRow(MyRow);
            }
        }

        private void CmdItemAdd_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.ProfileItemEdit(true);
        }

        private void CmdItemDelete_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.ProfileItemDelete();
        }

        private void CmdItemRefresh_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            this.ProfileItemRefresh();
        }
        #endregion
        #endregion
    }
}