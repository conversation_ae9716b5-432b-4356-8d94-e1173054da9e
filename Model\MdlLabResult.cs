﻿/**  版本信息模板在安装目录下，可自行修改。
* MdlLabResult.cs
*
* 功 能： N/A
* 类 名： MdlLabResult
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025/6/10 14:46:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace Model
{
	/// <summary>
	/// 检验结果主表：保存审核通过的最终结果记录
	/// </summary>
	[Serializable]
	public partial class MdlLabResult
	{
		public MdlLabResult()
		{ }
		#region Model
		private long _resultid;
		private long _orderitemid;
		private int _instrumentid;
		private string _instritemcode;
		private string _resultvalue;
		private string _unit;
		private string _abnormalflag;
		private bool _issystemgenerated = false;
		private DateTime? _measuredat;
		private string _firstreviewedby;
		private DateTime? _firstreviewedat;
		private string _finalreviewedby;
		private DateTime? _finalreviewedat;
		private int _reviewstatus = 0;
		/// <summary>
		/// 主键 ID（自动增长）
		/// </summary>
		public long ResultId
		{
			set { _resultid = value; }
			get { return _resultid; }
		}
		/// <summary>
		/// 检验医嘱明细 ID（LabOrderItem.OrderItemId）
		/// </summary>
		public long OrderItemId
		{
			set { _orderitemid = value; }
			get { return _orderitemid; }
		}
		/// <summary>
		/// 仪器 ID（LabInstrument.InstrumentId）
		/// </summary>
		public int InstrumentId
		{
			set { _instrumentid = value; }
			get { return _instrumentid; }
		}
		/// <summary>
		/// 仪器项目代码（冗余存储，便于追溯）
		/// </summary>
		public string InstrItemCode
		{
			set { _instritemcode = value; }
			get { return _instritemcode; }
		}
		/// <summary>
		/// 结果值（字符或数字）
		/// </summary>
		public string ResultValue
		{
			set { _resultvalue = value; }
			get { return _resultvalue; }
		}
		/// <summary>
		/// 检验结果单位
		/// </summary>
		public string Unit
		{
			set { _unit = value; }
			get { return _unit; }
		}
		/// <summary>
		/// 异常标志：H=高 L=低 A=阳性 …
		/// </summary>
		public string AbnormalFlag
		{
			set { _abnormalflag = value; }
			get { return _abnormalflag; }
		}
		/// <summary>
		/// 是否系统生成：0=否 1=是
		/// </summary>
		public bool IsSystemGenerated
		{
			set { _issystemgenerated = value; }
			get { return _issystemgenerated; }
		}
		/// <summary>
		/// 仪器测定时间（DATETIME）
		/// </summary>
		public DateTime? MeasuredAt
		{
			set { _measuredat = value; }
			get { return _measuredat; }
		}
		/// <summary>
		/// 初审技师工号
		/// </summary>
		public string FirstReviewedBy
		{
			set { _firstreviewedby = value; }
			get { return _firstreviewedby; }
		}
		/// <summary>
		/// 初审通过时间
		/// </summary>
		public DateTime? FirstReviewedAt
		{
			set { _firstreviewedat = value; }
			get { return _firstreviewedat; }
		}
		/// <summary>
		/// 复审技师工号
		/// </summary>
		public string FinalReviewedBy
		{
			set { _finalreviewedby = value; }
			get { return _finalreviewedby; }
		}
		/// <summary>
		/// 复审通过时间
		/// </summary>
		public DateTime? FinalReviewedAt
		{
			set { _finalreviewedat = value; }
			get { return _finalreviewedat; }
		}
		/// <summary>
		/// 审核状态：0待审 1初审通过 2复审通过
		/// </summary>
		public int ReviewStatus
		{
			set { _reviewstatus = value; }
			get { return _reviewstatus; }
		}
		#endregion Model

	}
}

